{"version": 3, "sources": ["loggers.js"], "names": ["getTimeStamp", "just<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug", "warn", "error", "args", "console", "debugLogger"], "mappings": ";;;;;;;AAAA;MACOA,Y,4CAEP;AACA;;;AACA,MAAMC,gBAAgB,GAAG;AACvBC,EAAAA,KAAK,EAAE,MAAM,CAAE,CADQ;AAEvBC,EAAAA,IAAI,EAAE,MAAM,CAAE,CAFS;AAGvBC,EAAAA,KAAK,EAAE;AAAA,sCAAIC,IAAJ;AAAIA,MAAAA,IAAJ;AAAA;;AAAA,WAAaC,OAAO,CAACF,KAAR,CAAe,WAAUJ,YAAY,EAAG,GAAxC,EAA4C,GAAGK,IAA/C,CAAb;AAAA;AAHgB,CAAzB,C,CAMA;AACA;;;AACA,MAAME,WAAW,GAAG;AAClBL,EAAAA,KAAK,EAAE;AAAA,uCAAIG,IAAJ;AAAIA,MAAAA,IAAJ;AAAA;;AAAA,WAAaC,OAAO,CAACJ,KAAR,CAAe,WAAUF,YAAY,EAAG,GAAxC,EAA4C,GAAGK,IAA/C,CAAb;AAAA,GADW;AAElBF,EAAAA,IAAI,EAAE;AAAA,uCAAIE,IAAJ;AAAIA,MAAAA,IAAJ;AAAA;;AAAA,WAAaC,OAAO,CAACH,IAAR,CAAc,WAAUH,YAAY,EAAG,GAAvC,EAA2C,GAAGK,IAA9C,CAAb;AAAA,GAFY;AAGlBD,EAAAA,KAAK,EAAE;AAAA,uCAAIC,IAAJ;AAAIA,MAAAA,IAAJ;AAAA;;AAAA,WAAaC,OAAO,CAACF,KAAR,CAAe,WAAUJ,YAAY,EAAG,GAAxC,EAA4C,GAAGK,IAA/C,CAAb;AAAA;AAHW,CAApB", "sourcesContent": ["/* eslint-disable no-console */\nimport getTimeStamp from '@uppy/utils/lib/getTimeStamp'\n\n// Swallow all logs, except errors.\n// default if logger is not set or debug: false\nconst justErrorsLogger = {\n  debug: () => {},\n  warn: () => {},\n  error: (...args) => console.error(`[Uppy] [${getTimeStamp()}]`, ...args),\n}\n\n// Print logs to console with namespace + timestamp,\n// set by logger: Uppy.debugLogger or debug: true\nconst debugLogger = {\n  debug: (...args) => console.debug(`[Uppy] [${getTimeStamp()}]`, ...args),\n  warn: (...args) => console.warn(`[Uppy] [${getTimeStamp()}]`, ...args),\n  error: (...args) => console.error(`[Uppy] [${getTimeStamp()}]`, ...args),\n}\n\nexport {\n  justErrorsLogger,\n  debugLogger,\n}\n"]}