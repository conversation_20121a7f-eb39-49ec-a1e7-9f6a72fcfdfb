# 第五阶段功能测试指南

## 📋 测试概述

本指南涵盖第五阶段新增的所有功能，包括消息通知系统、用户个人中心优化、系统设置管理、用户管理、归档功能以及相关的问题修复。

## 🔧 测试环境准备

### 1. 启动服务
```bash
# 启动后端服务
cd blog-server
mvn spring-boot:run

# 启动前端服务（端口3000）
cd blog-web
npm run dev
```

### 2. 数据库准备
确保数据库中包含新的通知表：
```sql
-- 检查通知表是否存在
SHOW TABLES LIKE 'notification';

-- 如果不存在，执行以下SQL创建表
SOURCE blog-server/src/main/resources/db/notification.sql;
```

### 3. 测试用户准备
- **管理员账户**：admin / admin123 (角色：admin)
- **普通用户1**：user1 / password123 (角色：user)
- **普通用户2**：user2 / password123 (角色：user)

**权限说明**：
- **管理员**：可以发布文章、管理分类标签、审核评论、删除任何内容
- **普通用户**：可以评论、点赞收藏、关注用户，但不能发布文章

如果没有这些用户，请先注册创建。

## 📱 消息通知系统测试

### 测试用例1：点赞通知功能

#### 测试步骤：
1. **管理员登录** → 发布一篇文章（只有管理员能发布文章）
2. **普通用户2登录** → 浏览管理员的文章 → 点击点赞按钮
3. **切换到管理员** → 查看导航栏通知图标是否显示红点
4. **点击消息中心** → 验证是否收到点赞通知

#### 预期结果：
- ✅ 管理员应该收到"普通用户2 点赞了你的文章《文章标题》"的通知
- ✅ 导航栏通知图标显示未读数量
- ✅ 通知列表中显示点赞通知，包含发送者头像和昵称
- ✅ 点击通知可以跳转到对应文章

#### 测试数据：
```
发送者：user2 (昵称：普通用户2)
接收者：admin (昵称：管理员)
文章标题：《测试文章标题》
通知类型：点赞通知
```

### 测试用例2：收藏通知功能

#### 测试步骤：
1. **用户2登录** → 浏览用户1的文章 → 点击收藏按钮
2. **切换到用户1** → 检查是否收到收藏通知

#### 预期结果：
- ✅ 用户1收到"用户2 收藏了你的文章《文章标题》"的通知
- ✅ 通知类型为收藏通知
- ✅ 点击通知跳转到对应文章

### 测试用例3：评论通知功能

#### 测试步骤：
1. **用户2登录** → 在用户1的文章下发表评论
2. **切换到用户1** → 检查是否收到评论通知

#### 预期结果：
- ✅ 用户1收到"用户2 评论了你的文章《文章标题》"的通知
- ✅ 通知内容显示文章标题
- ✅ 点击通知跳转到文章评论区

### 测试用例4：回复通知功能

#### 测试步骤：
1. **用户1登录** → 在自己文章下发表评论
2. **用户2登录** → 回复用户1的评论
3. **切换到用户1** → 检查是否收到回复通知

#### 预期结果：
- ✅ 用户1收到"用户2 回复了你的评论"的通知
- ✅ 通知内容显示在哪篇文章中
- ✅ 点击通知跳转到对应评论

### 测试用例5：关注通知功能

#### 测试步骤：
1. **普通用户2登录** → 访问普通用户1的个人页面 → 点击关注按钮
2. **切换到普通用户1** → 检查是否收到关注通知

#### 预期结果：
- ✅ 普通用户1收到"普通用户2 关注了你"的通知
- ✅ 通知内容为"快去看看TA的动态吧！"
- ✅ 点击通知跳转到普通用户2的个人页面

### 测试用例6：防重复通知机制

#### 测试步骤：
1. **普通用户2登录** → 对同一篇文章连续点赞3次（点赞→取消→点赞→取消→点赞）
2. **切换到普通用户1** → 检查通知数量

#### 预期结果：
- ✅ 普通用户1只收到1条点赞通知（不是3条）
- ✅ 防重复机制正常工作

### 测试用例7：自己操作不发送通知

#### 测试步骤：
1. **普通用户1登录** → 对自己的文章点赞、收藏、评论
2. **检查消息中心** → 验证是否收到通知

#### 预期结果：
- ✅ 普通用户1不应该收到任何来自自己的通知
- ✅ 自己操作自己内容不触发通知

## 🔔 消息中心界面测试

### 测试用例8：消息中心页面功能

#### 测试步骤：
1. **准备测试数据** → 确保有多条不同类型的通知（已读和未读）
2. **访问消息中心** → `/user/notifications`
3. **测试筛选功能** → 点击"全部"、"未读"、"已读"标签
4. **测试批量操作** → 选中多条通知 → 点击"删除选中"
5. **测试全部标记已读** → 点击"全部标记已读"按钮

#### 预期结果：
- ✅ 页面正确显示通知列表
- ✅ 筛选功能正常工作
- ✅ 批量删除功能正常
- ✅ 全部标记已读功能正常
- ✅ 未读通知有视觉区分（蓝色边框）

### 测试用例9：导航栏通知图标

#### 测试步骤：
1. **确保有未读通知** → 创建一些未读通知
2. **检查导航栏** → 查看通知图标是否显示红色数字
3. **点击通知图标** → 验证是否跳转到消息中心
4. **标记通知已读** → 检查数字是否更新

#### 预期结果：
- ✅ 有未读通知时显示红色数字徽章
- ✅ 数字显示正确的未读数量（最大99+）
- ✅ 点击图标跳转到消息中心
- ✅ 标记已读后数字实时更新

## 👤 用户个人中心测试

### 测试用例10：个人资料页面优化

#### 测试步骤：
1. **登录任意用户** → 访问 `/user/profile`
2. **检查页面布局** → 验证卡片式设计
3. **查看统计信息** → 检查文章数、评论数等统计数据
4. **测试快捷操作** → 点击各个快捷按钮

#### 预期结果：
- ✅ 页面采用现代化卡片式布局
- ✅ 统计信息正确显示（可以是模拟数据）
- ✅ 快捷操作按钮正常跳转
- ✅ 消息中心按钮显示未读数量
- ✅ 响应式设计在移动端正常显示

### 测试用例11：头像上传功能

#### 测试步骤：
1. **访问个人资料页面** → 点击"更换头像"按钮
2. **上传头像** → 选择一张JPG/PNG图片（小于2MB）
3. **保存头像** → 点击保存按钮
4. **验证更新** → 检查头像是否在各处更新

#### 预期结果：
- ✅ 头像上传对话框正常打开
- ✅ 支持JPG/PNG格式验证
- ✅ 文件大小限制正常工作
- ✅ 头像上传成功后在导航栏、个人页面等处更新

### 测试用例12：个人资料编辑

#### 测试步骤：
1. **点击"编辑资料"按钮** → 打开编辑对话框
2. **修改昵称和邮箱** → 输入新的信息
3. **保存修改** → 点击保存按钮
4. **验证更新** → 检查信息是否更新

#### 预期结果：
- ✅ 编辑对话框正常打开
- ✅ 表单验证正常工作
- ✅ 保存成功后信息更新
- ✅ 昵称在通知等处也同步更新

## 🐛 问题修复验证测试

### 测试用例13：评论管理文章删除问题修复

#### 测试步骤：
1. **管理员登录** → 创建一篇文章
2. **普通用户登录** → 对该文章发表评论
3. **管理员登录** → 删除该文章
4. **访问评论管理页面** → `/admin/comment`
5. **查看该评论的显示** → 检查文章链接显示

#### 预期结果：
- ✅ 评论列表中该评论的文章显示为"(文章已删除)"
- ✅ 文章标题为灰色斜体，不可点击
- ✅ 不会出现"服务器端出错"的情况

### 测试用例14：文章详情页错误处理

#### 测试步骤：
1. **访问不存在的文章** → 直接访问 `/article/99999`
2. **检查错误处理** → 验证是否正确处理

#### 预期结果：
- ✅ 显示友好的错误提示："文章不存在或已被删除"
- ✅ 自动跳转到404页面
- ✅ 不显示技术性错误信息

## 📱 移动端响应式测试

### 测试用例15：移动端适配

#### 测试步骤：
1. **打开浏览器开发者工具** → 切换到移动设备模式
2. **测试消息中心页面** → 检查布局是否正常
3. **测试个人资料页面** → 检查卡片布局适配
4. **测试通知操作** → 验证按钮大小和间距

#### 预期结果：
- ✅ 消息中心在移动端布局合理
- ✅ 个人资料页面统计卡片自适应排列
- ✅ 按钮大小适合触摸操作
- ✅ 文字大小和间距合适

## 🧪 自动化测试运行

### 后端单元测试
```bash
cd blog-server
mvn test -Dtest=NotificationServiceTest
```

### 后端集成测试
```bash
cd blog-server
mvn test -Dtest=NotificationIntegrationTest
```

### 前端组件测试
```bash
cd blog-web
npm run test
```

## 📊 测试报告模板

### 测试执行记录表

| 测试用例 | 执行状态 | 结果 | 备注 |
|---------|---------|------|------|
| 测试用例1：点赞通知 | ⭕ 待测试 | - | - |
| 测试用例2：收藏通知 | ⭕ 待测试 | - | - |
| 测试用例3：评论通知 | ⭕ 待测试 | - | - |
| 测试用例4：回复通知 | ⭕ 待测试 | - | - |
| 测试用例5：关注通知 | ⭕ 待测试 | - | - |
| 测试用例6：防重复通知 | ⭕ 待测试 | - | - |
| 测试用例7：自己操作不通知 | ⭕ 待测试 | - | - |
| 测试用例8：消息中心页面 | ⭕ 待测试 | - | - |
| 测试用例9：导航栏通知图标 | ⭕ 待测试 | - | - |
| 测试用例10：个人资料页面 | ⭕ 待测试 | - | - |
| 测试用例11：头像上传 | ⭕ 待测试 | - | - |
| 测试用例12：资料编辑 | ⭕ 待测试 | - | - |
| 测试用例13：评论管理修复 | ⭕ 待测试 | - | - |
| 测试用例14：错误处理 | ⭕ 待测试 | - | - |
| 测试用例15：移动端适配 | ⭕ 待测试 | - | - |
| 测试用例16：归档功能 | ⭕ 待测试 | - | - |
| 测试用例17：用户管理 | ⭕ 待测试 | - | - |
| 测试用例18：系统设置 | ⭕ 待测试 | - | - |
| 测试用例19：权限验证 | ⭕ 待测试 | - | - |
| 测试用例20：数据一致性 | ⭕ 待测试 | - | - |

### 结果标记说明：
- ✅ 通过
- ❌ 失败
- ⚠️ 部分通过
- ⭕ 待测试

## 🚨 常见问题排查

### 1. 通知不显示
- 检查数据库连接
- 确认notification表是否存在
- 检查浏览器控制台错误

### 2. 头像上传失败
- 检查文件格式和大小
- 确认上传目录权限
- 检查后端上传配置

### 3. 页面样式异常
- 清除浏览器缓存
- 检查CSS文件是否正确加载
- 验证Element Plus版本兼容性

## 🗂️ 归档功能测试

### 测试用例1：文章归档展示

#### 测试步骤：
1. **管理员登录** → 确保系统中有多篇不同时间发布的文章
2. **访问归档页面** → 点击导航栏"归档"菜单
3. **验证归档展示** → 检查文章是否按年月正确分组显示
4. **测试文章跳转** → 点击归档中的文章标题，验证是否正确跳转到文章详情

#### 预期结果：
- ✅ 归档页面正常加载，显示按年月分组的文章列表
- ✅ 文章按创建时间倒序排列（最新的年月在前）
- ✅ 点击文章标题能正确跳转到文章详情页面
- ✅ 如果没有文章，显示"暂无文章"的空状态

#### 测试数据：
- 确保数据库中至少有2-3篇不同月份的文章用于测试

## 👥 用户管理功能测试

### 测试用例1：用户列表查询

#### 测试步骤：
1. **管理员登录** → 访问 `/admin/user` 页面
2. **查看用户列表** → 验证是否显示所有用户信息
3. **测试搜索功能** → 在用户名搜索框输入关键词，点击搜索
4. **测试状态筛选** → 选择不同的用户状态进行筛选
5. **测试分页功能** → 如果用户较多，测试分页是否正常

#### 预期结果：
- ✅ 用户列表正常显示，包含用户名、邮箱、角色、状态等信息
- ✅ 搜索功能能根据用户名模糊查询
- ✅ 状态筛选功能正常工作
- ✅ 分页功能正常，显示总数和页码

### 测试用例2：用户状态管理

#### 测试步骤：
1. **在用户列表中** → 找到一个普通用户
2. **切换用户状态** → 点击状态开关，禁用用户
3. **验证状态变更** → 确认用户状态已更新
4. **重新启用用户** → 再次点击开关，启用用户
5. **测试管理员保护** → 尝试禁用管理员账户（应该被阻止）

#### 预期结果：
- ✅ 普通用户状态可以正常切换（启用/禁用）
- ✅ 状态变更后立即在列表中反映
- ✅ 管理员账户受到保护，不能被禁用
- ✅ 操作成功后显示相应的提示信息

### 测试用例3：用户删除功能

#### 测试步骤：
1. **创建测试用户** → 先注册一个测试用户账号
2. **删除普通用户** → 在用户列表中点击删除按钮
3. **确认删除操作** → 在弹出的确认框中点击确定
4. **验证删除结果** → 确认用户已从列表中移除
5. **测试管理员保护** → 尝试删除管理员账户（应该被阻止）

#### 预期结果：
- ✅ 普通用户可以正常删除
- ✅ 删除前有确认提示
- ✅ 删除后用户从列表中消失
- ✅ 管理员账户不能被删除
- ✅ 显示相应的成功或错误提示

## ⚙️ 系统设置功能测试

### 测试用例1：基本设置管理

#### 测试步骤：
1. **管理员登录** → 访问 `/admin/setting` 页面
2. **查看当前设置** → 验证是否显示默认的系统设置
3. **修改基本设置** → 更改网站标题、描述、关键词等
4. **保存设置** → 点击"保存基本设置"按钮
5. **验证保存结果** → 刷新页面，确认设置已保存

#### 预期结果：
- ✅ 设置页面正常加载，显示当前配置
- ✅ 可以修改网站标题、描述、关键词、Logo、ICP备案等
- ✅ 保存操作成功，显示成功提示
- ✅ 刷新页面后设置值保持不变

### 测试用例2：评论设置管理

#### 测试步骤：
1. **在系统设置页面** → 切换到评论设置选项卡
2. **修改评论设置** → 切换"评论审核"和"允许匿名评论"开关
3. **保存评论设置** → 点击"保存评论设置"按钮
4. **验证设置生效** → 刷新页面确认设置已保存

#### 预期结果：
- ✅ 评论设置选项卡正常显示
- ✅ 可以切换评论审核和匿名评论开关
- ✅ 保存操作成功，显示成功提示
- ✅ 设置值正确保存和显示

### 测试用例3：邮件设置管理

#### 测试步骤：
1. **在系统设置页面** → 切换到邮件设置选项卡
2. **配置邮件参数** → 填写SMTP服务器、端口、邮箱等信息
3. **保存邮件设置** → 点击"保存邮件设置"按钮
4. **测试邮件连接** → 点击"测试邮件设置"按钮
5. **验证测试结果** → 查看测试是否成功

#### 预期结果：
- ✅ 邮件设置选项卡正常显示
- ✅ 可以配置SMTP服务器、端口、发件人邮箱等
- ✅ 保存操作成功，显示成功提示
- ✅ 邮件测试功能正常（显示测试结果）

## 📧 邮件模板系统测试

### 测试用例1：邮件模板管理测试

#### 测试步骤：
1. **管理员登录** → 使用admin账号登录管理后台
2. **访问邮件模板页面** → 导航到 管理后台 → 邮件模板
3. **查看模板列表** → 确认显示所有邮件模板
4. **编辑模板** → 点击某个模板的"编辑"按钮
5. **修改模板内容** → 更改模板名称、主题或内容
6. **保存模板** → 点击"保存"按钮
7. **预览模板** → 点击"预览"按钮查看效果

#### 预期结果：
- ✅ 邮件模板页面正常显示
- ✅ 模板列表包含：测试邮件、评论通知、回复通知、点赞通知、关注通知、欢迎邮件
- ✅ 编辑功能正常，支持HTML内容编辑
- ✅ 保存操作成功，显示成功提示
- ✅ 预览功能正常显示模板效果
- ✅ 模板状态可以切换（启用/禁用）

### 测试用例2：邮件配置测试

#### 测试步骤：
1. **配置邮件服务** → 在系统设置 → 邮件设置中配置163邮箱
   - SMTP服务器：smtp.163.com
   - 端口：25
   - 发件人邮箱：您的163邮箱
   - 用户名：您的163邮箱
   - 密码：163邮箱授权码
2. **保存邮件配置** → 点击"保存邮件设置"
3. **测试邮件发送** → 点击"测试邮件配置"按钮
4. **检查邮件接收** → 查看配置的邮箱是否收到测试邮件

#### 预期结果：
- ✅ 邮件配置保存成功
- ✅ 测试邮件发送成功（后端日志显示发送成功）
- ✅ 收到HTML格式的测试邮件
- ✅ 邮件内容包含配置信息（SMTP服务器、端口、测试时间）
- ✅ 邮件样式美观（蓝紫色渐变头部，白色内容区域）

### 测试用例3：评论邮件通知测试

#### 测试步骤：
1. **准备测试环境** → 确保admin用户邮箱已设置为您的邮箱
2. **普通用户登录** → 使用chlingyu或其他普通用户登录
3. **评论文章** → 在admin发布的文章下发表评论
4. **检查后端日志** → 观察控制台是否显示"评论邮件通知发送成功"
5. **检查邮件接收** → 查看admin用户邮箱是否收到评论通知

#### 预期结果：
- ✅ 评论发布成功
- ✅ 后端日志显示：`评论邮件通知发送成功: to=邮箱地址, articleId=xx, commentId=xx`
- ✅ 收到评论通知邮件
- ✅ 邮件主题：您收到了新评论 - [文章标题]
- ✅ 邮件内容包含：评论者姓名、文章标题、评论内容、查看评论按钮
- ✅ 邮件样式：蓝色渐变头部，评论内容有蓝色边框

### 测试用例4：回复邮件通知测试

#### 测试步骤：
1. **找到现有评论** → 在文章详情页找到一条评论
2. **回复评论** → 点击评论的"回复"按钮，输入回复内容
3. **发送回复** → 提交回复内容
4. **检查后端日志** → 观察控制台是否显示"回复邮件通知发送成功"
5. **检查邮件接收** → 查看被回复用户的邮箱是否收到通知

#### 预期结果：
- ✅ 回复发布成功
- ✅ 后端日志显示：`回复邮件通知发送成功: to=邮箱地址, articleId=xx, replyId=xx`
- ✅ 收到回复通知邮件
- ✅ 邮件主题：您的评论收到了回复 - [文章标题]
- ✅ 邮件内容包含：回复者姓名、原评论内容、回复内容、查看回复按钮
- ✅ 邮件样式：粉色渐变头部，原评论蓝色框，回复内容橙色框

### 测试用例5：邮件模板变量替换测试

#### 测试步骤：
1. **编辑评论通知模板** → 在邮件模板管理中编辑COMMENT_NOTIFICATION模板
2. **添加测试变量** → 在模板中添加 `{{USER_NAME}}` 和 `{{ARTICLE_TITLE}}` 变量
3. **保存模板** → 保存修改后的模板
4. **触发评论通知** → 发表评论触发邮件发送
5. **检查变量替换** → 查看收到的邮件中变量是否正确替换

#### 预期结果：
- ✅ 模板编辑和保存成功
- ✅ 邮件发送时变量正确替换
- ✅ `{{USER_NAME}}` 替换为实际的用户昵称
- ✅ `{{ARTICLE_TITLE}}` 替换为实际的文章标题
- ✅ `{{COMMENT_CONTENT}}` 替换为实际的评论内容
- ✅ `{{ARTICLE_URL}}` 替换为正确的文章链接

## 🔍 新增功能综合测试

### 测试用例1：权限验证测试

#### 测试步骤：
1. **普通用户登录** → 尝试访问 `/admin/user` 和 `/admin/setting`
2. **验证权限控制** → 确认普通用户无法访问管理功能
3. **管理员登录** → 验证管理员可以正常访问所有功能
4. **测试API权限** → 直接调用管理API，验证权限控制

#### 预期结果：
- ✅ 普通用户无法访问用户管理和系统设置页面
- ✅ 管理员可以正常访问所有管理功能
- ✅ API层面的权限控制正常工作
- ✅ 未授权访问时显示适当的错误提示

### 测试用例2：数据一致性测试

#### 测试步骤：
1. **修改系统设置** → 保存一些配置更改
2. **重启后端服务** → 验证设置是否持久化
3. **多用户操作** → 同时进行用户管理和设置修改
4. **验证数据完整性** → 确认所有操作都正确保存

#### 预期结果：
- ✅ 系统设置正确持久化到数据库
- ✅ 服务重启后设置不丢失
- ✅ 并发操作不会导致数据冲突
- ✅ 数据库中的数据完整且一致

## 🐛 新增功能常见问题

### 1. 归档页面空白
- 检查是否有已发布的文章
- 确认后端归档API是否正常响应
- 检查浏览器控制台是否有JavaScript错误

### 2. 用户管理页面无数据
- 确认当前用户是否为管理员
- 检查用户管理API是否正常
- 验证数据库中是否有用户数据

### 3. 系统设置保存失败
- 确认system_setting表是否存在
- 检查管理员权限是否正确
- 查看后端日志是否有错误信息

### 4. 权限验证失败
- 确认JWT token是否有效
- 检查用户角色是否正确设置
- 验证Spring Security配置是否正确

### 5. 邮件发送失败
- **检查邮件配置**：确认SMTP服务器、端口、邮箱、授权码配置正确
- **查看后端日志**：搜索关键词"邮件发送"、"EMAIL"、"SMTP"
- **测试基础邮件**：先使用"测试邮件配置"功能验证基础邮件发送
- **检查邮箱设置**：确认163邮箱已开启SMTP服务并获取授权码
- **网络连接**：确认服务器可以访问smtp.163.com:25

### 6. 邮件模板问题
- **模板不存在**：检查数据库email_template表是否有对应的模板记录
- **变量替换失败**：确认模板中的变量格式为{{变量名}}
- **HTML显示异常**：检查邮件客户端是否支持HTML格式
- **模板状态**：确认模板状态为启用状态（status=1）

### 7. 邮件接收问题
- **检查垃圾邮件文件夹**：邮件可能被邮箱服务商拦截
- **邮件延迟**：等待5-10分钟，邮件服务器可能有延迟
- **收件人邮箱**：确认用户邮箱地址正确且有效
- **邮件客户端**：尝试不同的邮件客户端查看邮件

## 📞 测试支持

如果在测试过程中遇到问题，请记录：
1. 具体的测试步骤
2. 预期结果 vs 实际结果
3. 浏览器控制台错误信息
4. 网络请求响应信息

这样我可以快速定位和解决问题。

## ✅ 测试完成状态更新

### 最后更新时间：2025年7月15日

#### 已完成测试验证的功能：
- [x] **消息通知系统**：点赞、评论、回复、关注、收藏通知全部正常
- [x] **用户个人中心**：头像上传、资料编辑、统计信息显示正常
- [x] **归档功能**：文章按年月分组显示正常，跳转功能正常
- [x] **用户管理功能**：用户列表查询、状态管理、删除功能正常
- [x] **系统设置功能**：基本设置、评论设置、邮件设置保存和生效正常
- [x] **邮件模板系统**：模板管理、邮件发送、HTML渲染正常
- [x] **邮件通知功能**：评论通知、回复通知、测试邮件发送正常

#### 问题修复验证：
- [x] **邮件HTML显示问题**：已修复，HTML邮件正确渲染
- [x] **系统设置生效问题**：已修复，设置保存后立即生效
- [x] **用户管理状态控制问题**：已修复，状态切换正常工作

#### 测试环境验证：
- [x] **163邮箱配置**：<EMAIL> 邮件发送接收正常
- [x] **权限控制**：管理员和普通用户权限区分正确
- [x] **数据一致性**：所有统计数据准确无误
- [x] **界面交互**：所有页面响应正常，用户体验良好

**测试结论**：第五阶段所有功能测试通过，系统运行稳定，可投入生产使用。
