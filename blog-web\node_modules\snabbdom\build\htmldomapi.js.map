{"version": 3, "file": "htmldomapi.js", "sourceRoot": "", "sources": ["../src/htmldomapi.ts"], "names": [], "mappings": "AA6CA,SAAS,aAAa,CACpB,OAAY,EACZ,OAAgC;IAEhC,OAAO,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,eAAe,CACtB,YAAoB,EACpB,aAAqB,EACrB,OAAgC;IAEhC,OAAO,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,sBAAsB;IAC7B,OAAO,aAAa,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,cAAc,CAAC,IAAY;IAClC,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,aAAa,CAAC,IAAY;IACjC,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,YAAY,CACnB,UAAgB,EAChB,OAAa,EACb,aAA0B;IAE1B,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE;QAClC,IAAI,IAAI,GAAgB,UAAU,CAAC;QACnC,OAAO,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACvC,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;SACxB;QACD,UAAU,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,UAAU,CAAC;KACjC;IACD,IAAI,kBAAkB,CAAC,OAAO,CAAC,EAAE;QAC/B,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;KAC9C;IACD,IAAI,aAAa,IAAI,kBAAkB,CAAC,aAAa,CAAC,EAAE;QACtD,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC;KAC7D;IACD,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,WAAW,CAAC,IAAU,EAAE,KAAW;IAC1C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,WAAW,CAAC,IAAU,EAAE,KAAW;IAC1C,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE;QAC7B,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KACpC;IACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,UAAU,CAAC,IAAU;IAC5B,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACvC,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,GAAG,QAAQ,CAAC,MAAc,CAAC;SAChC;QACD,OAAO,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC;KACrB;IACD,OAAO,IAAI,CAAC,UAAU,CAAC;AACzB,CAAC;AAED,SAAS,WAAW,CAAC,IAAU;;IAC7B,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAC5B,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,MAAM,IAAI,QAAQ,CAAC,aAAa,EAAE;YACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YACvD,OAAO,MAAA,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,mCAAI,IAAI,CAAC;SACpC;QACD,OAAO,IAAI,CAAC;KACb;IACD,OAAO,IAAI,CAAC,WAAW,CAAC;AAC1B,CAAC;AAED,SAAS,OAAO,CAAC,GAAY;IAC3B,OAAO,GAAG,CAAC,OAAO,CAAC;AACrB,CAAC;AAED,SAAS,cAAc,CAAC,IAAU,EAAE,IAAmB;IACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B,CAAC;AAED,SAAS,cAAc,CAAC,IAAU;IAChC,OAAO,IAAI,CAAC,WAAW,CAAC;AAC1B,CAAC;AAED,SAAS,SAAS,CAAC,IAAU;IAC3B,OAAO,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,MAAM,CAAC,IAAU;IACxB,OAAO,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,SAAS,CAAC,IAAU;IAC3B,OAAO,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAU;IACpC,OAAO,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,aAAa,CACpB,YAA8B,EAC9B,UAAwB;;IAExB,MAAM,QAAQ,GAAG,YAAgC,CAAC;IAClD,MAAA,QAAQ,CAAC,MAAM,oCAAf,QAAQ,CAAC,MAAM,GAAK,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,IAAI,EAAC;IACvC,MAAA,QAAQ,CAAC,cAAc,oCAAvB,QAAQ,CAAC,cAAc,GAAK,YAAY,CAAC,UAAU,EAAC;IACpD,MAAA,QAAQ,CAAC,aAAa,oCAAtB,QAAQ,CAAC,aAAa,GAAK,YAAY,CAAC,SAAS,EAAC;IAClD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,CAAC,MAAM,UAAU,GAAW;IAChC,aAAa;IACb,eAAe;IACf,cAAc;IACd,sBAAsB;IACtB,aAAa;IACb,YAAY;IACZ,WAAW;IACX,WAAW;IACX,UAAU;IACV,WAAW;IACX,OAAO;IACP,cAAc;IACd,cAAc;IACd,SAAS;IACT,MAAM;IACN,SAAS;IACT,kBAAkB;CACnB,CAAC"}