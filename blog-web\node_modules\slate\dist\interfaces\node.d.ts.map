{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../packages/slate/src/interfaces/node.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,IAAI,CAAA;AAC9C,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAA;AAEjD;;;GAGG;AAEH,oBAAY,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAA;AAC9C,oBAAY,IAAI,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAA;AAE1C,MAAM,WAAW,aAAa;IAC5B,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,QAAQ,CAAA;IAC9C,SAAS,EAAE,CACT,IAAI,EAAE,IAAI,EACV,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,EAAE,OAAO,CAAA;KAClB,KACE,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACpD,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,KAAK,UAAU,CAAA;IAChD,QAAQ,EAAE,CACR,IAAI,EAAE,IAAI,EACV,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,EAAE,OAAO,CAAA;KAClB,KACE,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACtD,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,KAAK,SAAS,CAAA;IAC5D,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,UAAU,CAAA;IAClD,WAAW,EAAE,CACX,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,IAAI,CAAA;QACX,EAAE,CAAC,EAAE,IAAI,CAAA;QACT,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,OAAO,CAAA;KACpC,KACE,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACtD,QAAQ,EAAE,CACR,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,IAAI,CAAA;QACX,EAAE,CAAC,EAAE,IAAI,CAAA;QACT,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,OAAO,CAAA;KACpC,KACE,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IAC7C,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,SAAS,CAAA;IACvC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,SAAS,CAAA;IAC5C,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,KAAK,UAAU,EAAE,CAAA;IACpD,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,CAAA;IACrC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,OAAO,CAAA;IACxC,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,IAAI,CAAA;IACrC,UAAU,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,IAAI,EAAE,CAAA;IAC3C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,SAAS,CAAA;IAC3C,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,CAAA;IACtC,MAAM,EAAE,CACN,IAAI,EAAE,IAAI,EACV,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,EAAE,OAAO,CAAA;KAClB,KACE,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IAC1C,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,OAAO,CAAA;IACtD,KAAK,EAAE,CACL,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,IAAI,CAAA;QACX,EAAE,CAAC,EAAE,IAAI,CAAA;QACT,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,OAAO,CAAA;KACrC,KACE,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IAC1C,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,QAAQ,CAAA;IAC5C,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,MAAM,CAAA;IAC9B,KAAK,EAAE,CACL,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,IAAI,CAAA;QACX,EAAE,CAAC,EAAE,IAAI,CAAA;QACT,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,OAAO,CAAA;KACpC,KACE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;CACjD;AAID,eAAO,MAAM,IAAI,EAAE,aAkgBlB,CAAA;AAED;;;;GAIG;AAEH,oBAAY,UAAU,GAAG,OAAO,GAAG,IAAI,CAAA;AAEvC;;;;GAIG;AAEH,oBAAY,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAA;AAEvC;;;;GAIG;AAEH,oBAAY,SAAS,CAAC,CAAC,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AAExD;;GAEG;AACH,oBAAY,SAAS,GACjB,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,GACxB,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,GACzB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA"}