{"version": 3, "sources": ["locale.js"], "names": ["strings", "addBulkFilesFailed", "youCanOnlyUploadX", "youHaveToAtLeastSelectX", "exceedsSize", "missingRequiredMetaField", "missingRequiredMetaFieldOnFile", "inferiorSize", "youCanOnlyUploadFileTypes", "noMoreFilesAllowed", "noDuplicates", "companionError", "authAborted", "companionUnauthorizeHint", "failedToUpload", "noInternetConnection", "connectedToInternet", "noFilesFound", "selectX", "allFilesFromFolderNamed", "openFolderNamed", "cancel", "logOut", "filter", "resetFilter", "loading", "authenticateWithTitle", "authenticateWith", "signInWithGoogle", "searchImages", "enterTextToSearch", "search", "emptyFolderAdded", "folderAlreadyAdded", "folderAdded"], "mappings": ";;iBAAe;AACbA,EAAAA,OAAO,EAAE;AACPC,IAAAA,kBAAkB,EAAE;AAClB,SAAG,4DADe;AAElB,SAAG;AAFe,KADb;AAKPC,IAAAA,iBAAiB,EAAE;AACjB,SAAG,yCADc;AAEjB,SAAG;AAFc,KALZ;AASPC,IAAAA,uBAAuB,EAAE;AACvB,SAAG,iDADoB;AAEvB,SAAG;AAFoB,KATlB;AAaPC,IAAAA,WAAW,EAAE,iDAbN;AAcPC,IAAAA,wBAAwB,EAAE,8BAdnB;AAePC,IAAAA,8BAA8B,EAC5B,6CAhBK;AAiBPC,IAAAA,YAAY,EAAE,uDAjBP;AAkBPC,IAAAA,yBAAyB,EAAE,+BAlBpB;AAmBPC,IAAAA,kBAAkB,EAAE,uBAnBb;AAoBPC,IAAAA,YAAY,EACV,gEArBK;AAsBPC,IAAAA,cAAc,EAAE,kCAtBT;AAuBPC,IAAAA,WAAW,EAAE,wBAvBN;AAwBPC,IAAAA,wBAAwB,EACtB,iEAzBK;AA0BPC,IAAAA,cAAc,EAAE,0BA1BT;AA2BPC,IAAAA,oBAAoB,EAAE,wBA3Bf;AA4BPC,IAAAA,mBAAmB,EAAE,2BA5Bd;AA6BP;AACAC,IAAAA,YAAY,EAAE,mCA9BP;AA+BPC,IAAAA,OAAO,EAAE;AACP,SAAG,uBADI;AAEP,SAAG;AAFI,KA/BF;AAmCPC,IAAAA,uBAAuB,EAAE,+BAnClB;AAoCPC,IAAAA,eAAe,EAAE,qBApCV;AAqCPC,IAAAA,MAAM,EAAE,QArCD;AAsCPC,IAAAA,MAAM,EAAE,SAtCD;AAuCPC,IAAAA,MAAM,EAAE,QAvCD;AAwCPC,IAAAA,WAAW,EAAE,cAxCN;AAyCPC,IAAAA,OAAO,EAAE,YAzCF;AA0CPC,IAAAA,qBAAqB,EACnB,wDA3CK;AA4CPC,IAAAA,gBAAgB,EAAE,0BA5CX;AA6CPC,IAAAA,gBAAgB,EAAE,qBA7CX;AA8CPC,IAAAA,YAAY,EAAE,mBA9CP;AA+CPC,IAAAA,iBAAiB,EAAE,iCA/CZ;AAgDPC,IAAAA,MAAM,EAAE,QAhDD;AAiDPC,IAAAA,gBAAgB,EAAE,uCAjDX;AAkDPC,IAAAA,kBAAkB,EAAE,0CAlDb;AAmDPC,IAAAA,WAAW,EAAE;AACX,SAAG,0CADQ;AAEX,SAAG;AAFQ;AAnDN;AADI,C", "sourcesContent": ["export default {\n  strings: {\n    addBulkFilesFailed: {\n      0: 'Failed to add %{smart_count} file due to an internal error',\n      1: 'Failed to add %{smart_count} files due to internal errors',\n    },\n    youCanOnlyUploadX: {\n      0: 'You can only upload %{smart_count} file',\n      1: 'You can only upload %{smart_count} files',\n    },\n    youHaveToAtLeastSelectX: {\n      0: 'You have to select at least %{smart_count} file',\n      1: 'You have to select at least %{smart_count} files',\n    },\n    exceedsSize: '%{file} exceeds maximum allowed size of %{size}',\n    missingRequiredMetaField: 'Missing required meta fields',\n    missingRequiredMetaFieldOnFile:\n      'Missing required meta fields in %{fileName}',\n    inferiorSize: 'This file is smaller than the allowed size of %{size}',\n    youCanOnlyUploadFileTypes: 'You can only upload: %{types}',\n    noMoreFilesAllowed: 'Cannot add more files',\n    noDuplicates:\n      \"Cannot add the duplicate file '%{fileName}', it already exists\",\n    companionError: 'Connection with <PERSON> failed',\n    authAborted: 'Authentication aborted',\n    companionUnauthorizeHint:\n      'To unauthorize to your %{provider} account, please go to %{url}',\n    failedToUpload: 'Failed to upload %{file}',\n    noInternetConnection: 'No Internet connection',\n    connectedToInternet: 'Connected to the Internet',\n    // Strings for remote providers\n    noFilesFound: 'You have no files or folders here',\n    selectX: {\n      0: 'Select %{smart_count}',\n      1: 'Select %{smart_count}',\n    },\n    allFilesFromFolderNamed: 'All files from folder %{name}',\n    openFolderNamed: 'Open folder %{name}',\n    cancel: 'Cancel',\n    logOut: 'Log out',\n    filter: 'Filter',\n    resetFilter: 'Reset filter',\n    loading: 'Loading...',\n    authenticateWithTitle:\n      'Please authenticate with %{pluginName} to select files',\n    authenticateWith: 'Connect to %{pluginName}',\n    signInWithGoogle: 'Sign in with Google',\n    searchImages: 'Search for images',\n    enterTextToSearch: 'Enter text to search for images',\n    search: 'Search',\n    emptyFolderAdded: 'No files were added from empty folder',\n    folderAlreadyAdded: 'The folder \"%{folder}\" was already added',\n    folderAdded: {\n      0: 'Added %{smart_count} file from %{folder}',\n      1: 'Added %{smart_count} files from %{folder}',\n    },\n  },\n}\n"]}