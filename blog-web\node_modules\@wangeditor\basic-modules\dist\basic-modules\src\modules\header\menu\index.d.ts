/**
 * @description menu entry
 * <AUTHOR>
 */
import HeaderSelectMenu from './HeaderSelectMenu';
import Header1ButtonMenu from './Header1ButtonMenu';
import Header2ButtonMenu from './Header2ButtonMenu';
import Header3ButtonMenu from './Header3ButtonMenu';
import Header4ButtonMenu from './Header4ButtonMenu';
import Header5ButtonMenu from './Header5ButtonMenu';
export declare const HeaderSelectMenuConf: {
    key: string;
    factory(): HeaderSelectMenu;
};
export declare const Header1ButtonMenuConf: {
    key: string;
    factory(): Header1ButtonMenu;
};
export declare const Header2ButtonMenuConf: {
    key: string;
    factory(): Header2ButtonMenu;
};
export declare const Header3ButtonMenuConf: {
    key: string;
    factory(): Header3ButtonMenu;
};
export declare const Header4ButtonMenuConf: {
    key: string;
    factory(): Header4ButtonMenu;
};
export declare const Header5ButtonMenuConf: {
    key: string;
    factory(): Header5ButtonMenu;
};
