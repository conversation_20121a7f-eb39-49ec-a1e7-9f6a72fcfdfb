!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@wangeditor/core"),require("snabbdom"),require("slate"),require("dom7"),require("nanoid"),require("lodash.throttle")):"function"==typeof define&&define.amd?define(["exports","@wangeditor/core","snabbdom","slate","dom7","nanoid","lodash.throttle"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).WangEditorBasicModules={},t.core,t.snabbdom,t.slate,t.$,t.nanoid,t.throttle)}(this,(function(t,e,n,r,o,i,u){"use strict";function a(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var c=a(o),l=a(u);e.i18nAddResources("en",{common:{ok:"OK",delete:"Delete",enter:"Enter"},blockQuote:{title:"Quote"},codeBlock:{title:"Code block"},color:{color:"Font color",bgColor:"Back color",default:"Default color",clear:"Clear back color"},divider:{title:"Divider"},emotion:{title:"Emotion"},fontSize:{title:"Font size",default:"Default"},fontFamily:{title:"Font family",default:"Default"},fullScreen:{title:"Full screen"},header:{title:"Header",text:"Text"},image:{netImage:"Net image",delete:"Delete image",edit:"Edit image",viewLink:"View link",src:"Image src",desc:"Description",link:"Image link"},indent:{decrease:"Decrease",increase:"Increase"},justify:{left:"Left",right:"Right",center:"Center",justify:"Justify"},lineHeight:{title:"Line height",default:"Default"},link:{insert:"Insert link",text:"Link text",url:"Link source",unLink:"Unlink",edit:"Edit link",view:"View link"},textStyle:{bold:"Bold",clear:"Clear styles",code:"Inline code",italic:"Italic",sub:"Sub",sup:"Sup",through:"Through",underline:"Underline"},undo:{undo:"undo",redo:"Redo"},todo:{todo:"Todo"}}),e.i18nAddResources("zh-CN",{common:{ok:"确定",delete:"删除",enter:"回车"},blockQuote:{title:"引用"},codeBlock:{title:"代码块"},color:{color:"文字颜色",bgColor:"背景色",default:"默认颜色",clear:"清除背景色"},divider:{title:"分割线"},emotion:{title:"表情"},fontSize:{title:"字号",default:"默认字号"},fontFamily:{title:"字体",default:"默认字体"},fullScreen:{title:"全屏"},header:{title:"标题",text:"正文"},image:{netImage:"网络图片",delete:"删除图片",edit:"编辑图片",viewLink:"查看链接",src:"图片地址",desc:"图片描述",link:"图片链接"},indent:{decrease:"减少缩进",increase:"增加缩进"},justify:{left:"左对齐",right:"右对齐",center:"居中对齐",justify:"两端对齐"},lineHeight:{title:"行高",default:"默认行高"},link:{insert:"插入链接",text:"链接文本",url:"链接地址",unLink:"取消链接",edit:"修改链接",view:"查看链接"},textStyle:{bold:"粗体",clear:"清除格式",code:"行内代码",italic:"斜体",sub:"下标",sup:"上标",through:"删除线",underline:"下划线"},undo:{undo:"撤销",redo:"重做"},todo:{todo:"待办"}});var s={type:"paragraph",renderElem:function(t,e,r){return n.jsx("p",null,e)}};var f={type:"paragraph",elemToHtml:function(t,e){return""===e?"<p><br></p>":"<p>"+e+"</p>"}},d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function h(t){var e={exports:{}};return t(e,e.exports),e.exports}var v,g,m=function(t){return t&&t.Math==Math&&t},y=m("object"==typeof globalThis&&globalThis)||m("object"==typeof window&&window)||m("object"==typeof self&&self)||m("object"==typeof d&&d)||function(){return this}()||Function("return this")(),b=function(t){try{return!!t()}catch(t){return!0}},x=!b((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),w=Function.prototype.call,E=w.bind?w.bind(w):function(){return w.apply(w,arguments)},S={}.propertyIsEnumerable,k=Object.getOwnPropertyDescriptor,T=k&&!S.call({1:2},1)?function(t){var e=k(this,t);return!!e&&e.enumerable}:S,M={f:T},I=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},N=Function.prototype,H=N.bind,O=N.call,j=H&&H.bind(O),D=H?function(t){return t&&j(O,t)}:function(t){return t&&function(){return O.apply(t,arguments)}},L=D({}.toString),z=D("".slice),A=function(t){return z(L(t),8,-1)},P=y.Object,C=D("".split),V=b((function(){return!P("z").propertyIsEnumerable(0)}))?function(t){return"String"==A(t)?C(t,""):P(t)}:P,B=y.TypeError,R=function(t){if(null==t)throw B("Can't call method on "+t);return t},F=function(t){return V(R(t))},_=function(t){return"function"==typeof t},q=function(t){return"object"==typeof t?null!==t:_(t)},$=function(t){return _(t)?t:void 0},W=function(t,e){return arguments.length<2?$(y[t]):y[t]&&y[t][e]},G=D({}.isPrototypeOf),U=W("navigator","userAgent")||"",X=y.process,Y=y.Deno,J=X&&X.versions||Y&&Y.version,K=J&&J.v8;K&&(g=(v=K.split("."))[0]>0&&v[0]<4?1:+(v[0]+v[1])),!g&&U&&(!(v=U.match(/Edge\/(\d+)/))||v[1]>=74)&&(v=U.match(/Chrome\/(\d+)/))&&(g=+v[1]);var Q=g,Z=!!Object.getOwnPropertySymbols&&!b((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Q&&Q<41})),tt=Z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,et=y.Object,nt=tt?function(t){return"symbol"==typeof t}:function(t){var e=W("Symbol");return _(e)&&G(e.prototype,et(t))},rt=y.String,ot=function(t){try{return rt(t)}catch(t){return"Object"}},it=y.TypeError,ut=function(t){if(_(t))return t;throw it(ot(t)+" is not a function")},at=function(t,e){var n=t[e];return null==n?void 0:ut(n)},ct=y.TypeError,lt=Object.defineProperty,st=function(t,e){try{lt(y,t,{value:e,configurable:!0,writable:!0})}catch(n){y[t]=e}return e},ft="__core-js_shared__",dt=y[ft]||st(ft,{}),pt=h((function(t){(t.exports=function(t,e){return dt[t]||(dt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),ht=y.Object,vt=function(t){return ht(R(t))},gt=D({}.hasOwnProperty),mt=Object.hasOwn||function(t,e){return gt(vt(t),e)},yt=0,bt=Math.random(),xt=D(1..toString),wt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+xt(++yt+bt,36)},Et=pt("wks"),St=y.Symbol,kt=St&&St.for,Tt=tt?St:St&&St.withoutSetter||wt,Mt=function(t){if(!mt(Et,t)||!Z&&"string"!=typeof Et[t]){var e="Symbol."+t;Z&&mt(St,t)?Et[t]=St[t]:Et[t]=tt&&kt?kt(e):Tt(e)}return Et[t]},It=y.TypeError,Nt=Mt("toPrimitive"),Ht=function(t,e){if(!q(t)||nt(t))return t;var n,r=at(t,Nt);if(r){if(void 0===e&&(e="default"),n=E(r,t,e),!q(n)||nt(n))return n;throw It("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&_(n=t.toString)&&!q(r=E(n,t)))return r;if(_(n=t.valueOf)&&!q(r=E(n,t)))return r;if("string"!==e&&_(n=t.toString)&&!q(r=E(n,t)))return r;throw ct("Can't convert object to primitive value")}(t,e)},Ot=function(t){var e=Ht(t,"string");return nt(e)?e:e+""},jt=y.document,Dt=q(jt)&&q(jt.createElement),Lt=function(t){return Dt?jt.createElement(t):{}},zt=!x&&!b((function(){return 7!=Object.defineProperty(Lt("div"),"a",{get:function(){return 7}}).a})),At=Object.getOwnPropertyDescriptor,Pt={f:x?At:function(t,e){if(t=F(t),e=Ot(e),zt)try{return At(t,e)}catch(t){}if(mt(t,e))return I(!E(M.f,t,e),t[e])}},Ct=y.String,Vt=y.TypeError,Bt=function(t){if(q(t))return t;throw Vt(Ct(t)+" is not an object")},Rt=y.TypeError,Ft=Object.defineProperty,_t={f:x?Ft:function(t,e,n){if(Bt(t),e=Ot(e),Bt(n),zt)try{return Ft(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Rt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},qt=x?function(t,e,n){return _t.f(t,e,I(1,n))}:function(t,e,n){return t[e]=n,t},$t=D(Function.toString);_(dt.inspectSource)||(dt.inspectSource=function(t){return $t(t)});var Wt,Gt,Ut,Xt=dt.inspectSource,Yt=y.WeakMap,Jt=_(Yt)&&/native code/.test(Xt(Yt)),Kt=pt("keys"),Qt=function(t){return Kt[t]||(Kt[t]=wt(t))},Zt={},te="Object already initialized",ee=y.TypeError,ne=y.WeakMap;if(Jt||dt.state){var re=dt.state||(dt.state=new ne),oe=D(re.get),ie=D(re.has),ue=D(re.set);Wt=function(t,e){if(ie(re,t))throw new ee(te);return e.facade=t,ue(re,t,e),e},Gt=function(t){return oe(re,t)||{}},Ut=function(t){return ie(re,t)}}else{var ae=Qt("state");Zt[ae]=!0,Wt=function(t,e){if(mt(t,ae))throw new ee(te);return e.facade=t,qt(t,ae,e),e},Gt=function(t){return mt(t,ae)?t[ae]:{}},Ut=function(t){return mt(t,ae)}}var ce={set:Wt,get:Gt,has:Ut,enforce:function(t){return Ut(t)?Gt(t):Wt(t,{})},getterFor:function(t){return function(e){var n;if(!q(e)||(n=Gt(e)).type!==t)throw ee("Incompatible receiver, "+t+" required");return n}}},le=Function.prototype,se=x&&Object.getOwnPropertyDescriptor,fe=mt(le,"name"),de={EXISTS:fe,PROPER:fe&&"something"===function(){}.name,CONFIGURABLE:fe&&(!x||x&&se(le,"name").configurable)},pe=h((function(t){var e=de.CONFIGURABLE,n=ce.get,r=ce.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var a,c=!!u&&!!u.unsafe,l=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,f=u&&void 0!==u.name?u.name:n;_(i)&&("Symbol("===String(f).slice(0,7)&&(f="["+String(f).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!mt(i,"name")||e&&i.name!==f)&&qt(i,"name",f),(a=r(i)).source||(a.source=o.join("string"==typeof f?f:""))),t!==y?(c?!s&&t[n]&&(l=!0):delete t[n],l?t[n]=i:qt(t,n,i)):l?t[n]=i:st(n,i)})(Function.prototype,"toString",(function(){return _(this)&&n(this).source||Xt(this)}))})),he=Math.ceil,ve=Math.floor,ge=function(t){var e=+t;return e!=e||0===e?0:(e>0?ve:he)(e)},me=Math.max,ye=Math.min,be=function(t,e){var n=ge(t);return n<0?me(n+e,0):ye(n,e)},xe=Math.min,we=function(t){return t>0?xe(ge(t),9007199254740991):0},Ee=function(t){return we(t.length)},Se=function(t){return function(e,n,r){var o,i=F(e),u=Ee(i),a=be(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},ke={includes:Se(!0),indexOf:Se(!1)},Te=ke.indexOf,Me=D([].push),Ie=function(t,e){var n,r=F(t),o=0,i=[];for(n in r)!mt(Zt,n)&&mt(r,n)&&Me(i,n);for(;e.length>o;)mt(r,n=e[o++])&&(~Te(i,n)||Me(i,n));return i},Ne=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],He=Ne.concat("length","prototype"),Oe={f:Object.getOwnPropertyNames||function(t){return Ie(t,He)}},je={f:Object.getOwnPropertySymbols},De=D([].concat),Le=W("Reflect","ownKeys")||function(t){var e=Oe.f(Bt(t)),n=je.f;return n?De(e,n(t)):e},ze=function(t,e){for(var n=Le(e),r=_t.f,o=Pt.f,i=0;i<n.length;i++){var u=n[i];mt(t,u)||r(t,u,o(e,u))}},Ae=/#|\.prototype\./,Pe=function(t,e){var n=Ve[Ce(t)];return n==Re||n!=Be&&(_(e)?b(e):!!e)},Ce=Pe.normalize=function(t){return String(t).replace(Ae,".").toLowerCase()},Ve=Pe.data={},Be=Pe.NATIVE="N",Re=Pe.POLYFILL="P",Fe=Pe,_e=Pt.f,qe=function(t,e){var n,r,o,i,u,a=t.target,c=t.global,l=t.stat;if(n=c?y:l?y[a]||st(a,{}):(y[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=_e(n,r))&&u.value:n[r],!Fe(c?r:a+(l?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;ze(i,o)}(t.sham||o&&o.sham)&&qt(i,"sham",!0),pe(n,r,i,t)}},$e=D(D.bind),We=Array.isArray||function(t){return"Array"==A(t)},Ge={};Ge[Mt("toStringTag")]="z";var Ue="[object z]"===String(Ge),Xe=Mt("toStringTag"),Ye=y.Object,Je="Arguments"==A(function(){return arguments}()),Ke=Ue?A:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Ye(t),Xe))?n:Je?A(e):"Object"==(r=A(e))&&_(e.callee)?"Arguments":r},Qe=function(){},Ze=[],tn=W("Reflect","construct"),en=/^\s*(?:class|function)\b/,nn=D(en.exec),rn=!en.exec(Qe),on=function(t){if(!_(t))return!1;try{return tn(Qe,Ze,t),!0}catch(t){return!1}},un=!tn||b((function(){var t;return on(on.call)||!on(Object)||!on((function(){t=!0}))||t}))?function(t){if(!_(t))return!1;switch(Ke(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return rn||!!nn(en,Xt(t))}:on,an=Mt("species"),cn=y.Array,ln=function(t,e){return new(function(t){var e;return We(t)&&(e=t.constructor,(un(e)&&(e===cn||We(e.prototype))||q(e)&&null===(e=e[an]))&&(e=void 0)),void 0===e?cn:e}(t))(0===e?0:e)},sn=D([].push),fn=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,l,s,f){for(var d,p,h=vt(c),v=V(h),g=function(t,e){return ut(t),void 0===e?t:$e?$e(t,e):function(){return t.apply(e,arguments)}}(l,s),m=Ee(v),y=0,b=f||ln,x=e?b(c,m):n||u?b(c,0):void 0;m>y;y++)if((a||y in v)&&(p=g(d=v[y],y,h),t))if(e)x[y]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return y;case 2:sn(x,d)}else switch(t){case 4:return!1;case 7:sn(x,d)}return i?-1:r||o?o:x}},dn={forEach:fn(0),map:fn(1),filter:fn(2),some:fn(3),every:fn(4),find:fn(5),findIndex:fn(6),filterReject:fn(7)},pn=Mt("species"),hn=function(t){return Q>=51||!b((function(){var e=[];return(e.constructor={})[pn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},vn=dn.filter;qe({target:"Array",proto:!0,forced:!hn("filter")},{filter:function(t){return vn(this,t,arguments.length>1?arguments[1]:void 0)}});var gn=Ue?{}.toString:function(){return"[object "+Ke(this)+"]"};Ue||pe(Object.prototype,"toString",gn,{unsafe:!0});var mn,yn=y.String,bn=function(t){if("Symbol"===Ke(t))throw TypeError("Cannot convert a Symbol value to a string");return yn(t)},xn=function(){var t=Bt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},wn=y.RegExp,En=b((function(){var t=wn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Sn=En||b((function(){return!wn("a","y").sticky})),kn={BROKEN_CARET:En||b((function(){var t=wn("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Sn,UNSUPPORTED_Y:En},Tn=Object.keys||function(t){return Ie(t,Ne)},Mn=x?Object.defineProperties:function(t,e){Bt(t);for(var n,r=F(e),o=Tn(e),i=o.length,u=0;i>u;)_t.f(t,n=o[u++],r[n]);return t},In=W("document","documentElement"),Nn=Qt("IE_PROTO"),Hn=function(){},On=function(t){return"<script>"+t+"</"+"script>"},jn=function(t){t.write(On("")),t.close();var e=t.parentWindow.Object;return t=null,e},Dn=function(){try{mn=new ActiveXObject("htmlfile")}catch(t){}var t,e;Dn="undefined"!=typeof document?document.domain&&mn?jn(mn):((e=Lt("iframe")).style.display="none",In.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(On("document.F=Object")),t.close(),t.F):jn(mn);for(var n=Ne.length;n--;)delete Dn.prototype[Ne[n]];return Dn()};Zt[Nn]=!0;var Ln,zn,An=Object.create||function(t,e){var n;return null!==t?(Hn.prototype=Bt(t),n=new Hn,Hn.prototype=null,n[Nn]=t):n=Dn(),void 0===e?n:Mn(n,e)},Pn=y.RegExp,Cn=b((function(){var t=Pn(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),Vn=y.RegExp,Bn=b((function(){var t=Vn("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Rn=ce.get,Fn=pt("native-string-replace",String.prototype.replace),_n=RegExp.prototype.exec,qn=_n,$n=D("".charAt),Wn=D("".indexOf),Gn=D("".replace),Un=D("".slice),Xn=(zn=/b*/g,E(_n,Ln=/a/,"a"),E(_n,zn,"a"),0!==Ln.lastIndex||0!==zn.lastIndex),Yn=kn.BROKEN_CARET,Jn=void 0!==/()??/.exec("")[1];(Xn||Jn||Yn||Cn||Bn)&&(qn=function(t){var e,n,r,o,i,u,a,c=this,l=Rn(c),s=bn(t),f=l.raw;if(f)return f.lastIndex=c.lastIndex,e=E(qn,f,s),c.lastIndex=f.lastIndex,e;var d=l.groups,p=Yn&&c.sticky,h=E(xn,c),v=c.source,g=0,m=s;if(p&&(h=Gn(h,"y",""),-1===Wn(h,"g")&&(h+="g"),m=Un(s,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==$n(s,c.lastIndex-1))&&(v="(?: "+v+")",m=" "+m,g++),n=new RegExp("^(?:"+v+")",h)),Jn&&(n=new RegExp("^"+v+"$(?!\\s)",h)),Xn&&(r=c.lastIndex),o=E(_n,p?n:c,m),p?o?(o.input=Un(o.input,g),o[0]=Un(o[0],g),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Xn&&o&&(c.lastIndex=c.global?o.index+o[0].length:r),Jn&&o&&o.length>1&&E(Fn,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&d)for(o.groups=u=An(null),i=0;i<d.length;i++)u[(a=d[i])[0]]=o[a[1]];return o});var Kn=qn;qe({target:"RegExp",proto:!0,forced:/./.exec!==Kn},{exec:Kn});var Qn=Function.prototype,Zn=Qn.apply,tr=Qn.bind,er=Qn.call,nr="object"==typeof Reflect&&Reflect.apply||(tr?er.bind(Zn):function(){return er.apply(Zn,arguments)}),rr=Mt("species"),or=RegExp.prototype,ir=function(t,e,n,r){var o=Mt(t),i=!b((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!b((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[rr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!u||n){var a=D(/./[o]),c=e(o,""[t],(function(t,e,n,r,o){var u=D(t),c=e.exec;return c===Kn||c===or.exec?i&&!o?{done:!0,value:a(e,n,r)}:{done:!0,value:u(n,e,r)}:{done:!1}}));pe(String.prototype,t,c[0]),pe(or,o,c[1])}r&&qt(or[o],"sham",!0)},ur=D("".charAt),ar=D("".charCodeAt),cr=D("".slice),lr=function(t){return function(e,n){var r,o,i=bn(R(e)),u=ge(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=ar(i,u))<55296||r>56319||u+1===a||(o=ar(i,u+1))<56320||o>57343?t?ur(i,u):r:t?cr(i,u,u+2):o-56320+(r-55296<<10)+65536}},sr={codeAt:lr(!1),charAt:lr(!0)}.charAt,fr=function(t,e,n){return e+(n?sr(t,e).length:1)},dr=Math.floor,pr=D("".charAt),hr=D("".replace),vr=D("".slice),gr=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,mr=/\$([$&'`]|\d{1,2})/g,yr=function(t,e,n,r,o,i){var u=n+t.length,a=r.length,c=mr;return void 0!==o&&(o=vt(o),c=gr),hr(i,c,(function(i,c){var l;switch(pr(c,0)){case"$":return"$";case"&":return t;case"`":return vr(e,0,n);case"'":return vr(e,u);case"<":l=o[vr(c,1,-1)];break;default:var s=+c;if(0===s)return i;if(s>a){var f=dr(s/10);return 0===f?i:f<=a?void 0===r[f-1]?pr(c,1):r[f-1]+pr(c,1):i}l=r[s-1]}return void 0===l?"":l}))},br=y.TypeError,xr=function(t,e){var n=t.exec;if(_(n)){var r=E(n,t,e);return null!==r&&Bt(r),r}if("RegExp"===A(t))return E(Kn,t,e);throw br("RegExp#exec called on incompatible receiver")},wr=Mt("replace"),Er=Math.max,Sr=Math.min,kr=D([].concat),Tr=D([].push),Mr=D("".indexOf),Ir=D("".slice),Nr="$0"==="a".replace(/./,"$0"),Hr=!!/./[wr]&&""===/./[wr]("a","$0");ir("replace",(function(t,e,n){var r=Hr?"$":"$0";return[function(t,n){var r=R(this),o=null==t?void 0:at(t,wr);return o?E(o,t,r,n):E(e,bn(r),t,n)},function(t,o){var i=Bt(this),u=bn(t);if("string"==typeof o&&-1===Mr(o,r)&&-1===Mr(o,"$<")){var a=n(e,i,u,o);if(a.done)return a.value}var c=_(o);c||(o=bn(o));var l=i.global;if(l){var s=i.unicode;i.lastIndex=0}for(var f=[];;){var d=xr(i,u);if(null===d)break;if(Tr(f,d),!l)break;""===bn(d[0])&&(i.lastIndex=fr(u,we(i.lastIndex),s))}for(var p,h="",v=0,g=0;g<f.length;g++){for(var m=bn((d=f[g])[0]),y=Er(Sr(ge(d.index),u.length),0),b=[],x=1;x<d.length;x++)Tr(b,void 0===(p=d[x])?p:String(p));var w=d.groups;if(c){var E=kr([m],b,y,u);void 0!==w&&Tr(E,w);var S=bn(nr(o,void 0,E))}else S=yr(m,u,y,b,w,o);y>=v&&(h+=Ir(u,v,y)+S,v=y+m.length)}return h+Ir(u,v)}]}),!!b((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!Nr||Hr);var Or=Mt("unscopables"),jr=Array.prototype;null==jr[Or]&&_t.f(jr,Or,{configurable:!0,value:An(null)});var Dr=function(t){jr[Or][t]=!0},Lr=dn.find,zr="find",Ar=!0;zr in[]&&Array(1).find((function(){Ar=!1})),qe({target:"Array",proto:!0,forced:Ar},{find:function(t){return Lr(this,t,arguments.length>1?arguments[1]:void 0)}}),Dr(zr);var Pr=Mt("match"),Cr=function(t){var e;return q(t)&&(void 0!==(e=t[Pr])?!!e:"RegExp"==A(t))},Vr=y.TypeError,Br=Mt("species"),Rr=function(t,e){var n,r=Bt(t).constructor;return void 0===r||null==(n=Bt(r)[Br])?e:function(t){if(un(t))return t;throw Vr(ot(t)+" is not a constructor")}(n)},Fr=function(t,e,n){var r=Ot(e);r in t?_t.f(t,r,I(0,n)):t[r]=n},_r=y.Array,qr=Math.max,$r=function(t,e,n){for(var r=Ee(t),o=be(e,r),i=be(void 0===n?r:n,r),u=_r(qr(i-o,0)),a=0;o<i;o++,a++)Fr(u,a,t[o]);return u.length=a,u},Wr=kn.UNSUPPORTED_Y,Gr=4294967295,Ur=Math.min,Xr=[].push,Yr=D(/./.exec),Jr=D(Xr),Kr=D("".slice),Qr=!b((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]}));ir("split",(function(t,e,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var r=bn(R(this)),o=void 0===n?Gr:n>>>0;if(0===o)return[];if(void 0===t)return[r];if(!Cr(t))return E(e,r,t,o);for(var i,u,a,c=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),s=0,f=new RegExp(t.source,l+"g");(i=E(Kn,f,r))&&!((u=f.lastIndex)>s&&(Jr(c,Kr(r,s,i.index)),i.length>1&&i.index<r.length&&nr(Xr,c,$r(i,1)),a=i[0].length,s=u,c.length>=o));)f.lastIndex===i.index&&f.lastIndex++;return s===r.length?!a&&Yr(f,"")||Jr(c,""):Jr(c,Kr(r,s)),c.length>o?$r(c,0,o):c}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:E(e,this,t,n)}:e,[function(e,n){var o=R(this),i=null==e?void 0:at(e,t);return i?E(i,e,o,n):E(r,bn(o),e,n)},function(t,o){var i=Bt(this),u=bn(t),a=n(r,i,u,o,r!==e);if(a.done)return a.value;var c=Rr(i,RegExp),l=i.unicode,s=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(Wr?"g":"y"),f=new c(Wr?"^(?:"+i.source+")":i,s),d=void 0===o?Gr:o>>>0;if(0===d)return[];if(0===u.length)return null===xr(f,u)?[u]:[];for(var p=0,h=0,v=[];h<u.length;){f.lastIndex=Wr?0:h;var g,m=xr(f,Wr?Kr(u,h):u);if(null===m||(g=Ur(we(f.lastIndex+(Wr?h:0)),u.length))===p)h=fr(u,h,l);else{if(Jr(v,Kr(u,p,h)),v.length===d)return v;for(var y=1;y<=m.length-1;y++)if(Jr(v,m[y]),v.length===d)return v;h=p=g}}return Jr(v,Kr(u,p)),v}]}),!Qr,Wr);var Zr,to="\t\n\v\f\r                　\u2028\u2029\ufeff",eo=D("".replace),no="["+to+"]",ro=RegExp("^"+no+no+"*"),oo=RegExp(no+no+"*$"),io=function(t){return function(e){var n=bn(R(e));return 1&t&&(n=eo(n,ro,"")),2&t&&(n=eo(n,oo,"")),n}},uo={start:io(1),end:io(2),trim:io(3)},ao=de.PROPER,co=uo.trim;function lo(t){return 0===c.default("<div>"+t+"</div>").children().filter((function(t){return"BR"!==t.tagName})).length}function so(t){return 0===t.length?"":t[0].outerHTML}function fo(t){return t.length?t[0].tagName.toLowerCase():""}function po(t,e){for(var n="",r=(t.attr("style")||"").split(";"),o=r.length,i=0;i<o;i++){var u=r[i];if(u){var a=u.split(":");a[0].trim()===e&&(n=a[1].trim())}}return n}qe({target:"String",proto:!0,forced:(Zr="trim",b((function(){return!!to[Zr]()||"​᠎"!=="​᠎"[Zr]()||ao&&to[Zr].name!==Zr})))},{trim:function(){return co(this)}}),qe({global:!0},{globalThis:y}),o.css&&(c.default.fn.css=o.css),o.append&&(c.default.fn.append=o.append),o.prepend&&(c.default.fn.prepend=o.prepend),o.addClass&&(c.default.fn.addClass=o.addClass),o.removeClass&&(c.default.fn.removeClass=o.removeClass),o.hasClass&&(c.default.fn.hasClass=o.hasClass),o.on&&(c.default.fn.on=o.on),o.off&&(c.default.fn.off=o.off),o.focus&&(c.default.fn.focus=o.focus),o.attr&&(c.default.fn.attr=o.attr),o.removeAttr&&(c.default.fn.removeAttr=o.removeAttr),o.hide&&(c.default.fn.hide=o.hide),o.show&&(c.default.fn.show=o.show),o.parents&&(c.default.fn.parents=o.parents),o.dataset&&(c.default.fn.dataset=o.dataset),o.val&&(c.default.fn.val=o.val),o.text&&(c.default.fn.text=o.text),o.html&&(c.default.fn.html=o.html),o.children&&(c.default.fn.children=o.children),o.remove&&(c.default.fn.remove=o.remove),o.find&&(c.default.fn.find=o.find),o.width&&(c.default.fn.width=o.width),o.height&&(c.default.fn.height=o.height),o.filter&&(c.default.fn.filter=o.filter),o.empty&&(c.default.fn.empty=o.empty);var ho=function(t,e){return ho=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},ho(t,e)};
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */function vo(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}ho(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var go=function(){return go=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},go.apply(this,arguments)};function mo(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}c((r=r.apply(t,e||[])).next())}))}function yo(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function bo(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function xo(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function wo(t){var e=xo(r.Editor.nodes(t,{match:function(e){return t.children[0]===e},mode:"highest"}),1),n=e[0];if(null==n)return!1;var o=n[0];if(!r.Element.isElement(o))return!1;if("paragraph"===o.type)return!1;if(""!==r.Node.string(o))return!1;var i=o.children,u=void 0===i?[]:i;return!!r.Text.isText(u[0])&&(r.Transforms.setNodes(t,{type:"paragraph"}),!0)}var Eo={renderElems:[s],elemsToHtml:[f],parseElemsHtml:[{selector:"p:not([data-w-e-type])",parseElemHtml:function(t,e,n){var o=c.default(t);return 0===(e=e.filter((function(t){return!!r.Text.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:o.text().replace(/\s+/gm," ")}]),{type:"paragraph",children:e}}}],editorPlugin:function(t){var e=t.deleteBackward,n=t.deleteForward;t.insertText,t.insertBreak;var r=t;return r.deleteBackward=function(t){wo(r)||e(t)},r.deleteForward=function(t){wo(r)||n(t)},r}},So=/"/g,ko=D("".replace),To=function(t,e,n,r){var o=bn(R(t)),i="<"+e;return""!==n&&(i+=" "+n+'="'+ko(bn(r),So,"&quot;")+'"'),i+">"+o+"</"+e+">"},Mo=function(t){return b((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))};function Io(t,e){var n=e,r=t,o=r.bold,i=r.italic,u=r.underline;return o&&(n="<strong>"+n+"</strong>"),r.code&&(n="<code>"+n+"</code>"),i&&(n="<em>"+n+"</em>"),u&&(n="<u>"+n+"</u>"),r.through&&(n="<s>"+n+"</s>"),r.sub&&(n="<sub>"+n+"</sub>"),r.sup&&(n="<sup>"+n+"</sup>"),n}function No(t,e){return 0!==t.length&&(!!t[0].matches(e)||t.find(e).length>0)}qe({target:"String",proto:!0,forced:Mo("bold")},{bold:function(){return To(this,"b","","")}}),qe({target:"String",proto:!0,forced:Mo("italics")},{italics:function(){return To(this,"i","","")}}),qe({target:"String",proto:!0,forced:Mo("sub")},{sub:function(){return To(this,"sub","","")}}),qe({target:"String",proto:!0,forced:Mo("sup")},{sup:function(){return To(this,"sup","","")}});var Ho={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Oo=Lt("span").classList,jo=Oo&&Oo.constructor&&Oo.constructor.prototype,Do=jo===Object.prototype?void 0:jo,Lo=function(t,e){var n=[][t];return!!n&&b((function(){n.call(null,e||function(){throw 1},1)}))},zo=dn.forEach,Ao=Lo("forEach")?[].forEach:function(t){return zo(this,t,arguments.length>1?arguments[1]:void 0)},Po=function(t){if(t&&t.forEach!==Ao)try{qt(t,"forEach",Ao)}catch(e){t.forEach=Ao}};for(var Co in Ho)Ho[Co]&&Po(y[Co]&&y[Co].prototype);function Vo(t,n){return null==t.selection||!!xo(r.Editor.nodes(t,{match:function(n){return"pre"===e.DomEditor.getNodeType(n)||!!r.Editor.isVoid(t,n)},universal:!0}),1)[0]}function Bo(t,e){Object.keys(e).forEach((function(e){"text"!==e&&r.Editor.removeMark(t,e)}))}Po(Do),qe({target:"Object",stat:!0,forced:b((function(){Tn(1)}))},{keys:function(t){return Tn(vt(t))}});var Ro=function(){function t(){this.marksNeedToRemove=[],this.tag="button"}return t.prototype.getValue=function(t){var e=this.mark,n=r.Editor.marks(t);return n?n[e]:!!xo(r.Editor.nodes(t,{match:function(t){return!0===t[e]}}),1)[0]},t.prototype.isActive=function(t){return!!this.getValue(t)},t.prototype.isDisabled=function(t){return Vo(t,this.mark)},t.prototype.exec=function(t,e){var n=this.mark,r=this.marksNeedToRemove;e?t.removeMark(n):(t.addMark(n,!0),r&&r.forEach((function(e){return t.removeMark(e)})))},t}(),Fo='<svg viewBox="0 0 1024 1024"><path d="M707.872 484.64A254.88 254.88 0 0 0 768 320c0-141.152-114.848-256-256-256H192v896h384c141.152 0 256-114.848 256-256a256.096 256.096 0 0 0-124.128-219.36zM384 192h101.504c55.968 0 101.504 57.408 101.504 128s-45.536 128-101.504 128H384V192z m159.008 640H384v-256h159.008c58.464 0 106.016 57.408 106.016 128s-47.552 128-106.016 128z"></path></svg>',_o='<svg viewBox="0 0 1024 1024"><path d="M704 64l128 0 0 416c0 159.072-143.264 288-320 288s-320-128.928-320-288l0-416 128 0 0 416c0 40.16 18.24 78.688 51.36 108.512 36.896 33.216 86.848 51.488 140.64 51.488s103.744-18.304 140.64-51.488c33.12-29.792 51.36-68.352 51.36-108.512l0-416zM192 832l640 0 0 128-640 0z"></path></svg>',qo='<svg viewBox="0 0 1024 1024"><path d="M896 64v64h-128L448 896h128v64H128v-64h128L576 128h-128V64z"></path></svg>',$o='<svg viewBox="0 0 1024 1024"><path d="M1024 512v64h-234.496c27.52 38.496 42.496 82.688 42.496 128 0 70.88-36.672 139.04-100.576 186.976C672.064 935.488 594.144 960 512 960s-160.064-24.512-219.424-69.024C228.64 843.04 192 774.88 192 704h128c0 69.376 87.936 128 192 128s192-58.624 192-128-87.936-128-192-128H0v-64h299.52a385.984 385.984 0 0 1-6.944-5.024C228.64 459.04 192 390.88 192 320s36.672-139.04 100.576-186.976C351.936 88.512 429.856 64 512 64s160.064 24.512 219.424 69.024C795.328 180.96 832 249.12 832 320h-128c0-69.376-87.936-128-192-128s-192 58.624-192 128 87.936 128 192 128c78.976 0 154.048 22.688 212.48 64H1024z"></path></svg>',Wo='<svg viewBox="0 0 1024 1024"><path d="M576 736l96 96 320-320L672 192l-96 96 224 224zM448 288l-96-96L32 512l320 320 96-96-224-224z"></path></svg>',Go='<svg viewBox="0 0 1024 1024"><path d="M864 0a160 160 0 0 1 128 256l-64 64-224-224 64-64c26.752-20.096 59.968-32 96-32zM64 736l-64 288 288-64 592-592-224-224L64 736z m651.584-372.416l-448 448-55.168-55.168 448-448 55.168 55.168z"></path></svg>',Uo='<svg viewBox="0 0 1024 1024"><path d="M924.402464 1023.068211H0.679665V99.345412h461.861399v98.909208H99.596867v725.896389h725.896389V561.206811h98.909208z" p-id="10909"></path><path d="M930.805104 22.977336l69.965436 69.965436-453.492405 453.492404-69.965435-69.901489z" p-id="10910"></path><path d="M1022.464381 304.030081h-98.917201V99.345412H709.230573V0.428211h313.233808z"></path></svg>',Xo='<svg viewBox="0 0 1024 1024"><path d="M64 864h896v96H64zM360.58 576h302.85l81.53 224h102.16L579.24 64H444.77L176.89 800h102.16l81.53-224zM512 159.96L628.49 480H395.52L512 159.96z"></path></svg>',Yo='<svg viewBox="0 0 1024 1024"><path d="M510.030769 315.076923l84.676923 196.923077h-177.230769l76.8-196.923077h15.753846zM945.230769 157.538462v708.923076c0 43.323077-35.446154 78.769231-78.769231 78.769231H157.538462c-43.323077 0-78.769231-35.446154-78.769231-78.769231V157.538462c0-43.323077 35.446154-78.769231 78.769231-78.769231h708.923076c43.323077 0 78.769231 35.446154 78.769231 78.769231z m-108.307692 643.938461L600.615385 216.615385c-5.907692-11.815385-15.753846-19.692308-29.538462-19.692308h-139.815385c-11.815385 0-23.630769 7.876923-27.56923 19.692308l-216.615385 584.861538c-3.938462 11.815385 3.938462 25.6 17.723077 25.6h80.738462c11.815385 0 23.630769-9.846154 27.56923-21.661538l63.015385-175.261539h263.876923l68.923077 175.261539c3.938462 11.815385 15.753846 21.661538 27.569231 21.661538h80.738461c13.784615 0 23.630769-13.784615 19.692308-25.6z"></path></svg>',Jo='<svg viewBox="0 0 1024 1024"><path d="M64 512h384v128h-128V1024h-128V640h-128z m896-256H708.2496v768h-136.4992V256H320V128h640z"></path></svg>',Ko='<svg viewBox="0 0 1024 1024"><path d="M956.788364 152.110545h-24.110546l23.924364 9.029819 0.186182 121.018181h-65.070546l-86.574545-130.048H566.551273v650.14691l130.048 64.977454v65.163636h-390.050909v-65.163636l129.954909-64.977454V152.110545H198.283636L111.429818 282.065455H46.545455V69.259636C46.545455 33.792 82.664727 22.062545 98.955636 22.062545h812.683637c23.738182 0 45.056 15.173818 45.056 41.053091V169.425455v-17.221819z"></path></svg>',Qo='<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m256-512v384l-256-192z"></path></svg>',Zo='<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',ti='<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',ei='<svg viewBox="0 0 1024 1024"><path d="M972.8 793.6v102.4H256v-102.4h716.8z m0-230.4v102.4H51.2v-102.4h921.6z m0-230.4v102.4H256v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',ni='<svg viewBox="0 0 1024 1024"><path d="M870.4 793.6v102.4H153.6v-102.4h716.8z m102.4-230.4v102.4H51.2v-102.4h921.6z m-102.4-230.4v102.4H153.6v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',ri='<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m0 192h1024v128H0z m0 192h1024v128H0z m0 192h1024v128H0z m0 192h1024v128H0z"></path></svg>',oi='<svg viewBox="0 0 1024 1024"><path d="M768 206.016v50.016h128v64h-192V174.016l128-60V64h-128V0h192v146.016zM676 256h-136L352 444 164 256H28l256 256-256 256h136L352 580 540 768h136l-256-256z"></path></svg>',ii='<svg viewBox="0 0 1024 1024"><path d="M768 910.016v50.016h128v64h-192v-146.016l128-60V768h-128v-64h192v146.016zM676 256h-136L352 444 164 256H28l256 256-256 256h136L352 580 540 768h136l-256-256z"></path></svg>',ui=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="bold",n.title=e.t("textStyle.bold"),n.iconSvg=Fo,n.hotkey="mod+b",n}return vo(n,t),n}(Ro),ai=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="code",n.title=e.t("textStyle.code"),n.iconSvg=Wo,n.hotkey="mod+e",n}return vo(n,t),n}(Ro),ci=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="italic",n.title=e.t("textStyle.italic"),n.iconSvg=qo,n.hotkey="mod+i",n}return vo(n,t),n}(Ro),li=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="through",n.title=e.t("textStyle.through"),n.iconSvg=$o,n.hotkey="mod+shift+x",n}return vo(n,t),n}(Ro),si=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="underline",n.title=e.t("textStyle.underline"),n.iconSvg=_o,n.hotkey="mod+u",n}return vo(n,t),n}(Ro),fi=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="sub",n.marksNeedToRemove=["sup"],n.title=e.t("textStyle.sub"),n.iconSvg=ii,n.hotkey="",n}return vo(n,t),n}(Ro),di=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.mark="sup",n.marksNeedToRemove=["sub"],n.title=e.t("textStyle.sup"),n.iconSvg=oi,n.hotkey="",n}return vo(n,t),n}(Ro),pi=function(){function t(){this.title=e.t("textStyle.clear"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M969.382408 288.738615l-319.401123-270.852152a67.074236 67.074236 0 0 0-96.459139 5.74922l-505.931379 574.922021a68.35184 68.35184 0 0 0-17.886463 47.910169 74.101061 74.101061 0 0 0 24.274486 47.910168l156.50655 132.232065h373.060512L975.131628 383.281347a67.074236 67.074236 0 0 0-5.74922-96.459139z m-440.134747 433.746725H264.144729l-90.071117-78.572676c-5.74922-5.74922-12.137243-12.137243-12.137243-17.886463a36.411728 36.411728 0 0 1 5.749221-24.274485l210.804741-240.828447 265.102932 228.691204z m-439.495945 180.781036h843.218964a60.047411 60.047411 0 1 1 0 120.733624H89.751716a60.047411 60.047411 0 1 1 0-120.733624z m0 0"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return Vo(t)},t.prototype.exec=function(t,e){var n,o,i=r.Editor.nodes(t,{match:function(t){return r.Text.isText(t)},universal:!0});try{for(var u=bo(i),a=u.next();!a.done;a=u.next()){Bo(t,a.value[0])}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}},t}(),hi={renderStyle:function(t,e){var r=t,o=r.bold,i=r.italic,u=r.underline,a=r.code,c=r.through,l=r.sub,s=r.sup,f=e;return o&&(f=n.jsx("strong",null,f)),a&&(f=n.jsx("code",null,f)),i&&(f=n.jsx("em",null,f)),u&&(f=n.jsx("u",null,f)),c&&(f=n.jsx("s",null,f)),l&&(f=n.jsx("sub",null,f)),s&&(f=n.jsx("sup",null,f)),f},menus:[{key:"bold",factory:function(){return new ui}},{key:"underline",factory:function(){return new si}},{key:"italic",factory:function(){return new ci}},{key:"through",factory:function(){return new li}},{key:"code",factory:function(){return new ai}},{key:"sub",factory:function(){return new fi}},{key:"sup",factory:function(){return new di}},{key:"clearStyle",factory:function(){return new pi}}],styleToHtml:function(t,e){if(!r.Text.isText(t))return e;if(lo(e))return Io(t,e);var n=c.default(e);if("br"===fo(n))return Io(t,"<br>");var o=n.html();return o=Io(t,o),n.html(o),so(n)},parseStyleHtml:function(t,e,n){var o=c.default(t);if(!r.Text.isText(e))return e;var i=e;return No(o,"b,strong")&&(i.bold=!0),No(o,"i,em")&&(i.italic=!0),No(o,"u")&&(i.underline=!0),No(o,"s,strike")&&(i.through=!0),No(o,"sub")&&(i.sub=!0),No(o,"sup")&&(i.sup=!0),No(o,"code")&&(i.code=!0),i}};function vi(t){return function(e,r,o){var i="h"+t;return n.jsx(i,null,r)}}var gi={type:"header1",renderElem:vi(1)},mi={type:"header2",renderElem:vi(2)},yi={type:"header3",renderElem:vi(3)},bi={type:"header4",renderElem:vi(4)},xi={type:"header5",renderElem:vi(5)},wi=de.PROPER,Ei="toString",Si=RegExp.prototype,ki=Si.toString,Ti=D(xn),Mi=b((function(){return"/a/b"!=ki.call({source:"a",flags:"b"})})),Ii=wi&&ki.name!=Ei;(Mi||Ii)&&pe(RegExp.prototype,Ei,(function(){var t=Bt(this),e=bn(t.source),n=t.flags;return"/"+e+"/"+bn(void 0===n&&G(Si,t)&&!("flags"in Si)?Ti(t):n)}),{unsafe:!0});var Ni,Hi=y.TypeError,Oi=function(t){if(Cr(t))throw Hi("The method doesn't accept regular expressions");return t},ji=Mt("match"),Di=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[ji]=!1,"/./"[t](e)}catch(t){}}return!1},Li=Pt.f,zi=D("".startsWith),Ai=D("".slice),Pi=Math.min,Ci=Di("startsWith");function Vi(t){var n=xo(r.Editor.nodes(t,{match:function(t){return e.DomEditor.getNodeType(t).startsWith("header")},universal:!0}),1),o=n[0];if(null==o)return"paragraph";var i=xo(o,1)[0];return e.DomEditor.getNodeType(i)}function Bi(t){return null==t.selection||!xo(r.Editor.nodes(t,{match:function(t){var n=e.DomEditor.getNodeType(t);return"paragraph"===n||!!n.startsWith("header")},universal:!0,mode:"highest"}),1)[0]}function Ri(t,e){e&&r.Transforms.setNodes(t,{type:e})}qe({target:"String",proto:!0,forced:!!(Ci||(Ni=Li(String.prototype,"startsWith"),!Ni||Ni.writable))&&!Ci},{startsWith:function(t){var e=bn(R(this));Oi(t);var n=we(Pi(arguments.length>1?arguments[1]:void 0,e.length)),r=bn(t);return zi?zi(e,r,n):Ai(e,n,n+r.length)===r}});var Fi=function(){function t(){this.title=e.t("header.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M960 960c-51.2 0-102.4-3.2-153.6-3.2-51.2 0-99.2 3.2-150.4 3.2-19.2 0-28.8-22.4-28.8-38.4 0-51.2 57.6-28.8 86.4-48 19.2-12.8 19.2-60.8 19.2-80v-224-19.2c-9.6-3.2-19.2-3.2-28.8-3.2H320c-9.6 0-19.2 0-28.8 3.2V780.8c0 22.4 0 80 22.4 92.8 28.8 19.2 96-6.4 96 44.8 0 16-9.6 41.6-28.8 41.6-54.4 0-105.6-3.2-160-3.2-48 0-96 3.2-147.2 3.2-19.2 0-28.8-22.4-28.8-38.4 0-51.2 51.2-28.8 80-48 19.2-12.8 19.2-60.8 19.2-83.2V294.4c0-28.8 3.2-115.2-22.4-131.2-25.6-16-86.4 9.6-86.4-41.6 0-16 6.4-41.6 28.8-41.6 51.2 0 105.6 3.2 156.8 3.2 48 0 96-3.2 144-3.2 19.2 0 28.8 22.4 28.8 41.6 0 48-57.6 25.6-83.2 41.6-19.2 12.8-19.2 73.6-19.2 92.8v201.6c6.4 3.2 16 3.2 22.4 3.2h400c6.4 0 12.8 0 22.4-3.2V256c0-22.4 0-80-19.2-92.8-28.8-16-86.4 6.4-86.4-41.6 0-16 9.6-41.6 28.8-41.6 51.2 0 99.2 3.2 150.4 3.2 48 0 99.2-3.2 147.2-3.2 19.2 0 28.8 22.4 28.8 41.6 0 51.2-57.6 25.6-86.4 41.6-19.2 12.8-19.2 70.4-19.2 92.8v537.6c0 19.2 0 67.2 19.2 80 28.8 19.2 89.6-6.4 89.6 44.8 0 19.2-6.4 41.6-28.8 41.6z"></path></svg>',this.tag="select",this.width=60}return t.prototype.getOptions=function(t){var n=[{value:"header1",text:"H1",styleForRenderMenuList:{"font-size":"32px","font-weight":"bold"}},{value:"header2",text:"H2",styleForRenderMenuList:{"font-size":"24px","font-weight":"bold"}},{value:"header3",text:"H3",styleForRenderMenuList:{"font-size":"18px","font-weight":"bold"}},{value:"header4",text:"H4",styleForRenderMenuList:{"font-size":"16px","font-weight":"bold"}},{value:"header5",text:"H5",styleForRenderMenuList:{"font-size":"13px","font-weight":"bold"}},{value:"paragraph",text:e.t("header.text")}],r=this.getValue(t).toString();return n.forEach((function(t){t.value===r?t.selected=!0:delete t.selected})),n},t.prototype.isActive=function(t){return!1},t.prototype.getValue=function(t){return Vi(t)},t.prototype.isDisabled=function(t){return Bi(t)},t.prototype.exec=function(t,e){Ri(t,e.toString())},t}(),_i=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){return Vi(t)},t.prototype.isActive=function(t){return this.getValue(t)===this.type},t.prototype.isDisabled=function(t){return Bi(t)},t.prototype.exec=function(t,e){var n=this.type;Ri(t,e===n?"paragraph":n)},t}(),qi=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H1",e.type="header1",e}return vo(e,t),e}(_i),$i=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H2",e.type="header2",e}return vo(e,t),e}(_i),Wi=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H3",e.type="header3",e}return vo(e,t),e}(_i),Gi=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H4",e.type="header4",e}return vo(e,t),e}(_i),Ui=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H5",e.type="header5",e}return vo(e,t),e}(_i),Xi={key:"headerSelect",factory:function(){return new Fi}},Yi={key:"header1",factory:function(){return new qi}},Ji={key:"header2",factory:function(){return new $i}},Ki={key:"header3",factory:function(){return new Wi}},Qi={key:"header4",factory:function(){return new Gi}},Zi={key:"header5",factory:function(){return new Ui}};function tu(t){return function(e,n){return"<h"+t+">"+n+"</h"+t+">"}}function eu(t){return function(e,n,o){var i=c.default(e);return 0===(n=n.filter((function(t){return!!r.Text.isText(t)||!!o.isInline(t)}))).length&&(n=[{text:i.text().replace(/\s+/gm," ")}]),{type:"header"+t,children:n}}}var nu={renderElems:[gi,mi,yi,bi,xi],elemsToHtml:[{type:"header1",elemToHtml:tu(1)},{type:"header2",elemToHtml:tu(2)},{type:"header3",elemToHtml:tu(3)},{type:"header4",elemToHtml:tu(4)},{type:"header5",elemToHtml:tu(5)}],parseElemsHtml:[{selector:"h1:not([data-w-e-type])",parseElemHtml:eu(1)},{selector:"h2:not([data-w-e-type])",parseElemHtml:eu(2)},{selector:"h3:not([data-w-e-type])",parseElemHtml:eu(3)},{selector:"h4:not([data-w-e-type])",parseElemHtml:eu(4)},{selector:"h5:not([data-w-e-type])",parseElemHtml:eu(5)}],menus:[Xi,Yi,Ji,Ki,Qi,Zi],editorPlugin:function(t){var n=t.insertBreak;t.insertNode;var o=t;return o.insertBreak=function(){var i=xo(r.Editor.nodes(o,{match:function(t){return e.DomEditor.getNodeType(t).startsWith("header")},universal:!0}),1)[0];if(i)if(e.DomEditor.isSelectionAtLineEnd(t,i[1])){r.Transforms.insertNodes(o,{type:"paragraph",children:[{text:""}]},{mode:"highest"})}else n();else n()},o}},ru=Object.assign,ou=Object.defineProperty,iu=D([].concat),uu=!ru||b((function(){if(x&&1!==ru({b:1},ru(ou({},"a",{enumerable:!0,get:function(){ou(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=ru({},t)[n]||Tn(ru({},e)).join("")!=r}))?function(t,e){for(var n=vt(t),r=arguments.length,o=1,i=je.f,u=M.f;r>o;)for(var a,c=V(arguments[o++]),l=i?iu(Tn(c),i(c)):Tn(c),s=l.length,f=0;s>f;)a=l[f++],x&&!E(u,c,a)||(n[a]=c[a]);return n}:ru;function au(t,e){null==t.data&&(t.data={});var n=t.data;null==n.style&&(n.style={}),Object.assign(n.style,e)}qe({target:"Object",stat:!0,forced:Object.assign!==uu},{assign:uu});var cu={selector:"font",preParseHtml:function(t){var e=c.default(t);if("font"!==fo(e))return t;var n=e.attr("color")||"";return n&&(e.removeAttr("color"),e.css("color",n)),e[0]}};var lu=function(){function t(){this.tag="button",this.showDropPanel=!0,this.$content=null}return t.prototype.exec=function(t,e){},t.prototype.getValue=function(t){var e=this.mark,n=r.Editor.marks(t);return n&&n[e]?n[e]:""},t.prototype.isActive=function(t){return!!this.getValue(t)},t.prototype.isDisabled=function(t){return null==t.selection||!!xo(r.Editor.nodes(t,{match:function(n){return"pre"===e.DomEditor.getNodeType(n)||!!r.Editor.isVoid(t,n)},universal:!0}),1)[0]},t.prototype.getPanelContentElem=function(t){var n=this.mark;if(null==this.$content){var o=c.default('<ul class="w-e-panel-content-color"></ul>');o.on("click","li",(function(e){var o=e.target;if(null!=o&&(e.preventDefault(),null!=t.selection)){var i=c.default(o).attr("data-value");"0"===i?r.Editor.removeMark(t,n):r.Editor.addMark(t,n,i)}})),this.$content=o}var i=this.$content;if(null==i)return document.createElement("ul");i.empty();var u=this.getValue(t),a=t.getMenuConfig(n).colors;(void 0===a?[]:a).forEach((function(t){var e=c.default('<div class="color-block" data-value="'+t+'"></div>');e.css("background-color",t);var n=c.default('<li data-value="'+t+'"></li>');u===t&&n.addClass("active"),n.append(e),i.append(n)}));var l="";"color"===n&&(l=e.t("color.default")),"bgColor"===n&&(l=e.t("color.clear"));var s=c.default('\n      <li data-value="0" class="clear">\n        <svg viewBox="0 0 1024 1024"><path d="M236.8 128L896 787.2V128H236.8z m614.4 704L192 172.8V832h659.2zM192 64h704c38.4 0 64 25.6 64 64v704c0 38.4-25.6 64-64 64H192c-38.4 0-64-25.6-64-64V128c0-38.4 25.6-64 64-64z"></path></svg>\n        '+l+"\n      </li>\n    ");return i.prepend(s),i[0]},t}(),su=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("color.color"),n.iconSvg=Xo,n.mark="color",n}return vo(n,t),n}(lu),fu=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("color.bgColor"),n.iconSvg=Yo,n.mark="bgColor",n}return vo(n,t),n}(lu),du=["rgb(0, 0, 0)","rgb(38, 38, 38)","rgb(89, 89, 89)","rgb(140, 140, 140)","rgb(191, 191, 191)","rgb(217, 217, 217)","rgb(233, 233, 233)","rgb(245, 245, 245)","rgb(250, 250, 250)","rgb(255, 255, 255)","rgb(225, 60, 57)","rgb(231, 95, 51)","rgb(235, 144, 58)","rgb(245, 219, 77)","rgb(114, 192, 64)","rgb(89, 191, 192)","rgb(66, 144, 247)","rgb(54, 88, 226)","rgb(106, 57, 201)","rgb(216, 68, 147)","rgb(251, 233, 230)","rgb(252, 237, 225)","rgb(252, 239, 212)","rgb(252, 251, 207)","rgb(231, 246, 213)","rgb(218, 244, 240)","rgb(217, 237, 250)","rgb(224, 232, 250)","rgb(237, 225, 248)","rgb(246, 226, 234)","rgb(255, 163, 158)","rgb(255, 187, 150)","rgb(255, 213, 145)","rgb(255, 251, 143)","rgb(183, 235, 143)","rgb(135, 232, 222)","rgb(145, 213, 255)","rgb(173, 198, 255)","rgb(211, 173, 247)","rgb(255, 173, 210)","rgb(255, 77, 79)","rgb(255, 122, 69)","rgb(255, 169, 64)","rgb(255, 236, 61)","rgb(115, 209, 61)","rgb(54, 207, 201)","rgb(64, 169, 255)","rgb(89, 126, 247)","rgb(146, 84, 222)","rgb(247, 89, 171)","rgb(207, 19, 34)","rgb(212, 56, 13)","rgb(212, 107, 8)","rgb(212, 177, 6)","rgb(56, 158, 13)","rgb(8, 151, 156)","rgb(9, 109, 217)","rgb(29, 57, 196)","rgb(83, 29, 171)","rgb(196, 29, 127)","rgb(130, 0, 20)","rgb(135, 20, 0)","rgb(135, 56, 0)","rgb(97, 71, 0)","rgb(19, 82, 0)","rgb(0, 71, 79)","rgb(0, 58, 140)","rgb(6, 17, 120)","rgb(34, 7, 94)","rgb(120, 6, 80)"];var pu={renderStyle:function(t,e){var n=t,r=n.color,o=n.bgColor,i=e;return r&&au(i,{color:r}),o&&au(i,{backgroundColor:o}),i},styleToHtml:function(t,e){if(!r.Text.isText(t))return e;var n,o=t,i=o.color,u=o.bgColor;return i||u?((lo(e)||"span"!==fo(n=c.default(e)))&&(n=c.default("<span>"+e+"</span>")),i&&n.css("color",i),u&&n.css("background-color",u),so(n)):e},preParseHtml:[cu],parseStyleHtml:function(t,e,n){var o=c.default(t);if(!r.Text.isText(e))return e;var i=e,u=po(o,"color");u&&(i.color=u);var a=po(o,"background-color");return a||(a=po(o,"background")),a&&(i.bgColor=a),i},menus:[{key:"color",factory:function(){return new su},config:{colors:du}},{key:"bgColor",factory:function(){return new fu},config:{colors:du}}]},hu=function(t){if("string"!=typeof t)return!1;var e=t.match(vu);if(!e)return!1;var n=e[1];if(!n)return!1;if(gu.test(n)||mu.test(n))return!0;return!1},vu=/^(?:\w+:)?\/\/(\S+)$/,gu=/^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/,mu=/^[^\s\.]+\.\S{2,}$/;var yu=ke.includes;function bu(t){return void 0===t&&(t="r"),t+"-"+i.nanoid()}function xu(t){return t.replace(/</g,"&lt;").replace(/>/g,"&gt;")}function wu(t,e,n,r){return mo(this,void 0,void 0,(function(){var o,i;return yo(this,(function(u){switch(u.label){case 0:return(o=e.getMenuConfig(t).checkLink)?[4,o(n,r)]:[3,2];case 1:if("string"==typeof(i=u.sent()))return e.alert(i,"error"),[2,!1];if(null==i)return[2,!1];u.label=2;case 2:return[2,!0]}}))}))}function Eu(t,e,n){return mo(this,void 0,void 0,(function(){var r;return yo(this,(function(o){switch(o.label){case 0:return(r=e.getMenuConfig(t).parseLinkUrl)?[4,r(n)]:[3,2];case 1:return[2,o.sent()];case 2:return[2,n]}}))}))}function Su(t){return null==t.selection||!!e.DomEditor.getSelectedElems(t).some((function(e){var n=e.type;return!!t.isVoid(e)||(!!["pre","code","link"].includes(n)||void 0)}))}function ku(t,e){return{type:"link",url:xu(t),children:e?[{text:e}]:[]}}function Tu(t,e,n){return mo(this,void 0,void 0,(function(){var o,i,u;return yo(this,(function(a){switch(a.label){case 0:return n?(e||(e=n),t.restoreSelection(),Su(t)?[2]:[4,wu("insertLink",t,e,n)]):[2];case 1:return a.sent()?[4,Eu("insertLink",t,n)]:[2];case 2:return o=a.sent(),null==(i=t.selection)?[2]:(r.Range.isCollapsed(i)?(t.insertText(" "),u=ku(o,e),r.Transforms.insertNodes(t,u),t.insertFragment([{text:" "}])):r.Editor.string(t,i)!==e?(t.deleteFragment(),u=ku(o,e),r.Transforms.insertNodes(t,u)):(u=ku(o),r.Transforms.wrapNodes(t,u,{split:!0}),r.Transforms.collapse(t,{edge:"end"})),[2])}}))}))}qe({target:"Array",proto:!0},{includes:function(t){return yu(this,t,arguments.length>1?arguments[1]:void 0)}}),Dr("includes");var Mu={type:"link",renderElem:function(t,e,r){var o=t,i=o.url,u=o.target,a=void 0===u?"_blank":u;return n.jsx("a",{href:i,target:a},e)}};var Iu={type:"link",elemToHtml:function(t,e){var n=t,r=n.url,o=n.target;return'<a href="'+r+'" target="'+(void 0===o?"_blank":o)+'">'+e+"</a>"}};var Nu={selector:"a:not([data-w-e-type])",parseElemHtml:function(t,e,n){var o=c.default(t);return 0===(e=e.filter((function(t){return!!r.Text.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:o.text().replace(/\s+/gm," ")}]),{type:"link",url:o.attr("href")||"",target:o.attr("target")||"",children:e}}};function Hu(){return bu("w-e-insert-link")}var Ou=function(){function t(){this.title=e.t("link.insert"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M440.224 635.776a51.84 51.84 0 0 1-36.768-15.232c-95.136-95.136-95.136-249.92 0-345.056l192-192C641.536 37.408 702.816 12.032 768 12.032s126.432 25.376 172.544 71.456c95.136 95.136 95.136 249.92 0 345.056l-87.776 87.776a51.968 51.968 0 1 1-73.536-73.536l87.776-87.776a140.16 140.16 0 0 0 0-197.984c-26.432-26.432-61.6-40.992-99.008-40.992s-72.544 14.56-99.008 40.992l-192 192a140.16 140.16 0 0 0 0 197.984 51.968 51.968 0 0 1-36.768 88.768z"></path><path d="M256 1012a242.4 242.4 0 0 1-172.544-71.456c-95.136-95.136-95.136-249.92 0-345.056l87.776-87.776a51.968 51.968 0 1 1 73.536 73.536l-87.776 87.776a140.16 140.16 0 0 0 0 197.984c26.432 26.432 61.6 40.992 99.008 40.992s72.544-14.56 99.008-40.992l192-192a140.16 140.16 0 0 0 0-197.984 51.968 51.968 0 1 1 73.536-73.536c95.136 95.136 95.136 249.92 0 345.056l-192 192A242.4 242.4 0 0 1 256 1012z"></path></svg>',this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.textInputId=Hu(),this.urlInputId=Hu(),this.buttonId=Hu()}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return Su(t)},t.prototype.getModalPositionNode=function(t){return null},t.prototype.getModalContentElem=function(t){var n=t.selection,o=this,i=o.textInputId,u=o.urlInputId,a=o.buttonId,l=xo(e.genModalInputElems(e.t("link.text"),i),2),s=l[0],f=l[1],d=c.default(f),p=xo(e.genModalInputElems(e.t("link.url"),u),2),h=p[0],v=p[1],g=c.default(v),m=xo(e.genModalButtonElems(a,e.t("common.ok")),1)[0];if(null==this.$content){var y=c.default("<div></div>");y.on("click","#"+a,(function(e){e.preventDefault();var n=y.find("#"+i).val(),r=y.find("#"+u).val();Tu(t,n,r),t.hidePanelOrModal()})),this.$content=y}var b=this.$content;if(b.empty(),b.append(s),b.append(h),b.append(m),null==n||r.Range.isCollapsed(n))d.val("");else{var x=r.Editor.string(t,n);d.val(x)}return g.val(""),setTimeout((function(){d.focus()})),b[0]},t}();function ju(){return bu("w-e-update-link")}var Du=function(){function t(){this.title=e.t("link.edit"),this.iconSvg=Go,this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.urlInputId=ju(),this.buttonId=ju()}return t.prototype.getSelectedLinkElem=function(t){var n=e.DomEditor.getSelectedNodeByType(t,"link");return null==n?null:n},t.prototype.getValue=function(t){var e=this.getSelectedLinkElem(t);return e&&e.url||""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedLinkElem(t)},t.prototype.getModalPositionNode=function(t){return e.DomEditor.getSelectedNodeByType(t,"link")},t.prototype.getModalContentElem=function(t){var n=this.urlInputId,o=this.buttonId,i=xo(e.genModalInputElems(e.t("link.url"),n),2),u=i[0],a=i[1],l=c.default(a),s=xo(e.genModalButtonElems(o,e.t("common.ok")),1)[0];if(null==this.$content){var f=c.default("<div></div>");f.on("click","button",(function(o){o.preventDefault(),t.restoreSelection();var i=e.DomEditor.getSelectedNodeByType(t,"link"),u=i?r.Node.string(i):"",a=f.find("#"+n).val();!function(t,n,o){mo(this,void 0,void 0,(function(){var i,u;return yo(this,(function(a){switch(a.label){case 0:return o?[4,wu("editLink",t,n,o)]:[2];case 1:return a.sent()?[4,Eu("editLink",t,o)]:[2];case 2:return i=a.sent(),u={url:xu(i)},r.Transforms.setNodes(t,u,{match:function(t){return e.DomEditor.checkNodeType(t,"link")}}),[2]}}))}))}(t,u,a),t.hidePanelOrModal()})),this.$content=f}var d=this.$content;d.empty(),d.append(u),d.append(s);var p=this.getValue(t);return l.val(p),setTimeout((function(){l.focus()})),d[0]},t}(),Lu=function(){function t(){this.title=e.t("link.unLink"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M608.16328 811.815036c9.371954 9.371954 9.371954 24.56788 0 33.941834l-89.347563 89.347564c-118.525421 118.523421-311.38448 118.531421-429.919901 0-118.527421-118.529421-118.527421-311.39048 0-429.917901l89.349564-89.349563c9.371954-9.371954 24.56788-9.371954 33.941834 0l79.195613 79.195613c9.371954 9.371954 9.371954 24.56788 0 33.941834l-89.349563 89.347564c-56.143726 56.145726-56.143726 147.49928 0 203.645005 56.143726 56.143726 147.49928 56.145726 203.647005 0l89.347564-89.347563c9.371954-9.371954 24.56788-9.371954 33.941834 0l79.193613 79.195613z m-113.135447-520.429459c9.371954 9.371954 24.56788 9.371954 33.941834 0l89.347564-89.347564c56.143726-56.149726 147.49928-56.145726 203.647006 0 56.143726 56.145726 56.143726 147.49928 0 203.645006l-89.349564 89.347564c-9.371954 9.371954-9.371954 24.56788 0 33.941834l79.195613 79.195613c9.371954 9.371954 24.56788 9.371954 33.941834 0l89.349564-89.349563c118.529421-118.529421 118.529421-311.38848 0-429.917901-118.531421-118.527421-311.38848-118.527421-429.919901 0l-89.347563 89.347564c-9.371954 9.371954-9.371954 24.56788 0 33.941834l79.193613 79.195613z m469.653707 718.556492l45.253779-45.253779c18.745908-18.745908 18.745908-49.13776 0-67.881669L127.195629 14.062931c-18.745908-18.745908-49.13776-18.745908-67.881669 0L14.058181 59.31871c-18.745908 18.745908-18.745908 49.13776 0 67.881669l882.74169 882.74169c18.745908 18.743908 49.13776 18.743908 67.881669 0z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==e.DomEditor.getSelectedNodeByType(t,"link")},t.prototype.exec=function(t,n){this.isDisabled(t)||r.Transforms.unwrapNodes(t,{match:function(t){return e.DomEditor.checkNodeType(t,"link")}})},t}(),zu=function(){function t(){this.title=e.t("link.view"),this.iconSvg=Uo,this.tag="button"}return t.prototype.getSelectedLinkElem=function(t){var n=e.DomEditor.getSelectedNodeByType(t,"link");return null==n?null:n},t.prototype.getValue=function(t){var e=this.getSelectedLinkElem(t);return e&&e.url||""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedLinkElem(t)},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){if(!e||"string"!=typeof e)throw new Error("View link failed, link url is '"+e+"'");window.open(e,"_blank")}},t}();var Au={checkLink:function(t,e){return!0},parseLinkUrl:function(t){return t}},Pu={renderElems:[Mu],elemsToHtml:[Iu],parseElemsHtml:[Nu],menus:[{key:"insertLink",factory:function(){return new Ou},config:Au},{key:"editLink",factory:function(){return new Du},config:Au},{key:"unLink",factory:function(){return new Lu}},{key:"viewLink",factory:function(){return new zu}}],editorPlugin:function(t){var n=t.isInline,o=t.insertData,i=t.normalizeNode;t.insertNode,t.insertText;var u=t;return u.isInline=function(t){return"link"===t.type||n(t)},u.insertData=function(t){var e=t.getData("text/plain");if(hu(e)){if(!Su(u)){var n=u.selection;if(null!=n){var i=r.Editor.string(u,n);Tu(u,i,e)}}}else o(t)},u.normalizeNode=function(t){var n=xo(t,2),o=n[0],a=n[1];return"link"!==e.DomEditor.getNodeType(o)?i([o,a]):""===r.Node.string(o)?r.Transforms.removeNodes(u,{at:a}):i([o,a])},u}};var Cu=D(1..valueOf),Vu=y.RangeError,Bu=y.RangeError,Ru=y.String,Fu=Math.floor,_u=D((function(t){var e=bn(R(this)),n="",r=ge(t);if(r<0||r==1/0)throw Vu("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(n+=e);return n})),qu=D("".slice),$u=D(1..toFixed),Wu=function(t,e,n){return 0===e?n:e%2==1?Wu(t,e-1,n*t):Wu(t*t,e/2,n)},Gu=function(t,e,n){for(var r=-1,o=n;++r<6;)o+=e*t[r],t[r]=o%1e7,o=Fu(o/1e7)},Uu=function(t,e){for(var n=6,r=0;--n>=0;)r+=t[n],t[n]=Fu(r/e),r=r%e*1e7},Xu=function(t){for(var e=6,n="";--e>=0;)if(""!==n||0===e||0!==t[e]){var r=Ru(t[e]);n=""===n?r:n+_u("0",7-r.length)+r}return n};function Yu(t,n){return"w-e-image-container-"+e.DomEditor.findKey(t,n).id}function Ju(t,o,i,u){var a=c.default("body"),s=Yu(t,o),f=u.width,d=u.height,p=0,h=0,v=0,g=!1,m=null;function y(n){m=function(){var t=c.default("#"+s);if(0===t.length)throw new Error("Cannot find image container elem");return t}(),p=n;var r=m.find("img");if(0===r.length)throw new Error("Cannot find image elem");h=r.width(),v=r.height(),a.on("mousemove",b),a.on("mouseup",x);var o=e.DomEditor.getHoverbar(t);o&&o.hideAndClean()}var b=l.default((function(t){t.preventDefault();var e=t.clientX,n=h+(g?p-e:e-p),r=v*(n/h);null!=m&&(n<=15||r<=15||(m.css("width",n+"px"),m.css("height",r+"px")))}),100);function x(n){if(a.off("mousemove",b),null!=m){var i=m.width().toFixed(2),u=m.height().toFixed(2),c={style:go(go({},o.style),{width:i+"px",height:u+"px"})};r.Transforms.setNodes(t,c,{at:e.DomEditor.findPath(t,o)}),a.off("mouseup",x)}}var w={};return f&&(w.width=f),d&&(w.height=d),n.jsx("div",{id:s,style:w,className:"w-e-image-container w-e-selected-image-container",on:{mousedown:function(t){var e=c.default(t.target);e.hasClass("w-e-image-dragger")&&(t.preventDefault(),(e.hasClass("left-top")||e.hasClass("left-bottom"))&&(g=!0),y(t.clientX))}}},i,n.jsx("div",{className:"w-e-image-dragger left-top"}),n.jsx("div",{className:"w-e-image-dragger right-top"}),n.jsx("div",{className:"w-e-image-dragger left-bottom"}),n.jsx("div",{className:"w-e-image-dragger right-bottom"}))}qe({target:"Number",proto:!0,forced:b((function(){return"0.000"!==$u(8e-5,3)||"1"!==$u(.9,0)||"1.25"!==$u(1.255,2)||"1000000000000000128"!==$u(0xde0b6b3a7640080,0)}))||!b((function(){$u({})}))},{toFixed:function(t){var e,n,r,o,i=Cu(this),u=ge(t),a=[0,0,0,0,0,0],c="",l="0";if(u<0||u>20)throw Bu("Incorrect fraction digits");if(i!=i)return"NaN";if(i<=-1e21||i>=1e21)return Ru(i);if(i<0&&(c="-",i=-i),i>1e-21)if(n=(e=function(t){for(var e=0,n=t;n>=4096;)e+=12,n/=4096;for(;n>=2;)e+=1,n/=2;return e}(i*Wu(2,69,1))-69)<0?i*Wu(2,-e,1):i/Wu(2,e,1),n*=4503599627370496,(e=52-e)>0){for(Gu(a,0,n),r=u;r>=7;)Gu(a,1e7,0),r-=7;for(Gu(a,Wu(10,r,1),0),r=e-1;r>=23;)Uu(a,1<<23),r-=23;Uu(a,1<<r),Gu(a,1,1),Uu(a,2),l=Xu(a)}else Gu(a,0,n),Gu(a,1<<-e,0),l=Xu(a)+_u("0",u);return l=u>0?c+((o=l.length)<=u?"0."+_u("0",u-o)+l:qu(l,0,o-u)+"."+qu(l,o-u)):c+l}});var Ku={type:"image",renderElem:function(t,r,o){var i=t,u=i.src,a=i.alt,c=void 0===a?"":a,l=i.href,s=void 0===l?"":l,f=i.style,d=void 0===f?{}:f,p=d.width,h=void 0===p?"":p,v=d.height,g=void 0===v?"":v,m=e.DomEditor.isNodeSelected(o,t),y={};h&&(y.width="100%"),g&&(y.height="100%");var b=n.jsx("img",{style:y,src:u,alt:c,"data-href":s}),x=o.isDisabled();return m&&!x?Ju(o,t,b,{width:h,height:g}):function(t,e,r,o){var i=o.width,u=o.height,a={};i&&(a.width=i),u&&(a.height=u);var c=Yu(t,e);return n.jsx("div",{id:c,style:a,className:"w-e-image-container"},r)}(o,t,b,{width:h,height:g})}};var Qu={type:"image",elemToHtml:function(t,e){var n=t,r=n.src,o=n.alt,i=void 0===o?"":o,u=n.href,a=void 0===u?"":u,c=n.style,l=void 0===c?{}:c,s=l.width,f=void 0===s?"":s,d=l.height,p=void 0===d?"":d,h="";return f&&(h+="width: "+f+";"),p&&(h+="height: "+p+";"),'<img src="'+r+'" alt="'+i+'" data-href="'+a+'" style="'+h+'"/>'}};var Zu={selector:"img:not([data-w-e-type])",parseElemHtml:function(t,e,n){var r=c.default(t),o=r.attr("data-href")||"";return o=decodeURIComponent(o),{type:"image",src:r.attr("src")||"",alt:r.attr("alt")||"",href:o,style:{width:po(r,"width"),height:po(r,"height")},children:[{text:""}]}}};function ta(t,e,n,r,o){return void 0===r&&(r=""),void 0===o&&(o=""),mo(this,void 0,void 0,(function(){var i,u;return yo(this,(function(a){switch(a.label){case 0:return(i=e.getMenuConfig(t).checkImage)?[4,i(n,r,o)]:[3,2];case 1:if("string"==typeof(u=a.sent()))return e.alert(u,"error"),[2,!1];if(null==u)return[2,!1];a.label=2;case 2:return[2,!0]}}))}))}function ea(t,e,n){return mo(this,void 0,void 0,(function(){var r;return yo(this,(function(o){switch(o.label){case 0:return(r=e.getMenuConfig(t).parseImageSrc)?[4,r(n)]:[3,2];case 1:return[2,o.sent()];case 2:return[2,n]}}))}))}function na(t,n,o,i){return void 0===o&&(o=""),void 0===i&&(i=""),mo(this,void 0,void 0,(function(){var u,a,c;return yo(this,(function(l){switch(l.label){case 0:return[4,ta("insertImage",t,n,o,i)];case 1:return l.sent()?[4,ea("insertImage",t,n)]:[2];case 2:return u=l.sent(),a={type:"image",src:xu(u),href:i,alt:o,style:{},children:[{text:""}]},null===t.selection&&t.restoreSelection(),e.DomEditor.getSelectedNodeByType(t,"image")&&t.move(1),oa(t)?[2]:(r.Transforms.insertNodes(t,a),(c=t.getMenuConfig("insertImage").onInsertedImage)&&c(a),[2])}}))}))}function ra(t,n,o,i,u){return void 0===o&&(o=""),void 0===i&&(i=""),void 0===u&&(u={}),mo(this,void 0,void 0,(function(){var a,c,l,s,f,d;return yo(this,(function(p){switch(p.label){case 0:return[4,ta("editImage",t,n,o,i)];case 1:return p.sent()?[4,ea("editImage",t,n)]:[2];case 2:return a=p.sent(),null==(c=e.DomEditor.getSelectedNodeByType(t,"image"))?[2]:(l=c.style,s={src:a,alt:o,href:i,style:go(go({},void 0===l?{}:l),u)},r.Transforms.setNodes(t,s,{match:function(t){return e.DomEditor.checkNodeType(t,"image")}}),f=e.DomEditor.getSelectedNodeByType(t,"image"),(d=t.getMenuConfig("editImage").onUpdatedImage)&&d(f),[2])}}))}))}function oa(t){var n=t.selection;return null==n||(!r.Range.isCollapsed(n)||!!xo(r.Editor.nodes(t,{match:function(n){var o=e.DomEditor.getNodeType(n);return"code"===o||("pre"===o||("link"===o||("list-item"===o||(!!o.startsWith("header")||("blockquote"===o||!!r.Editor.isVoid(t,n))))))},universal:!0}),1)[0])}function ia(){return bu("w-e-insert-image")}var ua=function(){function t(){this.title=e.t("image.netImage"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.srcInputId=ia(),this.altInputId=ia(),this.hrefInputId=ia(),this.buttonId=ia()}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return oa(t)},t.prototype.getModalPositionNode=function(t){return null},t.prototype.getModalContentElem=function(t){var n=this,r=this,o=r.srcInputId,i=r.altInputId,u=r.hrefInputId,a=r.buttonId,l=xo(e.genModalInputElems(e.t("image.src"),o),2),s=l[0],f=l[1],d=c.default(f),p=xo(e.genModalInputElems(e.t("image.desc"),i),2),h=p[0],v=p[1],g=c.default(v),m=xo(e.genModalInputElems(e.t("image.link"),u),2),y=m[0],b=m[1],x=c.default(b),w=xo(e.genModalButtonElems(a,e.t("common.ok")),1)[0];if(null==this.$content){var E=c.default("<div></div>");E.on("click","#"+a,(function(e){e.preventDefault();var r=E.find("#"+o).val().trim(),a=E.find("#"+i).val().trim(),c=E.find("#"+u).val().trim();n.insertImage(t,r,a,c),t.hidePanelOrModal()})),this.$content=E}var S=this.$content;return S.empty(),S.append(s),S.append(h),S.append(y),S.append(w),d.val(""),g.val(""),x.val(""),setTimeout((function(){d.focus()})),S[0]},t.prototype.insertImage=function(t,e,n,r){void 0===n&&(n=""),void 0===r&&(r=""),e&&(t.restoreSelection(),this.isDisabled(t)||na(t,e,n,r))},t}(),aa=function(){function t(){this.title=e.t("image.delete"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M826.8032 356.5312c-19.328 0-36.3776 15.6928-36.3776 35.0464v524.2624c0 19.328-16 34.56-35.328 34.56H264.9344c-19.328 0-35.5072-15.3088-35.5072-34.56V390.0416c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.6928-33.5104 35.0464V915.712c0 57.9328 44.6208 108.288 102.528 108.288H755.2c57.9328 0 108.0832-50.4576 108.0832-108.288V391.4752c-0.1024-19.2512-17.1264-34.944-36.48-34.944z" p-id="9577"></path><path d="M437.1712 775.7568V390.6048c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.616-33.5104 35.0464v385.152c0 19.328 14.1568 35.0464 33.5104 35.0464s33.5104-15.7184 33.5104-35.0464zM649.7024 775.7568V390.6048c0-19.328-17.0496-35.0464-36.3776-35.0464s-36.3776 15.616-36.3776 35.0464v385.152c0 19.328 17.0496 35.0464 36.3776 35.0464s36.3776-15.7184 36.3776-35.0464zM965.0432 217.0368h-174.6176V145.5104c0-57.9328-47.2064-101.76-104.6528-101.76h-350.976c-57.8304 0-105.3952 43.8528-105.3952 101.76v71.5264H54.784c-19.4304 0-35.0464 14.1568-35.0464 33.5104 0 19.328 15.616 33.5104 35.0464 33.5104h910.3616c19.328 0 35.0464-14.1568 35.0464-33.5104 0-19.3536-15.6928-33.5104-35.1488-33.5104z m-247.3728 0H297.3952V145.5104c0-19.328 18.2016-34.7648 37.4272-34.7648h350.976c19.1488 0 31.872 15.1296 31.872 34.7648v71.5264z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==e.DomEditor.getSelectedNodeByType(t,"image")},t.prototype.exec=function(t,n){this.isDisabled(t)||r.Transforms.removeNodes(t,{match:function(t){return e.DomEditor.checkNodeType(t,"image")}})},t}();function ca(){return bu("w-e-edit-image")}var la=function(){function t(){this.title=e.t("image.edit"),this.iconSvg=Go,this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.srcInputId=ca(),this.altInputId=ca(),this.hrefInputId=ca(),this.buttonId=ca()}return t.prototype.getValue=function(t){return""},t.prototype.getImageNode=function(t){return e.DomEditor.getSelectedNodeByType(t,"image")},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!r.Range.isCollapsed(n)||null==e.DomEditor.getSelectedNodeByType(t,"image"))},t.prototype.getModalPositionNode=function(t){return this.getImageNode(t)},t.prototype.getModalContentElem=function(t){var n=this,r=this,o=r.srcInputId,i=r.altInputId,u=r.hrefInputId,a=r.buttonId,l=this.getImageNode(t);if(null==l)throw new Error("Not found selected image node");var s=xo(e.genModalInputElems(e.t("image.src"),o),2),f=s[0],d=s[1],p=c.default(d),h=xo(e.genModalInputElems(e.t("image.desc"),i),2),v=h[0],g=h[1],m=c.default(g),y=xo(e.genModalInputElems(e.t("image.link"),u),2),b=y[0],x=y[1],w=c.default(x),E=xo(e.genModalButtonElems(a,e.t("common.ok")),1)[0];if(null==this.$content){var S=c.default("<div></div>");S.on("click","#"+a,(function(e){e.preventDefault();var r=S.find("#"+o).val(),a=S.find("#"+i).val(),c=S.find("#"+u).val();n.updateImage(t,r,a,c),t.hidePanelOrModal()})),this.$content=S}var k=this.$content;k.empty(),k.append(f),k.append(v),k.append(b),k.append(E);var T=l,M=T.src,I=T.alt,N=void 0===I?"":I,H=T.href,O=void 0===H?"":H;return p.val(M),m.val(N),w.val(O),setTimeout((function(){p.focus()})),k[0]},t.prototype.updateImage=function(t,e,n,r,o){void 0===n&&(n=""),void 0===r&&(r=""),void 0===o&&(o={}),e&&(t.restoreSelection(),this.isDisabled(t)||ra(t,e,n,r,o))},t}(),sa=function(){function t(){this.title=e.t("image.viewLink"),this.iconSvg=Uo,this.tag="button"}return t.prototype.getValue=function(t){var n=e.DomEditor.getSelectedNodeByType(t,"image");return n&&n.href||""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||!this.getValue(t)},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){if(!e||"string"!=typeof e)throw new Error("View image link failed, image.href is '"+e+"'");window.open(e,"_blank")}},t}(),fa=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.getSelectedNode=function(t){return e.DomEditor.getSelectedNodeByType(t,"image")},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedNode(t)},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var o=this.getSelectedNode(t);if(null!=o){var i=e.DomEditor.getHoverbar(t);i&&i.hideAndClean();var u=o.style,a={style:go(go({},void 0===u?{}:u),{width:this.value,height:""})};r.Transforms.setNodes(t,a,{match:function(t){return e.DomEditor.checkNodeType(t,"image")}})}}},t}(),da=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="30%",e.value="30%",e}return vo(e,t),e}(fa),pa=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="50%",e.value="50%",e}return vo(e,t),e}(fa),ha=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="100%",e.value="100%",e}return vo(e,t),e}(fa);var va={onInsertedImage:function(t){},onUpdatedImage:function(t){},checkImage:function(t,e,n){return!0},parseImageSrc:function(t){return t}},ga={renderElems:[Ku],elemsToHtml:[Qu],parseElemsHtml:[Zu],menus:[{key:"insertImage",factory:function(){return new ua},config:va},{key:"deleteImage",factory:function(){return new aa}},{key:"editImage",factory:function(){return new la},config:va},{key:"viewImageLink",factory:function(){return new sa}},{key:"imageWidth30",factory:function(){return new da}},{key:"imageWidth50",factory:function(){return new pa}},{key:"imageWidth100",factory:function(){return new ha}}],editorPlugin:function(t){var e=t.isInline,n=t.isVoid;t.insertNode;var r=t;return r.isInline=function(t){return"image"===t.type||e(t)},r.isVoid=function(t){return"image"===t.type||n(t)},r}};var ma={type:"todo",renderElem:function(t,o,i){var u=!1;i.isDisabled()&&(u=!0);var a=t.checked,c=n.jsx("div",{style:{margin:"5px 0"}},n.jsx("span",{contentEditable:!1,style:{marginRight:"0.5em"}},n.jsx("input",{type:"checkbox",checked:a,disabled:u,on:{change:function(n){var o=e.DomEditor.findPath(i,t),u={checked:n.target.checked};r.Transforms.setNodes(i,u,{at:o})}}})),n.jsx("span",null,o));return c}};var ya=function(){function t(){this.title=e.t("todo.todo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M278.755556 403.911111l-79.644445 79.644445L455.111111 739.555556l568.888889-568.888889-79.644444-79.644445L455.111111 580.266667l-176.355555-176.355556zM910.222222 910.222222H113.777778V113.777778h568.888889V0H113.777778C51.2 0 0 51.2 0 113.777778v796.444444c0 62.577778 51.2 113.777778 113.777778 113.777778h796.444444c62.577778 0 113.777778-51.2 113.777778-113.777778V455.111111h-113.777778v455.111111z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!!e.DomEditor.getSelectedNodeByType(t,"todo")},t.prototype.isDisabled=function(t){return null==t.selection||!!e.DomEditor.getSelectedElems(t).some((function(e){if(r.Editor.isVoid(t,e)&&r.Editor.isBlock(t,e))return!0;var n=e.type;return!!["pre","table","list-item"].includes(n)||void 0}))},t.prototype.exec=function(t,e){var n=this.isActive(t);r.Transforms.setNodes(t,{type:n?"paragraph":"todo"})},t}();var ba={renderElems:[ma],elemsToHtml:[{type:"todo",elemToHtml:function(t,e){return'<div data-w-e-type="todo"><input type="checkbox" disabled '+(t.checked?"checked":"")+">"+e+"</div>"}}],preParseHtml:[{selector:"ul.w-e-todo",preParseHtml:function(t){var e=c.default(t).find("li"),n=c.default('<div data-w-e-type="todo"></div>'),r=e.find("input[type]");return n.append(r),e.children()[0].remove(),n[0].innerHTML=n[0].innerHTML+e[0].innerHTML,n[0]}}],parseElemsHtml:[{selector:'div[data-w-e-type="todo"]',parseElemHtml:function(t,e,n){var o=c.default(t);0===(e=e.filter((function(t){return!!r.Text.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:o.text().replace(/\s+/gm," ")}]);var i=!1;return null!=o.find('input[type="checkbox"]').attr("checked")&&(i=!0),{type:"todo",checked:i,children:e}}}],menus:[{key:"todo",factory:function(){return new ya}}],editorPlugin:function(t){var n=t.deleteBackward,o=t;return o.deleteBackward=function(o){var i=t.selection;if(i&&r.Range.isCollapsed(i)){var u=e.DomEditor.getSelectedNodeByType(t,"todo");if(u&&0===r.Node.string(u).length)return void r.Transforms.setNodes(t,{type:"paragraph"},{mode:"highest"})}n(o)},o}};var xa={type:"blockquote",renderElem:function(t,e,r){return n.jsx("blockquote",null,e)}};var wa={type:"blockquote",elemToHtml:function(t,e){return"<blockquote>"+e+"</blockquote>"}};var Ea={selector:"blockquote:not([data-w-e-type])",parseElemHtml:function(t,e,n){var o=c.default(t);return 0===(e=e.filter((function(t){return!!r.Text.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:o.text().replace(/\s+/gm," ")}]),{type:"blockquote",children:e}}},Sa=function(){function t(){this.title=e.t("blockQuote.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M894.6 907.1H605.4c-32.6 0-59-26.4-59-59V608.2l-4-14.9c0-315.9 125.5-485.1 376.5-507.5v59.8C752.7 180.4 711.3 315.8 711.3 442.4v41.2l31.5 12.3h151.8c32.6 0 59 26.4 59 59v293.2c0 32.5-26.4 59-59 59z m-472 0H133.4c-32.6 0-59-26.4-59-59V608.2l-4-14.9c0-315.9 125.5-485.1 376.5-507.5v59.8C280.7 180.4 239.3 315.8 239.3 442.4v41.2l31.5 12.3h151.8c32.6 0 59 26.4 59 59v293.2c0 32.5-26.4 59-59 59z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!!e.DomEditor.getSelectedNodeByType(t,"blockquote")},t.prototype.isDisabled=function(t){return null==t.selection||!xo(r.Editor.nodes(t,{match:function(t){var n=e.DomEditor.getNodeType(t);return"paragraph"===n||"blockquote"===n},universal:!0,mode:"highest"}),1)[0]},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){var n=this.isActive(t)?"paragraph":"blockquote";r.Transforms.setNodes(t,{type:n},{mode:"highest"})}},t}(),ka={key:"blockquote",factory:function(){return new Sa}},Ta=D([].slice),Ma=hn("slice"),Ia=Mt("species"),Na=y.Array,Ha=Math.max;qe({target:"Array",proto:!0,forced:!Ma},{slice:function(t,e){var n,r,o,i=F(this),u=Ee(i),a=be(t,u),c=be(void 0===e?u:e,u);if(We(i)&&(n=i.constructor,(un(n)&&(n===Na||We(n.prototype))||q(n)&&null===(n=n[Ia]))&&(n=void 0),n===Na||void 0===n))return Ta(i,a,c);for(r=new(void 0===n?Na:n)(Ha(c-a,0)),o=0;a<c;a++,o++)a in i&&Fr(r,o,i[a]);return r.length=o,r}});var Oa={renderElems:[xa],elemsToHtml:[wa],parseElemsHtml:[Ea],menus:[ka],editorPlugin:function(t){var n=t.insertBreak,o=t.insertText,i=t;return i.insertBreak=function(){var u=i.selection;if(null==u)return n();var a=xo(r.Editor.nodes(t,{match:function(t){return e.DomEditor.checkNodeType(t,"blockquote")},universal:!0}),1)[0];if(!a)return n();var c=a[0],l=e.DomEditor.findPath(t,c),s=r.Editor.end(t,l);if(r.Point.equals(s,u.focus)){var f=r.Node.string(c);if(f&&"\n"===f.slice(-1)){t.deleteBackward("character");return void r.Transforms.insertNodes(i,{type:"paragraph",children:[{text:""}]},{mode:"highest"})}}o("\n")},i}},ja=function(){function t(){this.title=e.t("emotion.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M512 1024C230.4 1024 0 793.6 0 512S230.4 0 512 0s512 230.4 512 512-230.4 512-512 512z m0-102.4c226.742857 0 409.6-182.857143 409.6-409.6S738.742857 102.4 512 102.4 102.4 285.257143 102.4 512s182.857143 409.6 409.6 409.6z m-204.8-358.4h409.6c0 113.371429-91.428571 204.8-204.8 204.8s-204.8-91.428571-204.8-204.8z m0-102.4c-43.885714 0-76.8-32.914286-76.8-76.8s32.914286-76.8 76.8-76.8 76.8 32.914286 76.8 76.8-32.914286 76.8-76.8 76.8z m409.6 0c-43.885714 0-76.8-32.914286-76.8-76.8s32.914286-76.8 76.8-76.8c43.885714 0 76.8 32.914286 76.8 76.8s-32.914286 76.8-76.8 76.8z"></path></svg>',this.tag="button",this.showDropPanel=!0,this.$content=null}return t.prototype.exec=function(t,e){},t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||!!xo(r.Editor.nodes(t,{match:function(n){return"pre"===e.DomEditor.getNodeType(n)||!!r.Editor.isVoid(t,n)},universal:!0}),1)[0]},t.prototype.getPanelContentElem=function(t){if(null==this.$content){var e=c.default('<ul class="w-e-panel-content-emotion"></ul>');e.on("click","li",(function(e){var n=e.target;if(null!=n){e.preventDefault();var r=c.default(n).text();t.insertText(r)}})),this.$content=e}var n=this.$content;if(null==n)return document.createElement("ul");n.empty();var r=t.getMenuConfig("emotion").emotions;return(void 0===r?[]:r).forEach((function(t){var e=c.default("<li>"+t+"</li>");n.append(e)})),n[0]},t}();var Da={menus:[{key:"emotion",factory:function(){return new ja},config:{emotions:"😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉 😌 😍 😘 😗 😙 😚 😋 😛 😝 😜 🤓 😎 😏 😒 😞 😔 😟 😕 🙁 😣 😖 😫 😩 😢 😭 😤 😠 😡 😳 😱 😨 🤗 🤔 😶 😑 😬 🙄 😯 😴 😷 🤑 😈 🤡 💩 👻 💀 👀 👣 👐 🙌 👏 🤝 👍 👎 👊 ✊ 🤛 🤜 🤞 ✌️ 🤘 👌 👈 👉 👆 👇 ☝️ ✋ 🤚 🖐 🖖 👋 🤙 💪 🖕 ✍️ 🙏".split(" ")}}]};var La={1:"12px",2:"14px",3:"16px",4:"19px",5:"24px",6:"32px",7:"48px"};var za={selector:"font",preParseHtml:function(t){var e=c.default(t);if("font"!==fo(e))return t;var n=e.attr("size")||"";n&&(e.removeAttr("size"),e.css("font-size",La[n]));var r=e.attr("face")||"";return r&&(e.removeAttr("face"),e.css("font-family",r)),e[0]}},Aa=D("".indexOf);qe({target:"String",proto:!0,forced:!Di("includes")},{includes:function(t){return!!~Aa(bn(R(this)),bn(Oi(t)),arguments.length>1?arguments[1]:void 0)}});var Pa,Ca,Va=function(t){return t&&t.Math==Math&&t},Ba=Va("object"==typeof globalThis&&globalThis)||Va("object"==typeof window&&window)||Va("object"==typeof self&&self)||Va("object"==typeof d&&d)||function(){return this}()||Function("return this")(),Ra=Function.prototype,Fa=Ra.apply,_a=Ra.bind,qa=Ra.call,$a="object"==typeof Reflect&&Reflect.apply||(_a?qa.bind(Fa):function(){return qa.apply(Fa,arguments)}),Wa=Function.prototype,Ga=Wa.bind,Ua=Wa.call,Xa=Ga&&Ga.bind(Ua),Ya=Ga?function(t){return t&&Xa(Ua,t)}:function(t){return t&&function(){return Ua.apply(t,arguments)}},Ja=function(t){return"function"==typeof t},Ka=function(t){try{return!!t()}catch(t){return!0}},Qa=!Ka((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),Za=Function.prototype.call,tc=Za.bind?Za.bind(Za):function(){return Za.apply(Za,arguments)},ec={}.propertyIsEnumerable,nc=Object.getOwnPropertyDescriptor,rc=nc&&!ec.call({1:2},1)?function(t){var e=nc(this,t);return!!e&&e.enumerable}:ec,oc={f:rc},ic=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},uc=Ya({}.toString),ac=Ya("".slice),cc=function(t){return ac(uc(t),8,-1)},lc=Ba.Object,sc=Ya("".split),fc=Ka((function(){return!lc("z").propertyIsEnumerable(0)}))?function(t){return"String"==cc(t)?sc(t,""):lc(t)}:lc,dc=Ba.TypeError,pc=function(t){if(null==t)throw dc("Can't call method on "+t);return t},hc=function(t){return fc(pc(t))},vc=function(t){return"object"==typeof t?null!==t:Ja(t)},gc={},mc=function(t){return Ja(t)?t:void 0},yc=function(t,e){return arguments.length<2?mc(gc[t])||mc(Ba[t]):gc[t]&&gc[t][e]||Ba[t]&&Ba[t][e]},bc=Ya({}.isPrototypeOf),xc=yc("navigator","userAgent")||"",wc=Ba.process,Ec=Ba.Deno,Sc=wc&&wc.versions||Ec&&Ec.version,kc=Sc&&Sc.v8;kc&&(Ca=(Pa=kc.split("."))[0]>0&&Pa[0]<4?1:+(Pa[0]+Pa[1])),!Ca&&xc&&(!(Pa=xc.match(/Edge\/(\d+)/))||Pa[1]>=74)&&(Pa=xc.match(/Chrome\/(\d+)/))&&(Ca=+Pa[1]);var Tc=Ca,Mc=!!Object.getOwnPropertySymbols&&!Ka((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Tc&&Tc<41})),Ic=Mc&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Nc=Ba.Object,Hc=Ic?function(t){return"symbol"==typeof t}:function(t){var e=yc("Symbol");return Ja(e)&&bc(e.prototype,Nc(t))},Oc=Ba.String,jc=Ba.TypeError,Dc=function(t){if(Ja(t))return t;throw jc(function(t){try{return Oc(t)}catch(t){return"Object"}}(t)+" is not a function")},Lc=Ba.TypeError,zc=Object.defineProperty,Ac="__core-js_shared__",Pc=Ba[Ac]||function(t,e){try{zc(Ba,t,{value:e,configurable:!0,writable:!0})}catch(n){Ba[t]=e}return e}(Ac,{}),Cc=h((function(t){(t.exports=function(t,e){return Pc[t]||(Pc[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"pure",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),Vc=Ba.Object,Bc=function(t){return Vc(pc(t))},Rc=Ya({}.hasOwnProperty),Fc=Object.hasOwn||function(t,e){return Rc(Bc(t),e)},_c=0,qc=Math.random(),$c=Ya(1..toString),Wc=function(t){return"Symbol("+(void 0===t?"":t)+")_"+$c(++_c+qc,36)},Gc=Cc("wks"),Uc=Ba.Symbol,Xc=Uc&&Uc.for,Yc=Ic?Uc:Uc&&Uc.withoutSetter||Wc,Jc=function(t){if(!Fc(Gc,t)||!Mc&&"string"!=typeof Gc[t]){var e="Symbol."+t;Mc&&Fc(Uc,t)?Gc[t]=Uc[t]:Gc[t]=Ic&&Xc?Xc(e):Yc(e)}return Gc[t]},Kc=Ba.TypeError,Qc=Jc("toPrimitive"),Zc=function(t,e){if(!vc(t)||Hc(t))return t;var n,r,o=null==(n=t[Qc])?void 0:Dc(n);if(o){if(void 0===e&&(e="default"),r=tc(o,t,e),!vc(r)||Hc(r))return r;throw Kc("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&Ja(n=t.toString)&&!vc(r=tc(n,t)))return r;if(Ja(n=t.valueOf)&&!vc(r=tc(n,t)))return r;if("string"!==e&&Ja(n=t.toString)&&!vc(r=tc(n,t)))return r;throw Lc("Can't convert object to primitive value")}(t,e)},tl=function(t){var e=Zc(t,"string");return Hc(e)?e:e+""},el=Ba.document,nl=vc(el)&&vc(el.createElement),rl=function(t){return nl?el.createElement(t):{}},ol=!Qa&&!Ka((function(){return 7!=Object.defineProperty(rl("div"),"a",{get:function(){return 7}}).a})),il=Object.getOwnPropertyDescriptor,ul={f:Qa?il:function(t,e){if(t=hc(t),e=tl(e),ol)try{return il(t,e)}catch(t){}if(Fc(t,e))return ic(!tc(oc.f,t,e),t[e])}},al=/#|\.prototype\./,cl=function(t,e){var n=sl[ll(t)];return n==dl||n!=fl&&(Ja(e)?Ka(e):!!e)},ll=cl.normalize=function(t){return String(t).replace(al,".").toLowerCase()},sl=cl.data={},fl=cl.NATIVE="N",dl=cl.POLYFILL="P",pl=cl,hl=Ya(Ya.bind),vl=function(t,e){return Dc(t),void 0===e?t:hl?hl(t,e):function(){return t.apply(e,arguments)}},gl=Ba.String,ml=Ba.TypeError,yl=function(t){if(vc(t))return t;throw ml(gl(t)+" is not an object")},bl=Ba.TypeError,xl=Object.defineProperty,wl={f:Qa?xl:function(t,e,n){if(yl(t),e=tl(e),yl(n),ol)try{return xl(t,e,n)}catch(t){}if("get"in n||"set"in n)throw bl("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},El=Qa?function(t,e,n){return wl.f(t,e,ic(1,n))}:function(t,e,n){return t[e]=n,t},Sl=ul.f,kl=function(t){var e=function(n,r,o){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(n);case 2:return new t(n,r)}return new t(n,r,o)}return $a(t,this,arguments)};return e.prototype=t.prototype,e},Tl=function(t,e){var n,r,o,i,u,a,c,l,s=t.target,f=t.global,d=t.stat,p=t.proto,h=f?Ba:d?Ba[s]:(Ba[s]||{}).prototype,v=f?gc:gc[s]||El(gc,s,{})[s],g=v.prototype;for(o in e)n=!pl(f?o:s+(d?".":"#")+o,t.forced)&&h&&Fc(h,o),u=v[o],n&&(a=t.noTargetGet?(l=Sl(h,o))&&l.value:h[o]),i=n&&a?a:e[o],n&&typeof u==typeof i||(c=t.bind&&n?vl(i,Ba):t.wrap&&n?kl(i):p&&Ja(i)?Ya(i):i,(t.sham||i&&i.sham||u&&u.sham)&&El(c,"sham",!0),El(v,o,c),p&&(Fc(gc,r=s+"Prototype")||El(gc,r,{}),El(gc[r],o,i),t.real&&g&&!g[o]&&El(g,o,i)))},Ml=Array.isArray||function(t){return"Array"==cc(t)},Il=Math.ceil,Nl=Math.floor,Hl=function(t){var e=+t;return e!=e||0===e?0:(e>0?Nl:Il)(e)},Ol=Math.min,jl=function(t){return(e=t.length)>0?Ol(Hl(e),9007199254740991):0;var e},Dl=function(t,e,n){var r=tl(e);r in t?wl.f(t,r,ic(0,n)):t[r]=n},Ll={};Ll[Jc("toStringTag")]="z";var zl="[object z]"===String(Ll),Al=Jc("toStringTag"),Pl=Ba.Object,Cl="Arguments"==cc(function(){return arguments}()),Vl=zl?cc:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Pl(t),Al))?n:Cl?cc(e):"Object"==(r=cc(e))&&Ja(e.callee)?"Arguments":r},Bl=Ya(Function.toString);Ja(Pc.inspectSource)||(Pc.inspectSource=function(t){return Bl(t)});var Rl=Pc.inspectSource,Fl=function(){},_l=[],ql=yc("Reflect","construct"),$l=/^\s*(?:class|function)\b/,Wl=Ya($l.exec),Gl=!$l.exec(Fl),Ul=function(t){if(!Ja(t))return!1;try{return ql(Fl,_l,t),!0}catch(t){return!1}},Xl=!ql||Ka((function(){var t;return Ul(Ul.call)||!Ul(Object)||!Ul((function(){t=!0}))||t}))?function(t){if(!Ja(t))return!1;switch(Vl(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Gl||!!Wl($l,Rl(t))}:Ul,Yl=Jc("species"),Jl=Ba.Array,Kl=function(t,e){return new(function(t){var e;return Ml(t)&&(e=t.constructor,(Xl(e)&&(e===Jl||Ml(e.prototype))||vc(e)&&null===(e=e[Yl]))&&(e=void 0)),void 0===e?Jl:e}(t))(0===e?0:e)},Ql=Jc("species"),Zl=Jc("isConcatSpreadable"),ts=9007199254740991,es="Maximum allowed index exceeded",ns=Ba.TypeError,rs=Tc>=51||!Ka((function(){var t=[];return t[Zl]=!1,t.concat()[0]!==t})),os=function(t){return Tc>=51||!Ka((function(){var e=[];return(e.constructor={})[Ql]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}("concat"),is=function(t){if(!vc(t))return!1;var e=t[Zl];return void 0!==e?!!e:Ml(t)};Tl({target:"Array",proto:!0,forced:!rs||!os},{concat:function(t){var e,n,r,o,i,u=Bc(this),a=Kl(u,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(is(i=-1===e?u:arguments[e])){if(c+(o=jl(i))>ts)throw ns(es);for(n=0;n<o;n++,c++)n in i&&Dl(a,c,i[n])}else{if(c>=ts)throw ns(es);Dl(a,c++,i)}return a.length=c,a}});var us,as=Ba.String,cs=function(t){if("Symbol"===Vl(t))throw TypeError("Cannot convert a Symbol value to a string");return as(t)},ls=Math.max,ss=Math.min,fs=function(t,e){var n=Hl(t);return n<0?ls(n+e,0):ss(n,e)},ds=function(t){return function(e,n,r){var o,i=hc(e),u=jl(i),a=fs(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},ps={includes:ds(!0),indexOf:ds(!1)},hs={},vs=ps.indexOf,gs=Ya([].push),ms=function(t,e){var n,r=hc(t),o=0,i=[];for(n in r)!Fc(hs,n)&&Fc(r,n)&&gs(i,n);for(;e.length>o;)Fc(r,n=e[o++])&&(~vs(i,n)||gs(i,n));return i},ys=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],bs=Object.keys||function(t){return ms(t,ys)},xs=Qa?Object.defineProperties:function(t,e){yl(t);for(var n,r=hc(e),o=bs(e),i=o.length,u=0;i>u;)wl.f(t,n=o[u++],r[n]);return t},ws=yc("document","documentElement"),Es=Cc("keys"),Ss=function(t){return Es[t]||(Es[t]=Wc(t))},ks=Ss("IE_PROTO"),Ts=function(){},Ms=function(t){return"<script>"+t+"</"+"script>"},Is=function(t){t.write(Ms("")),t.close();var e=t.parentWindow.Object;return t=null,e},Ns=function(){try{us=new ActiveXObject("htmlfile")}catch(t){}var t,e;Ns="undefined"!=typeof document?document.domain&&us?Is(us):((e=rl("iframe")).style.display="none",ws.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Ms("document.F=Object")),t.close(),t.F):Is(us);for(var n=ys.length;n--;)delete Ns.prototype[ys[n]];return Ns()};hs[ks]=!0;var Hs,Os,js,Ds=Object.create||function(t,e){var n;return null!==t?(Ts.prototype=yl(t),n=new Ts,Ts.prototype=null,n[ks]=t):n=Ns(),void 0===e?n:xs(n,e)},Ls=ys.concat("length","prototype"),zs={f:Object.getOwnPropertyNames||function(t){return ms(t,Ls)}},As=Ba.Array,Ps=Math.max,Cs=zs.f,Vs="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Bs=function(t){try{return Cs(t)}catch(t){return function(t,e,n){for(var r=jl(t),o=fs(e,r),i=fs(void 0===n?r:n,r),u=As(Ps(i-o,0)),a=0;o<i;o++,a++)Dl(u,a,t[o]);return u.length=a,u}(Vs)}},Rs={f:function(t){return Vs&&"Window"==cc(t)?Bs(t):Cs(hc(t))}},Fs={f:Object.getOwnPropertySymbols},_s=Ya([].slice),qs=function(t,e,n,r){r&&r.enumerable?t[e]=n:El(t,e,n)},$s={f:Jc},Ws=wl.f,Gs=function(t){var e=gc.Symbol||(gc.Symbol={});Fc(e,t)||Ws(e,t,{value:$s.f(t)})},Us=zl?{}.toString:function(){return"[object "+Vl(this)+"]"},Xs=wl.f,Ys=Jc("toStringTag"),Js=function(t,e,n,r){if(t){var o=n?t:t.prototype;Fc(o,Ys)||Xs(o,Ys,{configurable:!0,value:e}),r&&!zl&&El(o,"toString",Us)}},Ks=Ba.WeakMap,Qs=Ja(Ks)&&/native code/.test(Rl(Ks)),Zs="Object already initialized",tf=Ba.TypeError,ef=Ba.WeakMap;if(Qs||Pc.state){var nf=Pc.state||(Pc.state=new ef),rf=Ya(nf.get),of=Ya(nf.has),uf=Ya(nf.set);Hs=function(t,e){if(of(nf,t))throw new tf(Zs);return e.facade=t,uf(nf,t,e),e},Os=function(t){return rf(nf,t)||{}},js=function(t){return of(nf,t)}}else{var af=Ss("state");hs[af]=!0,Hs=function(t,e){if(Fc(t,af))throw new tf(Zs);return e.facade=t,El(t,af,e),e},Os=function(t){return Fc(t,af)?t[af]:{}},js=function(t){return Fc(t,af)}}var cf={set:Hs,get:Os,has:js,enforce:function(t){return js(t)?Os(t):Hs(t,{})},getterFor:function(t){return function(e){var n;if(!vc(e)||(n=Os(e)).type!==t)throw tf("Incompatible receiver, "+t+" required");return n}}},lf=Ya([].push),sf=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,l,s,f){for(var d,p,h=Bc(c),v=fc(h),g=vl(l,s),m=jl(v),y=0,b=f||Kl,x=e?b(c,m):n||u?b(c,0):void 0;m>y;y++)if((a||y in v)&&(p=g(d=v[y],y,h),t))if(e)x[y]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return y;case 2:lf(x,d)}else switch(t){case 4:return!1;case 7:lf(x,d)}return i?-1:r||o?o:x}},ff={forEach:sf(0),map:sf(1),filter:sf(2),some:sf(3),every:sf(4),find:sf(5),findIndex:sf(6),filterReject:sf(7)}.forEach,df=Ss("hidden"),pf="Symbol",hf=Jc("toPrimitive"),vf=cf.set,gf=cf.getterFor(pf),mf=Object.prototype,yf=Ba.Symbol,bf=yf&&yf.prototype,xf=Ba.TypeError,wf=Ba.QObject,Ef=yc("JSON","stringify"),Sf=ul.f,kf=wl.f,Tf=Rs.f,Mf=oc.f,If=Ya([].push),Nf=Cc("symbols"),Hf=Cc("op-symbols"),Of=Cc("string-to-symbol-registry"),jf=Cc("symbol-to-string-registry"),Df=Cc("wks"),Lf=!wf||!wf.prototype||!wf.prototype.findChild,zf=Qa&&Ka((function(){return 7!=Ds(kf({},"a",{get:function(){return kf(this,"a",{value:7}).a}})).a}))?function(t,e,n){var r=Sf(mf,e);r&&delete mf[e],kf(t,e,n),r&&t!==mf&&kf(mf,e,r)}:kf,Af=function(t,e){var n=Nf[t]=Ds(bf);return vf(n,{type:pf,tag:t,description:e}),Qa||(n.description=e),n},Pf=function(t,e,n){t===mf&&Pf(Hf,e,n),yl(t);var r=tl(e);return yl(n),Fc(Nf,r)?(n.enumerable?(Fc(t,df)&&t[df][r]&&(t[df][r]=!1),n=Ds(n,{enumerable:ic(0,!1)})):(Fc(t,df)||kf(t,df,ic(1,{})),t[df][r]=!0),zf(t,r,n)):kf(t,r,n)},Cf=function(t,e){yl(t);var n=hc(e),r=bs(n).concat(Ff(n));return ff(r,(function(e){Qa&&!tc(Vf,n,e)||Pf(t,e,n[e])})),t},Vf=function(t){var e=tl(t),n=tc(Mf,this,e);return!(this===mf&&Fc(Nf,e)&&!Fc(Hf,e))&&(!(n||!Fc(this,e)||!Fc(Nf,e)||Fc(this,df)&&this[df][e])||n)},Bf=function(t,e){var n=hc(t),r=tl(e);if(n!==mf||!Fc(Nf,r)||Fc(Hf,r)){var o=Sf(n,r);return!o||!Fc(Nf,r)||Fc(n,df)&&n[df][r]||(o.enumerable=!0),o}},Rf=function(t){var e=Tf(hc(t)),n=[];return ff(e,(function(t){Fc(Nf,t)||Fc(hs,t)||If(n,t)})),n},Ff=function(t){var e=t===mf,n=Tf(e?Hf:hc(t)),r=[];return ff(n,(function(t){!Fc(Nf,t)||e&&!Fc(mf,t)||If(r,Nf[t])})),r};if(Mc||(yf=function(){if(bc(bf,this))throw xf("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?cs(arguments[0]):void 0,e=Wc(t),n=function(t){this===mf&&tc(n,Hf,t),Fc(this,df)&&Fc(this[df],e)&&(this[df][e]=!1),zf(this,e,ic(1,t))};return Qa&&Lf&&zf(mf,e,{configurable:!0,set:n}),Af(e,t)},bf=yf.prototype,qs(bf,"toString",(function(){return gf(this).tag})),qs(yf,"withoutSetter",(function(t){return Af(Wc(t),t)})),oc.f=Vf,wl.f=Pf,ul.f=Bf,zs.f=Rs.f=Rf,Fs.f=Ff,$s.f=function(t){return Af(Jc(t),t)},Qa&&kf(bf,"description",{configurable:!0,get:function(){return gf(this).description}})),Tl({global:!0,wrap:!0,forced:!Mc,sham:!Mc},{Symbol:yf}),ff(bs(Df),(function(t){Gs(t)})),Tl({target:pf,stat:!0,forced:!Mc},{for:function(t){var e=cs(t);if(Fc(Of,e))return Of[e];var n=yf(e);return Of[e]=n,jf[n]=e,n},keyFor:function(t){if(!Hc(t))throw xf(t+" is not a symbol");if(Fc(jf,t))return jf[t]},useSetter:function(){Lf=!0},useSimple:function(){Lf=!1}}),Tl({target:"Object",stat:!0,forced:!Mc,sham:!Qa},{create:function(t,e){return void 0===e?Ds(t):Cf(Ds(t),e)},defineProperty:Pf,defineProperties:Cf,getOwnPropertyDescriptor:Bf}),Tl({target:"Object",stat:!0,forced:!Mc},{getOwnPropertyNames:Rf,getOwnPropertySymbols:Ff}),Tl({target:"Object",stat:!0,forced:Ka((function(){Fs.f(1)}))},{getOwnPropertySymbols:function(t){return Fs.f(Bc(t))}}),Ef){var _f=!Mc||Ka((function(){var t=yf();return"[null]"!=Ef([t])||"{}"!=Ef({a:t})||"{}"!=Ef(Object(t))}));Tl({target:"JSON",stat:!0,forced:_f},{stringify:function(t,e,n){var r=_s(arguments),o=e;if((vc(e)||void 0!==t)&&!Hc(t))return Ml(e)||(e=function(t,e){if(Ja(o)&&(e=tc(o,this,t,e)),!Hc(e))return e}),r[1]=e,$a(Ef,null,r)}})}if(!bf[hf]){var qf=bf.valueOf;qs(bf,hf,(function(t){return tc(qf,this)}))}Js(yf,pf),hs[df]=!0,Gs("asyncIterator"),Gs("hasInstance"),Gs("isConcatSpreadable"),Gs("iterator"),Gs("match"),Gs("matchAll"),Gs("replace"),Gs("search"),Gs("species"),Gs("split"),Gs("toPrimitive"),Gs("toStringTag"),Gs("unscopables"),Js(Ba.JSON,"JSON",!0);var $f,Wf,Gf,Uf=gc.Symbol,Xf={},Yf=Function.prototype,Jf=Qa&&Object.getOwnPropertyDescriptor,Kf=Fc(Yf,"name"),Qf={EXISTS:Kf,PROPER:Kf&&"something"===function(){}.name,CONFIGURABLE:Kf&&(!Qa||Qa&&Jf(Yf,"name").configurable)},Zf=!Ka((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),td=Ss("IE_PROTO"),ed=Ba.Object,nd=ed.prototype,rd=Zf?ed.getPrototypeOf:function(t){var e=Bc(t);if(Fc(e,td))return e[td];var n=e.constructor;return Ja(n)&&e instanceof n?n.prototype:e instanceof ed?nd:null},od=Jc("iterator"),id=!1;[].keys&&("next"in(Gf=[].keys())?(Wf=rd(rd(Gf)))!==Object.prototype&&($f=Wf):id=!0);var ud=null==$f||Ka((function(){var t={};return $f[od].call(t)!==t}));$f=ud?{}:Ds($f),Ja($f[od])||qs($f,od,(function(){return this}));var ad={IteratorPrototype:$f,BUGGY_SAFARI_ITERATORS:id},cd=ad.IteratorPrototype,ld=function(){return this};Ba.String,Ba.TypeError;Object.setPrototypeOf||"__proto__"in{}&&function(){var t,e=!1,n={};try{(t=Ya(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),e=n instanceof Array}catch(t){}}();var sd=Qf.PROPER,fd=ad.BUGGY_SAFARI_ITERATORS,dd=Jc("iterator"),pd="keys",hd="values",vd="entries",gd=function(){return this},md=function(t,e,n,r,o,i,u){!function(t,e,n,r){var o=e+" Iterator";t.prototype=Ds(cd,{next:ic(+!r,n)}),Js(t,o,!1,!0),Xf[o]=ld}(n,e,r);var a,c,l,s=function(t){if(t===o&&v)return v;if(!fd&&t in p)return p[t];switch(t){case pd:case hd:case vd:return function(){return new n(this,t)}}return function(){return new n(this)}},f=e+" Iterator",d=!1,p=t.prototype,h=p[dd]||p["@@iterator"]||o&&p[o],v=!fd&&h||s(o),g="Array"==e&&p.entries||h;if(g&&(a=rd(g.call(new t)))!==Object.prototype&&a.next&&(Js(a,f,!0,!0),Xf[f]=gd),sd&&o==hd&&h&&h.name!==hd&&(d=!0,v=function(){return tc(h,this)}),o)if(c={values:s(hd),keys:i?v:s(pd),entries:s(vd)},u)for(l in c)(fd||d||!(l in p))&&qs(p,l,c[l]);else Tl({target:e,proto:!0,forced:fd||d},c);return u&&p[dd]!==v&&qs(p,dd,v,{name:o}),Xf[e]=v,c},yd="Array Iterator",bd=cf.set,xd=cf.getterFor(yd);md(Array,"Array",(function(t,e){bd(this,{type:yd,target:hc(t),index:0,kind:e})}),(function(){var t=xd(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values"),Xf.Arguments=Xf.Array;var wd=Jc("toStringTag");for(var Ed in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var Sd=Ba[Ed],kd=Sd&&Sd.prototype;kd&&Vl(kd)!==wd&&El(kd,wd,Ed),Xf[Ed]=Xf.Array}var Td=Uf;Gs("asyncDispose"),Gs("dispose"),Gs("matcher"),Gs("metadata"),Gs("observable"),Gs("patternMatch"),Gs("replaceAll");var Md=Td,Id=Ya("".charAt),Nd=Ya("".charCodeAt),Hd=Ya("".slice),Od=function(t){return function(e,n){var r,o,i=cs(pc(e)),u=Hl(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=Nd(i,u))<55296||r>56319||u+1===a||(o=Nd(i,u+1))<56320||o>57343?t?Id(i,u):r:t?Hd(i,u,u+2):o-56320+(r-55296<<10)+65536}},jd={codeAt:Od(!1),charAt:Od(!0)}.charAt,Dd="String Iterator",Ld=cf.set,zd=cf.getterFor(Dd);md(String,"String",(function(t){Ld(this,{type:Dd,string:cs(t),index:0})}),(function(){var t,e=zd(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=jd(n,r),e.index+=t.length,{value:t,done:!1})}));var Ad=$s.f("iterator"),Pd=h((function(t){function e(n){return"function"==typeof Md&&"symbol"==typeof Ad?(t.exports=e=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=e=function(t){return t&&"function"==typeof Md&&t.constructor===Md&&t!==Md.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),e(n)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0})),Cd=p(Pd),Vd=de.EXISTS,Bd=_t.f,Rd=Function.prototype,Fd=D(Rd.toString),_d=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,qd=D(_d.exec);x&&!Vd&&Bd(Rd,"name",{configurable:!0,get:function(){try{return qd(_d,Fd(this))[1]}catch(t){return""}}});var $d=function(){function t(){this.tag="select",this.width=80}return t.prototype.isActive=function(t){return!1},t.prototype.getValue=function(t){var e=this.mark,n=r.Editor.marks(t);return n&&n[e]?n[e]:""},t.prototype.isDisabled=function(t){return null==t.selection||(this.mark,!!xo(r.Editor.nodes(t,{match:function(n){return"pre"===e.DomEditor.getNodeType(n)||!!r.Editor.isVoid(t,n)},universal:!0}),1)[0])},t.prototype.exec=function(t,e){var n=this.mark;e?t.addMark(n,e):t.removeMark(n)},t}(),Wd=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("fontSize.title"),n.iconSvg=Jo,n.mark="fontSize",n}return vo(n,t),n.prototype.getOptions=function(t){var n=[],r=t.getMenuConfig(this.mark).fontSizeList,o=void 0===r?[]:r;n.push({text:e.t("fontSize.default"),value:""}),o.forEach((function(t){if("string"==typeof t)n.push({text:t,value:t});else if("object"===Cd(t)){var e=t.name,r=t.value;n.push({text:e,value:r})}}));var i=this.getValue(t);return n.forEach((function(t){t.value===i?t.selected=!0:delete t.selected})),n},n}($d),Gd=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("fontFamily.title"),n.iconSvg=Ko,n.mark="fontFamily",n.selectPanelWidth=150,n}return vo(n,t),n.prototype.getOptions=function(t){var n=[],r=t.getMenuConfig(this.mark).fontFamilyList,o=void 0===r?[]:r;n.push({text:e.t("fontFamily.default"),value:""}),o.forEach((function(t){if("string"==typeof t)n.push({text:t,value:t,styleForRenderMenuList:{"font-family":t}});else if("object"===Cd(t)){var e=t.name,r=t.value;n.push({text:e,value:r,styleForRenderMenuList:{"font-family":r}})}}));var i=this.getValue(t);return n.forEach((function(t){t.value===i?t.selected=!0:delete t.selected})),n},n}($d);var Ud={renderStyle:function(t,e){var n=t,r=n.fontSize,o=n.fontFamily,i=e;return r&&au(i,{fontSize:r}),o&&au(i,{fontFamily:o}),i},styleToHtml:function(t,e){if(!r.Text.isText(t))return e;var n,o=t,i=o.fontSize,u=o.fontFamily;return i||u?((lo(e)||"span"!==fo(n=c.default(e)))&&(n=c.default("<span>"+e+"</span>")),i&&n.css("font-size",i),u&&n.css("font-family",u),so(n)):e},preParseHtml:[za],parseStyleHtml:function(t,e,n){var o=c.default(t);if(!r.Text.isText(e))return e;var i=e,u=n.getMenuConfig("fontSize").fontSizeList,a=void 0===u?[]:u,l=po(o,"font-size"),s=a.find((function(t){return t.value&&t.value===l}))||a.includes(l);l&&s&&(i.fontSize=l);var f=n.getMenuConfig("fontFamily").fontFamilyList,d=void 0===f?[]:f,p=po(o,"font-family").replace(/"/g,""),h=d.find((function(t){return t.value&&t.value===p}))||d.includes(p);return p&&h&&(i.fontFamily=p),i},menus:[{key:"fontSize",factory:function(){return new Wd},config:{fontSizeList:["12px",{name:"13px",value:"13px"},"14px","15px","16px","19px",{name:"22px",value:"22px"},"24px","29px","32px","40px","48px"]}},{key:"fontFamily",factory:function(){return new Gd},config:{fontFamilyList:["黑体",{name:"仿宋",value:"仿宋"},"楷体","标楷体","华文仿宋","华文楷体",{name:"宋体",value:"宋体"},"微软雅黑","Arial","Tahoma","Verdana","Times New Roman","Courier New"]}}]};var Xd={selector:"p,h1,h2,h3,h4,h5",preParseHtml:function(t){var e=c.default(t),n=po(e,"padding-left");return/\dem/.test(n)&&e.css("text-indent","2em"),/\dpx/.test(n)&&parseInt(n,10)%32==0&&e.css("text-indent","2em"),e[0]}};var Yd=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){var e=xo(r.Editor.nodes(t,{match:function(t){return!!t.indent},universal:!0}),1),n=e[0];return null==n?"":xo(n,1)[0].indent||""},t.prototype.isActive=function(t){return!1},t.prototype.getMatchNode=function(t){var n=xo(r.Editor.nodes(t,{match:function(t){var n=e.DomEditor.getNodeType(t);return"paragraph"===n||!!n.startsWith("header")},universal:!0,mode:"highest"}),1)[0];return null==n?null:n[0]},t}(),Jd=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("indent.decrease"),n.iconSvg=Qo,n}return vo(n,t),n.prototype.isDisabled=function(t){var e=this.getMatchNode(t);return null==e||!e.indent},n.prototype.exec=function(t,e){r.Transforms.setNodes(t,{indent:null},{match:function(t){return r.Element.isElement(t)}})},n}(Yd),Kd=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("indent.increase"),n.iconSvg=Zo,n}return vo(n,t),n.prototype.isDisabled=function(t){var e=this.getMatchNode(t);return null==e||!!e.indent},n.prototype.exec=function(t,e){r.Transforms.setNodes(t,{indent:"2em"},{match:function(t){return r.Element.isElement(t)},mode:"highest"})},n}(Yd),Qd={renderStyle:function(t,e){if(!r.Element.isElement(t))return e;var n=t.indent,o=e;return n&&au(o,{textIndent:n}),o},styleToHtml:function(t,e){if(!r.Element.isElement(t))return e;var n=t.indent;if(!n)return e;var o=c.default(e);return o.css("text-indent",n),so(o)},preParseHtml:[Xd],parseStyleHtml:function(t,e,n){var o=c.default(t);if(!r.Element.isElement(e))return e;var i=e,u=po(o,"text-indent"),a=parseInt(u,10);return u&&a>0&&(i.indent=u),i},menus:[{key:"indent",factory:function(){return new Kd}},{key:"delIndent",factory:function(){return new Jd}}]};var Zd=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.getMatchNode=function(t){var n=xo(r.Editor.nodes(t,{match:function(t){var n=e.DomEditor.getNodeType(t);return"paragraph"===n||("blockquote"===n||!!n.startsWith("header"))},universal:!0,mode:"highest"}),1)[0];return null==n?null:n[0]},t.prototype.isDisabled=function(t){return null==t.selection||!!e.DomEditor.getSelectedElems(t).some((function(e){if(r.Editor.isVoid(t,e)&&r.Editor.isBlock(t,e))return!0;var n=e.type;return!!["pre","code"].includes(n)||void 0}))},t}(),tp=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("justify.left"),n.iconSvg=ti,n}return vo(n,t),n.prototype.exec=function(t,e){r.Transforms.setNodes(t,{textAlign:"left"},{match:function(e){return r.Element.isElement(e)&&!t.isInline(e)}})},n}(Zd),ep=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("justify.right"),n.iconSvg=ei,n}return vo(n,t),n.prototype.exec=function(t,e){r.Transforms.setNodes(t,{textAlign:"right"},{match:function(e){return r.Element.isElement(e)&&!t.isInline(e)}})},n}(Zd),np=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("justify.center"),n.iconSvg=ni,n}return vo(n,t),n.prototype.exec=function(t,e){r.Transforms.setNodes(t,{textAlign:"center"},{match:function(e){return r.Element.isElement(e)&&!t.isInline(e)}})},n}(Zd),rp=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.title=e.t("justify.justify"),n.iconSvg=ri,n}return vo(n,t),n.prototype.exec=function(t,e){r.Transforms.setNodes(t,{textAlign:"justify"},{match:function(e){return r.Element.isElement(e)&&!t.isInline(e)}})},n}(Zd),op={renderStyle:function(t,e){if(!r.Element.isElement(t))return e;var n=t.textAlign,o=e;return n&&au(o,{textAlign:n}),o},styleToHtml:function(t,e){if(!r.Element.isElement(t))return e;var n=t.textAlign;if(!n)return e;var o=c.default(e);return o.css("text-align",n),so(o)},parseStyleHtml:function(t,e,n){var o=c.default(t);if(!r.Element.isElement(e))return e;var i=e,u=po(o,"text-align");return u&&(i.textAlign=u),i},menus:[{key:"justifyLeft",factory:function(){return new tp}},{key:"justifyRight",factory:function(){return new ep}},{key:"justifyCenter",factory:function(){return new np}},{key:"justifyJustify",factory:function(){return new rp}}]};var ip=function(){function t(){this.title=e.t("lineHeight.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M964 788a8 8 0 0 1 8 8v98a8 8 0 0 1-8 8H438a8 8 0 0 1-8-8v-98a8 8 0 0 1 8-8h526zM198.93 144.306c6.668-5.798 16.774-5.094 22.573 1.574l122.26 140.582a16 16 0 0 1 3.927 10.5c0 8.836-7.164 16-16 16h-61.8a8 8 0 0 0-8 8v390.077h69.819a16 16 0 0 1 10.502 3.928c6.666 5.8 7.37 15.906 1.57 22.573L221.476 878.123a16 16 0 0 1-1.57 1.57c-6.668 5.8-16.774 5.097-22.574-1.57L75.051 737.538a16 16 0 0 1-3.928-10.5c0-8.837 7.163-16 16-16h69.822V312.96H87.127a16 16 0 0 1-10.502-3.928c-6.666-5.8-7.37-15.906-1.57-22.573l122.303-140.582a16 16 0 0 1 1.572-1.572zM964 465a8 8 0 0 1 8 8v98a8 8 0 0 1-8 8H438a8 8 0 0 1-8-8v-98a8 8 0 0 1 8-8h526z m0-323a8 8 0 0 1 8 8v98a8 8 0 0 1-8 8H438a8 8 0 0 1-8-8v-98a8 8 0 0 1 8-8h526z"></path></svg>',this.tag="select",this.width=80}return t.prototype.getOptions=function(t){var n=[],r=t.getMenuConfig("lineHeight").lineHeightList,o=void 0===r?[]:r;n.push({text:e.t("lineHeight.default"),value:""}),o.forEach((function(t){n.push({text:t,value:t})}));var i=this.getValue(t);return n.forEach((function(t){t.value===i?t.selected=!0:delete t.selected})),n},t.prototype.getMatchNode=function(t){var n=xo(r.Editor.nodes(t,{match:function(t){var n=e.DomEditor.getNodeType(t);return!!n.startsWith("header")||!!["paragraph","blockquote","list-item"].includes(n)},universal:!0,mode:"highest"}),1)[0];return null==n?null:n[0]},t.prototype.isActive=function(t){return!1},t.prototype.getValue=function(t){var e=this.getMatchNode(t);return null==e?"":r.Element.isElement(e)&&e.lineHeight||""},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getMatchNode(t)},t.prototype.exec=function(t,e){r.Transforms.setNodes(t,{lineHeight:e.toString()},{mode:"highest"})},t}();var up={renderStyle:function(t,e){if(!r.Element.isElement(t))return e;var n=t.lineHeight,o=e;return n&&au(o,{lineHeight:n}),o},styleToHtml:function(t,e){if(!r.Element.isElement(t))return e;var n=t.lineHeight;if(!n)return e;var o=c.default(e);return o.css("line-height",n),so(o)},parseStyleHtml:function(t,e,n){var o=c.default(t);if(!r.Element.isElement(e))return e;var i=e,u=n.getMenuConfig("lineHeight").lineHeightList,a=void 0===u?[]:u,l=po(o,"line-height");return l&&a.includes(l)&&(i.lineHeight=l),i},menus:[{key:"lineHeight",factory:function(){return new ip},config:{lineHeightList:["1","1.15","1.5","2","2.5","3"]}}]},ap=function(){function t(){this.title=e.t("undo.redo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M0.00032 576a510.72 510.72 0 0 0 173.344 384l84.672-96A383.136 383.136 0 0 1 128.00032 576C128.00032 363.936 299.93632 192 512.00032 192c106.048 0 202.048 42.976 271.52 112.48L640.00032 448h384V64l-149.984 149.984A510.272 510.272 0 0 0 512.00032 64C229.21632 64 0.00032 293.216 0.00032 576z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection},t.prototype.exec=function(t,e){"function"==typeof t.redo&&t.redo()},t}(),cp=function(){function t(){this.title=e.t("undo.undo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M512 64A510.272 510.272 0 0 0 149.984 213.984L0.032 64v384h384L240.512 304.48A382.784 382.784 0 0 1 512.032 192c212.064 0 384 171.936 384 384 0 114.688-50.304 217.632-130.016 288l84.672 96a510.72 510.72 0 0 0 173.344-384c0-282.784-229.216-512-512-512z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection},t.prototype.exec=function(t,e){"function"==typeof t.undo&&t.undo()},t}(),lp={menus:[{key:"redo",factory:function(){return new ap}},{key:"undo",factory:function(){return new cp}}]};var sp={type:"divider",renderElem:function(t,r,o){var i=e.DomEditor.isNodeSelected(o,t);return n.h("div",{props:{contentEditable:!1,className:"w-e-textarea-divider"},dataset:{selected:i?"true":""},style:{},on:{mousedown:function(t){return t.preventDefault()}}},[n.h("hr")])}};var fp={type:"divider",elemToHtml:function(t,e){return"<hr/>"}};var dp={selector:"hr:not([data-w-e-type])",parseElemHtml:function(t,e,n){return{type:"divider",children:[{text:""}]}}},pp=function(){function t(){this.title=e.t("divider.title"),this.iconSvg='<svg viewBox="0 0 1092 1024"><path d="M0 51.2m51.2 0l989.866667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-989.866667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M0 460.8m51.2 0l170.666667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-170.666667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M819.2 460.8m51.2 0l170.666667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-170.666667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M409.6 460.8m51.2 0l170.666667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-170.666667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M0 870.4m51.2 0l989.866667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-989.866667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||!!e.DomEditor.getSelectedElems(t).some((function(n){if(t.isVoid(n))return!0;var r=e.DomEditor.getNodeType(n);return"table"===r||("pre"===r||void 0)}))},t.prototype.exec=function(t,e){r.Transforms.insertNodes(t,{type:"divider",children:[{text:""}]},{mode:"highest"})},t}(),hp={renderElems:[sp],elemsToHtml:[fp],parseElemsHtml:[dp],menus:[{key:"divider",factory:function(){return new pp}}],editorPlugin:function(t){var n=t.isVoid,o=t.normalizeNode,i=t;return i.isVoid=function(t){return"divider"===t.type||n(t)},i.normalizeNode=function(t){var n=xo(t,2),u=n[0],a=n[1];if("divider"!==e.DomEditor.getNodeType(u))return o([u,a]);e.DomEditor.isLastNode(i,u)&&r.Transforms.insertNodes(i,e.DomEditor.genEmptyParagraph(),{at:[a[0]+1]})},i}},vp=dn.map;qe({target:"Array",proto:!0,forced:!hn("map")},{map:function(t){return vp(this,t,arguments.length>1?arguments[1]:void 0)}});var gp=D([].join),mp=V!=Object,yp=Lo("join",",");qe({target:"Array",proto:!0,forced:mp||!yp},{join:function(t){return gp(F(this),void 0===t?",":t)}});var bp=function(){function t(){this.title=e.t("codeBlock.title"),this.iconSvg='<svg viewBox="0 0 1280 1024"><path d="M832 736l96 96 320-320L928 192l-96 96 224 224zM448 288l-96-96L32 512l320 320 96-96-224-224zM701.312 150.528l69.472 18.944-192 704.032-69.472-18.944 192-704.032z"></path></svg>',this.tag="button"}return t.prototype.getSelectCodeElem=function(t){var n=e.DomEditor.getSelectedNodeByType(t,"code");if(null==n)return null;var r=e.DomEditor.getParentNode(t,n);return null==r||"pre"!==e.DomEditor.getNodeType(r)?null:n},t.prototype.getValue=function(t){var e=this.getSelectCodeElem(t);return null==e?"":e.language||""},t.prototype.isActive=function(t){return!!this.getSelectCodeElem(t)},t.prototype.isDisabled=function(t){if(null==t.selection)return!0;var n=e.DomEditor.getSelectedElems(t);return!!n.some((function(e){return t.isVoid(e)}))||!n.some((function(t){var n=e.DomEditor.getNodeType(t);if("pre"===n||"paragraph"===n)return!0}))},t.prototype.exec=function(t,e){this.isActive(t)?this.changeToPlainText(t):this.changeToCodeBlock(t,e.toString())},t.prototype.changeToPlainText=function(t){var e=this.getSelectCodeElem(t);if(null!=e){var n=r.Node.string(e);r.Transforms.removeNodes(t,{mode:"highest"});var o=n.split("\n").map((function(t){return{type:"paragraph",children:[{text:t}]}}));r.Transforms.insertNodes(t,o,{mode:"highest"})}},t.prototype.changeToCodeBlock=function(t,e){var n,o,i=[],u=r.Editor.nodes(t,{match:function(e){return t.children.includes(e)},universal:!0});try{for(var a=bo(u),c=a.next();!c.done;c=a.next()){var l=xo(c.value,1)[0];l&&i.push(r.Node.string(l))}}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}r.Transforms.removeNodes(t,{mode:"highest"});var s={type:"pre",children:[{type:"code",language:e,children:[{text:i.join("\n")}]}]};r.Transforms.insertNodes(t,s,{mode:"highest"})},t}(),xp={key:"codeBlock",factory:function(){return new bp}};qe({target:"String",proto:!0,forced:Mo("anchor")},{anchor:function(t){return To(this,"a","name",t)}}),ir("match",(function(t,e,n){return[function(e){var n=R(this),r=null==e?void 0:at(e,t);return r?E(r,e,n):new RegExp(e)[t](bn(n))},function(t){var r=Bt(this),o=bn(t),i=n(e,r,o);if(i.done)return i.value;if(!r.global)return xr(r,o);var u=r.unicode;r.lastIndex=0;for(var a,c=[],l=0;null!==(a=xr(r,o));){var s=bn(a[0]);c[l]=s,""===s&&(r.lastIndex=fr(o,we(r.lastIndex),u)),l++}return 0===l?null:c}]}));var wp={menus:[xp],editorPlugin:function(t){var n=t.insertBreak,o=t.normalizeNode,i=t.insertData;t.insertNode;var u=t;return u.insertBreak=function(){var t=e.DomEditor.getSelectedNodeByType(u,"code");if(null!=t){var o=function(t,e){var n=e.selection;if(null==n)return"";var o=r.Node.string(t),i=n.anchor.offset,u=o.slice(0,i).split("\n"),a=u.length;return 0===a?"":u[a-1]}(t,u);if(o){var i=o.match(/^\s+/);if(null!=i&&null!=i[0]){var a=i[0];return void u.insertText("\n"+a)}}u.insertText("\n")}else n()},u.normalizeNode=function(t){var n=xo(t,2),i=n[0],a=n[1],c=e.DomEditor.getNodeType(i);("code"===c&&a.length<=1&&r.Transforms.setNodes(u,{type:"paragraph"},{at:a}),"pre"===c)&&(e.DomEditor.isLastNode(u,i)&&r.Transforms.insertNodes(u,e.DomEditor.genEmptyParagraph(),{at:[a[0]+1]}),"code"!==e.DomEditor.getNodeType(i.children[0])&&(r.Transforms.unwrapNodes(u),r.Transforms.setNodes(u,{type:"paragraph"},{mode:"highest"})));return o([i,a])},u.insertData=function(t){if(null!=e.DomEditor.getSelectedNodeByType(u,"code")){var n=t.getData("text/plain");r.Editor.insertText(u,n)}else i(t)},u},renderElems:[{type:"pre",renderElem:function(t,e,r){return n.jsx("pre",null,e)}},{type:"code",renderElem:function(t,e,r){return n.jsx("code",null,e)}}],elemsToHtml:[{type:"code",elemToHtml:function(t,e){return"<code>"+e+"</code>"}},{type:"pre",elemToHtml:function(t,e){return"<pre>"+e+"</pre>"}}],preParseHtml:[{selector:"pre>code",preParseHtml:function(t){var e=c.default(t);if("code"!==fo(e))return t;var n=e.find("xmp");if(0===n.length)return t;var r=n.text();return n.remove(),e.text(r),e[0]}}],parseElemsHtml:[{selector:"pre:not([data-w-e-type])>code",parseElemHtml:function(t,e,n){return{type:"code",language:"",children:[{text:c.default(t)[0].textContent||""}]}}},{selector:"pre:not([data-w-e-type])",parseElemHtml:function(t,n,r){var o=c.default(t);return 0===(n=n.filter((function(t){return"code"===e.DomEditor.getNodeType(t)}))).length&&(n=[{type:"code",language:"",children:[{text:o[0].textContent||""}]}]),{type:"pre",children:n.filter((function(t){return"code"===e.DomEditor.getNodeType(t)}))}}}]},Ep=function(){function t(){this.title=e.t("fullScreen.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M133.705143 335.433143V133.851429h201.581714a29.622857 29.622857 0 0 0 29.622857-29.549715V68.754286a29.622857 29.622857 0 0 0-29.622857-29.622857H61.732571A22.893714 22.893714 0 0 0 38.765714 62.025143V335.725714c0 16.310857 13.238857 29.622857 29.622857 29.622857h35.547429a29.842286 29.842286 0 0 0 29.696-29.842285zM690.980571 133.851429h201.581715v201.654857c0 16.310857 13.238857 29.549714 29.622857 29.549714h35.547428a29.622857 29.622857 0 0 0 29.549715-29.549714V61.952a22.893714 22.893714 0 0 0-22.820572-22.893714h-273.554285a29.622857 29.622857 0 0 0-29.549715 29.622857v35.547428c0 16.310857 13.238857 29.696 29.622857 29.696zM335.286857 892.781714H133.705143V691.2a29.622857 29.622857 0 0 0-29.622857-29.622857H68.534857a29.622857 29.622857 0 0 0-29.549714 29.622857v273.554286c0 12.653714 10.24 22.893714 22.820571 22.893714h273.554286a29.622857 29.622857 0 0 0 29.696-29.622857v-35.547429a29.769143 29.769143 0 0 0-29.769143-29.696z m557.348572-201.581714v201.581714H690.907429a29.622857 29.622857 0 0 0-29.622858 29.622857v35.547429c0 16.310857 13.238857 29.622857 29.622858 29.622857h273.554285c12.580571 0 22.893714-10.313143 22.893715-22.893714V691.2a29.622857 29.622857 0 0 0-29.622858-29.622857h-35.547428a29.622857 29.622857 0 0 0-29.696 29.622857z"></path></svg>',this.tag="button",this.alwaysEnable=!0}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return t.isFullScreen},t.prototype.isDisabled=function(t){return!1},t.prototype.exec=function(t,e){t.isFullScreen?t.unFullScreen():t.fullScreen()},t}(),Sp={menus:[{key:"fullScreen",factory:function(){return new Ep}}]},kp=function(){function t(){this.title=e.t("common.enter"),this.iconSvg='<svg viewBox="0 0 1255 1024"><path d="M1095.111111 731.477333h-625.777778V1024L0 658.318222 469.333333 292.408889v292.636444h625.777778V0h156.444445v731.477333z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){var e=t.selection;return null==e||!!r.Range.isExpanded(e)},t.prototype.exec=function(t,e){var n=t.selection;if(null!=n){var o=[n.anchor.path[0]];r.Transforms.insertNodes(t,{type:"paragraph",children:[{text:""}]},{at:o}),t.select(r.Editor.start(t,o))}},t}(),Tp=[hi,pu,Ud,Qd,op,up,ga,hp,Da,Pu,wp,Oa,nu,Eo,ba,lp,Sp,{menus:[{key:"enter",factory:function(){return new kp}}]}];t.default=Tp,t.insertImageNode=na,t.isInsertImageMenuDisabled=oa,t.updateImageNode=ra,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=index.js.map
