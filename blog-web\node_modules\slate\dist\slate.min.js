!function(e,u){"object"==typeof exports&&"undefined"!=typeof module?u(exports):"function"==typeof define&&define.amd?define(["exports"],u):u((e="undefined"!=typeof globalThis?globalThis:e||self).Slate={})}(this,(function(e){"use strict";function u(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,u){return e(u={exports:{}},u.exports),u.exports}var r=t((function(e){e.exports=function(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r},e.exports.default=e.exports,e.exports.__esModule=!0}));u(r);var n=t((function(e){e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.default=e.exports,e.exports.__esModule=!0}));u(n);var o=t((function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.default=e.exports,e.exports.__esModule=!0}));u(o);var i=t((function(e){e.exports=function(e,u){if(e){if("string"==typeof e)return r(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?r(e,u):void 0}},e.exports.default=e.exports,e.exports.__esModule=!0}));u(i);var a=t((function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.default=e.exports,e.exports.__esModule=!0}));u(a);var s=u(t((function(e){e.exports=function(e){return n(e)||o(e)||i(e)||a()},e.exports.default=e.exports,e.exports.__esModule=!0}))),c=t((function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.default=e.exports,e.exports.__esModule=!0}));u(c);var f=t((function(e){e.exports=function(e,u){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,n,o=[],i=!0,a=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(o.push(r.value),!u||o.length!==u);i=!0);}catch(e){a=!0,n=e}finally{try{i||null==t.return||t.return()}finally{if(a)throw n}}return o}},e.exports.default=e.exports,e.exports.__esModule=!0}));u(f);var l=t((function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.default=e.exports,e.exports.__esModule=!0}));u(l);var D=u(t((function(e){e.exports=function(e,u){return c(e)||f(e,u)||i(e,u)||l()},e.exports.default=e.exports,e.exports.__esModule=!0}))),C=u(t((function(e){e.exports=function(e,u,t){return u in e?Object.defineProperty(e,u,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[u]=t,e},e.exports.default=e.exports,e.exports.__esModule=!0}))),d=new WeakMap,h=new WeakMap,v=new WeakMap,p=new WeakMap,B=new WeakMap,A=new WeakMap,F=new WeakMap;function E(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function g(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?E(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):E(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}function y(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return m(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return m(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function m(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var b=function(e){switch(e.type){case"insert_text":case"remove_text":case"set_node":var u=e.path;return bu.levels(u);case"insert_node":var t=e.node,r=e.path,n=bu.levels(r),o=Vu.isText(t)?[]:Array.from(Eu.nodes(t),(function(e){var u=D(e,2)[1];return r.concat(u)}));return[].concat(s(n),s(o));case"merge_node":var i=e.path,a=bu.ancestors(i),c=bu.previous(i);return[].concat(s(a),[c]);case"move_node":var f=e.path,l=e.newPath;if(bu.equals(f,l))return[];var C,d=[],h=[],v=y(bu.ancestors(f));try{for(v.s();!(C=v.n()).done;){var p=C.value,B=bu.transform(p,e);d.push(B)}}catch(e){v.e(e)}finally{v.f()}var A,F=y(bu.ancestors(l));try{for(F.s();!(A=F.n()).done;){var E=A.value,g=bu.transform(E,e);h.push(g)}}catch(e){F.e(e)}finally{F.f()}var m=h[h.length-1],b=l[l.length-1],x=m.concat(b);return[].concat(d,h,[x]);case"remove_node":var w=e.path,O=bu.ancestors(w);return s(O);case"split_node":var P=e.path,k=bu.levels(P),j=bu.next(P);return[].concat(s(k),[j]);default:return[]}},x=t((function(e){e.exports=function(e,u){if(null==e)return{};var t,r,n={},o=Object.keys(e);for(r=0;r<o.length;r++)t=o[r],u.indexOf(t)>=0||(n[t]=e[t]);return n},e.exports.default=e.exports,e.exports.__esModule=!0}));u(x);var w=u(t((function(e){e.exports=function(e,u){if(null==e)return{};var t,r,n=x(e,u);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)t=o[r],u.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n},e.exports.default=e.exports,e.exports.__esModule=!0}))),O=t((function(e){var u=function(e){var u,t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function s(e,u,t){return Object.defineProperty(e,u,{value:t,enumerable:!0,configurable:!0,writable:!0}),e[u]}try{s({},"")}catch(e){s=function(e,u,t){return e[u]=t}}function c(e,u,t,r){var n=u&&u.prototype instanceof v?u:v,o=Object.create(n.prototype),i=new O(r||[]);return o._invoke=function(e,u,t){var r=l;return function(n,o){if(r===C)throw new Error("Generator is already running");if(r===d){if("throw"===n)throw o;return k()}for(t.method=n,t.arg=o;;){var i=t.delegate;if(i){var a=b(i,t);if(a){if(a===h)continue;return a}}if("next"===t.method)t.sent=t._sent=t.arg;else if("throw"===t.method){if(r===l)throw r=d,t.arg;t.dispatchException(t.arg)}else"return"===t.method&&t.abrupt("return",t.arg);r=C;var s=f(e,u,t);if("normal"===s.type){if(r=t.done?d:D,s.arg===h)continue;return{value:s.arg,done:t.done}}"throw"===s.type&&(r=d,t.method="throw",t.arg=s.arg)}}}(e,t,i),o}function f(e,u,t){try{return{type:"normal",arg:e.call(u,t)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var l="suspendedStart",D="suspendedYield",C="executing",d="completed",h={};function v(){}function p(){}function B(){}var A={};s(A,o,(function(){return this}));var F=Object.getPrototypeOf,E=F&&F(F(P([])));E&&E!==t&&r.call(E,o)&&(A=E);var g=B.prototype=v.prototype=Object.create(A);function y(e){["next","throw","return"].forEach((function(u){s(e,u,(function(e){return this._invoke(u,e)}))}))}function m(e,u){function t(n,o,i,a){var s=f(e[n],e,o);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==typeof l&&r.call(l,"__await")?u.resolve(l.__await).then((function(e){t("next",e,i,a)}),(function(e){t("throw",e,i,a)})):u.resolve(l).then((function(e){c.value=e,i(c)}),(function(e){return t("throw",e,i,a)}))}a(s.arg)}var n;this._invoke=function(e,r){function o(){return new u((function(u,n){t(e,r,u,n)}))}return n=n?n.then(o,o):o()}}function b(e,t){var r=e.iterator[t.method];if(r===u){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=u,b(e,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=f(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,h;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=u),t.delegate=null,h):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function x(e){var u={tryLoc:e[0]};1 in e&&(u.catchLoc=e[1]),2 in e&&(u.finallyLoc=e[2],u.afterLoc=e[3]),this.tryEntries.push(u)}function w(e){var u=e.completion||{};u.type="normal",delete u.arg,e.completion=u}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function P(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=u,t.done=!0,t};return i.next=i}}return{next:k}}function k(){return{value:u,done:!0}}return p.prototype=B,s(g,"constructor",B),s(B,"constructor",p),p.displayName=s(B,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var u="function"==typeof e&&e.constructor;return!!u&&(u===p||"GeneratorFunction"===(u.displayName||u.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,B):(e.__proto__=B,s(e,a,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},y(m.prototype),s(m.prototype,i,(function(){return this})),e.AsyncIterator=m,e.async=function(u,t,r,n,o){void 0===o&&(o=Promise);var i=new m(c(u,t,r,n),o);return e.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},y(g),s(g,a,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var u=[];for(var t in e)u.push(t);return u.reverse(),function t(){for(;u.length;){var r=u.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=P,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(w),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=u)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=u),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,u){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=u&&u<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=u,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(i)},complete:function(e,u){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&u&&(this.next=u),h},finish:function(e){for(var u=this.tryEntries.length-1;u>=0;--u){var t=this.tryEntries[u];if(t.finallyLoc===e)return this.complete(t.completion,t.afterLoc),w(t),h}},catch:function(e){for(var u=this.tryEntries.length-1;u>=0;--u){var t=this.tryEntries[u];if(t.tryLoc===e){var r=t.completion;if("throw"===r.type){var n=r.arg;w(t)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:P(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=u),h}},e}(e.exports);try{regeneratorRuntime=u}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=u:Function("r","regeneratorRuntime = r")(u)}}));
/*!
	 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
	 *
	 * Copyright (c) 2014-2017, Jon Schlinkert.
	 * Released under the MIT License.
	 */
function P(e){return"[object Object]"===Object.prototype.toString.call(e)}function k(e){var u,t;return!1!==P(e)&&(void 0===(u=e.constructor)||!1!==P(t=u.prototype)&&!1!==t.hasOwnProperty("isPrototypeOf"))}function j(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return S(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return S(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function S(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var _,N=function(e){var u,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=!t,n=t?z(e):e,o=_.None,i=_.None,a=0,s=null,c=j(n);try{for(c.s();!(u=c.n()).done;){var f=u.value,l=f.codePointAt(0);if(!l)break;var C=Q(f,l),d=r?[i,C]:[C,o],h=D(d,2);if(o=h[0],i=h[1],ee(o,_.ZWJ)&&ee(i,_.ExtPict)&&!ne(r?e.substring(0,a):e.substring(0,e.length-a)))break;if(ee(o,_.RI)&&ee(i,_.RI)&&!(s=null!==s?!s:!!r||ie(e.substring(0,e.length-a))))break;if(o!==_.None&&i!==_.None&&te(o,i))break;a+=f.length}}catch(e){c.e(e)}finally{c.f()}return a||1},T=/\s/,R=/[\u0021-\u0023\u0025-\u002A\u002C-\u002F\u003A\u003B\u003F\u0040\u005B-\u005D\u005F\u007B\u007D\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u0AF0\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E3B\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/,I=/['\u2018\u2019]/,L=function(e){for(var u=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=0,r=!1;e.length>0;){var n=N(e,u),o=M(e,n,u),i=D(o,2),a=i[0],s=i[1];if(q(a,s,u))r=!0,t+=n;else{if(r)break;t+=n}e=s}return t},M=function(e,u,t){if(t){var r=e.length-u;return[e.slice(r,e.length),e.slice(0,r)]}return[e.slice(0,u),e.slice(u)]},q=function e(u,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(T.test(u))return!1;if(I.test(u)){var n=N(t,r),o=M(t,n,r),i=D(o,2),a=i[0],s=i[1];if(e(a,s,r))return!0}return!R.test(u)},z=O.mark((function e(u){var t,r,n,o;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=u.length-1,r=0;case 2:if(!(r<u.length)){e.next=16;break}if(n=u.charAt(t-r),!W(n.charCodeAt(0))){e.next=11;break}if(o=u.charAt(t-r-1),!V(o.charCodeAt(0))){e.next=11;break}return e.next=9,o+n;case 9:return r++,e.abrupt("continue",13);case 11:return e.next=13,n;case 13:r++,e.next=2;break;case 16:case"end":return e.stop()}}),e)})),V=function(e){return e>=55296&&e<=56319},W=function(e){return e>=56320&&e<=57343};!function(e){e[e.None=0]="None",e[e.Extend=1]="Extend",e[e.ZWJ=2]="ZWJ",e[e.RI=4]="RI",e[e.Prepend=8]="Prepend",e[e.SpacingMark=16]="SpacingMark",e[e.L=32]="L",e[e.V=64]="V",e[e.T=128]="T",e[e.LV=256]="LV",e[e.LVT=512]="LVT",e[e.ExtPict=1024]="ExtPict",e[e.Any=2048]="Any"}(_||(_={}));var $=/^(?:[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09BE\u09C1-\u09C4\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3E\u0B3F\u0B41-\u0B44\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B82\u0BBE\u0BC0\u0BCD\u0BD7\u0C00\u0C04\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CBF\u0CC2\u0CC6\u0CCC\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D3E\u0D41-\u0D44\u0D4D\u0D57\u0D62\u0D63\u0D81\u0DCA\u0DCF\u0DD2-\u0DD4\u0DD6\u0DDF\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1AC0\u1B00-\u1B03\u1B34-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u200C\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFF9E\uFF9F]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD803[\uDD24-\uDD27\uDEAB\uDEAC\uDF46-\uDF50]|\uD804[\uDC01\uDC38-\uDC46\uDC7F-\uDC81\uDCB3-\uDCB6\uDCB9\uDCBA\uDD00-\uDD02\uDD27-\uDD2B\uDD2D-\uDD34\uDD73\uDD80\uDD81\uDDB6-\uDDBE\uDDC9-\uDDCC\uDDCF\uDE2F-\uDE31\uDE34\uDE36\uDE37\uDE3E\uDEDF\uDEE3-\uDEEA\uDF00\uDF01\uDF3B\uDF3C\uDF3E\uDF40\uDF57\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC38-\uDC3F\uDC42-\uDC44\uDC46\uDC5E\uDCB0\uDCB3-\uDCB8\uDCBA\uDCBD\uDCBF\uDCC0\uDCC2\uDCC3\uDDAF\uDDB2-\uDDB5\uDDBC\uDDBD\uDDBF\uDDC0\uDDDC\uDDDD\uDE33-\uDE3A\uDE3D\uDE3F\uDE40\uDEAB\uDEAD\uDEB0-\uDEB5\uDEB7\uDF1D-\uDF1F\uDF22-\uDF25\uDF27-\uDF2B]|\uD806[\uDC2F-\uDC37\uDC39\uDC3A\uDD30\uDD3B\uDD3C\uDD3E\uDD43\uDDD4-\uDDD7\uDDDA\uDDDB\uDDE0\uDE01-\uDE0A\uDE33-\uDE38\uDE3B-\uDE3E\uDE47\uDE51-\uDE56\uDE59-\uDE5B\uDE8A-\uDE96\uDE98\uDE99]|\uD807[\uDC30-\uDC36\uDC38-\uDC3D\uDC3F\uDC92-\uDCA7\uDCAA-\uDCB0\uDCB2\uDCB3\uDCB5\uDCB6\uDD31-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD45\uDD47\uDD90\uDD91\uDD95\uDD97\uDEF3\uDEF4]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF4F\uDF8F-\uDF92\uDFE4]|\uD82F[\uDC9D\uDC9E]|\uD834[\uDD65\uDD67-\uDD69\uDD6E-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDD30-\uDD36\uDEEC-\uDEEF]|\uD83A[\uDCD0-\uDCD6\uDD44-\uDD4A]|\uD83C[\uDFFB-\uDFFF]|\uDB40[\uDC20-\uDC7F\uDD00-\uDDEF])$/,J=/^(?:[\u0600-\u0605\u06DD\u070F\u0890\u0891\u08E2\u0D4E]|\uD804[\uDCBD\uDCCD\uDDC2\uDDC3]|\uD806[\uDD3F\uDD41\uDE3A\uDE84-\uDE89]|\uD807\uDD46)$/,U=/^(?:[\u0903\u093B\u093E-\u0940\u0949-\u094C\u094E\u094F\u0982\u0983\u09BF\u09C0\u09C7\u09C8\u09CB\u09CC\u0A03\u0A3E-\u0A40\u0A83\u0ABE-\u0AC0\u0AC9\u0ACB\u0ACC\u0B02\u0B03\u0B40\u0B47\u0B48\u0B4B\u0B4C\u0BBF\u0BC1\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCC\u0C01-\u0C03\u0C41-\u0C44\u0C82\u0C83\u0CBE\u0CC0\u0CC1\u0CC3\u0CC4\u0CC7\u0CC8\u0CCA\u0CCB\u0D02\u0D03\u0D3F\u0D40\u0D46-\u0D48\u0D4A-\u0D4C\u0D82\u0D83\u0DD0\u0DD1\u0DD8-\u0DDE\u0DF2\u0DF3\u0E33\u0EB3\u0F3E\u0F3F\u0F7F\u1031\u103B\u103C\u1056\u1057\u1084\u1715\u1734\u17B6\u17BE-\u17C5\u17C7\u17C8\u1923-\u1926\u1929-\u192B\u1930\u1931\u1933-\u1938\u1A19\u1A1A\u1A55\u1A57\u1A6D-\u1A72\u1B04\u1B3B\u1B3D-\u1B41\u1B43\u1B44\u1B82\u1BA1\u1BA6\u1BA7\u1BAA\u1BE7\u1BEA-\u1BEC\u1BEE\u1BF2\u1BF3\u1C24-\u1C2B\u1C34\u1C35\u1CE1\u1CF7\uA823\uA824\uA827\uA880\uA881\uA8B4-\uA8C3\uA952\uA953\uA983\uA9B4\uA9B5\uA9BA\uA9BB\uA9BE-\uA9C0\uAA2F\uAA30\uAA33\uAA34\uAA4D\uAAEB\uAAEE\uAAEF\uAAF5\uABE3\uABE4\uABE6\uABE7\uABE9\uABEA\uABEC]|\uD804[\uDC00\uDC02\uDC82\uDCB0-\uDCB2\uDCB7\uDCB8\uDD2C\uDD45\uDD46\uDD82\uDDB3-\uDDB5\uDDBF\uDDC0\uDDCE\uDE2C-\uDE2E\uDE32\uDE33\uDE35\uDEE0-\uDEE2\uDF02\uDF03\uDF3F\uDF41-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF62\uDF63]|\uD805[\uDC35-\uDC37\uDC40\uDC41\uDC45\uDCB1\uDCB2\uDCB9\uDCBB\uDCBC\uDCBE\uDCC1\uDDB0\uDDB1\uDDB8-\uDDBB\uDDBE\uDE30-\uDE32\uDE3B\uDE3C\uDE3E\uDEAC\uDEAE\uDEAF\uDEB6\uDF26]|\uD806[\uDC2C-\uDC2E\uDC38\uDD31-\uDD35\uDD37\uDD38\uDD3D\uDD40\uDD42\uDDD1-\uDDD3\uDDDC-\uDDDF\uDDE4\uDE39\uDE57\uDE58\uDE97]|\uD807[\uDC2F\uDC3E\uDCA9\uDCB1\uDCB4\uDD8A-\uDD8E\uDD93\uDD94\uDD96\uDEF5\uDEF6]|\uD81B[\uDF51-\uDF87\uDFF0\uDFF1]|\uD834[\uDD66\uDD6D])$/,G=/^[\u1100-\u115F\uA960-\uA97C]$/,K=/^[\u1160-\u11A7\uD7B0-\uD7C6]$/,Z=/^[\u11A8-\u11FF\uD7CB-\uD7FB]$/,Y=/^[\uAC00\uAC1C\uAC38\uAC54\uAC70\uAC8C\uACA8\uACC4\uACE0\uACFC\uAD18\uAD34\uAD50\uAD6C\uAD88\uADA4\uADC0\uADDC\uADF8\uAE14\uAE30\uAE4C\uAE68\uAE84\uAEA0\uAEBC\uAED8\uAEF4\uAF10\uAF2C\uAF48\uAF64\uAF80\uAF9C\uAFB8\uAFD4\uAFF0\uB00C\uB028\uB044\uB060\uB07C\uB098\uB0B4\uB0D0\uB0EC\uB108\uB124\uB140\uB15C\uB178\uB194\uB1B0\uB1CC\uB1E8\uB204\uB220\uB23C\uB258\uB274\uB290\uB2AC\uB2C8\uB2E4\uB300\uB31C\uB338\uB354\uB370\uB38C\uB3A8\uB3C4\uB3E0\uB3FC\uB418\uB434\uB450\uB46C\uB488\uB4A4\uB4C0\uB4DC\uB4F8\uB514\uB530\uB54C\uB568\uB584\uB5A0\uB5BC\uB5D8\uB5F4\uB610\uB62C\uB648\uB664\uB680\uB69C\uB6B8\uB6D4\uB6F0\uB70C\uB728\uB744\uB760\uB77C\uB798\uB7B4\uB7D0\uB7EC\uB808\uB824\uB840\uB85C\uB878\uB894\uB8B0\uB8CC\uB8E8\uB904\uB920\uB93C\uB958\uB974\uB990\uB9AC\uB9C8\uB9E4\uBA00\uBA1C\uBA38\uBA54\uBA70\uBA8C\uBAA8\uBAC4\uBAE0\uBAFC\uBB18\uBB34\uBB50\uBB6C\uBB88\uBBA4\uBBC0\uBBDC\uBBF8\uBC14\uBC30\uBC4C\uBC68\uBC84\uBCA0\uBCBC\uBCD8\uBCF4\uBD10\uBD2C\uBD48\uBD64\uBD80\uBD9C\uBDB8\uBDD4\uBDF0\uBE0C\uBE28\uBE44\uBE60\uBE7C\uBE98\uBEB4\uBED0\uBEEC\uBF08\uBF24\uBF40\uBF5C\uBF78\uBF94\uBFB0\uBFCC\uBFE8\uC004\uC020\uC03C\uC058\uC074\uC090\uC0AC\uC0C8\uC0E4\uC100\uC11C\uC138\uC154\uC170\uC18C\uC1A8\uC1C4\uC1E0\uC1FC\uC218\uC234\uC250\uC26C\uC288\uC2A4\uC2C0\uC2DC\uC2F8\uC314\uC330\uC34C\uC368\uC384\uC3A0\uC3BC\uC3D8\uC3F4\uC410\uC42C\uC448\uC464\uC480\uC49C\uC4B8\uC4D4\uC4F0\uC50C\uC528\uC544\uC560\uC57C\uC598\uC5B4\uC5D0\uC5EC\uC608\uC624\uC640\uC65C\uC678\uC694\uC6B0\uC6CC\uC6E8\uC704\uC720\uC73C\uC758\uC774\uC790\uC7AC\uC7C8\uC7E4\uC800\uC81C\uC838\uC854\uC870\uC88C\uC8A8\uC8C4\uC8E0\uC8FC\uC918\uC934\uC950\uC96C\uC988\uC9A4\uC9C0\uC9DC\uC9F8\uCA14\uCA30\uCA4C\uCA68\uCA84\uCAA0\uCABC\uCAD8\uCAF4\uCB10\uCB2C\uCB48\uCB64\uCB80\uCB9C\uCBB8\uCBD4\uCBF0\uCC0C\uCC28\uCC44\uCC60\uCC7C\uCC98\uCCB4\uCCD0\uCCEC\uCD08\uCD24\uCD40\uCD5C\uCD78\uCD94\uCDB0\uCDCC\uCDE8\uCE04\uCE20\uCE3C\uCE58\uCE74\uCE90\uCEAC\uCEC8\uCEE4\uCF00\uCF1C\uCF38\uCF54\uCF70\uCF8C\uCFA8\uCFC4\uCFE0\uCFFC\uD018\uD034\uD050\uD06C\uD088\uD0A4\uD0C0\uD0DC\uD0F8\uD114\uD130\uD14C\uD168\uD184\uD1A0\uD1BC\uD1D8\uD1F4\uD210\uD22C\uD248\uD264\uD280\uD29C\uD2B8\uD2D4\uD2F0\uD30C\uD328\uD344\uD360\uD37C\uD398\uD3B4\uD3D0\uD3EC\uD408\uD424\uD440\uD45C\uD478\uD494\uD4B0\uD4CC\uD4E8\uD504\uD520\uD53C\uD558\uD574\uD590\uD5AC\uD5C8\uD5E4\uD600\uD61C\uD638\uD654\uD670\uD68C\uD6A8\uD6C4\uD6E0\uD6FC\uD718\uD734\uD750\uD76C\uD788]$/,X=/^[\uAC01-\uAC1B\uAC1D-\uAC37\uAC39-\uAC53\uAC55-\uAC6F\uAC71-\uAC8B\uAC8D-\uACA7\uACA9-\uACC3\uACC5-\uACDF\uACE1-\uACFB\uACFD-\uAD17\uAD19-\uAD33\uAD35-\uAD4F\uAD51-\uAD6B\uAD6D-\uAD87\uAD89-\uADA3\uADA5-\uADBF\uADC1-\uADDB\uADDD-\uADF7\uADF9-\uAE13\uAE15-\uAE2F\uAE31-\uAE4B\uAE4D-\uAE67\uAE69-\uAE83\uAE85-\uAE9F\uAEA1-\uAEBB\uAEBD-\uAED7\uAED9-\uAEF3\uAEF5-\uAF0F\uAF11-\uAF2B\uAF2D-\uAF47\uAF49-\uAF63\uAF65-\uAF7F\uAF81-\uAF9B\uAF9D-\uAFB7\uAFB9-\uAFD3\uAFD5-\uAFEF\uAFF1-\uB00B\uB00D-\uB027\uB029-\uB043\uB045-\uB05F\uB061-\uB07B\uB07D-\uB097\uB099-\uB0B3\uB0B5-\uB0CF\uB0D1-\uB0EB\uB0ED-\uB107\uB109-\uB123\uB125-\uB13F\uB141-\uB15B\uB15D-\uB177\uB179-\uB193\uB195-\uB1AF\uB1B1-\uB1CB\uB1CD-\uB1E7\uB1E9-\uB203\uB205-\uB21F\uB221-\uB23B\uB23D-\uB257\uB259-\uB273\uB275-\uB28F\uB291-\uB2AB\uB2AD-\uB2C7\uB2C9-\uB2E3\uB2E5-\uB2FF\uB301-\uB31B\uB31D-\uB337\uB339-\uB353\uB355-\uB36F\uB371-\uB38B\uB38D-\uB3A7\uB3A9-\uB3C3\uB3C5-\uB3DF\uB3E1-\uB3FB\uB3FD-\uB417\uB419-\uB433\uB435-\uB44F\uB451-\uB46B\uB46D-\uB487\uB489-\uB4A3\uB4A5-\uB4BF\uB4C1-\uB4DB\uB4DD-\uB4F7\uB4F9-\uB513\uB515-\uB52F\uB531-\uB54B\uB54D-\uB567\uB569-\uB583\uB585-\uB59F\uB5A1-\uB5BB\uB5BD-\uB5D7\uB5D9-\uB5F3\uB5F5-\uB60F\uB611-\uB62B\uB62D-\uB647\uB649-\uB663\uB665-\uB67F\uB681-\uB69B\uB69D-\uB6B7\uB6B9-\uB6D3\uB6D5-\uB6EF\uB6F1-\uB70B\uB70D-\uB727\uB729-\uB743\uB745-\uB75F\uB761-\uB77B\uB77D-\uB797\uB799-\uB7B3\uB7B5-\uB7CF\uB7D1-\uB7EB\uB7ED-\uB807\uB809-\uB823\uB825-\uB83F\uB841-\uB85B\uB85D-\uB877\uB879-\uB893\uB895-\uB8AF\uB8B1-\uB8CB\uB8CD-\uB8E7\uB8E9-\uB903\uB905-\uB91F\uB921-\uB93B\uB93D-\uB957\uB959-\uB973\uB975-\uB98F\uB991-\uB9AB\uB9AD-\uB9C7\uB9C9-\uB9E3\uB9E5-\uB9FF\uBA01-\uBA1B\uBA1D-\uBA37\uBA39-\uBA53\uBA55-\uBA6F\uBA71-\uBA8B\uBA8D-\uBAA7\uBAA9-\uBAC3\uBAC5-\uBADF\uBAE1-\uBAFB\uBAFD-\uBB17\uBB19-\uBB33\uBB35-\uBB4F\uBB51-\uBB6B\uBB6D-\uBB87\uBB89-\uBBA3\uBBA5-\uBBBF\uBBC1-\uBBDB\uBBDD-\uBBF7\uBBF9-\uBC13\uBC15-\uBC2F\uBC31-\uBC4B\uBC4D-\uBC67\uBC69-\uBC83\uBC85-\uBC9F\uBCA1-\uBCBB\uBCBD-\uBCD7\uBCD9-\uBCF3\uBCF5-\uBD0F\uBD11-\uBD2B\uBD2D-\uBD47\uBD49-\uBD63\uBD65-\uBD7F\uBD81-\uBD9B\uBD9D-\uBDB7\uBDB9-\uBDD3\uBDD5-\uBDEF\uBDF1-\uBE0B\uBE0D-\uBE27\uBE29-\uBE43\uBE45-\uBE5F\uBE61-\uBE7B\uBE7D-\uBE97\uBE99-\uBEB3\uBEB5-\uBECF\uBED1-\uBEEB\uBEED-\uBF07\uBF09-\uBF23\uBF25-\uBF3F\uBF41-\uBF5B\uBF5D-\uBF77\uBF79-\uBF93\uBF95-\uBFAF\uBFB1-\uBFCB\uBFCD-\uBFE7\uBFE9-\uC003\uC005-\uC01F\uC021-\uC03B\uC03D-\uC057\uC059-\uC073\uC075-\uC08F\uC091-\uC0AB\uC0AD-\uC0C7\uC0C9-\uC0E3\uC0E5-\uC0FF\uC101-\uC11B\uC11D-\uC137\uC139-\uC153\uC155-\uC16F\uC171-\uC18B\uC18D-\uC1A7\uC1A9-\uC1C3\uC1C5-\uC1DF\uC1E1-\uC1FB\uC1FD-\uC217\uC219-\uC233\uC235-\uC24F\uC251-\uC26B\uC26D-\uC287\uC289-\uC2A3\uC2A5-\uC2BF\uC2C1-\uC2DB\uC2DD-\uC2F7\uC2F9-\uC313\uC315-\uC32F\uC331-\uC34B\uC34D-\uC367\uC369-\uC383\uC385-\uC39F\uC3A1-\uC3BB\uC3BD-\uC3D7\uC3D9-\uC3F3\uC3F5-\uC40F\uC411-\uC42B\uC42D-\uC447\uC449-\uC463\uC465-\uC47F\uC481-\uC49B\uC49D-\uC4B7\uC4B9-\uC4D3\uC4D5-\uC4EF\uC4F1-\uC50B\uC50D-\uC527\uC529-\uC543\uC545-\uC55F\uC561-\uC57B\uC57D-\uC597\uC599-\uC5B3\uC5B5-\uC5CF\uC5D1-\uC5EB\uC5ED-\uC607\uC609-\uC623\uC625-\uC63F\uC641-\uC65B\uC65D-\uC677\uC679-\uC693\uC695-\uC6AF\uC6B1-\uC6CB\uC6CD-\uC6E7\uC6E9-\uC703\uC705-\uC71F\uC721-\uC73B\uC73D-\uC757\uC759-\uC773\uC775-\uC78F\uC791-\uC7AB\uC7AD-\uC7C7\uC7C9-\uC7E3\uC7E5-\uC7FF\uC801-\uC81B\uC81D-\uC837\uC839-\uC853\uC855-\uC86F\uC871-\uC88B\uC88D-\uC8A7\uC8A9-\uC8C3\uC8C5-\uC8DF\uC8E1-\uC8FB\uC8FD-\uC917\uC919-\uC933\uC935-\uC94F\uC951-\uC96B\uC96D-\uC987\uC989-\uC9A3\uC9A5-\uC9BF\uC9C1-\uC9DB\uC9DD-\uC9F7\uC9F9-\uCA13\uCA15-\uCA2F\uCA31-\uCA4B\uCA4D-\uCA67\uCA69-\uCA83\uCA85-\uCA9F\uCAA1-\uCABB\uCABD-\uCAD7\uCAD9-\uCAF3\uCAF5-\uCB0F\uCB11-\uCB2B\uCB2D-\uCB47\uCB49-\uCB63\uCB65-\uCB7F\uCB81-\uCB9B\uCB9D-\uCBB7\uCBB9-\uCBD3\uCBD5-\uCBEF\uCBF1-\uCC0B\uCC0D-\uCC27\uCC29-\uCC43\uCC45-\uCC5F\uCC61-\uCC7B\uCC7D-\uCC97\uCC99-\uCCB3\uCCB5-\uCCCF\uCCD1-\uCCEB\uCCED-\uCD07\uCD09-\uCD23\uCD25-\uCD3F\uCD41-\uCD5B\uCD5D-\uCD77\uCD79-\uCD93\uCD95-\uCDAF\uCDB1-\uCDCB\uCDCD-\uCDE7\uCDE9-\uCE03\uCE05-\uCE1F\uCE21-\uCE3B\uCE3D-\uCE57\uCE59-\uCE73\uCE75-\uCE8F\uCE91-\uCEAB\uCEAD-\uCEC7\uCEC9-\uCEE3\uCEE5-\uCEFF\uCF01-\uCF1B\uCF1D-\uCF37\uCF39-\uCF53\uCF55-\uCF6F\uCF71-\uCF8B\uCF8D-\uCFA7\uCFA9-\uCFC3\uCFC5-\uCFDF\uCFE1-\uCFFB\uCFFD-\uD017\uD019-\uD033\uD035-\uD04F\uD051-\uD06B\uD06D-\uD087\uD089-\uD0A3\uD0A5-\uD0BF\uD0C1-\uD0DB\uD0DD-\uD0F7\uD0F9-\uD113\uD115-\uD12F\uD131-\uD14B\uD14D-\uD167\uD169-\uD183\uD185-\uD19F\uD1A1-\uD1BB\uD1BD-\uD1D7\uD1D9-\uD1F3\uD1F5-\uD20F\uD211-\uD22B\uD22D-\uD247\uD249-\uD263\uD265-\uD27F\uD281-\uD29B\uD29D-\uD2B7\uD2B9-\uD2D3\uD2D5-\uD2EF\uD2F1-\uD30B\uD30D-\uD327\uD329-\uD343\uD345-\uD35F\uD361-\uD37B\uD37D-\uD397\uD399-\uD3B3\uD3B5-\uD3CF\uD3D1-\uD3EB\uD3ED-\uD407\uD409-\uD423\uD425-\uD43F\uD441-\uD45B\uD45D-\uD477\uD479-\uD493\uD495-\uD4AF\uD4B1-\uD4CB\uD4CD-\uD4E7\uD4E9-\uD503\uD505-\uD51F\uD521-\uD53B\uD53D-\uD557\uD559-\uD573\uD575-\uD58F\uD591-\uD5AB\uD5AD-\uD5C7\uD5C9-\uD5E3\uD5E5-\uD5FF\uD601-\uD61B\uD61D-\uD637\uD639-\uD653\uD655-\uD66F\uD671-\uD68B\uD68D-\uD6A7\uD6A9-\uD6C3\uD6C5-\uD6DF\uD6E1-\uD6FB\uD6FD-\uD717\uD719-\uD733\uD735-\uD74F\uD751-\uD76B\uD76D-\uD787\uD789-\uD7A3]$/,H=/^(?:[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u2388\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2605\u2607-\u2612\u2614-\u2685\u2690-\u2705\u2708-\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763-\u2767\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC00-\uDCFF\uDD0D-\uDD0F\uDD2F\uDD6C-\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDAD-\uDDE5\uDE01-\uDE0F\uDE1A\uDE2F\uDE32-\uDE3A\uDE3C-\uDE3F\uDE49-\uDFFA]|\uD83D[\uDC00-\uDD3D\uDD46-\uDE4F\uDE80-\uDEFF\uDF74-\uDF7F\uDFD5-\uDFFF]|\uD83E[\uDC0C-\uDC0F\uDC48-\uDC4F\uDC5A-\uDC5F\uDC88-\uDC8F\uDCAE-\uDCFF\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDEFF]|\uD83F[\uDC00-\uDFFD])$/,Q=function(e,u){var t=_.Any;return-1!==e.search($)&&(t|=_.Extend),8205===u&&(t|=_.ZWJ),u>=127462&&u<=127487&&(t|=_.RI),-1!==e.search(J)&&(t|=_.Prepend),-1!==e.search(U)&&(t|=_.SpacingMark),-1!==e.search(G)&&(t|=_.L),-1!==e.search(K)&&(t|=_.V),-1!==e.search(Z)&&(t|=_.T),-1!==e.search(Y)&&(t|=_.LV),-1!==e.search(X)&&(t|=_.LVT),-1!==e.search(H)&&(t|=_.ExtPict),t};function ee(e,u){return 0!=(e&u)}var ue=[[_.L,_.L|_.V|_.LV|_.LVT],[_.LV|_.V,_.V|_.T],[_.LVT|_.T,_.T],[_.Any,_.Extend|_.ZWJ],[_.Any,_.SpacingMark],[_.Prepend,_.Any],[_.ZWJ,_.ExtPict],[_.RI,_.RI]];function te(e,u){return-1===ue.findIndex((function(t){return ee(e,t[0])&&ee(u,t[1])}))}var re=/(?:[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u2388\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2605\u2607-\u2612\u2614-\u2685\u2690-\u2705\u2708-\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763-\u2767\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC00-\uDCFF\uDD0D-\uDD0F\uDD2F\uDD6C-\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDAD-\uDDE5\uDE01-\uDE0F\uDE1A\uDE2F\uDE32-\uDE3A\uDE3C-\uDE3F\uDE49-\uDFFA]|\uD83D[\uDC00-\uDD3D\uDD46-\uDE4F\uDE80-\uDEFF\uDF74-\uDF7F\uDFD5-\uDFFF]|\uD83E[\uDC0C-\uDC0F\uDC48-\uDC4F\uDC5A-\uDC5F\uDC88-\uDC8F\uDCAE-\uDCFF\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDEFF]|\uD83F[\uDC00-\uDFFD])(?:[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0902\u093A\u093C\u0941-\u0948\u094D\u0951-\u0957\u0962\u0963\u0981\u09BC\u09BE\u09C1-\u09C4\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01\u0A02\u0A3C\u0A41\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81\u0A82\u0ABC\u0AC1-\u0AC5\u0AC7\u0AC8\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01\u0B3C\u0B3E\u0B3F\u0B41-\u0B44\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B82\u0BBE\u0BC0\u0BCD\u0BD7\u0C00\u0C04\u0C3E-\u0C40\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81\u0CBC\u0CBF\u0CC2\u0CC6\u0CCC\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00\u0D01\u0D3B\u0D3C\u0D3E\u0D41-\u0D44\u0D4D\u0D57\u0D62\u0D63\u0D81\u0DCA\u0DCF\u0DD2-\u0DD4\u0DD6\u0DDF\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F71-\u0F7E\u0F80-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102D-\u1030\u1032-\u1037\u1039\u103A\u103D\u103E\u1058\u1059\u105E-\u1060\u1071-\u1074\u1082\u1085\u1086\u108D\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4\u17B5\u17B7-\u17BD\u17C6\u17C9-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193B\u1A17\u1A18\u1A1B\u1A56\u1A58-\u1A5E\u1A60\u1A62\u1A65-\u1A6C\u1A73-\u1A7C\u1A7F\u1AB0-\u1AC0\u1B00-\u1B03\u1B34-\u1B3A\u1B3C\u1B42\u1B6B-\u1B73\u1B80\u1B81\u1BA2-\u1BA5\u1BA8\u1BA9\u1BAB-\u1BAD\u1BE6\u1BE8\u1BE9\u1BED\u1BEF-\u1BF1\u1C2C-\u1C33\u1C36\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE0\u1CE2-\u1CE8\u1CED\u1CF4\u1CF8\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u200C\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA825\uA826\uA82C\uA8C4\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA951\uA980-\uA982\uA9B3\uA9B6-\uA9B9\uA9BC\uA9BD\uA9E5\uAA29-\uAA2E\uAA31\uAA32\uAA35\uAA36\uAA43\uAA4C\uAA7C\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEC\uAAED\uAAF6\uABE5\uABE8\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFF9E\uFF9F]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD803[\uDD24-\uDD27\uDEAB\uDEAC\uDF46-\uDF50]|\uD804[\uDC01\uDC38-\uDC46\uDC7F-\uDC81\uDCB3-\uDCB6\uDCB9\uDCBA\uDD00-\uDD02\uDD27-\uDD2B\uDD2D-\uDD34\uDD73\uDD80\uDD81\uDDB6-\uDDBE\uDDC9-\uDDCC\uDDCF\uDE2F-\uDE31\uDE34\uDE36\uDE37\uDE3E\uDEDF\uDEE3-\uDEEA\uDF00\uDF01\uDF3B\uDF3C\uDF3E\uDF40\uDF57\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC38-\uDC3F\uDC42-\uDC44\uDC46\uDC5E\uDCB0\uDCB3-\uDCB8\uDCBA\uDCBD\uDCBF\uDCC0\uDCC2\uDCC3\uDDAF\uDDB2-\uDDB5\uDDBC\uDDBD\uDDBF\uDDC0\uDDDC\uDDDD\uDE33-\uDE3A\uDE3D\uDE3F\uDE40\uDEAB\uDEAD\uDEB0-\uDEB5\uDEB7\uDF1D-\uDF1F\uDF22-\uDF25\uDF27-\uDF2B]|\uD806[\uDC2F-\uDC37\uDC39\uDC3A\uDD30\uDD3B\uDD3C\uDD3E\uDD43\uDDD4-\uDDD7\uDDDA\uDDDB\uDDE0\uDE01-\uDE0A\uDE33-\uDE38\uDE3B-\uDE3E\uDE47\uDE51-\uDE56\uDE59-\uDE5B\uDE8A-\uDE96\uDE98\uDE99]|\uD807[\uDC30-\uDC36\uDC38-\uDC3D\uDC3F\uDC92-\uDCA7\uDCAA-\uDCB0\uDCB2\uDCB3\uDCB5\uDCB6\uDD31-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD45\uDD47\uDD90\uDD91\uDD95\uDD97\uDEF3\uDEF4]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF4F\uDF8F-\uDF92\uDFE4]|\uD82F[\uDC9D\uDC9E]|\uD834[\uDD65\uDD67-\uDD69\uDD6E-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDD30-\uDD36\uDEEC-\uDEEF]|\uD83A[\uDCD0-\uDCD6\uDD44-\uDD4A]|\uD83C[\uDFFB-\uDFFF]|\uDB40[\uDC20-\uDC7F\uDD00-\uDDEF])*\u200D$/,ne=function(e){return-1!==e.search(re)},oe=/(?:\uD83C[\uDDE6-\uDDFF])+$/g,ie=function(e){var u=e.match(oe);return null!==u&&u[0].length/2%2==1},ae=function(e){return k(e)&&Eu.isNodeList(e.children)&&!ve.isEditor(e)},se={isAncestor:function(e){return k(e)&&Eu.isNodeList(e.children)},isElement:ae,isElementList:function(e){return Array.isArray(e)&&e.every((function(e){return se.isElement(e)}))},isElementProps:function(e){return void 0!==e.children},isElementType:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"type";return ae(e)&&e[t]===u},matches:function(e,u){for(var t in u)if("children"!==t&&e[t]!==u[t])return!1;return!0}},ce=["text"],fe=["text"];function le(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function De(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?le(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):le(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}function Ce(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return de(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return de(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function de(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var he=new WeakMap,ve={above:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.voids,r=void 0!==t&&t,n=u.mode,o=void 0===n?"lowest":n,i=u.at,a=void 0===i?e.selection:i,s=u.match;if(a){var c,f=ve.path(e,a),l="lowest"===o,C=Ce(ve.levels(e,{at:f,voids:r,match:s,reverse:l}));try{for(C.s();!(c=C.n()).done;){var d=D(c.value,2),h=d[0],v=d[1];if(!Vu.isText(h)&&!bu.equals(f,v))return[h,v]}}catch(e){C.e(e)}finally{C.f()}}},addMark:function(e,u,t){e.addMark(u,t)},after:function(e,u){var t,r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=ve.point(e,u,{edge:"end"}),i=ve.end(e,[]),a={anchor:o,focus:i},s=n.distance,c=void 0===s?1:s,f=0,l=Ce(ve.positions(e,De(De({},n),{},{at:a})));try{for(l.s();!(r=l.n()).done;){var D=r.value;if(f>c)break;0!==f&&(t=D),f++}}catch(e){l.e(e)}finally{l.f()}return t},before:function(e,u){var t,r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=ve.start(e,[]),i=ve.point(e,u,{edge:"start"}),a={anchor:o,focus:i},s=n.distance,c=void 0===s?1:s,f=0,l=Ce(ve.positions(e,De(De({},n),{},{at:a,reverse:!0})));try{for(l.s();!(r=l.n()).done;){var D=r.value;if(f>c)break;0!==f&&(t=D),f++}}catch(e){l.e(e)}finally{l.f()}return t},deleteBackward:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.unit,r=void 0===t?"character":t;e.deleteBackward(r)},deleteForward:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.unit,r=void 0===t?"character":t;e.deleteForward(r)},deleteFragment:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.direction,r=void 0===t?"forward":t;e.deleteFragment(r)},edges:function(e,u){return[ve.start(e,u),ve.end(e,u)]},end:function(e,u){return ve.point(e,u,{edge:"end"})},first:function(e,u){var t=ve.path(e,u,{edge:"start"});return ve.node(e,t)},fragment:function(e,u){var t=ve.range(e,u),r=Eu.fragment(e,t);return r},hasBlocks:function(e,u){return u.children.some((function(u){return ve.isBlock(e,u)}))},hasInlines:function(e,u){return u.children.some((function(u){return Vu.isText(u)||ve.isInline(e,u)}))},hasTexts:function(e,u){return u.children.every((function(e){return Vu.isText(e)}))},insertBreak:function(e){e.insertBreak()},insertFragment:function(e,u){e.insertFragment(u)},insertNode:function(e,u){e.insertNode(u)},insertText:function(e,u){e.insertText(u)},isBlock:function(e,u){return se.isElement(u)&&!e.isInline(u)},isEditor:function(e){if(!k(e))return!1;var u=he.get(e);if(void 0!==u)return u;var t="function"==typeof e.addMark&&"function"==typeof e.apply&&"function"==typeof e.deleteBackward&&"function"==typeof e.deleteForward&&"function"==typeof e.deleteFragment&&"function"==typeof e.insertBreak&&"function"==typeof e.insertFragment&&"function"==typeof e.insertNode&&"function"==typeof e.insertText&&"function"==typeof e.isInline&&"function"==typeof e.isVoid&&"function"==typeof e.normalizeNode&&"function"==typeof e.onChange&&"function"==typeof e.removeMark&&(null===e.marks||k(e.marks))&&(null===e.selection||_u.isRange(e.selection))&&Eu.isNodeList(e.children)&&mu.isOperationList(e.operations);return he.set(e,t),t},isEnd:function(e,u,t){var r=ve.end(e,t);return Pu.equals(u,r)},isEdge:function(e,u,t){return ve.isStart(e,u,t)||ve.isEnd(e,u,t)},isEmpty:function(e,u){var t=u.children,r=D(t,1)[0];return 0===t.length||1===t.length&&Vu.isText(r)&&""===r.text&&!e.isVoid(u)},isInline:function(e,u){return se.isElement(u)&&e.isInline(u)},isNormalizing:function(e){var u=p.get(e);return void 0===u||u},isStart:function(e,u,t){if(0!==u.offset)return!1;var r=ve.start(e,t);return Pu.equals(u,r)},isVoid:function(e,u){return se.isElement(u)&&e.isVoid(u)},last:function(e,u){var t=ve.path(e,u,{edge:"end"});return ve.node(e,t)},leaf:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=ve.path(e,u,t),n=Eu.leaf(e,r);return[n,r]},levels:O.mark((function(e){var u,t,r,n,o,i,a,s,c,f,l,C,d,h,v,p=arguments;return O.wrap((function(B){for(;;)switch(B.prev=B.next){case 0:if(u=p.length>1&&void 0!==p[1]?p[1]:{},t=u.at,r=void 0===t?e.selection:t,n=u.reverse,o=void 0!==n&&n,i=u.voids,a=void 0!==i&&i,null==(s=u.match)&&(s=function(){return!0}),r){B.next=6;break}return B.abrupt("return");case 6:c=[],f=ve.path(e,r),l=Ce(Eu.levels(e,f)),B.prev=9,l.s();case 11:if((C=l.n()).done){B.next=20;break}if(d=D(C.value,2),h=d[0],v=d[1],s(h,v)){B.next=15;break}return B.abrupt("continue",18);case 15:if(c.push([h,v]),a||!ve.isVoid(e,h)){B.next=18;break}return B.abrupt("break",20);case 18:B.next=11;break;case 20:B.next=25;break;case 22:B.prev=22,B.t0=B.catch(9),l.e(B.t0);case 25:return B.prev=25,l.f(),B.finish(25);case 28:return o&&c.reverse(),B.delegateYield(c,"t1",30);case 30:case"end":return B.stop()}}),c,null,[[9,22,25,28]])})),marks:function(e){var u=e.marks,t=e.selection;if(!t)return null;if(u)return u;if(_u.isExpanded(t)){var r=ve.nodes(e,{match:Vu.isText}),n=D(r,1)[0];if(n){var o=D(n,1)[0];return o.text,w(o,ce)}return{}}var i=t.anchor,a=i.path,s=ve.leaf(e,a),c=D(s,1)[0];if(0===i.offset){var f=ve.previous(e,{at:a,match:Vu.isText}),l=ve.above(e,{match:function(u){return ve.isBlock(e,u)}});if(f&&l){var C=D(f,2),d=C[0],h=C[1],v=D(l,2)[1];bu.isAncestor(v,h)&&(c=d)}}var p=c;return p.text,w(p,fe)},next:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.mode,r=void 0===t?"lowest":t,n=u.voids,o=void 0!==n&&n,i=u.match,a=u.at,s=void 0===a?e.selection:a;if(s){var c=ve.after(e,s,{voids:o});if(c){var f=ve.last(e,[]),l=D(f,2),C=l[1],d=[c.path,C];if(bu.isPath(s)&&0===s.length)throw new Error("Cannot get the next node from the root node!");if(null==i)if(bu.isPath(s)){var h=ve.parent(e,s),v=D(h,1),p=v[0];i=function(e){return p.children.includes(e)}}else i=function(){return!0};var B=ve.nodes(e,{at:d,match:i,mode:r,voids:o}),A=D(B,1),F=A[0];return F}}},node:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=ve.path(e,u,t),n=Eu.get(e,r);return[n,r]},nodes:O.mark((function e(u){var t,r,n,o,i,a,s,c,f,l,C,d,h,v,p,B,A,F,E,g,y,m,b,x,w,P,k=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=k.length>1&&void 0!==k[1]?k[1]:{},r=t.at,n=void 0===r?u.selection:r,o=t.mode,i=void 0===o?"all":o,a=t.universal,s=void 0!==a&&a,c=t.reverse,f=void 0!==c&&c,l=t.voids,C=void 0!==l&&l,(d=t.match)||(d=function(){return!0}),n){e.next=6;break}return e.abrupt("return");case 6:Be.isSpan(n)?(h=n[0],v=n[1]):(p=ve.path(u,n,{edge:"start"}),B=ve.path(u,n,{edge:"end"}),h=f?B:p,v=f?p:B),A=Eu.nodes(u,{reverse:f,from:h,to:v,pass:function(e){var t=D(e,1)[0];return!C&&ve.isVoid(u,t)}}),F=[],g=Ce(A),e.prev=10,g.s();case 12:if((y=g.n()).done){e.next=37;break}if(m=D(y.value,2),b=m[0],x=m[1],w=E&&0===bu.compare(x,E[1]),"highest"!==i||!w){e.next=17;break}return e.abrupt("continue",35);case 17:if(d(b,x)){e.next=23;break}if(!s||w||!Vu.isText(b)){e.next=22;break}return e.abrupt("return");case 22:return e.abrupt("continue",35);case 23:if("lowest"!==i||!w){e.next=26;break}return E=[b,x],e.abrupt("continue",35);case 26:if(!(P="lowest"===i?E:[b,x])){e.next=34;break}if(!s){e.next=32;break}F.push(P),e.next=34;break;case 32:return e.next=34,P;case 34:E=[b,x];case 35:e.next=12;break;case 37:e.next=42;break;case 39:e.prev=39,e.t0=e.catch(10),g.e(e.t0);case 42:return e.prev=42,g.f(),e.finish(42);case 45:if("lowest"!==i||!E){e.next=52;break}if(!s){e.next=50;break}F.push(E),e.next=52;break;case 50:return e.next=52,E;case 52:if(!s){e.next=54;break}return e.delegateYield(F,"t1",54);case 54:case"end":return e.stop()}}),e,null,[[10,39,42,45]])})),normalize:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.force,r=void 0!==t&&t,n=function(e){return d.get(e)||[]},o=function(e){return h.get(e)||new Set},i=function(e){var u=n(e).pop(),t=u.join(",");return o(e).delete(t),u};if(ve.isNormalizing(e)){if(r){var a=Array.from(Eu.nodes(e),(function(e){return D(e,2)[1]})),s=new Set(a.map((function(e){return e.join(",")})));d.set(e,a),h.set(e,s)}0!==n(e).length&&ve.withoutNormalizing(e,(function(){var u,t=Ce(n(e));try{for(t.s();!(u=t.n()).done;){var r=u.value;if(Eu.has(e,r)){var o=ve.node(e,r),a=D(o,2),s=a[0];a[1];se.isElement(s)&&0===s.children.length&&e.normalizeNode(o)}}}catch(e){t.e(e)}finally{t.f()}for(var c=42*n(e).length,f=0;0!==n(e).length;){if(f>c)throw new Error("\n            Could not completely normalize the editor after ".concat(c," iterations! This is usually due to incorrect normalization logic that leaves a node in an invalid state.\n          "));var l=i(e);if(Eu.has(e,l)){var C=ve.node(e,l);e.normalizeNode(C)}f++}}))}},parent:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=ve.path(e,u,t),n=bu.parent(r),o=ve.node(e,n);return o},path:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.depth,n=t.edge;if(bu.isPath(u))if("start"===n){var o=Eu.first(e,u),i=D(o,2),a=i[1];u=a}else if("end"===n){var s=Eu.last(e,u),c=D(s,2),f=c[1];u=f}return _u.isRange(u)&&(u="start"===n?_u.start(u):"end"===n?_u.end(u):bu.common(u.anchor.path,u.focus.path)),Pu.isPoint(u)&&(u=u.path),null!=r&&(u=u.slice(0,r)),u},hasPath:function(e,u){return Eu.has(e,u)},pathRef:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.affinity,n=void 0===r?"forward":r,o={current:u,affinity:n,unref:function(){var u=o.current;return ve.pathRefs(e).delete(o),o.current=null,u}},i=ve.pathRefs(e);return i.add(o),o},pathRefs:function(e){var u=B.get(e);return u||(u=new Set,B.set(e,u)),u},point:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.edge,n=void 0===r?"start":r;if(bu.isPath(u)){var o;if("end"===n){var i=Eu.last(e,u),a=D(i,2),s=a[1];o=s}else{var c=Eu.first(e,u),f=D(c,2),l=f[1];o=l}var C=Eu.get(e,o);if(!Vu.isText(C))throw new Error("Cannot get the ".concat(n," point in the node at path [").concat(u,"] because it has no ").concat(n," text node."));return{path:o,offset:"end"===n?C.text.length:0}}if(_u.isRange(u)){var d=_u.edges(u),h=D(d,2),v=h[0],p=h[1];return"start"===n?v:p}return u},pointRef:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.affinity,n=void 0===r?"forward":r,o={current:u,affinity:n,unref:function(){var u=o.current;return ve.pointRefs(e).delete(o),o.current=null,u}},i=ve.pointRefs(e);return i.add(o),o},pointRefs:function(e){var u=A.get(e);return u||(u=new Set,A.set(e,u)),u},positions:O.mark((function e(u){var t,r,n,o,i,a,s,c,f,l,C,d,h,v,p,B,A,F,E,g,y,m,b,x,w,P,k,j,S,_=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(S=function(e,u,t){return"character"===u?N(e,t):"word"===u?L(e,t):"line"===u||"block"===u?e.length:1},t=_.length>1&&void 0!==_[1]?_[1]:{},r=t.at,n=void 0===r?u.selection:r,o=t.unit,i=void 0===o?"offset":o,a=t.reverse,s=void 0!==a&&a,c=t.voids,f=void 0!==c&&c,n){e.next=5;break}return e.abrupt("return");case 5:l=ve.range(u,n),C=_u.edges(l),d=D(C,2),h=d[0],v=d[1],p=s?v:h,B=!1,A="",F=0,E=0,g=0,y=Ce(ve.nodes(u,{at:n,reverse:s,voids:f})),e.prev=14,y.s();case 16:if((m=y.n()).done){e.next=51;break}if(b=D(m.value,2),x=b[0],w=b[1],!se.isElement(x)){e.next=26;break}if(f||!u.isVoid(x)){e.next=23;break}return e.next=22,ve.start(u,w);case 22:return e.abrupt("continue",49);case 23:if(!u.isInline(x)){e.next=25;break}return e.abrupt("continue",49);case 25:ve.hasInlines(u,x)&&(P=bu.isAncestor(w,v.path)?v:ve.end(u,w),k=bu.isAncestor(w,h.path)?h:ve.start(u,w),A=ve.string(u,{anchor:k,focus:P},{voids:f}),B=!0);case 26:if(!Vu.isText(x)){e.next=49;break}if((j=bu.equals(w,p.path))?(E=s?p.offset:x.text.length-p.offset,g=p.offset):(E=x.text.length,g=s?E:0),!j&&!B&&"offset"!==i){e.next=33;break}return e.next=32,{path:w,offset:g};case 32:B=!1;case 33:if(0!==F){e.next=39;break}if(""!==A){e.next=37;break}return e.abrupt("break",49);case 37:F=S(A,i,s),A=M(A,F,s)[1];case 39:if(g=s?g-F:g+F,!((E-=F)<0)){e.next=44;break}return F=-E,e.abrupt("break",49);case 44:return F=0,e.next=47,{path:w,offset:g};case 47:e.next=33;break;case 49:e.next=16;break;case 51:e.next=56;break;case 53:e.prev=53,e.t0=e.catch(14),y.e(e.t0);case 56:return e.prev=56,y.f(),e.finish(56);case 59:case"end":return e.stop()}}),e,null,[[14,53,56,59]])})),previous:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.mode,r=void 0===t?"lowest":t,n=u.voids,o=void 0!==n&&n,i=u.match,a=u.at,s=void 0===a?e.selection:a;if(s){var c=ve.before(e,s,{voids:o});if(c){var f=ve.first(e,[]),l=D(f,2),C=l[1],d=[c.path,C];if(bu.isPath(s)&&0===s.length)throw new Error("Cannot get the previous node from the root node!");if(null==i)if(bu.isPath(s)){var h=ve.parent(e,s),v=D(h,1),p=v[0];i=function(e){return p.children.includes(e)}}else i=function(){return!0};var B=ve.nodes(e,{reverse:!0,at:d,match:i,mode:r,voids:o}),A=D(B,1),F=A[0];return F}}},range:function(e,u,t){return _u.isRange(u)&&!t?u:{anchor:ve.start(e,u),focus:ve.end(e,t||u)}},rangeRef:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.affinity,n=void 0===r?"forward":r,o={current:u,affinity:n,unref:function(){var u=o.current;return ve.rangeRefs(e).delete(o),o.current=null,u}},i=ve.rangeRefs(e);return i.add(o),o},rangeRefs:function(e){var u=F.get(e);return u||(u=new Set,F.set(e,u)),u},removeMark:function(e,u){e.removeMark(u)},setNormalizing:function(e,u){p.set(e,u)},start:function(e,u){return ve.point(e,u,{edge:"start"})},string:function(e,u){var t,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.voids,o=void 0!==n&&n,i=ve.range(e,u),a=_u.edges(i),s=D(a,2),c=s[0],f=s[1],l="",C=Ce(ve.nodes(e,{at:i,match:Vu.isText,voids:o}));try{for(C.s();!(t=C.n()).done;){var d=D(t.value,2),h=d[0],v=d[1],p=h.text;bu.equals(v,f.path)&&(p=p.slice(0,f.offset)),bu.equals(v,c.path)&&(p=p.slice(c.offset)),l+=p}}catch(e){C.e(e)}finally{C.f()}return l},unhangRange:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.voids,n=void 0!==r&&r,o=_u.edges(u),i=D(o,2),a=i[0],s=i[1];if(0!==a.offset||0!==s.offset||_u.isCollapsed(u))return u;var c,f=ve.above(e,{at:s,match:function(u){return ve.isBlock(e,u)}}),l=f?f[1]:[],C=ve.start(e,a),d={anchor:C,focus:s},h=!0,v=Ce(ve.nodes(e,{at:d,match:Vu.isText,reverse:!0,voids:n}));try{for(v.s();!(c=v.n()).done;){var p=D(c.value,2),B=p[0],A=p[1];if(h)h=!1;else if(""!==B.text||bu.isBefore(A,l)){s={path:A,offset:B.text.length};break}}}catch(e){v.e(e)}finally{v.f()}return{anchor:a,focus:s}},void:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return ve.above(e,De(De({},u),{},{match:function(u){return ve.isVoid(e,u)}}))},withoutNormalizing:function(e,u){var t=ve.isNormalizing(e);ve.setNormalizing(e,!1);try{u()}finally{ve.setNormalizing(e,t)}ve.normalize(e)}},pe={isLocation:function(e){return bu.isPath(e)||Pu.isPoint(e)||_u.isRange(e)}},Be={isSpan:function(e){return Array.isArray(e)&&2===e.length&&e.every(bu.isPath)}};function Ae(e){for(var u=arguments.length,t=Array(u>1?u-1:0),r=1;r<u;r++)t[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(t.length?" "+t.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function Fe(e){return!!e&&!!e[ou]}function Ee(e){return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var u=Object.getPrototypeOf(e);if(null===u)return!0;var t=Object.hasOwnProperty.call(u,"constructor")&&u.constructor;return t===Object||"function"==typeof t&&Function.toString.call(t)===iu}(e)||Array.isArray(e)||!!e[nu]||!!e.constructor[nu]||xe(e)||we(e))}function ge(e,u,t){void 0===t&&(t=!1),0===ye(e)?(t?Object.keys:au)(e).forEach((function(r){t&&"symbol"==typeof r||u(r,e[r],e)})):e.forEach((function(t,r){return u(r,t,e)}))}function ye(e){var u=e[ou];return u?u.i>3?u.i-4:u.i:Array.isArray(e)?1:xe(e)?2:we(e)?3:0}function me(e,u){return 2===ye(e)?e.has(u):Object.prototype.hasOwnProperty.call(e,u)}function be(e,u,t){var r=ye(e);2===r?e.set(u,t):3===r?(e.delete(u),e.add(t)):e[u]=t}function xe(e){return eu&&e instanceof Map}function we(e){return uu&&e instanceof Set}function Oe(e){return e.o||e.t}function Pe(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var u=su(e);delete u[ou];for(var t=au(u),r=0;r<t.length;r++){var n=t[r],o=u[n];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(u[n]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[n]})}return Object.create(Object.getPrototypeOf(e),u)}function ke(e,u){return void 0===u&&(u=!1),Se(e)||Fe(e)||!Ee(e)||(ye(e)>1&&(e.set=e.add=e.clear=e.delete=je),Object.freeze(e),u&&ge(e,(function(e,u){return ke(u,!0)}),!0)),e}function je(){Ae(2)}function Se(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function _e(e){var u=cu[e];return u||Ae(18,e),u}function Ne(){return He}function Te(e,u){u&&(_e("Patches"),e.u=[],e.s=[],e.v=u)}function Re(e){Ie(e),e.p.forEach(Me),e.p=null}function Ie(e){e===He&&(He=e.l)}function Le(e){return He={p:[],l:He,h:e,m:!0,_:0}}function Me(e){var u=e[ou];0===u.i||1===u.i?u.j():u.O=!0}function qe(e,u){u._=u.p.length;var t=u.p[0],r=void 0!==e&&e!==t;return u.h.g||_e("ES5").S(u,e,r),r?(t[ou].P&&(Re(u),Ae(4)),Ee(e)&&(e=ze(u,e),u.l||We(u,e)),u.u&&_e("Patches").M(t[ou],e,u.u,u.s)):e=ze(u,t,[]),Re(u),u.u&&u.v(u.u,u.s),e!==ru?e:void 0}function ze(e,u,t){if(Se(u))return u;var r=u[ou];if(!r)return ge(u,(function(n,o){return Ve(e,r,u,n,o,t)}),!0),u;if(r.A!==e)return u;if(!r.P)return We(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var n=4===r.i||5===r.i?r.o=Pe(r.k):r.o;ge(3===r.i?new Set(n):n,(function(u,o){return Ve(e,r,n,u,o,t)})),We(e,n,!1),t&&e.u&&_e("Patches").R(r,t,e.u,e.s)}return r.o}function Ve(e,u,t,r,n,o){if(Fe(n)){var i=ze(e,n,o&&u&&3!==u.i&&!me(u.D,r)?o.concat(r):void 0);if(be(t,r,i),!Fe(i))return;e.m=!1}if(Ee(n)&&!Se(n)){if(!e.h.F&&e._<1)return;ze(e,n),u&&u.A.l||We(e,n)}}function We(e,u,t){void 0===t&&(t=!1),e.h.F&&e.m&&ke(u,t)}function $e(e,u){var t=e[ou];return(t?Oe(t):e)[u]}function Je(e,u){if(u in e)for(var t=Object.getPrototypeOf(e);t;){var r=Object.getOwnPropertyDescriptor(t,u);if(r)return r;t=Object.getPrototypeOf(t)}}function Ue(e){e.P||(e.P=!0,e.l&&Ue(e.l))}function Ge(e){e.o||(e.o=Pe(e.t))}function Ke(e,u,t){var r=xe(u)?_e("MapSet").N(u,t):we(u)?_e("MapSet").T(u,t):e.g?function(e,u){var t=Array.isArray(e),r={i:t?1:0,A:u?u.A:Ne(),P:!1,I:!1,D:{},l:u,t:e,k:null,o:null,j:null,C:!1},n=r,o=fu;t&&(n=[r],o=lu);var i=Proxy.revocable(n,o),a=i.revoke,s=i.proxy;return r.k=s,r.j=a,s}(u,t):_e("ES5").J(u,t);return(t?t.A:Ne()).p.push(r),r}function Ze(e){return Fe(e)||Ae(22,e),function e(u){if(!Ee(u))return u;var t,r=u[ou],n=ye(u);if(r){if(!r.P&&(r.i<4||!_e("ES5").K(r)))return r.t;r.I=!0,t=Ye(u,n),r.I=!1}else t=Ye(u,n);return ge(t,(function(u,n){r&&function(e,u){return 2===ye(e)?e.get(u):e[u]}(r.t,u)===n||be(t,u,e(n))})),3===n?new Set(t):t}(e)}function Ye(e,u){switch(u){case 2:return new Map(e);case 3:return Array.from(e)}return Pe(e)}var Xe,He,Qe="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),eu="undefined"!=typeof Map,uu="undefined"!=typeof Set,tu="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,ru=Qe?Symbol.for("immer-nothing"):((Xe={})["immer-nothing"]=!0,Xe),nu=Qe?Symbol.for("immer-draftable"):"__$immer_draftable",ou=Qe?Symbol.for("immer-state"):"__$immer_state",iu=""+Object.prototype.constructor,au="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,su=Object.getOwnPropertyDescriptors||function(e){var u={};return au(e).forEach((function(t){u[t]=Object.getOwnPropertyDescriptor(e,t)})),u},cu={},fu={get:function(e,u){if(u===ou)return e;var t=Oe(e);if(!me(t,u))return function(e,u,t){var r,n=Je(u,t);return n?"value"in n?n.value:null===(r=n.get)||void 0===r?void 0:r.call(e.k):void 0}(e,t,u);var r=t[u];return e.I||!Ee(r)?r:r===$e(e.t,u)?(Ge(e),e.o[u]=Ke(e.A.h,r,e)):r},has:function(e,u){return u in Oe(e)},ownKeys:function(e){return Reflect.ownKeys(Oe(e))},set:function(e,u,t){var r=Je(Oe(e),u);if(null==r?void 0:r.set)return r.set.call(e.k,t),!0;if(!e.P){var n=$e(Oe(e),u),o=null==n?void 0:n[ou];if(o&&o.t===t)return e.o[u]=t,e.D[u]=!1,!0;if(function(e,u){return e===u?0!==e||1/e==1/u:e!=e&&u!=u}(t,n)&&(void 0!==t||me(e.t,u)))return!0;Ge(e),Ue(e)}return e.o[u]===t&&"number"!=typeof t&&(void 0!==t||u in e.o)||(e.o[u]=t,e.D[u]=!0,!0)},deleteProperty:function(e,u){return void 0!==$e(e.t,u)||u in e.t?(e.D[u]=!1,Ge(e),Ue(e)):delete e.D[u],e.o&&delete e.o[u],!0},getOwnPropertyDescriptor:function(e,u){var t=Oe(e),r=Reflect.getOwnPropertyDescriptor(t,u);return r?{writable:!0,configurable:1!==e.i||"length"!==u,enumerable:r.enumerable,value:t[u]}:r},defineProperty:function(){Ae(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Ae(12)}},lu={};ge(fu,(function(e,u){lu[e]=function(){return arguments[0]=arguments[0][0],u.apply(this,arguments)}})),lu.deleteProperty=function(e,u){return fu.deleteProperty.call(this,e[0],u)},lu.set=function(e,u,t){return fu.set.call(this,e[0],u,t,e[0])};var Du=new(function(){function e(e){var u=this;this.g=tu,this.F=!0,this.produce=function(e,t,r){if("function"==typeof e&&"function"!=typeof t){var n=t;t=e;var o=u;return function(e){var u=this;void 0===e&&(e=n);for(var r=arguments.length,i=Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return o.produce(e,(function(e){var r;return(r=t).call.apply(r,[u,e].concat(i))}))}}var i;if("function"!=typeof t&&Ae(6),void 0!==r&&"function"!=typeof r&&Ae(7),Ee(e)){var a=Le(u),s=Ke(u,e,void 0),c=!0;try{i=t(s),c=!1}finally{c?Re(a):Ie(a)}return"undefined"!=typeof Promise&&i instanceof Promise?i.then((function(e){return Te(a,r),qe(e,a)}),(function(e){throw Re(a),e})):(Te(a,r),qe(i,a))}if(!e||"object"!=typeof e){if((i=t(e))===ru)return;return void 0===i&&(i=e),u.F&&ke(i,!0),i}Ae(21,e)},this.produceWithPatches=function(e,t){return"function"==typeof e?function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return u.produceWithPatches(t,(function(u){return e.apply(void 0,[u].concat(n))}))}:[u.produce(e,t,(function(e,u){r=e,n=u})),r,n];var r,n},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var u=e.prototype;return u.createDraft=function(e){Ee(e)||Ae(8),Fe(e)&&(e=Ze(e));var u=Le(this),t=Ke(this,e,void 0);return t[ou].C=!0,Ie(u),t},u.finishDraft=function(e,u){var t=(e&&e[ou]).A;return Te(t,u),qe(void 0,t)},u.setAutoFreeze=function(e){this.F=e},u.setUseProxies=function(e){e&&!tu&&Ae(20),this.g=e},u.applyPatches=function(e,u){var t;for(t=u.length-1;t>=0;t--){var r=u[t];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}var n=_e("Patches").$;return Fe(e)?n(e,u):this.produce(e,(function(e){return n(e,u.slice(t+1))}))},e}()),Cu=Du.produce;Du.produceWithPatches.bind(Du),Du.setAutoFreeze.bind(Du),Du.setUseProxies.bind(Du),Du.applyPatches.bind(Du);var du=Du.createDraft.bind(Du),hu=Du.finishDraft.bind(Du),vu=["children"],pu=["text"];function Bu(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return Au(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Au(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function Au(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var Fu=new WeakMap,Eu={ancestor:function(e,u){var t=Eu.get(e,u);if(Vu.isText(t))throw new Error("Cannot get the ancestor node at path [".concat(u,"] because it refers to a text node instead: ").concat(t));return t},ancestors:O.mark((function e(u,t){var r,n,o,i,a,s,c=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=c.length>2&&void 0!==c[2]?c[2]:{},n=Bu(bu.ancestors(t,r)),e.prev=2,n.s();case 4:if((o=n.n()).done){e.next=12;break}return i=o.value,a=Eu.ancestor(u,i),s=[a,i],e.next=10,s;case 10:e.next=4;break;case 12:e.next=17;break;case 14:e.prev=14,e.t0=e.catch(2),n.e(e.t0);case 17:return e.prev=17,n.f(),e.finish(17);case 20:case"end":return e.stop()}}),e,null,[[2,14,17,20]])})),child:function(e,u){if(Vu.isText(e))throw new Error("Cannot get the child of a text node: ".concat(JSON.stringify(e)));var t=e.children[u];if(null==t)throw new Error("Cannot get child at index `".concat(u,"` in node: ").concat(JSON.stringify(e)));return t},children:O.mark((function(e,u){var t,r,n,o,i,a,s,c,f=arguments;return O.wrap((function(l){for(;;)switch(l.prev=l.next){case 0:t=f.length>2&&void 0!==f[2]?f[2]:{},r=t.reverse,n=void 0!==r&&r,o=Eu.ancestor(e,u),i=o.children,a=n?i.length-1:0;case 5:if(!(n?a>=0:a<i.length)){l.next=13;break}return s=Eu.child(o,a),c=u.concat(a),l.next=10,[s,c];case 10:a=n?a-1:a+1,l.next=5;break;case 13:case"end":return l.stop()}}),i)})),common:function(e,u,t){var r=bu.common(u,t);return[Eu.get(e,r),r]},descendant:function(e,u){var t=Eu.get(e,u);if(ve.isEditor(t))throw new Error("Cannot get the descendant node at path [".concat(u,"] because it refers to the root editor node instead: ").concat(t));return t},descendants:O.mark((function e(u){var t,r,n,o,i,a,s=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=s.length>1&&void 0!==s[1]?s[1]:{},r=Bu(Eu.nodes(u,t)),e.prev=2,r.s();case 4:if((n=r.n()).done){e.next=11;break}if(o=D(n.value,2),i=o[0],0===(a=o[1]).length){e.next=9;break}return e.next=9,[i,a];case 9:e.next=4;break;case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(2),r.e(e.t0);case 16:return e.prev=16,r.f(),e.finish(16);case 19:case"end":return e.stop()}}),e,null,[[2,13,16,19]])})),elements:O.mark((function e(u){var t,r,n,o,i,a,s=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=s.length>1&&void 0!==s[1]?s[1]:{},r=Bu(Eu.nodes(u,t)),e.prev=2,r.s();case 4:if((n=r.n()).done){e.next=11;break}if(o=D(n.value,2),i=o[0],a=o[1],!se.isElement(i)){e.next=9;break}return e.next=9,[i,a];case 9:e.next=4;break;case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(2),r.e(e.t0);case 16:return e.prev=16,r.f(),e.finish(16);case 19:case"end":return e.stop()}}),e,null,[[2,13,16,19]])})),extractProps:function(e){return se.isAncestor(e)?(e.children,w(e,vu)):(e.text,w(e,pu))},first:function(e,u){for(var t=u.slice(),r=Eu.get(e,t);r&&!Vu.isText(r)&&0!==r.children.length;)r=r.children[0],t.push(0);return[r,t]},fragment:function(e,u){if(Vu.isText(e))throw new Error("Cannot get a fragment starting from a root text node: ".concat(JSON.stringify(e)));return Cu({children:e.children},(function(e){var t,r=_u.edges(u),n=D(r,2),o=n[0],i=n[1],a=Bu(Eu.nodes(e,{reverse:!0,pass:function(e){var t=D(e,2)[1];return!_u.includes(u,t)}}));try{for(a.s();!(t=a.n()).done;){var s=D(t.value,2)[1];if(!_u.includes(u,s)){var c=Eu.parent(e,s),f=s[s.length-1];c.children.splice(f,1)}if(bu.equals(s,i.path)){var l=Eu.leaf(e,s);l.text=l.text.slice(0,i.offset)}if(bu.equals(s,o.path)){var C=Eu.leaf(e,s);C.text=C.text.slice(o.offset)}}}catch(e){a.e(e)}finally{a.f()}ve.isEditor(e)&&(e.selection=null)})).children},get:function(e,u){for(var t=e,r=0;r<u.length;r++){var n=u[r];if(Vu.isText(t)||!t.children[n])throw new Error("Cannot find a descendant at path [".concat(u,"] in node: ").concat(JSON.stringify(e)));t=t.children[n]}return t},has:function(e,u){for(var t=e,r=0;r<u.length;r++){var n=u[r];if(Vu.isText(t)||!t.children[n])return!1;t=t.children[n]}return!0},isNode:function(e){return Vu.isText(e)||se.isElement(e)||ve.isEditor(e)},isNodeList:function(e){if(!Array.isArray(e))return!1;var u=Fu.get(e);if(void 0!==u)return u;var t=e.every((function(e){return Eu.isNode(e)}));return Fu.set(e,t),t},last:function(e,u){for(var t=u.slice(),r=Eu.get(e,t);r&&!Vu.isText(r)&&0!==r.children.length;){var n=r.children.length-1;r=r.children[n],t.push(n)}return[r,t]},leaf:function(e,u){var t=Eu.get(e,u);if(!Vu.isText(t))throw new Error("Cannot get the leaf node at path [".concat(u,"] because it refers to a non-leaf node: ").concat(t));return t},levels:O.mark((function e(u,t){var r,n,o,i,a,s=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=s.length>2&&void 0!==s[2]?s[2]:{},n=Bu(bu.levels(t,r)),e.prev=2,n.s();case 4:if((o=n.n()).done){e.next=11;break}return i=o.value,a=Eu.get(u,i),e.next=9,[a,i];case 9:e.next=4;break;case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(2),n.e(e.t0);case 16:return e.prev=16,n.f(),e.finish(16);case 19:case"end":return e.stop()}}),e,null,[[2,13,16,19]])})),matches:function(e,u){return se.isElement(e)&&se.isElementProps(u)&&se.matches(e,u)||Vu.isText(e)&&Vu.isTextProps(u)&&Vu.matches(e,u)},nodes:O.mark((function e(u){var t,r,n,o,i,a,s,c,f,l,D,C,d,h=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=h.length>1&&void 0!==h[1]?h[1]:{},r=t.pass,n=t.reverse,o=void 0!==n&&n,i=t.from,a=void 0===i?[]:i,s=t.to,c=new Set,f=[],l=u;case 6:if(!s||!(o?bu.isBefore(f,s):bu.isAfter(f,s))){e.next=9;break}return e.abrupt("break",37);case 9:if(c.has(l)){e.next=12;break}return e.next=12,[l,f];case 12:if(c.has(l)||Vu.isText(l)||0===l.children.length||null!=r&&!1!==r([l,f])){e.next=19;break}return c.add(l),D=o?l.children.length-1:0,bu.isAncestor(f,a)&&(D=a[f.length]),f=f.concat(D),l=Eu.get(u,f),e.abrupt("continue",6);case 19:if(0!==f.length){e.next=21;break}return e.abrupt("break",37);case 21:if(o){e.next=27;break}if(C=bu.next(f),!Eu.has(u,C)){e.next=27;break}return f=C,l=Eu.get(u,f),e.abrupt("continue",6);case 27:if(!o||0===f[f.length-1]){e.next=32;break}return d=bu.previous(f),f=d,l=Eu.get(u,f),e.abrupt("continue",6);case 32:f=bu.parent(f),l=Eu.get(u,f),c.add(l),e.next=6;break;case 37:case"end":return e.stop()}}),e)})),parent:function(e,u){var t=bu.parent(u),r=Eu.get(e,t);if(Vu.isText(r))throw new Error("Cannot get the parent of path [".concat(u,"] because it does not exist in the root."));return r},string:function(e){return Vu.isText(e)?e.text:e.children.map(Eu.string).join("")},texts:O.mark((function e(u){var t,r,n,o,i,a,s=arguments;return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=s.length>1&&void 0!==s[1]?s[1]:{},r=Bu(Eu.nodes(u,t)),e.prev=2,r.s();case 4:if((n=r.n()).done){e.next=11;break}if(o=D(n.value,2),i=o[0],a=o[1],!Vu.isText(i)){e.next=9;break}return e.next=9,[i,a];case 9:e.next=4;break;case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(2),r.e(e.t0);case 16:return e.prev=16,r.f(),e.finish(16);case 19:case"end":return e.stop()}}),e,null,[[2,13,16,19]])}))};function gu(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function yu(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?gu(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):gu(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}var mu={isNodeOperation:function(e){return mu.isOperation(e)&&e.type.endsWith("_node")},isOperation:function(e){if(!k(e))return!1;switch(e.type){case"insert_node":return bu.isPath(e.path)&&Eu.isNode(e.node);case"insert_text":return"number"==typeof e.offset&&"string"==typeof e.text&&bu.isPath(e.path);case"merge_node":return"number"==typeof e.position&&bu.isPath(e.path)&&k(e.properties);case"move_node":return bu.isPath(e.path)&&bu.isPath(e.newPath);case"remove_node":return bu.isPath(e.path)&&Eu.isNode(e.node);case"remove_text":return"number"==typeof e.offset&&"string"==typeof e.text&&bu.isPath(e.path);case"set_node":return bu.isPath(e.path)&&k(e.properties)&&k(e.newProperties);case"set_selection":return null===e.properties&&_u.isRange(e.newProperties)||null===e.newProperties&&_u.isRange(e.properties)||k(e.properties)&&k(e.newProperties);case"split_node":return bu.isPath(e.path)&&"number"==typeof e.position&&k(e.properties);default:return!1}},isOperationList:function(e){return Array.isArray(e)&&e.every((function(e){return mu.isOperation(e)}))},isSelectionOperation:function(e){return mu.isOperation(e)&&e.type.endsWith("_selection")},isTextOperation:function(e){return mu.isOperation(e)&&e.type.endsWith("_text")},inverse:function(e){switch(e.type){case"insert_node":return yu(yu({},e),{},{type:"remove_node"});case"insert_text":return yu(yu({},e),{},{type:"remove_text"});case"merge_node":return yu(yu({},e),{},{type:"split_node",path:bu.previous(e.path)});case"move_node":var u=e.newPath,t=e.path;if(bu.equals(u,t))return e;if(bu.isSibling(t,u))return yu(yu({},e),{},{path:u,newPath:t});var r=bu.transform(t,e),n=bu.transform(bu.next(t),e);return yu(yu({},e),{},{path:r,newPath:n});case"remove_node":return yu(yu({},e),{},{type:"insert_node"});case"remove_text":return yu(yu({},e),{},{type:"insert_text"});case"set_node":var o=e.properties,i=e.newProperties;return yu(yu({},e),{},{properties:i,newProperties:o});case"set_selection":var a=e.properties,s=e.newProperties;return yu(yu({},e),{},null==a?{properties:s,newProperties:null}:null==s?{properties:null,newProperties:a}:{properties:s,newProperties:a});case"split_node":return yu(yu({},e),{},{type:"merge_node",path:bu.next(e.path)})}}},bu={ancestors:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.reverse,r=void 0!==t&&t,n=bu.levels(e,u);return n=r?n.slice(1):n.slice(0,-1)},common:function(e,u){for(var t=[],r=0;r<e.length&&r<u.length;r++){var n=e[r];if(n!==u[r])break;t.push(n)}return t},compare:function(e,u){for(var t=Math.min(e.length,u.length),r=0;r<t;r++){if(e[r]<u[r])return-1;if(e[r]>u[r])return 1}return 0},endsAfter:function(e,u){var t=e.length-1,r=e.slice(0,t),n=u.slice(0,t),o=e[t],i=u[t];return bu.equals(r,n)&&o>i},endsAt:function(e,u){var t=e.length,r=e.slice(0,t),n=u.slice(0,t);return bu.equals(r,n)},endsBefore:function(e,u){var t=e.length-1,r=e.slice(0,t),n=u.slice(0,t),o=e[t],i=u[t];return bu.equals(r,n)&&o<i},equals:function(e,u){return e.length===u.length&&e.every((function(e,t){return e===u[t]}))},hasPrevious:function(e){return e[e.length-1]>0},isAfter:function(e,u){return 1===bu.compare(e,u)},isAncestor:function(e,u){return e.length<u.length&&0===bu.compare(e,u)},isBefore:function(e,u){return-1===bu.compare(e,u)},isChild:function(e,u){return e.length===u.length+1&&0===bu.compare(e,u)},isCommon:function(e,u){return e.length<=u.length&&0===bu.compare(e,u)},isDescendant:function(e,u){return e.length>u.length&&0===bu.compare(e,u)},isParent:function(e,u){return e.length+1===u.length&&0===bu.compare(e,u)},isPath:function(e){return Array.isArray(e)&&(0===e.length||"number"==typeof e[0])},isSibling:function(e,u){if(e.length!==u.length)return!1;var t=e.slice(0,-1),r=u.slice(0,-1);return e[e.length-1]!==u[u.length-1]&&bu.equals(t,r)},levels:function(e){for(var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.reverse,r=void 0!==t&&t,n=[],o=0;o<=e.length;o++)n.push(e.slice(0,o));return r&&n.reverse(),n},next:function(e){if(0===e.length)throw new Error("Cannot get the next path of a root path [".concat(e,"], because it has no next index."));var u=e[e.length-1];return e.slice(0,-1).concat(u+1)},operationCanTransformPath:function(e){switch(e.type){case"insert_node":case"remove_node":case"merge_node":case"split_node":case"move_node":return!0;default:return!1}},parent:function(e){if(0===e.length)throw new Error("Cannot get the parent path of the root path [".concat(e,"]."));return e.slice(0,-1)},previous:function(e){if(0===e.length)throw new Error("Cannot get the previous path of a root path [".concat(e,"], because it has no previous index."));var u=e[e.length-1];if(u<=0)throw new Error("Cannot get the previous path of a first child path [".concat(e,"] because it would result in a negative index."));return e.slice(0,-1).concat(u-1)},relative:function(e,u){if(!bu.isAncestor(u,e)&&!bu.equals(e,u))throw new Error("Cannot get the relative path of [".concat(e,"] inside ancestor [").concat(u,"], because it is not above or equal to the path."));return e.slice(u.length)},transform:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Cu(e,(function(r){var n=t.affinity,o=void 0===n?"forward":n;if(e&&0!==(null==e?void 0:e.length)){if(null===r)return null;switch(u.type){case"insert_node":var i=u.path;(bu.equals(i,r)||bu.endsBefore(i,r)||bu.isAncestor(i,r))&&(r[i.length-1]+=1);break;case"remove_node":var a=u.path;if(bu.equals(a,r)||bu.isAncestor(a,r))return null;bu.endsBefore(a,r)&&(r[a.length-1]-=1);break;case"merge_node":var s=u.path,c=u.position;bu.equals(s,r)||bu.endsBefore(s,r)?r[s.length-1]-=1:bu.isAncestor(s,r)&&(r[s.length-1]-=1,r[s.length]+=c);break;case"split_node":var f=u.path,l=u.position;if(bu.equals(f,r)){if("forward"===o)r[r.length-1]+=1;else if("backward"!==o)return null}else bu.endsBefore(f,r)?r[f.length-1]+=1:bu.isAncestor(f,r)&&e[f.length]>=l&&(r[f.length-1]+=1,r[f.length]-=l);break;case"move_node":var D=u.path,C=u.newPath;if(bu.equals(D,C))return;if(bu.isAncestor(D,r)||bu.equals(D,r)){var d=C.slice();return bu.endsBefore(D,C)&&D.length<C.length&&(d[D.length-1]-=1),d.concat(r.slice(D.length))}bu.isSibling(D,C)&&(bu.isAncestor(C,r)||bu.equals(C,r))?bu.endsBefore(D,r)?r[D.length-1]-=1:r[D.length-1]+=1:bu.endsBefore(C,r)||bu.equals(C,r)||bu.isAncestor(C,r)?(bu.endsBefore(D,r)&&(r[D.length-1]-=1),r[C.length-1]+=1):bu.endsBefore(D,r)&&(bu.equals(C,r)&&(r[C.length-1]+=1),r[D.length-1]-=1)}}}))}},xu={transform:function(e,u){var t=e.current,r=e.affinity;if(null!=t){var n=bu.transform(t,u,{affinity:r});e.current=n,null==n&&e.unref()}}};function wu(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function Ou(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?wu(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):wu(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}var Pu={compare:function(e,u){var t=bu.compare(e.path,u.path);return 0===t?e.offset<u.offset?-1:e.offset>u.offset?1:0:t},isAfter:function(e,u){return 1===Pu.compare(e,u)},isBefore:function(e,u){return-1===Pu.compare(e,u)},equals:function(e,u){return e.offset===u.offset&&bu.equals(e.path,u.path)},isPoint:function(e){return k(e)&&"number"==typeof e.offset&&bu.isPath(e.path)},transform:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Cu(e,(function(e){if(null===e)return null;var r=t.affinity,n=void 0===r?"forward":r,o=e.path,i=e.offset;switch(u.type){case"insert_node":case"move_node":e.path=bu.transform(o,u,t);break;case"insert_text":bu.equals(u.path,o)&&u.offset<=i&&(e.offset+=u.text.length);break;case"merge_node":bu.equals(u.path,o)&&(e.offset+=u.position),e.path=bu.transform(o,u,t);break;case"remove_text":bu.equals(u.path,o)&&u.offset<=i&&(e.offset-=Math.min(i-u.offset,u.text.length));break;case"remove_node":if(bu.equals(u.path,o)||bu.isAncestor(u.path,o))return null;e.path=bu.transform(o,u,t);break;case"split_node":if(bu.equals(u.path,o)){if(u.position===i&&null==n)return null;(u.position<i||u.position===i&&"forward"===n)&&(e.offset-=u.position,e.path=bu.transform(o,u,Ou(Ou({},t),{},{affinity:"forward"})))}else e.path=bu.transform(o,u,t)}}))}},ku={transform:function(e,u){var t=e.current,r=e.affinity;if(null!=t){var n=Pu.transform(t,u,{affinity:r});e.current=n,null==n&&e.unref()}}},ju=["anchor","focus"];function Su(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}var _u={edges:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.reverse,r=void 0!==t&&t,n=e.anchor,o=e.focus;return _u.isBackward(e)===r?[n,o]:[o,n]},end:function(e){var u=_u.edges(e),t=D(u,2)[1];return t},equals:function(e,u){return Pu.equals(e.anchor,u.anchor)&&Pu.equals(e.focus,u.focus)},includes:function(e,u){if(_u.isRange(u)){if(_u.includes(e,u.anchor)||_u.includes(e,u.focus))return!0;var t=_u.edges(e),r=D(t,2),n=r[0],o=r[1],i=_u.edges(u),a=D(i,2),s=a[0],c=a[1];return Pu.isBefore(n,s)&&Pu.isAfter(o,c)}var f=_u.edges(e),l=D(f,2),C=l[0],d=l[1],h=!1,v=!1;return Pu.isPoint(u)?(h=Pu.compare(u,C)>=0,v=Pu.compare(u,d)<=0):(h=bu.compare(u,C.path)>=0,v=bu.compare(u,d.path)<=0),h&&v},intersection:function(e,u){e.anchor,e.focus;var t=w(e,ju),r=_u.edges(e),n=D(r,2),o=n[0],i=n[1],a=_u.edges(u),s=D(a,2),c=s[0],f=s[1],l=Pu.isBefore(o,c)?c:o,d=Pu.isBefore(i,f)?i:f;return Pu.isBefore(d,l)?null:function(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?Su(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Su(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}({anchor:l,focus:d},t)},isBackward:function(e){var u=e.anchor,t=e.focus;return Pu.isAfter(u,t)},isCollapsed:function(e){var u=e.anchor,t=e.focus;return Pu.equals(u,t)},isExpanded:function(e){return!_u.isCollapsed(e)},isForward:function(e){return!_u.isBackward(e)},isRange:function(e){return k(e)&&Pu.isPoint(e.anchor)&&Pu.isPoint(e.focus)},points:O.mark((function e(u){return O.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,[u.anchor,"anchor"];case 2:return e.next=4,[u.focus,"focus"];case 4:case"end":return e.stop()}}),e)})),start:function(e){var u=_u.edges(e),t=D(u,1)[0];return t},transform:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Cu(e,(function(e){if(null===e)return null;var r,n,o=t.affinity,i=void 0===o?"inward":o;if("inward"===i){var a=_u.isCollapsed(e);_u.isForward(e)?(r="forward",n=a?r:"backward"):(r="backward",n=a?r:"forward")}else"outward"===i?_u.isForward(e)?(r="backward",n="forward"):(r="forward",n="backward"):(r=i,n=i);var s=Pu.transform(e.anchor,u,{affinity:r}),c=Pu.transform(e.focus,u,{affinity:n});if(!s||!c)return null;e.anchor=s,e.focus=c}))}},Nu={transform:function(e,u){var t=e.current,r=e.affinity;if(null!=t){var n=_u.transform(t,u,{affinity:r});e.current=n,null==n&&e.unref()}}},Tu=function e(u,t){for(var r in u){var n=u[r],o=t[r];if(k(n)&&k(o)){if(!e(n,o))return!1}else if(Array.isArray(n)&&Array.isArray(o)){if(n.length!==o.length)return!1;for(var i=0;i<n.length;i++)if(n[i]!==o[i])return!1}else if(n!==o)return!1}for(var a in t)if(void 0===u[a]&&void 0!==t[a])return!1;return!0},Ru=["text"],Iu=["anchor","focus"];function Lu(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return Mu(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Mu(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function Mu(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}function qu(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function zu(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?qu(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):qu(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}var Vu={equals:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.loose,n=void 0!==r&&r;function o(e){return e.text,w(e,Ru)}return Tu(n?o(e):e,n?o(u):u)},isText:function(e){return k(e)&&"string"==typeof e.text},isTextList:function(e){return Array.isArray(e)&&e.every((function(e){return Vu.isText(e)}))},isTextProps:function(e){return void 0!==e.text},matches:function(e,u){for(var t in u)if("text"!==t&&(!e.hasOwnProperty(t)||e[t]!==u[t]))return!1;return!0},decorations:function(e,u){var t,r=[zu({},e)],n=Lu(u);try{for(n.s();!(t=n.n()).done;){var o,i=t.value,a=(i.anchor,i.focus,w(i,Iu)),s=_u.edges(i),c=D(s,2),f=c[0],l=c[1],C=[],d=0,h=Lu(r);try{for(h.s();!(o=h.n()).done;){var v=o.value,p=v.text.length,B=d;if(d+=p,f.offset<=B&&l.offset>=d)Object.assign(v,a),C.push(v);else if(f.offset!==l.offset&&(f.offset===d||l.offset===B)||f.offset>d||l.offset<B||l.offset===B&&0!==B)C.push(v);else{var A=v,F=void 0,E=void 0;if(l.offset<d){var g=l.offset-B;E=zu(zu({},A),{},{text:A.text.slice(g)}),A=zu(zu({},A),{},{text:A.text.slice(0,g)})}if(f.offset>B){var y=f.offset-B;F=zu(zu({},A),{},{text:A.text.slice(0,y)}),A=zu(zu({},A),{},{text:A.text.slice(y)})}Object.assign(A,a),F&&C.push(F),C.push(A),E&&C.push(E)}}}catch(e){h.e(e)}finally{h.f()}r=C}}catch(e){n.e(e)}finally{n.f()}return r}};function Wu(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function $u(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?Wu(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Wu(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}function Ju(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return Uu(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Uu(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function Uu(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var Gu=["text"],Ku=["children"];function Zu(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function Yu(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?Zu(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Zu(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}function Xu(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return Hu(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Hu(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function Hu(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var Qu={insertNodes:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};ve.withoutNormalizing(e,(function(){var r=t.hanging,n=void 0!==r&&r,o=t.voids,i=void 0!==o&&o,a=t.mode,s=void 0===a?"lowest":a,c=t.at,f=t.match,l=t.select;if(Eu.isNode(u)&&(u=[u]),0!==u.length){var C=D(u,1)[0];if(c||(c=e.selection?e.selection:e.children.length>0?ve.end(e,[]):[0],l=!0),null==l&&(l=!1),_u.isRange(c))if(n||(c=ve.unhangRange(e,c)),_u.isCollapsed(c))c=c.anchor;else{var d=_u.edges(c),h=D(d,2)[1],v=ve.pointRef(e,h);lt.delete(e,{at:c}),c=v.unref()}if(Pu.isPoint(c)){null==f&&(f=Vu.isText(C)?function(e){return Vu.isText(e)}:e.isInline(C)?function(u){return Vu.isText(u)||ve.isInline(e,u)}:function(u){return ve.isBlock(e,u)});var p=ve.nodes(e,{at:c.path,match:f,mode:s,voids:i}),B=D(p,1)[0];if(!B)return;var A=D(B,2)[1],F=ve.pathRef(e,A),E=ve.isEnd(e,c,A);lt.splitNodes(e,{at:c,match:f,mode:s,voids:i});var g=F.unref();c=E?bu.next(g):g}var y=bu.parent(c),m=c[c.length-1];if(i||!ve.void(e,{at:y})){var b,x=Xu(u);try{for(x.s();!(b=x.n()).done;){var w=b.value,O=y.concat(m);m++,e.apply({type:"insert_node",path:O,node:w}),c=bu.next(c)}}catch(e){x.e(e)}finally{x.f()}if(c=bu.previous(c),l){var P=ve.end(e,c);P&&lt.select(e,P)}}}}))},liftNodes:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ve.withoutNormalizing(e,(function(){var t=u.at,r=void 0===t?e.selection:t,n=u.mode,o=void 0===n?"lowest":n,i=u.voids,a=void 0!==i&&i,s=u.match;if(null==s&&(s=bu.isPath(r)?tt(e,r):function(u){return ve.isBlock(e,u)}),r)for(var c=ve.nodes(e,{at:r,match:s,mode:o,voids:a}),f=0,l=Array.from(c,(function(u){var t=D(u,2)[1];return ve.pathRef(e,t)}));f<l.length;f++){var C=l[f].unref();if(C.length<2)throw new Error("Cannot lift node at a path [".concat(C,"] because it has a depth of less than `2`."));var d=ve.node(e,bu.parent(C)),h=D(d,2),v=h[0],p=h[1],B=C[C.length-1],A=v.children.length;if(1===A){var F=bu.next(p);lt.moveNodes(e,{at:C,to:F,voids:a}),lt.removeNodes(e,{at:p,voids:a})}else if(0===B)lt.moveNodes(e,{at:C,to:p,voids:a});else if(B===A-1){var E=bu.next(p);lt.moveNodes(e,{at:C,to:E,voids:a})}else{var g=bu.next(C),y=bu.next(p);lt.splitNodes(e,{at:g,voids:a}),lt.moveNodes(e,{at:C,to:y,voids:a})}}}))},mergeNodes:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ve.withoutNormalizing(e,(function(){var t=u.match,r=u.at,n=void 0===r?e.selection:r,o=u.hanging,i=void 0!==o&&o,a=u.voids,s=void 0!==a&&a,c=u.mode,f=void 0===c?"lowest":c;if(n){if(null==t)if(bu.isPath(n)){var l=ve.parent(e,n),C=D(l,1)[0];t=function(e){return C.children.includes(e)}}else t=function(u){return ve.isBlock(e,u)};if(!i&&_u.isRange(n)&&(n=ve.unhangRange(e,n)),_u.isRange(n))if(_u.isCollapsed(n))n=n.anchor;else{var d=_u.edges(n),h=D(d,2)[1],v=ve.pointRef(e,h);lt.delete(e,{at:n}),n=v.unref(),null==u.at&&lt.select(e,n)}var p=ve.nodes(e,{at:n,match:t,voids:s,mode:f}),B=D(p,1)[0],A=ve.previous(e,{at:n,match:t,voids:s,mode:f});if(B&&A){var F=D(B,2),E=F[0],g=F[1],y=D(A,2),m=y[0],b=y[1];if(0!==g.length&&0!==b.length){var x,O,P=bu.next(b),k=bu.common(g,b),j=bu.isSibling(g,b),S=Array.from(ve.levels(e,{at:g}),(function(e){return D(e,1)[0]})).slice(k.length).slice(0,-1),_=ve.above(e,{at:g,mode:"highest",match:function(u){return S.includes(u)&&et(e,u)}}),N=_&&ve.pathRef(e,_[1]);if(Vu.isText(E)&&Vu.isText(m)){E.text;var T=w(E,Gu);O=m.text.length,x=T}else{if(!se.isElement(E)||!se.isElement(m))throw new Error("Cannot merge the node at path [".concat(g,"] with the previous sibling because it is not the same kind: ").concat(JSON.stringify(E)," ").concat(JSON.stringify(m)));E.children;var R=w(E,Ku);O=m.children.length,x=R}j||lt.moveNodes(e,{at:g,to:P,voids:s}),N&&lt.removeNodes(e,{at:N.current,voids:s}),se.isElement(m)&&ve.isEmpty(e,m)||Vu.isText(m)&&""===m.text&&0!==b[b.length-1]?lt.removeNodes(e,{at:b,voids:s}):e.apply({type:"merge_node",path:P,position:O,properties:x}),N&&N.unref()}}}}))},moveNodes:function(e,u){ve.withoutNormalizing(e,(function(){var t=u.to,r=u.at,n=void 0===r?e.selection:r,o=u.mode,i=void 0===o?"lowest":o,a=u.voids,s=void 0!==a&&a,c=u.match;if(n){null==c&&(c=bu.isPath(n)?tt(e,n):function(u){return ve.isBlock(e,u)});for(var f=ve.pathRef(e,t),l=ve.nodes(e,{at:n,match:c,mode:i,voids:s}),C=0,d=Array.from(l,(function(u){var t=D(u,2)[1];return ve.pathRef(e,t)}));C<d.length;C++){var h=d[C].unref(),v=f.current;0!==h.length&&e.apply({type:"move_node",path:h,newPath:v}),f.current&&bu.isSibling(v,h)&&bu.isAfter(v,h)&&(f.current=bu.next(f.current))}f.unref()}}))},removeNodes:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ve.withoutNormalizing(e,(function(){var t=u.hanging,r=void 0!==t&&t,n=u.voids,o=void 0!==n&&n,i=u.mode,a=void 0===i?"lowest":i,s=u.at,c=void 0===s?e.selection:s,f=u.match;if(c){null==f&&(f=bu.isPath(c)?tt(e,c):function(u){return ve.isBlock(e,u)}),!r&&_u.isRange(c)&&(c=ve.unhangRange(e,c));for(var l=ve.nodes(e,{at:c,match:f,mode:a,voids:o}),C=0,d=Array.from(l,(function(u){var t=D(u,2)[1];return ve.pathRef(e,t)}));C<d.length;C++){var h=d[C].unref();if(h){var v=ve.node(e,h),p=D(v,1)[0];e.apply({type:"remove_node",path:h,node:p})}}}}))},setNodes:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};ve.withoutNormalizing(e,(function(){var r=t.match,n=t.at,o=void 0===n?e.selection:n,i=t.hanging,a=void 0!==i&&i,s=t.mode,c=void 0===s?"lowest":s,f=t.split,l=void 0!==f&&f,C=t.voids,d=void 0!==C&&C;if(o){if(null==r&&(r=bu.isPath(o)?tt(e,o):function(u){return ve.isBlock(e,u)}),!a&&_u.isRange(o)&&(o=ve.unhangRange(e,o)),l&&_u.isRange(o)){if(_u.isCollapsed(o)&&ve.leaf(e,o.anchor)[0].text.length>0)return;var h=ve.rangeRef(e,o,{affinity:"inward"}),v=_u.edges(o),p=D(v,2),B=p[0],A=p[1],F="lowest"===c?"lowest":"highest",E=ve.isEnd(e,A,A.path);lt.splitNodes(e,{at:A,match:r,mode:F,voids:d,always:!E});var g=ve.isStart(e,B,B.path);lt.splitNodes(e,{at:B,match:r,mode:F,voids:d,always:!g}),o=h.unref(),null==t.at&&lt.select(e,o)}var y,m=Xu(ve.nodes(e,{at:o,match:r,mode:c,voids:d}));try{for(m.s();!(y=m.n()).done;){var b=D(y.value,2),x=b[0],w=b[1],O={},P={};if(0!==w.length){var k=!1;for(var j in u)"children"!==j&&"text"!==j&&u[j]!==x[j]&&(k=!0,x.hasOwnProperty(j)&&(O[j]=x[j]),null!=u[j]&&(P[j]=u[j]));k&&e.apply({type:"set_node",path:w,properties:O,newProperties:P})}}}catch(e){m.e(e)}finally{m.f()}}}))},splitNodes:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ve.withoutNormalizing(e,(function(){var t=u.mode,r=void 0===t?"lowest":t,n=u.voids,o=void 0!==n&&n,i=u.match,a=u.at,s=void 0===a?e.selection:a,c=u.height,f=void 0===c?0:c,l=u.always,C=void 0!==l&&l;if(null==i&&(i=function(u){return ve.isBlock(e,u)}),_u.isRange(s)&&(s=ut(e,s)),bu.isPath(s)){var d=s,h=ve.point(e,d),v=ve.parent(e,d),p=D(v,1)[0];i=function(e){return e===p},f=h.path.length-d.length+1,s=h,C=!0}if(s){var B=ve.pointRef(e,s,{affinity:"backward"}),A=ve.nodes(e,{at:s,match:i,mode:r,voids:o}),F=D(A,1)[0];if(F){var E=ve.void(e,{at:s,mode:"highest"});if(!o&&E){var g=D(E,2),y=g[0],m=g[1];if(se.isElement(y)&&e.isInline(y)){var b=ve.after(e,m);if(!b){var x=bu.next(m);lt.insertNodes(e,{text:""},{at:x,voids:o}),b=ve.point(e,x)}s=b,C=!0}f=s.path.length-m.length+1,C=!0}var w,O=ve.pointRef(e,s),P=s.path.length-f,k=D(F,2)[1],j=s.path.slice(0,P),S=0===f?s.offset:s.path[P]+0,_=Xu(ve.levels(e,{at:j,reverse:!0,voids:o}));try{for(_.s();!(w=_.n()).done;){var N=D(w.value,2),T=N[0],R=N[1],I=!1;if(R.length<k.length||0===R.length||!o&&ve.isVoid(e,T))break;var L=B.current,M=ve.isEnd(e,L,R);if(C||!B||!ve.isEdge(e,L,R)){I=!0;var q=Eu.extractProps(T);e.apply({type:"split_node",path:R,position:S,properties:q})}S=R[R.length-1]+(I||M?1:0)}}catch(e){_.e(e)}finally{_.f()}if(null==u.at){var z=O.current||ve.end(e,[]);lt.select(e,z)}B.unref(),O.unref()}}}))},unsetNodes:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Array.isArray(u)||(u=[u]);var r,n={},o=Xu(u);try{for(o.s();!(r=o.n()).done;){var i=r.value;n[i]=null}}catch(e){o.e(e)}finally{o.f()}lt.setNodes(e,n,t)},unwrapNodes:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ve.withoutNormalizing(e,(function(){var t=u.mode,r=void 0===t?"lowest":t,n=u.split,o=void 0!==n&&n,i=u.voids,a=void 0!==i&&i,s=u.at,c=void 0===s?e.selection:s,f=u.match;if(c){null==f&&(f=bu.isPath(c)?tt(e,c):function(u){return ve.isBlock(e,u)}),bu.isPath(c)&&(c=ve.range(e,c));var l,C=_u.isRange(c)?ve.rangeRef(e,c):null,d=ve.nodes(e,{at:c,match:f,mode:r,voids:a}),h=Xu(Array.from(d,(function(u){var t=D(u,2)[1];return ve.pathRef(e,t)})).reverse());try{var v=function(){var u=l.value.unref(),t=ve.node(e,u),r=D(t,1)[0],n=ve.range(e,u);o&&C&&(n=_u.intersection(C.current,n)),lt.liftNodes(e,{at:n,match:function(e){return se.isAncestor(r)&&r.children.includes(e)},voids:a})};for(h.s();!(l=h.n()).done;)v()}catch(e){h.e(e)}finally{h.f()}C&&C.unref()}}))},wrapNodes:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};ve.withoutNormalizing(e,(function(){var r=t.mode,n=void 0===r?"lowest":r,o=t.split,i=void 0!==o&&o,a=t.voids,s=void 0!==a&&a,c=t.match,f=t.at,l=void 0===f?e.selection:f;if(l){if(null==c&&(c=bu.isPath(l)?tt(e,l):e.isInline(u)?function(u){return ve.isInline(e,u)||Vu.isText(u)}:function(u){return ve.isBlock(e,u)}),i&&_u.isRange(l)){var C=_u.edges(l),d=D(C,2),h=d[0],v=d[1],p=ve.rangeRef(e,l,{affinity:"inward"});lt.splitNodes(e,{at:v,match:c,voids:s}),lt.splitNodes(e,{at:h,match:c,voids:s}),l=p.unref(),null==t.at&&lt.select(e,l)}for(var B=0,A=Array.from(ve.nodes(e,{at:l,match:e.isInline(u)?function(u){return ve.isBlock(e,u)}:function(e){return ve.isEditor(e)},mode:"lowest",voids:s}));B<A.length;B++){var F=D(A[B],2)[1],E=_u.isRange(l)?_u.intersection(l,ve.range(e,F)):l;if(E){var g=Array.from(ve.nodes(e,{at:E,match:c,mode:n,voids:s}));if(g.length>0)if("continue"===function(){var t=D(g,1)[0],r=g[g.length-1],n=D(t,2)[1],o=D(r,2)[1];if(0===n.length&&0===o.length)return"continue";var i=bu.equals(n,o)?bu.parent(n):bu.common(n,o),a=ve.range(e,n,o),c=ve.node(e,i),f=D(c,1)[0],l=i.length+1,C=bu.next(o.slice(0,l)),d=Yu(Yu({},u),{},{children:[]});lt.insertNodes(e,d,{at:C,voids:s}),lt.moveNodes(e,{at:a,match:function(e){return se.isAncestor(f)&&f.children.includes(e)},to:C.concat(0),voids:s})}())continue}}}}))}},et=function e(u,t){if(se.isElement(t)){var r=t;return!!ve.isVoid(u,t)||1===r.children.length&&e(u,r.children[0])}return!ve.isEditor(t)},ut=function(e,u){if(_u.isCollapsed(u))return u.anchor;var t=_u.edges(u),r=D(t,2)[1],n=ve.pointRef(e,r);return lt.delete(e,{at:u}),n.unref()},tt=function(e,u){var t=ve.node(e,u),r=D(t,1)[0];return function(e){return e===r}};function rt(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function nt(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?rt(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):rt(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}var ot={collapse:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=u.edge,r=void 0===t?"anchor":t,n=e.selection;if(n)if("anchor"===r)lt.select(e,n.anchor);else if("focus"===r)lt.select(e,n.focus);else if("start"===r){var o=_u.edges(n),i=D(o,1),a=i[0];lt.select(e,a)}else if("end"===r){var s=_u.edges(n),c=D(s,2),f=c[1];lt.select(e,f)}},deselect:function(e){var u=e.selection;u&&e.apply({type:"set_selection",properties:u,newProperties:null})},move:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.selection,r=u.distance,n=void 0===r?1:r,o=u.unit,i=void 0===o?"character":o,a=u.reverse,s=void 0!==a&&a,c=u.edge,f=void 0===c?null:c;if(t){"start"===f&&(f=_u.isBackward(t)?"focus":"anchor"),"end"===f&&(f=_u.isBackward(t)?"anchor":"focus");var l=t.anchor,D=t.focus,C={distance:n,unit:i},d={};if(null==f||"anchor"===f){var h=s?ve.before(e,l,C):ve.after(e,l,C);h&&(d.anchor=h)}if(null==f||"focus"===f){var v=s?ve.before(e,D,C):ve.after(e,D,C);v&&(d.focus=v)}lt.setSelection(e,d)}},select:function(e,u){var t=e.selection;if(u=ve.range(e,u),t)lt.setSelection(e,u);else{if(!_u.isRange(u))throw new Error("When setting the selection and the current selection is `null` you must provide at least an `anchor` and `focus`, but you passed: ".concat(JSON.stringify(u)));e.apply({type:"set_selection",properties:t,newProperties:u})}},setPoint:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=e.selection,n=t.edge,o=void 0===n?"both":n;if(r){"start"===o&&(o=_u.isBackward(r)?"focus":"anchor"),"end"===o&&(o=_u.isBackward(r)?"anchor":"focus");var i=r.anchor,a=r.focus,s="anchor"===o?i:a;lt.setSelection(e,C({},"anchor"===o?"anchor":"focus",nt(nt({},s),u)))}},setSelection:function(e,u){var t=e.selection,r={},n={};if(t){for(var o in u)("anchor"===o&&null!=u.anchor&&!Pu.equals(u.anchor,t.anchor)||"focus"===o&&null!=u.focus&&!Pu.equals(u.focus,t.focus)||"anchor"!==o&&"focus"!==o&&u[o]!==t[o])&&(r[o]=t[o],n[o]=u[o]);Object.keys(r).length>0&&e.apply({type:"set_selection",properties:r,newProperties:n})}}};function it(e,u){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,u){if(!e)return;if("string"==typeof e)return at(e,u);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return at(e,u)}(e))||u&&e&&"number"==typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==t.return||t.return()}finally{if(a)throw o}}}}function at(e,u){(null==u||u>e.length)&&(u=e.length);for(var t=0,r=new Array(u);t<u;t++)r[t]=e[t];return r}var st={delete:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ve.withoutNormalizing(e,(function(){var t=u.reverse,r=void 0!==t&&t,n=u.unit,o=void 0===n?"character":n,i=u.distance,a=void 0===i?1:i,s=u.voids,c=void 0!==s&&s,f=u.at,l=void 0===f?e.selection:f,C=u.hanging,d=void 0!==C&&C;if(l){if(_u.isRange(l)&&_u.isCollapsed(l)&&(l=l.anchor),Pu.isPoint(l)){var h=ve.void(e,{at:l,mode:"highest"});if(!c&&h){l=D(h,2)[1]}else{var v={unit:o,distance:a};l={anchor:l,focus:r?ve.before(e,l,v)||ve.start(e,[]):ve.after(e,l,v)||ve.end(e,[])},d=!0}}if(bu.isPath(l))lt.removeNodes(e,{at:l,voids:c});else if(!_u.isCollapsed(l)){if(!d){var p=_u.edges(l),B=D(p,2)[1],A=ve.end(e,[]);Pu.equals(B,A)||(l=ve.unhangRange(e,l,{voids:c}))}var F=_u.edges(l),E=D(F,2),g=E[0],y=E[1],m=ve.above(e,{match:function(u){return ve.isBlock(e,u)},at:g,voids:c}),b=ve.above(e,{match:function(u){return ve.isBlock(e,u)},at:y,voids:c}),x=m&&b&&!bu.equals(m[1],b[1]),w=bu.equals(g.path,y.path),O=c?null:ve.void(e,{at:g,mode:"highest"}),P=c?null:ve.void(e,{at:y,mode:"highest"});if(O){var k=ve.before(e,g);k&&m&&bu.isAncestor(m[1],k.path)&&(g=k)}if(P){var j=ve.after(e,y);j&&b&&bu.isAncestor(b[1],j.path)&&(y=j)}var S,_,N=[],T=it(ve.nodes(e,{at:l,voids:c}));try{for(T.s();!(_=T.n()).done;){var R=_.value,I=D(R,2),L=I[0],M=I[1];S&&0===bu.compare(M,S)||(!c&&ve.isVoid(e,L)||!bu.isCommon(M,g.path)&&!bu.isCommon(M,y.path))&&(N.push(R),S=M)}}catch(e){T.e(e)}finally{T.f()}var q=Array.from(N,(function(u){var t=D(u,2)[1];return ve.pathRef(e,t)})),z=ve.pointRef(e,g),V=ve.pointRef(e,y);if(!w&&!O){var W=z.current,$=ve.leaf(e,W),J=D($,1)[0],U=W.path,G=g.offset,K=J.text.slice(G);K.length>0&&e.apply({type:"remove_text",path:U,offset:G,text:K})}for(var Z=0,Y=q;Z<Y.length;Z++){var X=Y[Z].unref();lt.removeNodes(e,{at:X,voids:c})}if(!P){var H=V.current,Q=ve.leaf(e,H),ee=D(Q,1)[0],ue=H.path,te=w?g.offset:0,re=ee.text.slice(te,y.offset);re.length>0&&e.apply({type:"remove_text",path:ue,offset:te,text:re})}!w&&x&&V.current&&z.current&&lt.mergeNodes(e,{at:V.current,hanging:!0,voids:c});var ne=r?z.unref()||V.unref():V.unref()||z.unref();null==u.at&&ne&&lt.select(e,ne)}}}))},insertFragment:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};ve.withoutNormalizing(e,(function(){var r=t.hanging,n=void 0!==r&&r,o=t.voids,i=void 0!==o&&o,a=t.at,s=void 0===a?e.selection:a;if(u.length&&s){if(_u.isRange(s))if(n||(s=ve.unhangRange(e,s)),_u.isCollapsed(s))s=s.anchor;else{var c=_u.edges(s),f=D(c,2)[1];if(!i&&ve.void(e,{at:f}))return;var l=ve.pointRef(e,f);lt.delete(e,{at:s}),s=l.unref()}else bu.isPath(s)&&(s=ve.start(e,s));if(i||!ve.void(e,{at:s})){var C=ve.above(e,{at:s,match:function(u){return ve.isInline(e,u)},mode:"highest",voids:i});if(C){var d=D(C,2)[1];if(ve.isEnd(e,s,d))s=ve.after(e,d);else if(ve.isStart(e,s,d)){s=ve.before(e,d)}}var h,v=ve.above(e,{match:function(u){return ve.isBlock(e,u)},at:s,voids:i}),p=D(v,2)[1],B=ve.isStart(e,s,p),A=ve.isEnd(e,s,p),F=B&&A,E=!B||B&&A,g=!A,y=Eu.first({children:u},[]),m=D(y,2)[1],b=Eu.last({children:u},[]),x=D(b,2)[1],w=[],O=function(u){var t=D(u,2),r=t[0],n=t[1];return!(0===n.length)&&(!!F||!(E&&bu.isAncestor(n,m)&&se.isElement(r)&&!e.isVoid(r)&&!e.isInline(r))&&!(g&&bu.isAncestor(n,x)&&se.isElement(r)&&!e.isVoid(r)&&!e.isInline(r)))},P=it(Eu.nodes({children:u},{pass:O}));try{for(P.s();!(h=P.n()).done;){var k=h.value;O(k)&&w.push(k)}}catch(e){P.e(e)}finally{P.f()}for(var j=[],S=[],_=[],N=!0,T=!1,R=0,I=w;R<I.length;R++){var L=D(I[R],1)[0];se.isElement(L)&&!e.isInline(L)?(N=!1,T=!0,S.push(L)):N?j.push(L):_.push(L)}var M=ve.nodes(e,{at:s,match:function(u){return Vu.isText(u)||ve.isInline(e,u)},mode:"highest",voids:i}),q=D(M,1)[0],z=D(q,2)[1],V=ve.isStart(e,s,z),W=ve.isEnd(e,s,z),$=ve.pathRef(e,A?bu.next(p):p),J=ve.pathRef(e,W?bu.next(z):z),U=ve.pathRef(e,p);lt.splitNodes(e,{at:s,match:function(u){return T?ve.isBlock(e,u):Vu.isText(u)||ve.isInline(e,u)},mode:T?"lowest":"highest",voids:i});var G=ve.pathRef(e,!V||V&&W?bu.next(z):z);if(lt.insertNodes(e,j,{at:G.current,match:function(u){return Vu.isText(u)||ve.isInline(e,u)},mode:"highest",voids:i}),F&&S.length&&lt.delete(e,{at:U.unref(),voids:i}),lt.insertNodes(e,S,{at:$.current,match:function(u){return ve.isBlock(e,u)},mode:"lowest",voids:i}),lt.insertNodes(e,_,{at:J.current,match:function(u){return Vu.isText(u)||ve.isInline(e,u)},mode:"highest",voids:i}),!t.at){var K;K=_.length>0?bu.previous(J.current):S.length>0?bu.previous($.current):bu.previous(G.current);var Z=ve.end(e,K);lt.select(e,Z)}G.unref(),$.unref(),J.unref()}}}))},insertText:function(e,u){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};ve.withoutNormalizing(e,(function(){var r=t.voids,n=void 0!==r&&r,o=t.at,i=void 0===o?e.selection:o;if(i){if(bu.isPath(i)&&(i=ve.range(e,i)),_u.isRange(i))if(_u.isCollapsed(i))i=i.anchor;else{var a=_u.end(i);if(!n&&ve.void(e,{at:a}))return;var s=_u.start(i),c=ve.pointRef(e,s);lt.delete(e,{at:i,voids:n}),i=c.unref(),lt.setSelection(e,{anchor:i,focus:i})}if(n||!ve.void(e,{at:i})){var f=i,l=f.path,D=f.offset;u.length>0&&e.apply({type:"insert_text",path:l,offset:D,text:u})}}}))}};function ct(e,u){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);u&&(r=r.filter((function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable}))),t.push.apply(t,r)}return t}function ft(e){for(var u=1;u<arguments.length;u++){var t=null!=arguments[u]?arguments[u]:{};u%2?ct(Object(t),!0).forEach((function(u){C(e,u,t[u])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ct(Object(t)).forEach((function(u){Object.defineProperty(e,u,Object.getOwnPropertyDescriptor(t,u))}))}return e}var lt=ft(ft(ft(ft({},{transform:function(e,u){e.children=du(e.children);var t=e.selection&&du(e.selection);try{t=function(e,u,t){switch(t.type){case"insert_node":var r=t.path,n=t.node,o=Eu.parent(e,r),i=r[r.length-1];if(i>o.children.length)throw new Error('Cannot apply an "insert_node" operation at path ['.concat(r,"] because the destination is past the end of the node."));if(o.children.splice(i,0,n),u){var a,c=Ju(_u.points(u));try{for(c.s();!(a=c.n()).done;){var f=D(a.value,2),l=f[0];u[f[1]]=Pu.transform(l,t)}}catch(e){c.e(e)}finally{c.f()}}break;case"insert_text":var C=t.path,d=t.offset,h=t.text;if(0===h.length)break;var v=Eu.leaf(e,C),p=v.text.slice(0,d),B=v.text.slice(d);if(v.text=p+h+B,u){var A,F=Ju(_u.points(u));try{for(F.s();!(A=F.n()).done;){var E=D(A.value,2),g=E[0];u[E[1]]=Pu.transform(g,t)}}catch(e){F.e(e)}finally{F.f()}}break;case"merge_node":var y=t.path,m=Eu.get(e,y),b=bu.previous(y),x=Eu.get(e,b),w=Eu.parent(e,y),O=y[y.length-1];if(Vu.isText(m)&&Vu.isText(x))x.text+=m.text;else{if(Vu.isText(m)||Vu.isText(x))throw new Error('Cannot apply a "merge_node" operation at path ['.concat(y,"] to nodes of different interfaces: ").concat(m," ").concat(x));var P;(P=x.children).push.apply(P,s(m.children))}if(w.children.splice(O,1),u){var k,j=Ju(_u.points(u));try{for(j.s();!(k=j.n()).done;){var S=D(k.value,2),_=S[0];u[S[1]]=Pu.transform(_,t)}}catch(e){j.e(e)}finally{j.f()}}break;case"move_node":var N=t.path,T=t.newPath;if(bu.isAncestor(N,T))throw new Error("Cannot move a path [".concat(N,"] to new path [").concat(T,"] because the destination is inside itself."));var R=Eu.get(e,N),I=Eu.parent(e,N),L=N[N.length-1];I.children.splice(L,1);var M=bu.transform(N,t),q=Eu.get(e,bu.parent(M)),z=M[M.length-1];if(q.children.splice(z,0,R),u){var V,W=Ju(_u.points(u));try{for(W.s();!(V=W.n()).done;){var $=D(V.value,2),J=$[0];u[$[1]]=Pu.transform(J,t)}}catch(e){W.e(e)}finally{W.f()}}break;case"remove_node":var U=t.path,G=U[U.length-1];if(Eu.parent(e,U).children.splice(G,1),u){var K,Z=Ju(_u.points(u));try{for(Z.s();!(K=Z.n()).done;){var Y=D(K.value,2),X=Y[0],H=Y[1],Q=Pu.transform(X,t);if(null!=u&&null!=Q)u[H]=Q;else{var ee,ue=void 0,te=void 0,re=Ju(Eu.texts(e));try{for(re.s();!(ee=re.n()).done;){var ne=D(ee.value,2),oe=ne[0],ie=ne[1];if(-1!==bu.compare(ie,U)){te=[oe,ie];break}ue=[oe,ie]}}catch(e){re.e(e)}finally{re.f()}var ae=!1;ue&&te&&(ae=bu.equals(te[1],U)?!bu.hasPrevious(te[1]):bu.common(ue[1],U).length<bu.common(te[1],U).length),ue&&!ae?(X.path=ue[1],X.offset=ue[0].text.length):te?(X.path=te[1],X.offset=0):u=null}}}catch(e){Z.e(e)}finally{Z.f()}}break;case"remove_text":var se=t.path,ce=t.offset,fe=t.text;if(0===fe.length)break;var le=Eu.leaf(e,se),De=le.text.slice(0,ce),Ce=le.text.slice(ce+fe.length);if(le.text=De+Ce,u){var de,he=Ju(_u.points(u));try{for(he.s();!(de=he.n()).done;){var ve=D(de.value,2),pe=ve[0];u[ve[1]]=Pu.transform(pe,t)}}catch(e){he.e(e)}finally{he.f()}}break;case"set_node":var Be=t.path,Ae=t.properties,Fe=t.newProperties;if(0===Be.length)throw new Error("Cannot set properties on the root node!");var Ee=Eu.get(e,Be);for(var ge in Fe){if("children"===ge||"text"===ge)throw new Error('Cannot set the "'.concat(ge,'" property of nodes!'));var ye=Fe[ge];null==ye?delete Ee[ge]:Ee[ge]=ye}for(var me in Ae)Fe.hasOwnProperty(me)||delete Ee[me];break;case"set_selection":var be=t.newProperties;if(null==be)u=be;else{if(null==u){if(!_u.isRange(be))throw new Error('Cannot apply an incomplete "set_selection" operation properties '.concat(JSON.stringify(be)," when there is no current selection."));u=$u({},be)}for(var xe in be){var we=be[xe];if(null==we){if("anchor"===xe||"focus"===xe)throw new Error('Cannot remove the "'.concat(xe,'" selection property'));delete u[xe]}else u[xe]=we}}break;case"split_node":var Oe=t.path,Pe=t.position,ke=t.properties;if(0===Oe.length)throw new Error('Cannot apply a "split_node" operation at path ['.concat(Oe,"] because the root node cannot be split."));var je,Se=Eu.get(e,Oe),_e=Eu.parent(e,Oe),Ne=Oe[Oe.length-1];if(Vu.isText(Se)){var Te=Se.text.slice(0,Pe),Re=Se.text.slice(Pe);Se.text=Te,je=$u($u({},ke),{},{text:Re})}else{var Ie=Se.children.slice(0,Pe),Le=Se.children.slice(Pe);Se.children=Ie,je=$u($u({},ke),{},{children:Le})}if(_e.children.splice(Ne+1,0,je),u){var Me,qe=Ju(_u.points(u));try{for(qe.s();!(Me=qe.n()).done;){var ze=D(Me.value,2),Ve=ze[0];u[ze[1]]=Pu.transform(Ve,t)}}catch(e){qe.e(e)}finally{qe.f()}}}return u}(e,t,u)}finally{e.children=hu(e.children),e.selection=t?Fe(t)?hu(t):t:null}}}),Qu),ot),st);e.Editor=ve,e.Element=se,e.Location=pe,e.Node=Eu,e.Operation=mu,e.Path=bu,e.PathRef=xu,e.Point=Pu,e.PointRef=ku,e.Range=_u,e.RangeRef=Nu,e.Span=Be,e.Text=Vu,e.Transforms=lt,e.createEditor=function(){var e={children:[],operations:[],selection:null,marks:null,isInline:function(){return!1},isVoid:function(){return!1},onChange:function(){},apply:function(u){var t,r=y(ve.pathRefs(e));try{for(r.s();!(t=r.n()).done;){var n=t.value;xu.transform(n,u)}}catch(e){r.e(e)}finally{r.f()}var o,i=y(ve.pointRefs(e));try{for(i.s();!(o=i.n()).done;){var a=o.value;ku.transform(a,u)}}catch(e){i.e(e)}finally{i.f()}var s,c=y(ve.rangeRefs(e));try{for(c.s();!(s=c.n()).done;){var f=s.value;Nu.transform(f,u)}}catch(e){c.e(e)}finally{c.f()}var l,D,C=d.get(e)||[],p=h.get(e)||new Set,B=function(e){if(e){var u=e.join(",");D.has(u)||(D.add(u),l.push(e))}};if(bu.operationCanTransformPath(u)){l=[],D=new Set;var A,F=y(C);try{for(F.s();!(A=F.n()).done;){var E=A.value;B(bu.transform(E,u))}}catch(e){F.e(e)}finally{F.f()}}else l=C,D=p;var g,m=y(b(u));try{for(m.s();!(g=m.n()).done;){B(g.value)}}catch(e){m.e(e)}finally{m.f()}d.set(e,l),h.set(e,D),lt.transform(e,u),e.operations.push(u),ve.normalize(e),"set_selection"===u.type&&(e.marks=null),v.get(e)||(v.set(e,!0),Promise.resolve().then((function(){v.set(e,!1),e.onChange(),e.operations=[]})))},addMark:function(u,t){var r=e.selection;if(r)if(_u.isExpanded(r))lt.setNodes(e,C({},u,t),{match:Vu.isText,split:!0});else{var n=g(g({},ve.marks(e)||{}),{},C({},u,t));e.marks=n,v.get(e)||e.onChange()}},deleteBackward:function(u){var t=e.selection;t&&_u.isCollapsed(t)&&lt.delete(e,{unit:u,reverse:!0})},deleteForward:function(u){var t=e.selection;t&&_u.isCollapsed(t)&&lt.delete(e,{unit:u})},deleteFragment:function(u){var t=e.selection;t&&_u.isExpanded(t)&&lt.delete(e,{reverse:"backward"===u})},getFragment:function(){var u=e.selection;return u?Eu.fragment(e,u):[]},insertBreak:function(){lt.splitNodes(e,{always:!0})},insertFragment:function(u){lt.insertFragment(e,u)},insertNode:function(u){lt.insertNodes(e,u)},insertText:function(u){var t=e.selection,r=e.marks;if(t){if(r){var n=g({text:u},r);lt.insertNodes(e,n)}else lt.insertText(e,u);e.marks=null}},normalizeNode:function(u){var t=D(u,2),r=t[0],n=t[1];if(!Vu.isText(r))if(se.isElement(r)&&0===r.children.length){lt.insertNodes(e,{text:""},{at:n.concat(0),voids:!0})}else for(var o=!ve.isEditor(r)&&(se.isElement(r)&&(e.isInline(r)||0===r.children.length||Vu.isText(r.children[0])||e.isInline(r.children[0]))),i=0,a=0;a<r.children.length;a++,i++){var s=Eu.get(e,n);if(!Vu.isText(s)){var c=r.children[a],f=s.children[i-1],l=a===r.children.length-1;if((Vu.isText(c)||se.isElement(c)&&e.isInline(c))!==o)lt.removeNodes(e,{at:n.concat(i),voids:!0}),i--;else if(se.isElement(c)){if(e.isInline(c))if(null!=f&&Vu.isText(f)){if(l){lt.insertNodes(e,{text:""},{at:n.concat(i+1),voids:!0}),i++}}else{lt.insertNodes(e,{text:""},{at:n.concat(i),voids:!0}),i++}}else null!=f&&Vu.isText(f)&&(Vu.equals(c,f,{loose:!0})?(lt.mergeNodes(e,{at:n.concat(i),voids:!0}),i--):""===f.text?(lt.removeNodes(e,{at:n.concat(i-1),voids:!0}),i--):""===c.text&&(lt.removeNodes(e,{at:n.concat(i),voids:!0}),i--))}}},removeMark:function(u){var t=e.selection;if(t)if(_u.isExpanded(t))lt.unsetNodes(e,u,{match:Vu.isText,split:!0});else{var r=g({},ve.marks(e)||{});delete r[u],e.marks=r,v.get(e)||e.onChange()}}};return e},Object.defineProperty(e,"__esModule",{value:!0})}));
