{"version": 3, "file": "index.js", "sources": ["../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/arrayLikeToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/iterableToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/nonIterableSpread.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/toConsumableArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/arrayWithHoles.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/iterableToArrayLimit.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/nonIterableRest.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/slicedToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/defineProperty.js", "../src/utils/weak-maps.ts", "../src/create-editor.ts", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/objectWithoutProperties.js", "../src/utils/string.ts", "../src/interfaces/element.ts", "../src/interfaces/editor.ts", "../src/interfaces/location.ts", "../src/interfaces/node.ts", "../src/interfaces/operation.ts", "../src/interfaces/path.ts", "../src/interfaces/path-ref.ts", "../src/interfaces/point.ts", "../src/interfaces/point-ref.ts", "../src/interfaces/range.ts", "../src/interfaces/range-ref.ts", "../src/utils/deep-equal.ts", "../src/interfaces/text.ts", "../src/transforms/general.ts", "../src/transforms/node.ts", "../src/transforms/selection.ts", "../src/transforms/text.ts", "../src/transforms/index.ts"], "sourcesContent": ["function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nmodule.exports = _arrayLikeToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\n\nmodule.exports = _arrayWithoutHoles;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nmodule.exports = _iterableToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\n\nmodule.exports = _unsupportedIterableToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nmodule.exports = _nonIterableSpread;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayWithoutHoles = require(\"./arrayWithoutHoles.js\");\n\nvar iterableToArray = require(\"./iterableToArray.js\");\n\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\n\nvar nonIterableSpread = require(\"./nonIterableSpread.js\");\n\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\n\nmodule.exports = _toConsumableArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nmodule.exports = _arrayWithHoles;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nmodule.exports = _iterableToArrayLimit;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nmodule.exports = _nonIterableRest;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayWithHoles = require(\"./arrayWithHoles.js\");\n\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit.js\");\n\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\n\nvar nonIterableRest = require(\"./nonIterableRest.js\");\n\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\n\nmodule.exports = _slicedToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nmodule.exports = _defineProperty;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "import { Editor, Path, PathRef, PointRef, RangeRef } from '..'\n\nexport const DIRTY_PATHS: WeakMap<Editor, Path[]> = new WeakMap()\nexport const DIRTY_PATH_KEYS: WeakMap<Editor, Set<string>> = new WeakMap()\nexport const FLUSHING: WeakMap<Editor, boolean> = new WeakMap()\nexport const NORMALIZING: WeakMap<Editor, boolean> = new WeakMap()\nexport const PATH_REFS: WeakMap<Editor, Set<PathRef>> = new WeakMap()\nexport const POINT_REFS: WeakMap<Editor, Set<PointRef>> = new WeakMap()\nexport const RANGE_REFS: WeakMap<Editor, Set<RangeRef>> = new WeakMap()\n", "import {\n  Descendant,\n  Editor,\n  Element,\n  Node,\n  NodeEntry,\n  Operation,\n  Path,\n  PathRef,\n  PointRef,\n  Range,\n  RangeRef,\n  Text,\n  Transforms,\n} from './'\nimport { DIRTY_PATHS, DIRTY_PATH_KEYS, FLUSHING } from './utils/weak-maps'\n\n/**\n * Create a new Slate `Editor` object.\n */\n\nexport const createEditor = (): Editor => {\n  const editor: Editor = {\n    children: [],\n    operations: [],\n    selection: null,\n    marks: null,\n    isInline: () => false,\n    isVoid: () => false,\n    onChange: () => {},\n\n    apply: (op: Operation) => {\n      for (const ref of Editor.pathRefs(editor)) {\n        PathRef.transform(ref, op)\n      }\n\n      for (const ref of Editor.pointRefs(editor)) {\n        PointRef.transform(ref, op)\n      }\n\n      for (const ref of Editor.rangeRefs(editor)) {\n        RangeRef.transform(ref, op)\n      }\n\n      const oldDirtyPaths = DIRTY_PATHS.get(editor) || []\n      const oldDirtyPathKeys = DIRTY_PATH_KEYS.get(editor) || new Set()\n      let dirtyPaths: Path[]\n      let dirtyPathKeys: Set<string>\n\n      const add = (path: Path | null) => {\n        if (path) {\n          const key = path.join(',')\n\n          if (!dirtyPathKeys.has(key)) {\n            dirtyPathKeys.add(key)\n            dirtyPaths.push(path)\n          }\n        }\n      }\n\n      if (Path.operationCanTransformPath(op)) {\n        dirtyPaths = []\n        dirtyPathKeys = new Set()\n        for (const path of oldDirtyPaths) {\n          const newPath = Path.transform(path, op)\n          add(newPath)\n        }\n      } else {\n        dirtyPaths = oldDirtyPaths\n        dirtyPathKeys = oldDirtyPathKeys\n      }\n\n      const newDirtyPaths = getDirtyPaths(op)\n      for (const path of newDirtyPaths) {\n        add(path)\n      }\n\n      DIRTY_PATHS.set(editor, dirtyPaths)\n      DIRTY_PATH_KEYS.set(editor, dirtyPathKeys)\n      Transforms.transform(editor, op)\n      editor.operations.push(op)\n      Editor.normalize(editor)\n\n      // Clear any formats applied to the cursor if the selection changes.\n      if (op.type === 'set_selection') {\n        editor.marks = null\n      }\n\n      if (!FLUSHING.get(editor)) {\n        FLUSHING.set(editor, true)\n\n        Promise.resolve().then(() => {\n          FLUSHING.set(editor, false)\n          editor.onChange()\n          editor.operations = []\n        })\n      }\n    },\n\n    addMark: (key: string, value: any) => {\n      const { selection } = editor\n\n      if (selection) {\n        if (Range.isExpanded(selection)) {\n          Transforms.setNodes(\n            editor,\n            { [key]: value },\n            { match: Text.isText, split: true }\n          )\n        } else {\n          const marks = {\n            ...(Editor.marks(editor) || {}),\n            [key]: value,\n          }\n\n          editor.marks = marks\n          if (!FLUSHING.get(editor)) {\n            editor.onChange()\n          }\n        }\n      }\n    },\n\n    deleteBackward: (unit: 'character' | 'word' | 'line' | 'block') => {\n      const { selection } = editor\n\n      if (selection && Range.isCollapsed(selection)) {\n        Transforms.delete(editor, { unit, reverse: true })\n      }\n    },\n\n    deleteForward: (unit: 'character' | 'word' | 'line' | 'block') => {\n      const { selection } = editor\n\n      if (selection && Range.isCollapsed(selection)) {\n        Transforms.delete(editor, { unit })\n      }\n    },\n\n    deleteFragment: (direction?: 'forward' | 'backward') => {\n      const { selection } = editor\n\n      if (selection && Range.isExpanded(selection)) {\n        Transforms.delete(editor, { reverse: direction === 'backward' })\n      }\n    },\n\n    getFragment: () => {\n      const { selection } = editor\n\n      if (selection) {\n        return Node.fragment(editor, selection)\n      }\n      return []\n    },\n\n    insertBreak: () => {\n      Transforms.splitNodes(editor, { always: true })\n    },\n\n    insertFragment: (fragment: Node[]) => {\n      Transforms.insertFragment(editor, fragment)\n    },\n\n    insertNode: (node: Node) => {\n      Transforms.insertNodes(editor, node)\n    },\n\n    insertText: (text: string) => {\n      const { selection, marks } = editor\n\n      if (selection) {\n        if (marks) {\n          const node = { text, ...marks }\n          Transforms.insertNodes(editor, node)\n        } else {\n          Transforms.insertText(editor, text)\n        }\n\n        editor.marks = null\n      }\n    },\n\n    normalizeNode: (entry: NodeEntry) => {\n      const [node, path] = entry\n\n      // There are no core normalizations for text nodes.\n      if (Text.isText(node)) {\n        return\n      }\n\n      // Ensure that block and inline nodes have at least one text child.\n      if (Element.isElement(node) && node.children.length === 0) {\n        const child = { text: '' }\n        Transforms.insertNodes(editor, child, {\n          at: path.concat(0),\n          voids: true,\n        })\n        return\n      }\n\n      // Determine whether the node should have block or inline children.\n      const shouldHaveInlines = Editor.isEditor(node)\n        ? false\n        : Element.isElement(node) &&\n          (editor.isInline(node) ||\n            node.children.length === 0 ||\n            Text.isText(node.children[0]) ||\n            editor.isInline(node.children[0]))\n\n      // Since we'll be applying operations while iterating, keep track of an\n      // index that accounts for any added/removed nodes.\n      let n = 0\n\n      for (let i = 0; i < node.children.length; i++, n++) {\n        const currentNode = Node.get(editor, path)\n        if (Text.isText(currentNode)) continue\n        const child = node.children[i] as Descendant\n        const prev = currentNode.children[n - 1] as Descendant\n        const isLast = i === node.children.length - 1\n        const isInlineOrText =\n          Text.isText(child) ||\n          (Element.isElement(child) && editor.isInline(child))\n\n        // Only allow block nodes in the top-level children and parent blocks\n        // that only contain block nodes. Similarly, only allow inline nodes in\n        // other inline nodes, or parent blocks that only contain inlines and\n        // text.\n        if (isInlineOrText !== shouldHaveInlines) {\n          Transforms.removeNodes(editor, { at: path.concat(n), voids: true })\n          n--\n        } else if (Element.isElement(child)) {\n          // Ensure that inline nodes are surrounded by text nodes.\n          if (editor.isInline(child)) {\n            if (prev == null || !Text.isText(prev)) {\n              const newChild = { text: '' }\n              Transforms.insertNodes(editor, newChild, {\n                at: path.concat(n),\n                voids: true,\n              })\n              n++\n            } else if (isLast) {\n              const newChild = { text: '' }\n              Transforms.insertNodes(editor, newChild, {\n                at: path.concat(n + 1),\n                voids: true,\n              })\n              n++\n            }\n          }\n        } else {\n          // Merge adjacent text nodes that are empty or match.\n          if (prev != null && Text.isText(prev)) {\n            if (Text.equals(child, prev, { loose: true })) {\n              Transforms.mergeNodes(editor, { at: path.concat(n), voids: true })\n              n--\n            } else if (prev.text === '') {\n              Transforms.removeNodes(editor, {\n                at: path.concat(n - 1),\n                voids: true,\n              })\n              n--\n            } else if (child.text === '') {\n              Transforms.removeNodes(editor, {\n                at: path.concat(n),\n                voids: true,\n              })\n              n--\n            }\n          }\n        }\n      }\n    },\n\n    removeMark: (key: string) => {\n      const { selection } = editor\n\n      if (selection) {\n        if (Range.isExpanded(selection)) {\n          Transforms.unsetNodes(editor, key, {\n            match: Text.isText,\n            split: true,\n          })\n        } else {\n          const marks = { ...(Editor.marks(editor) || {}) }\n          delete marks[key]\n          editor.marks = marks\n          if (!FLUSHING.get(editor)) {\n            editor.onChange()\n          }\n        }\n      }\n    },\n  }\n\n  return editor\n}\n\n/**\n * Get the \"dirty\" paths generated from an operation.\n */\n\nconst getDirtyPaths = (op: Operation): Path[] => {\n  switch (op.type) {\n    case 'insert_text':\n    case 'remove_text':\n    case 'set_node': {\n      const { path } = op\n      return Path.levels(path)\n    }\n\n    case 'insert_node': {\n      const { node, path } = op\n      const levels = Path.levels(path)\n      const descendants = Text.isText(node)\n        ? []\n        : Array.from(Node.nodes(node), ([, p]) => path.concat(p))\n\n      return [...levels, ...descendants]\n    }\n\n    case 'merge_node': {\n      const { path } = op\n      const ancestors = Path.ancestors(path)\n      const previousPath = Path.previous(path)\n      return [...ancestors, previousPath]\n    }\n\n    case 'move_node': {\n      const { path, newPath } = op\n\n      if (Path.equals(path, newPath)) {\n        return []\n      }\n\n      const oldAncestors: Path[] = []\n      const newAncestors: Path[] = []\n\n      for (const ancestor of Path.ancestors(path)) {\n        const p = Path.transform(ancestor, op)\n        oldAncestors.push(p!)\n      }\n\n      for (const ancestor of Path.ancestors(newPath)) {\n        const p = Path.transform(ancestor, op)\n        newAncestors.push(p!)\n      }\n\n      const newParent = newAncestors[newAncestors.length - 1]\n      const newIndex = newPath[newPath.length - 1]\n      const resultPath = newParent.concat(newIndex)\n\n      return [...oldAncestors, ...newAncestors, resultPath]\n    }\n\n    case 'remove_node': {\n      const { path } = op\n      const ancestors = Path.ancestors(path)\n      return [...ancestors]\n    }\n\n    case 'split_node': {\n      const { path } = op\n      const levels = Path.levels(path)\n      const nextPath = Path.next(path)\n      return [...levels, nextPath]\n    }\n\n    default: {\n      return []\n    }\n  }\n}\n", "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nmodule.exports = _objectWithoutPropertiesLoose;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = _objectWithoutProperties;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "// Character (grapheme cluster) boundaries are determined according to\n// the default grapheme cluster boundary specification, extended grapheme clusters variant[1].\n//\n// References:\n//\n// [1] https://www.unicode.org/reports/tr29/#Default_Grapheme_Cluster_Table\n// [2] https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakProperty.txt\n// [3] https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakTest.html\n// [4] https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakTest.txt\n\n/**\n * Get the distance to the end of the first character in a string of text.\n */\n\nexport const getCharacterDistance = (str: string, isRTL = false): number => {\n  const isLTR = !isRTL\n  const codepoints = isRTL ? codepointsIteratorRTL(str) : str\n\n  let left: CodepointType = CodepointType.None\n  let right: CodepointType = CodepointType.None\n  let distance = 0\n  // Evaluation of these conditions are deferred.\n  let gb11: boolean | null = null // Is GB11 applicable?\n  let gb12Or13: boolean | null = null // Is GB12 or GB13 applicable?\n\n  for (const char of codepoints) {\n    const code = char.codePointAt(0)\n    if (!code) break\n\n    const type = getCodepointType(char, code)\n    ;[left, right] = isLTR ? [right, type] : [type, left]\n\n    if (\n      intersects(left, CodepointType.ZWJ) &&\n      intersects(right, CodepointType.ExtPict)\n    ) {\n      if (isLTR) {\n        gb11 = endsWithEmojiZWJ(str.substring(0, distance))\n      } else {\n        gb11 = endsWithEmojiZWJ(str.substring(0, str.length - distance))\n      }\n      if (!gb11) break\n    }\n\n    if (\n      intersects(left, CodepointType.RI) &&\n      intersects(right, CodepointType.RI)\n    ) {\n      if (gb12Or13 !== null) {\n        gb12Or13 = !gb12Or13\n      } else {\n        if (isLTR) {\n          gb12Or13 = true\n        } else {\n          gb12Or13 = endsWithOddNumberOfRIs(\n            str.substring(0, str.length - distance)\n          )\n        }\n      }\n      if (!gb12Or13) break\n    }\n\n    if (\n      left !== CodepointType.None &&\n      right !== CodepointType.None &&\n      isBoundaryPair(left, right)\n    ) {\n      break\n    }\n\n    distance += char.length\n  }\n\n  return distance || 1\n}\n\nconst SPACE = /\\s/\nconst PUNCTUATION = /[\\u0021-\\u0023\\u0025-\\u002A\\u002C-\\u002F\\u003A\\u003B\\u003F\\u0040\\u005B-\\u005D\\u005F\\u007B\\u007D\\u00A1\\u00A7\\u00AB\\u00B6\\u00B7\\u00BB\\u00BF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0AF0\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E3B\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\nconst CHAMELEON = /['\\u2018\\u2019]/\n\n/**\n * Get the distance to the end of the first word in a string of text.\n */\n\nexport const getWordDistance = (text: string, isRTL = false): number => {\n  let dist = 0\n  let started = false\n\n  while (text.length > 0) {\n    const charDist = getCharacterDistance(text, isRTL)\n    const [char, remaining] = splitByCharacterDistance(text, charDist, isRTL)\n\n    if (isWordCharacter(char, remaining, isRTL)) {\n      started = true\n      dist += charDist\n    } else if (!started) {\n      dist += charDist\n    } else {\n      break\n    }\n\n    text = remaining\n  }\n\n  return dist\n}\n\n/**\n * Split a string in two parts at a given distance starting from the end when\n * `isRTL` is set to `true`.\n */\n\nexport const splitByCharacterDistance = (\n  str: string,\n  dist: number,\n  isRTL?: boolean\n): [string, string] => {\n  if (isRTL) {\n    const at = str.length - dist\n    return [str.slice(at, str.length), str.slice(0, at)]\n  }\n\n  return [str.slice(0, dist), str.slice(dist)]\n}\n\n/**\n * Check if a character is a word character. The `remaining` argument is used\n * because sometimes you must read subsequent characters to truly determine it.\n */\n\nconst isWordCharacter = (\n  char: string,\n  remaining: string,\n  isRTL = false\n): boolean => {\n  if (SPACE.test(char)) {\n    return false\n  }\n\n  // Chameleons count as word characters as long as they're in a word, so\n  // recurse to see if the next one is a word character or not.\n  if (CHAMELEON.test(char)) {\n    const charDist = getCharacterDistance(remaining, isRTL)\n    const [nextChar, nextRemaining] = splitByCharacterDistance(\n      remaining,\n      charDist,\n      isRTL\n    )\n\n    if (isWordCharacter(nextChar, nextRemaining, isRTL)) {\n      return true\n    }\n  }\n\n  if (PUNCTUATION.test(char)) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * Iterate on codepoints from right to left.\n */\n\nexport const codepointsIteratorRTL = function*(str: string) {\n  const end = str.length - 1\n\n  for (let i = 0; i < str.length; i++) {\n    const char1 = str.charAt(end - i)\n\n    if (isLowSurrogate(char1.charCodeAt(0))) {\n      const char2 = str.charAt(end - i - 1)\n      if (isHighSurrogate(char2.charCodeAt(0))) {\n        yield char2 + char1\n\n        i++\n        continue\n      }\n    }\n\n    yield char1\n  }\n}\n\n/**\n * Is `charCode` a high surrogate.\n *\n * https://en.wikipedia.org/wiki/Universal_Character_Set_characters#Surrogates\n */\n\nconst isHighSurrogate = (charCode: number) => {\n  return charCode >= 0xd800 && charCode <= 0xdbff\n}\n\n/**\n * Is `charCode` a low surrogate.\n *\n * https://en.wikipedia.org/wiki/Universal_Character_Set_characters#Surrogates\n */\n\nconst isLowSurrogate = (charCode: number) => {\n  return charCode >= 0xdc00 && charCode <= 0xdfff\n}\n\nenum CodepointType {\n  None = 0,\n  Extend = 1 << 0,\n  ZWJ = 1 << 1,\n  RI = 1 << 2,\n  Prepend = 1 << 3,\n  SpacingMark = 1 << 4,\n  L = 1 << 5,\n  V = 1 << 6,\n  T = 1 << 7,\n  LV = 1 << 8,\n  LVT = 1 << 9,\n  ExtPict = 1 << 10,\n  Any = 1 << 11,\n}\n\nconst reExtend = /^[\\p{Gr_Ext}\\p{EMod}]$/u\nconst rePrepend = /^[\\u0600-\\u0605\\u06DD\\u070F\\u0890-\\u0891\\u08E2\\u0D4E\\u{110BD}\\u{110CD}\\u{111C2}-\\u{111C3}\\u{1193F}\\u{11941}\\u{11A3A}\\u{11A84}-\\u{11A89}\\u{11D46}]$/u\nconst reSpacingMark = /^[\\u0903\\u093B\\u093E-\\u0940\\u0949-\\u094C\\u094E-\\u094F\\u0982-\\u0983\\u09BF-\\u09C0\\u09C7-\\u09C8\\u09CB-\\u09CC\\u0A03\\u0A3E-\\u0A40\\u0A83\\u0ABE-\\u0AC0\\u0AC9\\u0ACB-\\u0ACC\\u0B02-\\u0B03\\u0B40\\u0B47-\\u0B48\\u0B4B-\\u0B4C\\u0BBF\\u0BC1-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCC\\u0C01-\\u0C03\\u0C41-\\u0C44\\u0C82-\\u0C83\\u0CBE\\u0CC0-\\u0CC1\\u0CC3-\\u0CC4\\u0CC7-\\u0CC8\\u0CCA-\\u0CCB\\u0D02-\\u0D03\\u0D3F-\\u0D40\\u0D46-\\u0D48\\u0D4A-\\u0D4C\\u0D82-\\u0D83\\u0DD0-\\u0DD1\\u0DD8-\\u0DDE\\u0DF2-\\u0DF3\\u0E33\\u0EB3\\u0F3E-\\u0F3F\\u0F7F\\u1031\\u103B-\\u103C\\u1056-\\u1057\\u1084\\u1715\\u1734\\u17B6\\u17BE-\\u17C5\\u17C7-\\u17C8\\u1923-\\u1926\\u1929-\\u192B\\u1930-\\u1931\\u1933-\\u1938\\u1A19-\\u1A1A\\u1A55\\u1A57\\u1A6D-\\u1A72\\u1B04\\u1B3B\\u1B3D-\\u1B41\\u1B43-\\u1B44\\u1B82\\u1BA1\\u1BA6-\\u1BA7\\u1BAA\\u1BE7\\u1BEA-\\u1BEC\\u1BEE\\u1BF2-\\u1BF3\\u1C24-\\u1C2B\\u1C34-\\u1C35\\u1CE1\\u1CF7\\uA823-\\uA824\\uA827\\uA880-\\uA881\\uA8B4-\\uA8C3\\uA952-\\uA953\\uA983\\uA9B4-\\uA9B5\\uA9BA-\\uA9BB\\uA9BE-\\uA9C0\\uAA2F-\\uAA30\\uAA33-\\uAA34\\uAA4D\\uAAEB\\uAAEE-\\uAAEF\\uAAF5\\uABE3-\\uABE4\\uABE6-\\uABE7\\uABE9-\\uABEA\\uABEC\\u{11000}\\u{11002}\\u{11082}\\u{110B0}-\\u{110B2}\\u{110B7}-\\u{110B8}\\u{1112C}\\u{11145}-\\u{11146}\\u{11182}\\u{111B3}-\\u{111B5}\\u{111BF}-\\u{111C0}\\u{111CE}\\u{1122C}-\\u{1122E}\\u{11232}-\\u{11233}\\u{11235}\\u{112E0}-\\u{112E2}\\u{11302}-\\u{11303}\\u{1133F}\\u{11341}-\\u{11344}\\u{11347}-\\u{11348}\\u{1134B}-\\u{1134D}\\u{11362}-\\u{11363}\\u{11435}-\\u{11437}\\u{11440}-\\u{11441}\\u{11445}\\u{114B1}-\\u{114B2}\\u{114B9}\\u{114BB}-\\u{114BC}\\u{114BE}\\u{114C1}\\u{115B0}-\\u{115B1}\\u{115B8}-\\u{115BB}\\u{115BE}\\u{11630}-\\u{11632}\\u{1163B}-\\u{1163C}\\u{1163E}\\u{116AC}\\u{116AE}-\\u{116AF}\\u{116B6}\\u{11726}\\u{1182C}-\\u{1182E}\\u{11838}\\u{11931}-\\u{11935}\\u{11937}-\\u{11938}\\u{1193D}\\u{11940}\\u{11942}\\u{119D1}-\\u{119D3}\\u{119DC}-\\u{119DF}\\u{119E4}\\u{11A39}\\u{11A57}-\\u{11A58}\\u{11A97}\\u{11C2F}\\u{11C3E}\\u{11CA9}\\u{11CB1}\\u{11CB4}\\u{11D8A}-\\u{11D8E}\\u{11D93}-\\u{11D94}\\u{11D96}\\u{11EF5}-\\u{11EF6}\\u{16F51}-\\u{16F87}\\u{16FF0}-\\u{16FF1}\\u{1D166}\\u{1D16D}]$/u\nconst reL = /^[\\u1100-\\u115F\\uA960-\\uA97C]$/u\nconst reV = /^[\\u1160-\\u11A7\\uD7B0-\\uD7C6]$/u\nconst reT = /^[\\u11A8-\\u11FF\\uD7CB-\\uD7FB]$/u\nconst reLV = /^[\\uAC00\\uAC1C\\uAC38\\uAC54\\uAC70\\uAC8C\\uACA8\\uACC4\\uACE0\\uACFC\\uAD18\\uAD34\\uAD50\\uAD6C\\uAD88\\uADA4\\uADC0\\uADDC\\uADF8\\uAE14\\uAE30\\uAE4C\\uAE68\\uAE84\\uAEA0\\uAEBC\\uAED8\\uAEF4\\uAF10\\uAF2C\\uAF48\\uAF64\\uAF80\\uAF9C\\uAFB8\\uAFD4\\uAFF0\\uB00C\\uB028\\uB044\\uB060\\uB07C\\uB098\\uB0B4\\uB0D0\\uB0EC\\uB108\\uB124\\uB140\\uB15C\\uB178\\uB194\\uB1B0\\uB1CC\\uB1E8\\uB204\\uB220\\uB23C\\uB258\\uB274\\uB290\\uB2AC\\uB2C8\\uB2E4\\uB300\\uB31C\\uB338\\uB354\\uB370\\uB38C\\uB3A8\\uB3C4\\uB3E0\\uB3FC\\uB418\\uB434\\uB450\\uB46C\\uB488\\uB4A4\\uB4C0\\uB4DC\\uB4F8\\uB514\\uB530\\uB54C\\uB568\\uB584\\uB5A0\\uB5BC\\uB5D8\\uB5F4\\uB610\\uB62C\\uB648\\uB664\\uB680\\uB69C\\uB6B8\\uB6D4\\uB6F0\\uB70C\\uB728\\uB744\\uB760\\uB77C\\uB798\\uB7B4\\uB7D0\\uB7EC\\uB808\\uB824\\uB840\\uB85C\\uB878\\uB894\\uB8B0\\uB8CC\\uB8E8\\uB904\\uB920\\uB93C\\uB958\\uB974\\uB990\\uB9AC\\uB9C8\\uB9E4\\uBA00\\uBA1C\\uBA38\\uBA54\\uBA70\\uBA8C\\uBAA8\\uBAC4\\uBAE0\\uBAFC\\uBB18\\uBB34\\uBB50\\uBB6C\\uBB88\\uBBA4\\uBBC0\\uBBDC\\uBBF8\\uBC14\\uBC30\\uBC4C\\uBC68\\uBC84\\uBCA0\\uBCBC\\uBCD8\\uBCF4\\uBD10\\uBD2C\\uBD48\\uBD64\\uBD80\\uBD9C\\uBDB8\\uBDD4\\uBDF0\\uBE0C\\uBE28\\uBE44\\uBE60\\uBE7C\\uBE98\\uBEB4\\uBED0\\uBEEC\\uBF08\\uBF24\\uBF40\\uBF5C\\uBF78\\uBF94\\uBFB0\\uBFCC\\uBFE8\\uC004\\uC020\\uC03C\\uC058\\uC074\\uC090\\uC0AC\\uC0C8\\uC0E4\\uC100\\uC11C\\uC138\\uC154\\uC170\\uC18C\\uC1A8\\uC1C4\\uC1E0\\uC1FC\\uC218\\uC234\\uC250\\uC26C\\uC288\\uC2A4\\uC2C0\\uC2DC\\uC2F8\\uC314\\uC330\\uC34C\\uC368\\uC384\\uC3A0\\uC3BC\\uC3D8\\uC3F4\\uC410\\uC42C\\uC448\\uC464\\uC480\\uC49C\\uC4B8\\uC4D4\\uC4F0\\uC50C\\uC528\\uC544\\uC560\\uC57C\\uC598\\uC5B4\\uC5D0\\uC5EC\\uC608\\uC624\\uC640\\uC65C\\uC678\\uC694\\uC6B0\\uC6CC\\uC6E8\\uC704\\uC720\\uC73C\\uC758\\uC774\\uC790\\uC7AC\\uC7C8\\uC7E4\\uC800\\uC81C\\uC838\\uC854\\uC870\\uC88C\\uC8A8\\uC8C4\\uC8E0\\uC8FC\\uC918\\uC934\\uC950\\uC96C\\uC988\\uC9A4\\uC9C0\\uC9DC\\uC9F8\\uCA14\\uCA30\\uCA4C\\uCA68\\uCA84\\uCAA0\\uCABC\\uCAD8\\uCAF4\\uCB10\\uCB2C\\uCB48\\uCB64\\uCB80\\uCB9C\\uCBB8\\uCBD4\\uCBF0\\uCC0C\\uCC28\\uCC44\\uCC60\\uCC7C\\uCC98\\uCCB4\\uCCD0\\uCCEC\\uCD08\\uCD24\\uCD40\\uCD5C\\uCD78\\uCD94\\uCDB0\\uCDCC\\uCDE8\\uCE04\\uCE20\\uCE3C\\uCE58\\uCE74\\uCE90\\uCEAC\\uCEC8\\uCEE4\\uCF00\\uCF1C\\uCF38\\uCF54\\uCF70\\uCF8C\\uCFA8\\uCFC4\\uCFE0\\uCFFC\\uD018\\uD034\\uD050\\uD06C\\uD088\\uD0A4\\uD0C0\\uD0DC\\uD0F8\\uD114\\uD130\\uD14C\\uD168\\uD184\\uD1A0\\uD1BC\\uD1D8\\uD1F4\\uD210\\uD22C\\uD248\\uD264\\uD280\\uD29C\\uD2B8\\uD2D4\\uD2F0\\uD30C\\uD328\\uD344\\uD360\\uD37C\\uD398\\uD3B4\\uD3D0\\uD3EC\\uD408\\uD424\\uD440\\uD45C\\uD478\\uD494\\uD4B0\\uD4CC\\uD4E8\\uD504\\uD520\\uD53C\\uD558\\uD574\\uD590\\uD5AC\\uD5C8\\uD5E4\\uD600\\uD61C\\uD638\\uD654\\uD670\\uD68C\\uD6A8\\uD6C4\\uD6E0\\uD6FC\\uD718\\uD734\\uD750\\uD76C\\uD788]$/u\nconst reLVT = /^[\\uAC01-\\uAC1B\\uAC1D-\\uAC37\\uAC39-\\uAC53\\uAC55-\\uAC6F\\uAC71-\\uAC8B\\uAC8D-\\uACA7\\uACA9-\\uACC3\\uACC5-\\uACDF\\uACE1-\\uACFB\\uACFD-\\uAD17\\uAD19-\\uAD33\\uAD35-\\uAD4F\\uAD51-\\uAD6B\\uAD6D-\\uAD87\\uAD89-\\uADA3\\uADA5-\\uADBF\\uADC1-\\uADDB\\uADDD-\\uADF7\\uADF9-\\uAE13\\uAE15-\\uAE2F\\uAE31-\\uAE4B\\uAE4D-\\uAE67\\uAE69-\\uAE83\\uAE85-\\uAE9F\\uAEA1-\\uAEBB\\uAEBD-\\uAED7\\uAED9-\\uAEF3\\uAEF5-\\uAF0F\\uAF11-\\uAF2B\\uAF2D-\\uAF47\\uAF49-\\uAF63\\uAF65-\\uAF7F\\uAF81-\\uAF9B\\uAF9D-\\uAFB7\\uAFB9-\\uAFD3\\uAFD5-\\uAFEF\\uAFF1-\\uB00B\\uB00D-\\uB027\\uB029-\\uB043\\uB045-\\uB05F\\uB061-\\uB07B\\uB07D-\\uB097\\uB099-\\uB0B3\\uB0B5-\\uB0CF\\uB0D1-\\uB0EB\\uB0ED-\\uB107\\uB109-\\uB123\\uB125-\\uB13F\\uB141-\\uB15B\\uB15D-\\uB177\\uB179-\\uB193\\uB195-\\uB1AF\\uB1B1-\\uB1CB\\uB1CD-\\uB1E7\\uB1E9-\\uB203\\uB205-\\uB21F\\uB221-\\uB23B\\uB23D-\\uB257\\uB259-\\uB273\\uB275-\\uB28F\\uB291-\\uB2AB\\uB2AD-\\uB2C7\\uB2C9-\\uB2E3\\uB2E5-\\uB2FF\\uB301-\\uB31B\\uB31D-\\uB337\\uB339-\\uB353\\uB355-\\uB36F\\uB371-\\uB38B\\uB38D-\\uB3A7\\uB3A9-\\uB3C3\\uB3C5-\\uB3DF\\uB3E1-\\uB3FB\\uB3FD-\\uB417\\uB419-\\uB433\\uB435-\\uB44F\\uB451-\\uB46B\\uB46D-\\uB487\\uB489-\\uB4A3\\uB4A5-\\uB4BF\\uB4C1-\\uB4DB\\uB4DD-\\uB4F7\\uB4F9-\\uB513\\uB515-\\uB52F\\uB531-\\uB54B\\uB54D-\\uB567\\uB569-\\uB583\\uB585-\\uB59F\\uB5A1-\\uB5BB\\uB5BD-\\uB5D7\\uB5D9-\\uB5F3\\uB5F5-\\uB60F\\uB611-\\uB62B\\uB62D-\\uB647\\uB649-\\uB663\\uB665-\\uB67F\\uB681-\\uB69B\\uB69D-\\uB6B7\\uB6B9-\\uB6D3\\uB6D5-\\uB6EF\\uB6F1-\\uB70B\\uB70D-\\uB727\\uB729-\\uB743\\uB745-\\uB75F\\uB761-\\uB77B\\uB77D-\\uB797\\uB799-\\uB7B3\\uB7B5-\\uB7CF\\uB7D1-\\uB7EB\\uB7ED-\\uB807\\uB809-\\uB823\\uB825-\\uB83F\\uB841-\\uB85B\\uB85D-\\uB877\\uB879-\\uB893\\uB895-\\uB8AF\\uB8B1-\\uB8CB\\uB8CD-\\uB8E7\\uB8E9-\\uB903\\uB905-\\uB91F\\uB921-\\uB93B\\uB93D-\\uB957\\uB959-\\uB973\\uB975-\\uB98F\\uB991-\\uB9AB\\uB9AD-\\uB9C7\\uB9C9-\\uB9E3\\uB9E5-\\uB9FF\\uBA01-\\uBA1B\\uBA1D-\\uBA37\\uBA39-\\uBA53\\uBA55-\\uBA6F\\uBA71-\\uBA8B\\uBA8D-\\uBAA7\\uBAA9-\\uBAC3\\uBAC5-\\uBADF\\uBAE1-\\uBAFB\\uBAFD-\\uBB17\\uBB19-\\uBB33\\uBB35-\\uBB4F\\uBB51-\\uBB6B\\uBB6D-\\uBB87\\uBB89-\\uBBA3\\uBBA5-\\uBBBF\\uBBC1-\\uBBDB\\uBBDD-\\uBBF7\\uBBF9-\\uBC13\\uBC15-\\uBC2F\\uBC31-\\uBC4B\\uBC4D-\\uBC67\\uBC69-\\uBC83\\uBC85-\\uBC9F\\uBCA1-\\uBCBB\\uBCBD-\\uBCD7\\uBCD9-\\uBCF3\\uBCF5-\\uBD0F\\uBD11-\\uBD2B\\uBD2D-\\uBD47\\uBD49-\\uBD63\\uBD65-\\uBD7F\\uBD81-\\uBD9B\\uBD9D-\\uBDB7\\uBDB9-\\uBDD3\\uBDD5-\\uBDEF\\uBDF1-\\uBE0B\\uBE0D-\\uBE27\\uBE29-\\uBE43\\uBE45-\\uBE5F\\uBE61-\\uBE7B\\uBE7D-\\uBE97\\uBE99-\\uBEB3\\uBEB5-\\uBECF\\uBED1-\\uBEEB\\uBEED-\\uBF07\\uBF09-\\uBF23\\uBF25-\\uBF3F\\uBF41-\\uBF5B\\uBF5D-\\uBF77\\uBF79-\\uBF93\\uBF95-\\uBFAF\\uBFB1-\\uBFCB\\uBFCD-\\uBFE7\\uBFE9-\\uC003\\uC005-\\uC01F\\uC021-\\uC03B\\uC03D-\\uC057\\uC059-\\uC073\\uC075-\\uC08F\\uC091-\\uC0AB\\uC0AD-\\uC0C7\\uC0C9-\\uC0E3\\uC0E5-\\uC0FF\\uC101-\\uC11B\\uC11D-\\uC137\\uC139-\\uC153\\uC155-\\uC16F\\uC171-\\uC18B\\uC18D-\\uC1A7\\uC1A9-\\uC1C3\\uC1C5-\\uC1DF\\uC1E1-\\uC1FB\\uC1FD-\\uC217\\uC219-\\uC233\\uC235-\\uC24F\\uC251-\\uC26B\\uC26D-\\uC287\\uC289-\\uC2A3\\uC2A5-\\uC2BF\\uC2C1-\\uC2DB\\uC2DD-\\uC2F7\\uC2F9-\\uC313\\uC315-\\uC32F\\uC331-\\uC34B\\uC34D-\\uC367\\uC369-\\uC383\\uC385-\\uC39F\\uC3A1-\\uC3BB\\uC3BD-\\uC3D7\\uC3D9-\\uC3F3\\uC3F5-\\uC40F\\uC411-\\uC42B\\uC42D-\\uC447\\uC449-\\uC463\\uC465-\\uC47F\\uC481-\\uC49B\\uC49D-\\uC4B7\\uC4B9-\\uC4D3\\uC4D5-\\uC4EF\\uC4F1-\\uC50B\\uC50D-\\uC527\\uC529-\\uC543\\uC545-\\uC55F\\uC561-\\uC57B\\uC57D-\\uC597\\uC599-\\uC5B3\\uC5B5-\\uC5CF\\uC5D1-\\uC5EB\\uC5ED-\\uC607\\uC609-\\uC623\\uC625-\\uC63F\\uC641-\\uC65B\\uC65D-\\uC677\\uC679-\\uC693\\uC695-\\uC6AF\\uC6B1-\\uC6CB\\uC6CD-\\uC6E7\\uC6E9-\\uC703\\uC705-\\uC71F\\uC721-\\uC73B\\uC73D-\\uC757\\uC759-\\uC773\\uC775-\\uC78F\\uC791-\\uC7AB\\uC7AD-\\uC7C7\\uC7C9-\\uC7E3\\uC7E5-\\uC7FF\\uC801-\\uC81B\\uC81D-\\uC837\\uC839-\\uC853\\uC855-\\uC86F\\uC871-\\uC88B\\uC88D-\\uC8A7\\uC8A9-\\uC8C3\\uC8C5-\\uC8DF\\uC8E1-\\uC8FB\\uC8FD-\\uC917\\uC919-\\uC933\\uC935-\\uC94F\\uC951-\\uC96B\\uC96D-\\uC987\\uC989-\\uC9A3\\uC9A5-\\uC9BF\\uC9C1-\\uC9DB\\uC9DD-\\uC9F7\\uC9F9-\\uCA13\\uCA15-\\uCA2F\\uCA31-\\uCA4B\\uCA4D-\\uCA67\\uCA69-\\uCA83\\uCA85-\\uCA9F\\uCAA1-\\uCABB\\uCABD-\\uCAD7\\uCAD9-\\uCAF3\\uCAF5-\\uCB0F\\uCB11-\\uCB2B\\uCB2D-\\uCB47\\uCB49-\\uCB63\\uCB65-\\uCB7F\\uCB81-\\uCB9B\\uCB9D-\\uCBB7\\uCBB9-\\uCBD3\\uCBD5-\\uCBEF\\uCBF1-\\uCC0B\\uCC0D-\\uCC27\\uCC29-\\uCC43\\uCC45-\\uCC5F\\uCC61-\\uCC7B\\uCC7D-\\uCC97\\uCC99-\\uCCB3\\uCCB5-\\uCCCF\\uCCD1-\\uCCEB\\uCCED-\\uCD07\\uCD09-\\uCD23\\uCD25-\\uCD3F\\uCD41-\\uCD5B\\uCD5D-\\uCD77\\uCD79-\\uCD93\\uCD95-\\uCDAF\\uCDB1-\\uCDCB\\uCDCD-\\uCDE7\\uCDE9-\\uCE03\\uCE05-\\uCE1F\\uCE21-\\uCE3B\\uCE3D-\\uCE57\\uCE59-\\uCE73\\uCE75-\\uCE8F\\uCE91-\\uCEAB\\uCEAD-\\uCEC7\\uCEC9-\\uCEE3\\uCEE5-\\uCEFF\\uCF01-\\uCF1B\\uCF1D-\\uCF37\\uCF39-\\uCF53\\uCF55-\\uCF6F\\uCF71-\\uCF8B\\uCF8D-\\uCFA7\\uCFA9-\\uCFC3\\uCFC5-\\uCFDF\\uCFE1-\\uCFFB\\uCFFD-\\uD017\\uD019-\\uD033\\uD035-\\uD04F\\uD051-\\uD06B\\uD06D-\\uD087\\uD089-\\uD0A3\\uD0A5-\\uD0BF\\uD0C1-\\uD0DB\\uD0DD-\\uD0F7\\uD0F9-\\uD113\\uD115-\\uD12F\\uD131-\\uD14B\\uD14D-\\uD167\\uD169-\\uD183\\uD185-\\uD19F\\uD1A1-\\uD1BB\\uD1BD-\\uD1D7\\uD1D9-\\uD1F3\\uD1F5-\\uD20F\\uD211-\\uD22B\\uD22D-\\uD247\\uD249-\\uD263\\uD265-\\uD27F\\uD281-\\uD29B\\uD29D-\\uD2B7\\uD2B9-\\uD2D3\\uD2D5-\\uD2EF\\uD2F1-\\uD30B\\uD30D-\\uD327\\uD329-\\uD343\\uD345-\\uD35F\\uD361-\\uD37B\\uD37D-\\uD397\\uD399-\\uD3B3\\uD3B5-\\uD3CF\\uD3D1-\\uD3EB\\uD3ED-\\uD407\\uD409-\\uD423\\uD425-\\uD43F\\uD441-\\uD45B\\uD45D-\\uD477\\uD479-\\uD493\\uD495-\\uD4AF\\uD4B1-\\uD4CB\\uD4CD-\\uD4E7\\uD4E9-\\uD503\\uD505-\\uD51F\\uD521-\\uD53B\\uD53D-\\uD557\\uD559-\\uD573\\uD575-\\uD58F\\uD591-\\uD5AB\\uD5AD-\\uD5C7\\uD5C9-\\uD5E3\\uD5E5-\\uD5FF\\uD601-\\uD61B\\uD61D-\\uD637\\uD639-\\uD653\\uD655-\\uD66F\\uD671-\\uD68B\\uD68D-\\uD6A7\\uD6A9-\\uD6C3\\uD6C5-\\uD6DF\\uD6E1-\\uD6FB\\uD6FD-\\uD717\\uD719-\\uD733\\uD735-\\uD74F\\uD751-\\uD76B\\uD76D-\\uD787\\uD789-\\uD7A3]$/u\nconst reExtPict = /^\\p{ExtPict}$/u\n\nconst getCodepointType = (char: string, code: number): CodepointType => {\n  let type = CodepointType.Any\n  if (char.search(reExtend) !== -1) {\n    type |= CodepointType.Extend\n  }\n  if (code === 0x200d) {\n    type |= CodepointType.ZWJ\n  }\n  if (code >= 0x1f1e6 && code <= 0x1f1ff) {\n    type |= CodepointType.RI\n  }\n  if (char.search(rePrepend) !== -1) {\n    type |= CodepointType.Prepend\n  }\n  if (char.search(reSpacingMark) !== -1) {\n    type |= CodepointType.SpacingMark\n  }\n  if (char.search(reL) !== -1) {\n    type |= CodepointType.L\n  }\n  if (char.search(reV) !== -1) {\n    type |= CodepointType.V\n  }\n  if (char.search(reT) !== -1) {\n    type |= CodepointType.T\n  }\n  if (char.search(reLV) !== -1) {\n    type |= CodepointType.LV\n  }\n  if (char.search(reLVT) !== -1) {\n    type |= CodepointType.LVT\n  }\n  if (char.search(reExtPict) !== -1) {\n    type |= CodepointType.ExtPict\n  }\n\n  return type\n}\n\nfunction intersects(x: CodepointType, y: CodepointType) {\n  return (x & y) !== 0\n}\n\nconst NonBoundaryPairs: [CodepointType, CodepointType][] = [\n  // GB6\n  [\n    CodepointType.L,\n    CodepointType.L | CodepointType.V | CodepointType.LV | CodepointType.LVT,\n  ],\n  // GB7\n  [CodepointType.LV | CodepointType.V, CodepointType.V | CodepointType.T],\n  // GB8\n  [CodepointType.LVT | CodepointType.T, CodepointType.T],\n  // GB9\n  [CodepointType.Any, CodepointType.Extend | CodepointType.ZWJ],\n  // GB9a\n  [CodepointType.Any, CodepointType.SpacingMark],\n  // GB9b\n  [CodepointType.Prepend, CodepointType.Any],\n  // GB11\n  [CodepointType.ZWJ, CodepointType.ExtPict],\n  // GB12 and GB13\n  [CodepointType.RI, CodepointType.RI],\n]\n\nfunction isBoundaryPair(left: CodepointType, right: CodepointType) {\n  return (\n    NonBoundaryPairs.findIndex(\n      r => intersects(left, r[0]) && intersects(right, r[1])\n    ) === -1\n  )\n}\n\nconst endingEmojiZWJ = /\\p{ExtPict}[\\p{Gr_Ext}\\p{EMod}]*\\u200D$/u\nconst endsWithEmojiZWJ = (str: string): boolean => {\n  return str.search(endingEmojiZWJ) !== -1\n}\n\nconst endingRIs = /\\p{RI}+$/gu\nconst endsWithOddNumberOfRIs = (str: string): boolean => {\n  const match = str.match(endingRIs)\n  if (match === null) {\n    return false\n  } else {\n    // A RI is represented by a surrogate pair.\n    const numRIs = match[0].length / 2\n    return numRIs % 2 === 1\n  }\n}\n", "import { isPlainObject } from 'is-plain-object'\nimport { Editor, Node, Path, Descendant, ExtendedType, Ances<PERSON> } from '..'\n\n/**\n * `Element` objects are a type of node in a Slate document that contain other\n * element nodes or text nodes. They can be either \"blocks\" or \"inlines\"\n * depending on the Slate editor's configuration.\n */\n\nexport interface BaseElement {\n  children: Descendant[]\n}\n\nexport type Element = ExtendedType<'Element', BaseElement>\n\nexport interface ElementInterface {\n  isAncestor: (value: any) => value is Ancestor\n  isElement: (value: any) => value is Element\n  isElementList: (value: any) => value is Element[]\n  isElementProps: (props: any) => props is Partial<Element>\n  isElementType: <T extends Element>(\n    value: any,\n    elementVal: string,\n    elementKey?: string\n  ) => value is T\n  matches: (element: Element, props: Partial<Element>) => boolean\n}\n\n/**\n * Shared the function with isElementType utility\n */\nconst isElement = (value: any): value is Element => {\n  return (\n    isPlainObject(value) &&\n    Node.isNodeList(value.children) &&\n    !Editor.isEditor(value)\n  )\n}\n\nexport const Element: ElementInterface = {\n  /**\n   * Check if a value implements the 'Ancestor' interface.\n   */\n\n  isAncestor(value: any): value is Ancestor {\n    return isPlainObject(value) && Node.isNodeList(value.children)\n  },\n\n  /**\n   * Check if a value implements the `Element` interface.\n   */\n\n  isElement,\n  /**\n   * Check if a value is an array of `Element` objects.\n   */\n\n  isElementList(value: any): value is Element[] {\n    return Array.isArray(value) && value.every(val => Element.isElement(val))\n  },\n\n  /**\n   * Check if a set of props is a partial of Element.\n   */\n\n  isElementProps(props: any): props is Partial<Element> {\n    return (props as Partial<Element>).children !== undefined\n  },\n\n  /**\n   * Check if a value implements the `Element` interface and has elementKey with selected value.\n   * Default it check to `type` key value\n   */\n\n  isElementType: <T extends Element>(\n    value: any,\n    elementVal: string,\n    elementKey: string = 'type'\n  ): value is T => {\n    return isElement(value) && value[elementKey] === elementVal\n  },\n\n  /**\n   * Check if an element matches set of properties.\n   *\n   * Note: this checks custom properties, and it does not ensure that any\n   * children are equivalent.\n   */\n\n  matches(element: Element, props: Partial<Element>): boolean {\n    for (const key in props) {\n      if (key === 'children') {\n        continue\n      }\n\n      if (element[key] !== props[key]) {\n        return false\n      }\n    }\n\n    return true\n  },\n}\n\n/**\n * `ElementEntry` objects refer to an `Element` and the `Path` where it can be\n * found inside a root node.\n */\n\nexport type ElementEntry = [Element, Path]\n", "import { isPlainObject } from 'is-plain-object'\n\nimport {\n  Ances<PERSON>,\n  ExtendedType,\n  Location,\n  Node,\n  NodeEntry,\n  Operation,\n  Path,\n  PathRef,\n  Point,\n  PointRef,\n  Range,\n  RangeRef,\n  Span,\n  Text,\n  Transforms,\n} from '..'\nimport {\n  DIRTY_PATHS,\n  DIRTY_PATH_KEYS,\n  NORMALIZING,\n  PATH_REFS,\n  POINT_REFS,\n  RANGE_REFS,\n} from '../utils/weak-maps'\nimport {\n  getWordDistance,\n  getCharacterDistance,\n  splitByCharacterDistance,\n} from '../utils/string'\nimport { Descendant } from './node'\nimport { Element } from './element'\n\nexport type BaseSelection = Range | null\n\nexport type Selection = ExtendedType<'Selection', BaseSelection>\n\n/**\n * The `Editor` interface stores all the state of a Slate editor. It is extended\n * by plugins that wish to add their own helpers and implement new behaviors.\n */\n\nexport interface BaseEditor {\n  children: Descendant[]\n  selection: Selection\n  operations: Operation[]\n  marks: Omit<Text, 'text'> | null\n\n  // Schema-specific node behaviors.\n  isInline: (element: Element) => boolean\n  isVoid: (element: Element) => boolean\n  normalizeNode: (entry: NodeEntry) => void\n  onChange: () => void\n\n  // Overrideable core actions.\n  addMark: (key: string, value: any) => void\n  apply: (operation: Operation) => void\n  deleteBackward: (unit: 'character' | 'word' | 'line' | 'block') => void\n  deleteForward: (unit: 'character' | 'word' | 'line' | 'block') => void\n  deleteFragment: (direction?: 'forward' | 'backward') => void\n  getFragment: () => Descendant[]\n  insertBreak: () => void\n  insertFragment: (fragment: Node[]) => void\n  insertNode: (node: Node) => void\n  insertText: (text: string) => void\n  removeMark: (key: string) => void\n}\n\nexport type Editor = ExtendedType<'Editor', BaseEditor>\n\nexport interface EditorInterface {\n  above: <T extends Ancestor>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<T> | undefined\n  addMark: (editor: Editor, key: string, value: any) => void\n  after: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    }\n  ) => Point | undefined\n  before: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    }\n  ) => Point | undefined\n  deleteBackward: (\n    editor: Editor,\n    options?: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    }\n  ) => void\n  deleteForward: (\n    editor: Editor,\n    options?: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    }\n  ) => void\n  deleteFragment: (\n    editor: Editor,\n    options?: {\n      direction?: 'forward' | 'backward'\n    }\n  ) => void\n  edges: (editor: Editor, at: Location) => [Point, Point]\n  end: (editor: Editor, at: Location) => Point\n  first: (editor: Editor, at: Location) => NodeEntry\n  fragment: (editor: Editor, at: Location) => Descendant[]\n  hasBlocks: (editor: Editor, element: Element) => boolean\n  hasInlines: (editor: Editor, element: Element) => boolean\n  hasPath: (editor: Editor, path: Path) => boolean\n  hasTexts: (editor: Editor, element: Element) => boolean\n  insertBreak: (editor: Editor) => void\n  insertFragment: (editor: Editor, fragment: Node[]) => void\n  insertNode: (editor: Editor, node: Node) => void\n  insertText: (editor: Editor, text: string) => void\n  isBlock: (editor: Editor, value: any) => value is Element\n  isEditor: (value: any) => value is Editor\n  isEnd: (editor: Editor, point: Point, at: Location) => boolean\n  isEdge: (editor: Editor, point: Point, at: Location) => boolean\n  isEmpty: (editor: Editor, element: Element) => boolean\n  isInline: (editor: Editor, value: any) => value is Element\n  isNormalizing: (editor: Editor) => boolean\n  isStart: (editor: Editor, point: Point, at: Location) => boolean\n  isVoid: (editor: Editor, value: any) => value is Element\n  last: (editor: Editor, at: Location) => NodeEntry\n  leaf: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => NodeEntry<Text>\n  levels: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      reverse?: boolean\n      voids?: boolean\n    }\n  ) => Generator<NodeEntry<T>, void, undefined>\n  marks: (editor: Editor) => Omit<Text, 'text'> | null\n  next: <T extends Descendant>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<T> | undefined\n  node: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => NodeEntry\n  nodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location | Span\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      universal?: boolean\n      reverse?: boolean\n      voids?: boolean\n    }\n  ) => Generator<NodeEntry<T>, void, undefined>\n  normalize: (\n    editor: Editor,\n    options?: {\n      force?: boolean\n    }\n  ) => void\n  parent: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => NodeEntry<Ancestor>\n  path: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => Path\n  pathRef: (\n    editor: Editor,\n    path: Path,\n    options?: {\n      affinity?: 'backward' | 'forward' | null\n    }\n  ) => PathRef\n  pathRefs: (editor: Editor) => Set<PathRef>\n  point: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      edge?: 'start' | 'end'\n    }\n  ) => Point\n  pointRef: (\n    editor: Editor,\n    point: Point,\n    options?: {\n      affinity?: 'backward' | 'forward' | null\n    }\n  ) => PointRef\n  pointRefs: (editor: Editor) => Set<PointRef>\n  positions: (\n    editor: Editor,\n    options?: {\n      at?: Location\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      voids?: boolean\n    }\n  ) => Generator<Point, void, undefined>\n  previous: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<T> | undefined\n  range: (editor: Editor, at: Location, to?: Location) => Range\n  rangeRef: (\n    editor: Editor,\n    range: Range,\n    options?: {\n      affinity?: 'backward' | 'forward' | 'outward' | 'inward' | null\n    }\n  ) => RangeRef\n  rangeRefs: (editor: Editor) => Set<RangeRef>\n  removeMark: (editor: Editor, key: string) => void\n  setNormalizing: (editor: Editor, isNormalizing: boolean) => void\n  start: (editor: Editor, at: Location) => Point\n  string: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      voids?: boolean\n    }\n  ) => string\n  unhangRange: (\n    editor: Editor,\n    range: Range,\n    options?: {\n      voids?: boolean\n    }\n  ) => Range\n  void: (\n    editor: Editor,\n    options?: {\n      at?: Location\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<Element> | undefined\n  withoutNormalizing: (editor: Editor, fn: () => void) => void\n}\n\nconst IS_EDITOR_CACHE = new WeakMap<object, boolean>()\n\nexport const Editor: EditorInterface = {\n  /**\n   * Get the ancestor above a location in the document.\n   */\n\n  above<T extends Ancestor>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<T> | undefined {\n    const {\n      voids = false,\n      mode = 'lowest',\n      at = editor.selection,\n      match,\n    } = options\n\n    if (!at) {\n      return\n    }\n\n    const path = Editor.path(editor, at)\n    const reverse = mode === 'lowest'\n\n    for (const [n, p] of Editor.levels(editor, {\n      at: path,\n      voids,\n      match,\n      reverse,\n    })) {\n      if (!Text.isText(n) && !Path.equals(path, p)) {\n        return [n, p]\n      }\n    }\n  },\n\n  /**\n   * Add a custom property to the leaf text nodes in the current selection.\n   *\n   * If the selection is currently collapsed, the marks will be added to the\n   * `editor.marks` property instead, and applied when text is inserted next.\n   */\n\n  addMark(editor: Editor, key: string, value: any): void {\n    editor.addMark(key, value)\n  },\n\n  /**\n   * Get the point after a location.\n   */\n\n  after(\n    editor: Editor,\n    at: Location,\n    options: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    } = {}\n  ): Point | undefined {\n    const anchor = Editor.point(editor, at, { edge: 'end' })\n    const focus = Editor.end(editor, [])\n    const range = { anchor, focus }\n    const { distance = 1 } = options\n    let d = 0\n    let target\n\n    for (const p of Editor.positions(editor, {\n      ...options,\n      at: range,\n    })) {\n      if (d > distance) {\n        break\n      }\n\n      if (d !== 0) {\n        target = p\n      }\n\n      d++\n    }\n\n    return target\n  },\n\n  /**\n   * Get the point before a location.\n   */\n\n  before(\n    editor: Editor,\n    at: Location,\n    options: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    } = {}\n  ): Point | undefined {\n    const anchor = Editor.start(editor, [])\n    const focus = Editor.point(editor, at, { edge: 'start' })\n    const range = { anchor, focus }\n    const { distance = 1 } = options\n    let d = 0\n    let target\n\n    for (const p of Editor.positions(editor, {\n      ...options,\n      at: range,\n      reverse: true,\n    })) {\n      if (d > distance) {\n        break\n      }\n\n      if (d !== 0) {\n        target = p\n      }\n\n      d++\n    }\n\n    return target\n  },\n\n  /**\n   * Delete content in the editor backward from the current selection.\n   */\n\n  deleteBackward(\n    editor: Editor,\n    options: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    } = {}\n  ): void {\n    const { unit = 'character' } = options\n    editor.deleteBackward(unit)\n  },\n\n  /**\n   * Delete content in the editor forward from the current selection.\n   */\n\n  deleteForward(\n    editor: Editor,\n    options: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    } = {}\n  ): void {\n    const { unit = 'character' } = options\n    editor.deleteForward(unit)\n  },\n\n  /**\n   * Delete the content in the current selection.\n   */\n\n  deleteFragment(\n    editor: Editor,\n    options: {\n      direction?: 'forward' | 'backward'\n    } = {}\n  ): void {\n    const { direction = 'forward' } = options\n    editor.deleteFragment(direction)\n  },\n\n  /**\n   * Get the start and end points of a location.\n   */\n\n  edges(editor: Editor, at: Location): [Point, Point] {\n    return [Editor.start(editor, at), Editor.end(editor, at)]\n  },\n\n  /**\n   * Get the end point of a location.\n   */\n\n  end(editor: Editor, at: Location): Point {\n    return Editor.point(editor, at, { edge: 'end' })\n  },\n\n  /**\n   * Get the first node at a location.\n   */\n\n  first(editor: Editor, at: Location): NodeEntry {\n    const path = Editor.path(editor, at, { edge: 'start' })\n    return Editor.node(editor, path)\n  },\n\n  /**\n   * Get the fragment at a location.\n   */\n\n  fragment(editor: Editor, at: Location): Descendant[] {\n    const range = Editor.range(editor, at)\n    const fragment = Node.fragment(editor, range)\n    return fragment\n  },\n  /**\n   * Check if a node has block children.\n   */\n\n  hasBlocks(editor: Editor, element: Element): boolean {\n    return element.children.some(n => Editor.isBlock(editor, n))\n  },\n\n  /**\n   * Check if a node has inline and text children.\n   */\n\n  hasInlines(editor: Editor, element: Element): boolean {\n    return element.children.some(\n      n => Text.isText(n) || Editor.isInline(editor, n)\n    )\n  },\n\n  /**\n   * Check if a node has text children.\n   */\n\n  hasTexts(editor: Editor, element: Element): boolean {\n    return element.children.every(n => Text.isText(n))\n  },\n\n  /**\n   * Insert a block break at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertBreak(editor: Editor): void {\n    editor.insertBreak()\n  },\n\n  /**\n   * Insert a fragment at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertFragment(editor: Editor, fragment: Node[]): void {\n    editor.insertFragment(fragment)\n  },\n\n  /**\n   * Insert a node at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertNode(editor: Editor, node: Node): void {\n    editor.insertNode(node)\n  },\n\n  /**\n   * Insert text at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertText(editor: Editor, text: string): void {\n    editor.insertText(text)\n  },\n\n  /**\n   * Check if a value is a block `Element` object.\n   */\n\n  isBlock(editor: Editor, value: any): value is Element {\n    return Element.isElement(value) && !editor.isInline(value)\n  },\n\n  /**\n   * Check if a value is an `Editor` object.\n   */\n\n  isEditor(value: any): value is Editor {\n    if (!isPlainObject(value)) return false\n    const cachedIsEditor = IS_EDITOR_CACHE.get(value)\n    if (cachedIsEditor !== undefined) {\n      return cachedIsEditor\n    }\n    const isEditor =\n      typeof value.addMark === 'function' &&\n      typeof value.apply === 'function' &&\n      typeof value.deleteBackward === 'function' &&\n      typeof value.deleteForward === 'function' &&\n      typeof value.deleteFragment === 'function' &&\n      typeof value.insertBreak === 'function' &&\n      typeof value.insertFragment === 'function' &&\n      typeof value.insertNode === 'function' &&\n      typeof value.insertText === 'function' &&\n      typeof value.isInline === 'function' &&\n      typeof value.isVoid === 'function' &&\n      typeof value.normalizeNode === 'function' &&\n      typeof value.onChange === 'function' &&\n      typeof value.removeMark === 'function' &&\n      (value.marks === null || isPlainObject(value.marks)) &&\n      (value.selection === null || Range.isRange(value.selection)) &&\n      Node.isNodeList(value.children) &&\n      Operation.isOperationList(value.operations)\n    IS_EDITOR_CACHE.set(value, isEditor)\n    return isEditor\n  },\n\n  /**\n   * Check if a point is the end point of a location.\n   */\n\n  isEnd(editor: Editor, point: Point, at: Location): boolean {\n    const end = Editor.end(editor, at)\n    return Point.equals(point, end)\n  },\n\n  /**\n   * Check if a point is an edge of a location.\n   */\n\n  isEdge(editor: Editor, point: Point, at: Location): boolean {\n    return Editor.isStart(editor, point, at) || Editor.isEnd(editor, point, at)\n  },\n\n  /**\n   * Check if an element is empty, accounting for void nodes.\n   */\n\n  isEmpty(editor: Editor, element: Element): boolean {\n    const { children } = element\n    const [first] = children\n    return (\n      children.length === 0 ||\n      (children.length === 1 &&\n        Text.isText(first) &&\n        first.text === '' &&\n        !editor.isVoid(element))\n    )\n  },\n\n  /**\n   * Check if a value is an inline `Element` object.\n   */\n\n  isInline(editor: Editor, value: any): value is Element {\n    return Element.isElement(value) && editor.isInline(value)\n  },\n\n  /**\n   * Check if the editor is currently normalizing after each operation.\n   */\n\n  isNormalizing(editor: Editor): boolean {\n    const isNormalizing = NORMALIZING.get(editor)\n    return isNormalizing === undefined ? true : isNormalizing\n  },\n\n  /**\n   * Check if a point is the start point of a location.\n   */\n\n  isStart(editor: Editor, point: Point, at: Location): boolean {\n    // PERF: If the offset isn't `0` we know it's not the start.\n    if (point.offset !== 0) {\n      return false\n    }\n\n    const start = Editor.start(editor, at)\n    return Point.equals(point, start)\n  },\n\n  /**\n   * Check if a value is a void `Element` object.\n   */\n\n  isVoid(editor: Editor, value: any): value is Element {\n    return Element.isElement(value) && editor.isVoid(value)\n  },\n\n  /**\n   * Get the last node at a location.\n   */\n\n  last(editor: Editor, at: Location): NodeEntry {\n    const path = Editor.path(editor, at, { edge: 'end' })\n    return Editor.node(editor, path)\n  },\n\n  /**\n   * Get the leaf text node at a location.\n   */\n\n  leaf(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): NodeEntry<Text> {\n    const path = Editor.path(editor, at, options)\n    const node = Node.leaf(editor, path)\n    return [node, path]\n  },\n\n  /**\n   * Iterate through all of the levels at a location.\n   */\n\n  *levels<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      reverse?: boolean\n      voids?: boolean\n    } = {}\n  ): Generator<NodeEntry<T>, void, undefined> {\n    const { at = editor.selection, reverse = false, voids = false } = options\n    let { match } = options\n\n    if (match == null) {\n      match = () => true\n    }\n\n    if (!at) {\n      return\n    }\n\n    const levels: NodeEntry<T>[] = []\n    const path = Editor.path(editor, at)\n\n    for (const [n, p] of Node.levels(editor, path)) {\n      if (!match(n, p)) {\n        continue\n      }\n\n      levels.push([n, p])\n\n      if (!voids && Editor.isVoid(editor, n)) {\n        break\n      }\n    }\n\n    if (reverse) {\n      levels.reverse()\n    }\n\n    yield* levels\n  },\n\n  /**\n   * Get the marks that would be added to text at the current selection.\n   */\n\n  marks(editor: Editor): Omit<Text, 'text'> | null {\n    const { marks, selection } = editor\n\n    if (!selection) {\n      return null\n    }\n\n    if (marks) {\n      return marks\n    }\n\n    if (Range.isExpanded(selection)) {\n      const [match] = Editor.nodes(editor, { match: Text.isText })\n\n      if (match) {\n        const [node] = match as NodeEntry<Text>\n        const { text, ...rest } = node\n        return rest\n      } else {\n        return {}\n      }\n    }\n\n    const { anchor } = selection\n    const { path } = anchor\n    let [node] = Editor.leaf(editor, path)\n\n    if (anchor.offset === 0) {\n      const prev = Editor.previous(editor, { at: path, match: Text.isText })\n      const block = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n      })\n\n      if (prev && block) {\n        const [prevNode, prevPath] = prev\n        const [, blockPath] = block\n\n        if (Path.isAncestor(blockPath, prevPath)) {\n          node = prevNode as Text\n        }\n      }\n    }\n\n    const { text, ...rest } = node\n    return rest\n  },\n\n  /**\n   * Get the matching node in the branch of the document after a location.\n   */\n\n  next<T extends Descendant>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<T> | undefined {\n    const { mode = 'lowest', voids = false } = options\n    let { match, at = editor.selection } = options\n\n    if (!at) {\n      return\n    }\n\n    const pointAfterLocation = Editor.after(editor, at, { voids })\n\n    if (!pointAfterLocation) return\n\n    const [, to] = Editor.last(editor, [])\n\n    const span: Span = [pointAfterLocation.path, to]\n\n    if (Path.isPath(at) && at.length === 0) {\n      throw new Error(`Cannot get the next node from the root node!`)\n    }\n\n    if (match == null) {\n      if (Path.isPath(at)) {\n        const [parent] = Editor.parent(editor, at)\n        match = n => parent.children.includes(n)\n      } else {\n        match = () => true\n      }\n    }\n\n    const [next] = Editor.nodes(editor, { at: span, match, mode, voids })\n    return next\n  },\n\n  /**\n   * Get the node at a location.\n   */\n\n  node(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): NodeEntry {\n    const path = Editor.path(editor, at, options)\n    const node = Node.get(editor, path)\n    return [node, path]\n  },\n\n  /**\n   * Iterate through all of the nodes in the Editor.\n   */\n\n  *nodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location | Span\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      universal?: boolean\n      reverse?: boolean\n      voids?: boolean\n    } = {}\n  ): Generator<NodeEntry<T>, void, undefined> {\n    const {\n      at = editor.selection,\n      mode = 'all',\n      universal = false,\n      reverse = false,\n      voids = false,\n    } = options\n    let { match } = options\n\n    if (!match) {\n      match = () => true\n    }\n\n    if (!at) {\n      return\n    }\n\n    let from\n    let to\n\n    if (Span.isSpan(at)) {\n      from = at[0]\n      to = at[1]\n    } else {\n      const first = Editor.path(editor, at, { edge: 'start' })\n      const last = Editor.path(editor, at, { edge: 'end' })\n      from = reverse ? last : first\n      to = reverse ? first : last\n    }\n\n    const nodeEntries = Node.nodes(editor, {\n      reverse,\n      from,\n      to,\n      pass: ([n]) => (voids ? false : Editor.isVoid(editor, n)),\n    })\n\n    const matches: NodeEntry<T>[] = []\n    let hit: NodeEntry<T> | undefined\n\n    for (const [node, path] of nodeEntries) {\n      const isLower = hit && Path.compare(path, hit[1]) === 0\n\n      // In highest mode any node lower than the last hit is not a match.\n      if (mode === 'highest' && isLower) {\n        continue\n      }\n\n      if (!match(node, path)) {\n        // If we've arrived at a leaf text node that is not lower than the last\n        // hit, then we've found a branch that doesn't include a match, which\n        // means the match is not universal.\n        if (universal && !isLower && Text.isText(node)) {\n          return\n        } else {\n          continue\n        }\n      }\n\n      // If there's a match and it's lower than the last, update the hit.\n      if (mode === 'lowest' && isLower) {\n        hit = [node, path]\n        continue\n      }\n\n      // In lowest mode we emit the last hit, once it's guaranteed lowest.\n      const emit: NodeEntry<T> | undefined =\n        mode === 'lowest' ? hit : [node, path]\n\n      if (emit) {\n        if (universal) {\n          matches.push(emit)\n        } else {\n          yield emit\n        }\n      }\n\n      hit = [node, path]\n    }\n\n    // Since lowest is always emitting one behind, catch up at the end.\n    if (mode === 'lowest' && hit) {\n      if (universal) {\n        matches.push(hit)\n      } else {\n        yield hit\n      }\n    }\n\n    // Universal defers to ensure that the match occurs in every branch, so we\n    // yield all of the matches after iterating.\n    if (universal) {\n      yield* matches\n    }\n  },\n  /**\n   * Normalize any dirty objects in the editor.\n   */\n\n  normalize(\n    editor: Editor,\n    options: {\n      force?: boolean\n    } = {}\n  ): void {\n    const { force = false } = options\n    const getDirtyPaths = (editor: Editor) => {\n      return DIRTY_PATHS.get(editor) || []\n    }\n\n    const getDirtyPathKeys = (editor: Editor) => {\n      return DIRTY_PATH_KEYS.get(editor) || new Set()\n    }\n\n    const popDirtyPath = (editor: Editor): Path => {\n      const path = getDirtyPaths(editor).pop()!\n      const key = path.join(',')\n      getDirtyPathKeys(editor).delete(key)\n      return path\n    }\n\n    if (!Editor.isNormalizing(editor)) {\n      return\n    }\n\n    if (force) {\n      const allPaths = Array.from(Node.nodes(editor), ([, p]) => p)\n      const allPathKeys = new Set(allPaths.map(p => p.join(',')))\n      DIRTY_PATHS.set(editor, allPaths)\n      DIRTY_PATH_KEYS.set(editor, allPathKeys)\n    }\n\n    if (getDirtyPaths(editor).length === 0) {\n      return\n    }\n\n    Editor.withoutNormalizing(editor, () => {\n      /*\n        Fix dirty elements with no children.\n        editor.normalizeNode() does fix this, but some normalization fixes also require it to work.\n        Running an initial pass avoids the catch-22 race condition.\n      */\n      for (const dirtyPath of getDirtyPaths(editor)) {\n        if (Node.has(editor, dirtyPath)) {\n          const entry = Editor.node(editor, dirtyPath)\n          const [node, _] = entry\n\n          /*\n            The default normalizer inserts an empty text node in this scenario, but it can be customised.\n            So there is some risk here.\n\n            As long as the normalizer only inserts child nodes for this case it is safe to do in any order;\n            by definition adding children to an empty node can't cause other paths to change.\n          */\n          if (Element.isElement(node) && node.children.length === 0) {\n            editor.normalizeNode(entry)\n          }\n        }\n      }\n\n      const max = getDirtyPaths(editor).length * 42 // HACK: better way?\n      let m = 0\n\n      while (getDirtyPaths(editor).length !== 0) {\n        if (m > max) {\n          throw new Error(`\n            Could not completely normalize the editor after ${max} iterations! This is usually due to incorrect normalization logic that leaves a node in an invalid state.\n          `)\n        }\n\n        const dirtyPath = popDirtyPath(editor)\n\n        // If the node doesn't exist in the tree, it does not need to be normalized.\n        if (Node.has(editor, dirtyPath)) {\n          const entry = Editor.node(editor, dirtyPath)\n          editor.normalizeNode(entry)\n        }\n        m++\n      }\n    })\n  },\n\n  /**\n   * Get the parent node of a location.\n   */\n\n  parent(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): NodeEntry<Ancestor> {\n    const path = Editor.path(editor, at, options)\n    const parentPath = Path.parent(path)\n    const entry = Editor.node(editor, parentPath)\n    return entry as NodeEntry<Ancestor>\n  },\n\n  /**\n   * Get the path of a location.\n   */\n\n  path(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): Path {\n    const { depth, edge } = options\n\n    if (Path.isPath(at)) {\n      if (edge === 'start') {\n        const [, firstPath] = Node.first(editor, at)\n        at = firstPath\n      } else if (edge === 'end') {\n        const [, lastPath] = Node.last(editor, at)\n        at = lastPath\n      }\n    }\n\n    if (Range.isRange(at)) {\n      if (edge === 'start') {\n        at = Range.start(at)\n      } else if (edge === 'end') {\n        at = Range.end(at)\n      } else {\n        at = Path.common(at.anchor.path, at.focus.path)\n      }\n    }\n\n    if (Point.isPoint(at)) {\n      at = at.path\n    }\n\n    if (depth != null) {\n      at = at.slice(0, depth)\n    }\n\n    return at\n  },\n\n  hasPath(editor: Editor, path: Path): boolean {\n    return Node.has(editor, path)\n  },\n\n  /**\n   * Create a mutable ref for a `Path` object, which will stay in sync as new\n   * operations are applied to the editor.\n   */\n\n  pathRef(\n    editor: Editor,\n    path: Path,\n    options: {\n      affinity?: 'backward' | 'forward' | null\n    } = {}\n  ): PathRef {\n    const { affinity = 'forward' } = options\n    const ref: PathRef = {\n      current: path,\n      affinity,\n      unref() {\n        const { current } = ref\n        const pathRefs = Editor.pathRefs(editor)\n        pathRefs.delete(ref)\n        ref.current = null\n        return current\n      },\n    }\n\n    const refs = Editor.pathRefs(editor)\n    refs.add(ref)\n    return ref\n  },\n\n  /**\n   * Get the set of currently tracked path refs of the editor.\n   */\n\n  pathRefs(editor: Editor): Set<PathRef> {\n    let refs = PATH_REFS.get(editor)\n\n    if (!refs) {\n      refs = new Set()\n      PATH_REFS.set(editor, refs)\n    }\n\n    return refs\n  },\n\n  /**\n   * Get the start or end point of a location.\n   */\n\n  point(\n    editor: Editor,\n    at: Location,\n    options: {\n      edge?: 'start' | 'end'\n    } = {}\n  ): Point {\n    const { edge = 'start' } = options\n\n    if (Path.isPath(at)) {\n      let path\n\n      if (edge === 'end') {\n        const [, lastPath] = Node.last(editor, at)\n        path = lastPath\n      } else {\n        const [, firstPath] = Node.first(editor, at)\n        path = firstPath\n      }\n\n      const node = Node.get(editor, path)\n\n      if (!Text.isText(node)) {\n        throw new Error(\n          `Cannot get the ${edge} point in the node at path [${at}] because it has no ${edge} text node.`\n        )\n      }\n\n      return { path, offset: edge === 'end' ? node.text.length : 0 }\n    }\n\n    if (Range.isRange(at)) {\n      const [start, end] = Range.edges(at)\n      return edge === 'start' ? start : end\n    }\n\n    return at\n  },\n\n  /**\n   * Create a mutable ref for a `Point` object, which will stay in sync as new\n   * operations are applied to the editor.\n   */\n\n  pointRef(\n    editor: Editor,\n    point: Point,\n    options: {\n      affinity?: 'backward' | 'forward' | null\n    } = {}\n  ): PointRef {\n    const { affinity = 'forward' } = options\n    const ref: PointRef = {\n      current: point,\n      affinity,\n      unref() {\n        const { current } = ref\n        const pointRefs = Editor.pointRefs(editor)\n        pointRefs.delete(ref)\n        ref.current = null\n        return current\n      },\n    }\n\n    const refs = Editor.pointRefs(editor)\n    refs.add(ref)\n    return ref\n  },\n\n  /**\n   * Get the set of currently tracked point refs of the editor.\n   */\n\n  pointRefs(editor: Editor): Set<PointRef> {\n    let refs = POINT_REFS.get(editor)\n\n    if (!refs) {\n      refs = new Set()\n      POINT_REFS.set(editor, refs)\n    }\n\n    return refs\n  },\n\n  /**\n   * Return all the positions in `at` range where a `Point` can be placed.\n   *\n   * By default, moves forward by individual offsets at a time, but\n   * the `unit` option can be used to to move by character, word, line, or block.\n   *\n   * The `reverse` option can be used to change iteration direction.\n   *\n   * Note: By default void nodes are treated as a single point and iteration\n   * will not happen inside their content unless you pass in true for the\n   * `voids` option, then iteration will occur.\n   */\n\n  *positions(\n    editor: Editor,\n    options: {\n      at?: Location\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      voids?: boolean\n    } = {}\n  ): Generator<Point, void, undefined> {\n    const {\n      at = editor.selection,\n      unit = 'offset',\n      reverse = false,\n      voids = false,\n    } = options\n\n    if (!at) {\n      return\n    }\n\n    /**\n     * Algorithm notes:\n     *\n     * Each step `distance` is dynamic depending on the underlying text\n     * and the `unit` specified.  Each step, e.g., a line or word, may\n     * span multiple text nodes, so we iterate through the text both on\n     * two levels in step-sync:\n     *\n     * `leafText` stores the text on a text leaf level, and is advanced\n     * through using the counters `leafTextOffset` and `leafTextRemaining`.\n     *\n     * `blockText` stores the text on a block level, and is shortened\n     * by `distance` every time it is advanced.\n     *\n     * We only maintain a window of one blockText and one leafText because\n     * a block node always appears before all of its leaf nodes.\n     */\n\n    const range = Editor.range(editor, at)\n    const [start, end] = Range.edges(range)\n    const first = reverse ? end : start\n    let isNewBlock = false\n    let blockText = ''\n    let distance = 0 // Distance for leafText to catch up to blockText.\n    let leafTextRemaining = 0\n    let leafTextOffset = 0\n\n    // Iterate through all nodes in range, grabbing entire textual content\n    // of block nodes in blockText, and text nodes in leafText.\n    // Exploits the fact that nodes are sequenced in such a way that we first\n    // encounter the block node, then all of its text nodes, so when iterating\n    // through the blockText and leafText we just need to remember a window of\n    // one block node and leaf node, respectively.\n    for (const [node, path] of Editor.nodes(editor, { at, reverse, voids })) {\n      /*\n       * ELEMENT NODE - Yield position(s) for voids, collect blockText for blocks\n       */\n      if (Element.isElement(node)) {\n        // Void nodes are a special case, so by default we will always\n        // yield their first point. If the `voids` option is set to true,\n        // then we will iterate over their content.\n        if (!voids && editor.isVoid(node)) {\n          yield Editor.start(editor, path)\n          continue\n        }\n\n        // Inline element nodes are ignored as they don't themselves\n        // contribute to `blockText` or `leafText` - their parent and\n        // children do.\n        if (editor.isInline(node)) continue\n\n        // Block element node - set `blockText` to its text content.\n        if (Editor.hasInlines(editor, node)) {\n          // We always exhaust block nodes before encountering a new one:\n          //   console.assert(blockText === '',\n          //     `blockText='${blockText}' - `+\n          //     `not exhausted before new block node`, path)\n\n          // Ensure range considered is capped to `range`, in the\n          // start/end edge cases where block extends beyond range.\n          // Equivalent to this, but presumably more performant:\n          //   blockRange = Editor.range(editor, ...Editor.edges(editor, path))\n          //   blockRange = Range.intersection(range, blockRange) // intersect\n          //   blockText = Editor.string(editor, blockRange, { voids })\n          const e = Path.isAncestor(path, end.path)\n            ? end\n            : Editor.end(editor, path)\n          const s = Path.isAncestor(path, start.path)\n            ? start\n            : Editor.start(editor, path)\n\n          blockText = Editor.string(editor, { anchor: s, focus: e }, { voids })\n          isNewBlock = true\n        }\n      }\n\n      /*\n       * TEXT LEAF NODE - Iterate through text content, yielding\n       * positions every `distance` offset according to `unit`.\n       */\n      if (Text.isText(node)) {\n        const isFirst = Path.equals(path, first.path)\n\n        // Proof that we always exhaust text nodes before encountering a new one:\n        //   console.assert(leafTextRemaining <= 0,\n        //     `leafTextRemaining=${leafTextRemaining} - `+\n        //     `not exhausted before new leaf text node`, path)\n\n        // Reset `leafText` counters for new text node.\n        if (isFirst) {\n          leafTextRemaining = reverse\n            ? first.offset\n            : node.text.length - first.offset\n          leafTextOffset = first.offset // Works for reverse too.\n        } else {\n          leafTextRemaining = node.text.length\n          leafTextOffset = reverse ? leafTextRemaining : 0\n        }\n\n        // Yield position at the start of node (potentially).\n        if (isFirst || isNewBlock || unit === 'offset') {\n          yield { path, offset: leafTextOffset }\n          isNewBlock = false\n        }\n\n        // Yield positions every (dynamically calculated) `distance` offset.\n        while (true) {\n          // If `leafText` has caught up with `blockText` (distance=0),\n          // and if blockText is exhausted, break to get another block node,\n          // otherwise advance blockText forward by the new `distance`.\n          if (distance === 0) {\n            if (blockText === '') break\n            distance = calcDistance(blockText, unit, reverse)\n            // Split the string at the previously found distance and use the\n            // remaining string for the next iteration.\n            blockText = splitByCharacterDistance(\n              blockText,\n              distance,\n              reverse\n            )[1]\n          }\n\n          // Advance `leafText` by the current `distance`.\n          leafTextOffset = reverse\n            ? leafTextOffset - distance\n            : leafTextOffset + distance\n          leafTextRemaining = leafTextRemaining - distance\n\n          // If `leafText` is exhausted, break to get a new leaf node\n          // and set distance to the overflow amount, so we'll (maybe)\n          // catch up to blockText in the next leaf text node.\n          if (leafTextRemaining < 0) {\n            distance = -leafTextRemaining\n            break\n          }\n\n          // Successfully walked `distance` offsets through `leafText`\n          // to catch up with `blockText`, so we can reset `distance`\n          // and yield this position in this node.\n          distance = 0\n          yield { path, offset: leafTextOffset }\n        }\n      }\n    }\n    // Proof that upon completion, we've exahusted both leaf and block text:\n    //   console.assert(leafTextRemaining <= 0, \"leafText wasn't exhausted\")\n    //   console.assert(blockText === '', \"blockText wasn't exhausted\")\n\n    // Helper:\n    // Return the distance in offsets for a step of size `unit` on given string.\n    function calcDistance(text: string, unit: string, reverse?: boolean) {\n      if (unit === 'character') {\n        return getCharacterDistance(text, reverse)\n      } else if (unit === 'word') {\n        return getWordDistance(text, reverse)\n      } else if (unit === 'line' || unit === 'block') {\n        return text.length\n      }\n      return 1\n    }\n  },\n\n  /**\n   * Get the matching node in the branch of the document before a location.\n   */\n\n  previous<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<T> | undefined {\n    const { mode = 'lowest', voids = false } = options\n    let { match, at = editor.selection } = options\n\n    if (!at) {\n      return\n    }\n\n    const pointBeforeLocation = Editor.before(editor, at, { voids })\n\n    if (!pointBeforeLocation) {\n      return\n    }\n\n    const [, to] = Editor.first(editor, [])\n\n    // The search location is from the start of the document to the path of\n    // the point before the location passed in\n    const span: Span = [pointBeforeLocation.path, to]\n\n    if (Path.isPath(at) && at.length === 0) {\n      throw new Error(`Cannot get the previous node from the root node!`)\n    }\n\n    if (match == null) {\n      if (Path.isPath(at)) {\n        const [parent] = Editor.parent(editor, at)\n        match = n => parent.children.includes(n)\n      } else {\n        match = () => true\n      }\n    }\n\n    const [previous] = Editor.nodes(editor, {\n      reverse: true,\n      at: span,\n      match,\n      mode,\n      voids,\n    })\n\n    return previous\n  },\n\n  /**\n   * Get a range of a location.\n   */\n\n  range(editor: Editor, at: Location, to?: Location): Range {\n    if (Range.isRange(at) && !to) {\n      return at\n    }\n\n    const start = Editor.start(editor, at)\n    const end = Editor.end(editor, to || at)\n    return { anchor: start, focus: end }\n  },\n\n  /**\n   * Create a mutable ref for a `Range` object, which will stay in sync as new\n   * operations are applied to the editor.\n   */\n\n  rangeRef(\n    editor: Editor,\n    range: Range,\n    options: {\n      affinity?: 'backward' | 'forward' | 'outward' | 'inward' | null\n    } = {}\n  ): RangeRef {\n    const { affinity = 'forward' } = options\n    const ref: RangeRef = {\n      current: range,\n      affinity,\n      unref() {\n        const { current } = ref\n        const rangeRefs = Editor.rangeRefs(editor)\n        rangeRefs.delete(ref)\n        ref.current = null\n        return current\n      },\n    }\n\n    const refs = Editor.rangeRefs(editor)\n    refs.add(ref)\n    return ref\n  },\n\n  /**\n   * Get the set of currently tracked range refs of the editor.\n   */\n\n  rangeRefs(editor: Editor): Set<RangeRef> {\n    let refs = RANGE_REFS.get(editor)\n\n    if (!refs) {\n      refs = new Set()\n      RANGE_REFS.set(editor, refs)\n    }\n\n    return refs\n  },\n\n  /**\n   * Remove a custom property from all of the leaf text nodes in the current\n   * selection.\n   *\n   * If the selection is currently collapsed, the removal will be stored on\n   * `editor.marks` and applied to the text inserted next.\n   */\n\n  removeMark(editor: Editor, key: string): void {\n    editor.removeMark(key)\n  },\n\n  /**\n   * Manually set if the editor should currently be normalizing.\n   *\n   * Note: Using this incorrectly can leave the editor in an invalid state.\n   *\n   */\n  setNormalizing(editor: Editor, isNormalizing: boolean): void {\n    NORMALIZING.set(editor, isNormalizing)\n  },\n\n  /**\n   * Get the start point of a location.\n   */\n\n  start(editor: Editor, at: Location): Point {\n    return Editor.point(editor, at, { edge: 'start' })\n  },\n\n  /**\n   * Get the text string content of a location.\n   *\n   * Note: by default the text of void nodes is considered to be an empty\n   * string, regardless of content, unless you pass in true for the voids option\n   */\n\n  string(\n    editor: Editor,\n    at: Location,\n    options: {\n      voids?: boolean\n    } = {}\n  ): string {\n    const { voids = false } = options\n    const range = Editor.range(editor, at)\n    const [start, end] = Range.edges(range)\n    let text = ''\n\n    for (const [node, path] of Editor.nodes(editor, {\n      at: range,\n      match: Text.isText,\n      voids,\n    })) {\n      let t = node.text\n\n      if (Path.equals(path, end.path)) {\n        t = t.slice(0, end.offset)\n      }\n\n      if (Path.equals(path, start.path)) {\n        t = t.slice(start.offset)\n      }\n\n      text += t\n    }\n\n    return text\n  },\n\n  /**\n   * Convert a range into a non-hanging one.\n   */\n\n  unhangRange(\n    editor: Editor,\n    range: Range,\n    options: {\n      voids?: boolean\n    } = {}\n  ): Range {\n    const { voids = false } = options\n    let [start, end] = Range.edges(range)\n\n    // PERF: exit early if we can guarantee that the range isn't hanging.\n    if (start.offset !== 0 || end.offset !== 0 || Range.isCollapsed(range)) {\n      return range\n    }\n\n    const endBlock = Editor.above(editor, {\n      at: end,\n      match: n => Editor.isBlock(editor, n),\n    })\n    const blockPath = endBlock ? endBlock[1] : []\n    const first = Editor.start(editor, start)\n    const before = { anchor: first, focus: end }\n    let skip = true\n\n    for (const [node, path] of Editor.nodes(editor, {\n      at: before,\n      match: Text.isText,\n      reverse: true,\n      voids,\n    })) {\n      if (skip) {\n        skip = false\n        continue\n      }\n\n      if (node.text !== '' || Path.isBefore(path, blockPath)) {\n        end = { path, offset: node.text.length }\n        break\n      }\n    }\n\n    return { anchor: start, focus: end }\n  },\n\n  /**\n   * Match a void node in the current branch of the editor.\n   */\n\n  void(\n    editor: Editor,\n    options: {\n      at?: Location\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<Element> | undefined {\n    return Editor.above(editor, {\n      ...options,\n      match: n => Editor.isVoid(editor, n),\n    })\n  },\n\n  /**\n   * Call a function, deferring normalization until after it completes.\n   */\n\n  withoutNormalizing(editor: Editor, fn: () => void): void {\n    const value = Editor.isNormalizing(editor)\n    Editor.setNormalizing(editor, false)\n    try {\n      fn()\n    } finally {\n      Editor.setNormalizing(editor, value)\n    }\n    Editor.normalize(editor)\n  },\n}\n\n/**\n * A helper type for narrowing matched nodes with a predicate.\n */\n\nexport type NodeMatch<T extends Node> =\n  | ((node: Node, path: Path) => node is T)\n  | ((node: Node, path: Path) => boolean)\n", "import { Path, Point, Range } from '..'\n\n/**\n * The `Location` interface is a union of the ways to refer to a specific\n * location in a Slate document: paths, points or ranges.\n *\n * Methods will often accept a `Location` instead of requiring only a `Path`,\n * `Point` or `Range`. This eliminates the need for developers to manage\n * converting between the different interfaces in their own code base.\n */\n\nexport type Location = Path | Point | Range\n\nexport interface LocationInterface {\n  isLocation: (value: any) => value is Location\n}\n\nexport const Location: LocationInterface = {\n  /**\n   * Check if a value implements the `Location` interface.\n   */\n\n  isLocation(value: any): value is Location {\n    return Path.isPath(value) || Point.isPoint(value) || Range.isRange(value)\n  },\n}\n\n/**\n * The `Span` interface is a low-level way to refer to locations in nodes\n * without using `Point` which requires leaf text nodes to be present.\n */\n\nexport type Span = [Path, Path]\n\nexport interface SpanInterface {\n  isSpan: (value: any) => value is Span\n}\n\nexport const Span: SpanInterface = {\n  /**\n   * Check if a value implements the `Span` interface.\n   */\n\n  isSpan(value: any): value is Span {\n    return (\n      Array.isArray(value) && value.length === 2 && value.every(Path.isPath)\n    )\n  },\n}\n", "import { produce } from 'immer'\nimport { Editor, Path, Range, Text } from '..'\nimport { Element, ElementEntry } from './element'\n\n/**\n * The `Node` union type represents all of the different types of nodes that\n * occur in a Slate document tree.\n */\n\nexport type BaseNode = Editor | Element | Text\nexport type Node = Editor | Element | Text\n\nexport interface NodeInterface {\n  ancestor: (root: Node, path: Path) => Ancestor\n  ancestors: (\n    root: Node,\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Generator<NodeEntry<Ancestor>, void, undefined>\n  child: (root: Node, index: number) => Descendant\n  children: (\n    root: Node,\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Generator<NodeEntry<Descendant>, void, undefined>\n  common: (root: Node, path: Path, another: Path) => NodeEntry\n  descendant: (root: Node, path: Path) => Descendant\n  descendants: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    }\n  ) => Generator<NodeEntry<Descendant>, void, undefined>\n  elements: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    }\n  ) => Generator<ElementEntry, void, undefined>\n  extractProps: (node: Node) => NodeProps\n  first: (root: Node, path: Path) => NodeEntry\n  fragment: (root: Node, range: Range) => Descendant[]\n  get: (root: Node, path: Path) => Node\n  has: (root: Node, path: Path) => boolean\n  isNode: (value: any) => value is Node\n  isNodeList: (value: any) => value is Node[]\n  last: (root: Node, path: Path) => NodeEntry\n  leaf: (root: Node, path: Path) => Text\n  levels: (\n    root: Node,\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Generator<NodeEntry, void, undefined>\n  matches: (node: Node, props: Partial<Node>) => boolean\n  nodes: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (entry: NodeEntry) => boolean\n    }\n  ) => Generator<NodeEntry, void, undefined>\n  parent: (root: Node, path: Path) => Ancestor\n  string: (node: Node) => string\n  texts: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    }\n  ) => Generator<NodeEntry<Text>, void, undefined>\n}\n\nconst IS_NODE_LIST_CACHE = new WeakMap<any[], boolean>()\n\nexport const Node: NodeInterface = {\n  /**\n   * Get the node at a specific path, asserting that it's an ancestor node.\n   */\n\n  ancestor(root: Node, path: Path): Ancestor {\n    const node = Node.get(root, path)\n\n    if (Text.isText(node)) {\n      throw new Error(\n        `Cannot get the ancestor node at path [${path}] because it refers to a text node instead: ${node}`\n      )\n    }\n\n    return node\n  },\n\n  /**\n   * Return a generator of all the ancestor nodes above a specific path.\n   *\n   * By default the order is bottom-up, from lowest to highest ancestor in\n   * the tree, but you can pass the `reverse: true` option to go top-down.\n   */\n\n  *ancestors(\n    root: Node,\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Generator<NodeEntry<Ancestor>, void, undefined> {\n    for (const p of Path.ancestors(path, options)) {\n      const n = Node.ancestor(root, p)\n      const entry: NodeEntry<Ancestor> = [n, p]\n      yield entry\n    }\n  },\n\n  /**\n   * Get the child of a node at a specific index.\n   */\n\n  child(root: Node, index: number): Descendant {\n    if (Text.isText(root)) {\n      throw new Error(\n        `Cannot get the child of a text node: ${JSON.stringify(root)}`\n      )\n    }\n\n    const c = root.children[index] as Descendant\n\n    if (c == null) {\n      throw new Error(\n        `Cannot get child at index \\`${index}\\` in node: ${JSON.stringify(\n          root\n        )}`\n      )\n    }\n\n    return c\n  },\n\n  /**\n   * Iterate over the children of a node at a specific path.\n   */\n\n  *children(\n    root: Node,\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Generator<NodeEntry<Descendant>, void, undefined> {\n    const { reverse = false } = options\n    const ancestor = Node.ancestor(root, path)\n    const { children } = ancestor\n    let index = reverse ? children.length - 1 : 0\n\n    while (reverse ? index >= 0 : index < children.length) {\n      const child = Node.child(ancestor, index)\n      const childPath = path.concat(index)\n      yield [child, childPath]\n      index = reverse ? index - 1 : index + 1\n    }\n  },\n\n  /**\n   * Get an entry for the common ancesetor node of two paths.\n   */\n\n  common(root: Node, path: Path, another: Path): NodeEntry {\n    const p = Path.common(path, another)\n    const n = Node.get(root, p)\n    return [n, p]\n  },\n\n  /**\n   * Get the node at a specific path, asserting that it's a descendant node.\n   */\n\n  descendant(root: Node, path: Path): Descendant {\n    const node = Node.get(root, path)\n\n    if (Editor.isEditor(node)) {\n      throw new Error(\n        `Cannot get the descendant node at path [${path}] because it refers to the root editor node instead: ${node}`\n      )\n    }\n\n    return node\n  },\n\n  /**\n   * Return a generator of all the descendant node entries inside a root node.\n   */\n\n  *descendants(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    } = {}\n  ): Generator<NodeEntry<Descendant>, void, undefined> {\n    for (const [node, path] of Node.nodes(root, options)) {\n      if (path.length !== 0) {\n        // NOTE: we have to coerce here because checking the path's length does\n        // guarantee that `node` is not a `Editor`, but TypeScript doesn't know.\n        yield [node, path] as NodeEntry<Descendant>\n      }\n    }\n  },\n\n  /**\n   * Return a generator of all the element nodes inside a root node. Each iteration\n   * will return an `ElementEntry` tuple consisting of `[Element, Path]`. If the\n   * root node is an element it will be included in the iteration as well.\n   */\n\n  *elements(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    } = {}\n  ): Generator<ElementEntry, void, undefined> {\n    for (const [node, path] of Node.nodes(root, options)) {\n      if (Element.isElement(node)) {\n        yield [node, path]\n      }\n    }\n  },\n\n  /**\n   * Extract props from a Node.\n   */\n\n  extractProps(node: Node): NodeProps {\n    if (Element.isAncestor(node)) {\n      const { children, ...properties } = node\n\n      return properties\n    } else {\n      const { text, ...properties } = node\n\n      return properties\n    }\n  },\n\n  /**\n   * Get the first node entry in a root node from a path.\n   */\n\n  first(root: Node, path: Path): NodeEntry {\n    const p = path.slice()\n    let n = Node.get(root, p)\n\n    while (n) {\n      if (Text.isText(n) || n.children.length === 0) {\n        break\n      } else {\n        n = n.children[0]\n        p.push(0)\n      }\n    }\n\n    return [n, p]\n  },\n\n  /**\n   * Get the sliced fragment represented by a range inside a root node.\n   */\n\n  fragment(root: Node, range: Range): Descendant[] {\n    if (Text.isText(root)) {\n      throw new Error(\n        `Cannot get a fragment starting from a root text node: ${JSON.stringify(\n          root\n        )}`\n      )\n    }\n\n    const newRoot = produce({ children: root.children }, r => {\n      const [start, end] = Range.edges(range)\n      const nodeEntries = Node.nodes(r, {\n        reverse: true,\n        pass: ([, path]) => !Range.includes(range, path),\n      })\n\n      for (const [, path] of nodeEntries) {\n        if (!Range.includes(range, path)) {\n          const parent = Node.parent(r, path)\n          const index = path[path.length - 1]\n          parent.children.splice(index, 1)\n        }\n\n        if (Path.equals(path, end.path)) {\n          const leaf = Node.leaf(r, path)\n          leaf.text = leaf.text.slice(0, end.offset)\n        }\n\n        if (Path.equals(path, start.path)) {\n          const leaf = Node.leaf(r, path)\n          leaf.text = leaf.text.slice(start.offset)\n        }\n      }\n\n      if (Editor.isEditor(r)) {\n        r.selection = null\n      }\n    })\n\n    return newRoot.children\n  },\n\n  /**\n   * Get the descendant node referred to by a specific path. If the path is an\n   * empty array, it refers to the root node itself.\n   */\n\n  get(root: Node, path: Path): Node {\n    let node = root\n\n    for (let i = 0; i < path.length; i++) {\n      const p = path[i]\n\n      if (Text.isText(node) || !node.children[p]) {\n        throw new Error(\n          `Cannot find a descendant at path [${path}] in node: ${JSON.stringify(\n            root\n          )}`\n        )\n      }\n\n      node = node.children[p]\n    }\n\n    return node\n  },\n\n  /**\n   * Check if a descendant node exists at a specific path.\n   */\n\n  has(root: Node, path: Path): boolean {\n    let node = root\n\n    for (let i = 0; i < path.length; i++) {\n      const p = path[i]\n\n      if (Text.isText(node) || !node.children[p]) {\n        return false\n      }\n\n      node = node.children[p]\n    }\n\n    return true\n  },\n\n  /**\n   * Check if a value implements the `Node` interface.\n   */\n\n  isNode(value: any): value is Node {\n    return (\n      Text.isText(value) || Element.isElement(value) || Editor.isEditor(value)\n    )\n  },\n\n  /**\n   * Check if a value is a list of `Node` objects.\n   */\n\n  isNodeList(value: any): value is Node[] {\n    if (!Array.isArray(value)) {\n      return false\n    }\n    const cachedResult = IS_NODE_LIST_CACHE.get(value)\n    if (cachedResult !== undefined) {\n      return cachedResult\n    }\n    const isNodeList = value.every(val => Node.isNode(val))\n    IS_NODE_LIST_CACHE.set(value, isNodeList)\n    return isNodeList\n  },\n\n  /**\n   * Get the last node entry in a root node from a path.\n   */\n\n  last(root: Node, path: Path): NodeEntry {\n    const p = path.slice()\n    let n = Node.get(root, p)\n\n    while (n) {\n      if (Text.isText(n) || n.children.length === 0) {\n        break\n      } else {\n        const i = n.children.length - 1\n        n = n.children[i]\n        p.push(i)\n      }\n    }\n\n    return [n, p]\n  },\n\n  /**\n   * Get the node at a specific path, ensuring it's a leaf text node.\n   */\n\n  leaf(root: Node, path: Path): Text {\n    const node = Node.get(root, path)\n\n    if (!Text.isText(node)) {\n      throw new Error(\n        `Cannot get the leaf node at path [${path}] because it refers to a non-leaf node: ${node}`\n      )\n    }\n\n    return node\n  },\n\n  /**\n   * Return a generator of the in a branch of the tree, from a specific path.\n   *\n   * By default the order is top-down, from lowest to highest node in the tree,\n   * but you can pass the `reverse: true` option to go bottom-up.\n   */\n\n  *levels(\n    root: Node,\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Generator<NodeEntry, void, undefined> {\n    for (const p of Path.levels(path, options)) {\n      const n = Node.get(root, p)\n      yield [n, p]\n    }\n  },\n\n  /**\n   * Check if a node matches a set of props.\n   */\n\n  matches(node: Node, props: Partial<Node>): boolean {\n    return (\n      (Element.isElement(node) &&\n        Element.isElementProps(props) &&\n        Element.matches(node, props)) ||\n      (Text.isText(node) &&\n        Text.isTextProps(props) &&\n        Text.matches(node, props))\n    )\n  },\n\n  /**\n   * Return a generator of all the node entries of a root node. Each entry is\n   * returned as a `[Node, Path]` tuple, with the path referring to the node's\n   * position inside the root node.\n   */\n\n  *nodes(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (entry: NodeEntry) => boolean\n    } = {}\n  ): Generator<NodeEntry, void, undefined> {\n    const { pass, reverse = false } = options\n    const { from = [], to } = options\n    const visited = new Set()\n    let p: Path = []\n    let n = root\n\n    while (true) {\n      if (to && (reverse ? Path.isBefore(p, to) : Path.isAfter(p, to))) {\n        break\n      }\n\n      if (!visited.has(n)) {\n        yield [n, p]\n      }\n\n      // If we're allowed to go downward and we haven't descended yet, do.\n      if (\n        !visited.has(n) &&\n        !Text.isText(n) &&\n        n.children.length !== 0 &&\n        (pass == null || pass([n, p]) === false)\n      ) {\n        visited.add(n)\n        let nextIndex = reverse ? n.children.length - 1 : 0\n\n        if (Path.isAncestor(p, from)) {\n          nextIndex = from[p.length]\n        }\n\n        p = p.concat(nextIndex)\n        n = Node.get(root, p)\n        continue\n      }\n\n      // If we're at the root and we can't go down, we're done.\n      if (p.length === 0) {\n        break\n      }\n\n      // If we're going forward...\n      if (!reverse) {\n        const newPath = Path.next(p)\n\n        if (Node.has(root, newPath)) {\n          p = newPath\n          n = Node.get(root, p)\n          continue\n        }\n      }\n\n      // If we're going backward...\n      if (reverse && p[p.length - 1] !== 0) {\n        const newPath = Path.previous(p)\n        p = newPath\n        n = Node.get(root, p)\n        continue\n      }\n\n      // Otherwise we're going upward...\n      p = Path.parent(p)\n      n = Node.get(root, p)\n      visited.add(n)\n    }\n  },\n\n  /**\n   * Get the parent of a node at a specific path.\n   */\n\n  parent(root: Node, path: Path): Ancestor {\n    const parentPath = Path.parent(path)\n    const p = Node.get(root, parentPath)\n\n    if (Text.isText(p)) {\n      throw new Error(\n        `Cannot get the parent of path [${path}] because it does not exist in the root.`\n      )\n    }\n\n    return p\n  },\n\n  /**\n   * Get the concatenated text string of a node's content.\n   *\n   * Note that this will not include spaces or line breaks between block nodes.\n   * It is not a user-facing string, but a string for performing offset-related\n   * computations for a node.\n   */\n\n  string(node: Node): string {\n    if (Text.isText(node)) {\n      return node.text\n    } else {\n      return node.children.map(Node.string).join('')\n    }\n  },\n\n  /**\n   * Return a generator of all leaf text nodes in a root node.\n   */\n\n  *texts(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    } = {}\n  ): Generator<NodeEntry<Text>, void, undefined> {\n    for (const [node, path] of Node.nodes(root, options)) {\n      if (Text.isText(node)) {\n        yield [node, path]\n      }\n    }\n  },\n}\n\n/**\n * The `Descendant` union type represents nodes that are descendants in the\n * tree. It is returned as a convenience in certain cases to narrow a value\n * further than the more generic `Node` union.\n */\n\nexport type Descendant = Element | Text\n\n/**\n * The `Ancestor` union type represents nodes that are ancestors in the tree.\n * It is returned as a convenience in certain cases to narrow a value further\n * than the more generic `Node` union.\n */\n\nexport type Ancestor = Editor | Element\n\n/**\n * `NodeEntry` objects are returned when iterating over the nodes in a Slate\n * document tree. They consist of the node and its `Path` relative to the root\n * node in the document.\n */\n\nexport type NodeEntry<T extends Node = Node> = [T, Path]\n\n/**\n * Convenience type for returning the props of a node.\n */\nexport type NodeProps =\n  | Omit<Editor, 'children'>\n  | Omit<Element, 'children'>\n  | Omit<Text, 'text'>\n", "import { ExtendedType, Node, Path, Range } from '..'\nimport { isPlainObject } from 'is-plain-object'\n\nexport type BaseInsertNodeOperation = {\n  type: 'insert_node'\n  path: Path\n  node: Node\n}\n\nexport type InsertNodeOperation = ExtendedType<\n  'InsertNodeOperation',\n  BaseInsertNodeOperation\n>\n\nexport type BaseInsertTextOperation = {\n  type: 'insert_text'\n  path: Path\n  offset: number\n  text: string\n}\n\nexport type InsertTextOperation = ExtendedType<\n  'InsertTextOperation',\n  BaseInsertTextOperation\n>\n\nexport type BaseMergeNodeOperation = {\n  type: 'merge_node'\n  path: Path\n  position: number\n  properties: Partial<Node>\n}\n\nexport type MergeNodeOperation = ExtendedType<\n  'MergeNodeOperation',\n  BaseMergeNodeOperation\n>\n\nexport type BaseMoveNodeOperation = {\n  type: 'move_node'\n  path: Path\n  newPath: Path\n}\n\nexport type MoveNodeOperation = ExtendedType<\n  'MoveNodeOperation',\n  BaseMoveNodeOperation\n>\n\nexport type BaseRemoveNodeOperation = {\n  type: 'remove_node'\n  path: Path\n  node: Node\n}\n\nexport type RemoveNodeOperation = ExtendedType<\n  'RemoveNodeOperation',\n  BaseRemoveNodeOperation\n>\n\nexport type BaseRemoveTextOperation = {\n  type: 'remove_text'\n  path: Path\n  offset: number\n  text: string\n}\n\nexport type RemoveTextOperation = ExtendedType<\n  'RemoveTextOperation',\n  BaseRemoveTextOperation\n>\n\nexport type BaseSetNodeOperation = {\n  type: 'set_node'\n  path: Path\n  properties: Partial<Node>\n  newProperties: Partial<Node>\n}\n\nexport type SetNodeOperation = ExtendedType<\n  'SetNodeOperation',\n  BaseSetNodeOperation\n>\n\nexport type BaseSetSelectionOperation =\n  | {\n      type: 'set_selection'\n      properties: null\n      newProperties: Range\n    }\n  | {\n      type: 'set_selection'\n      properties: Partial<Range>\n      newProperties: Partial<Range>\n    }\n  | {\n      type: 'set_selection'\n      properties: Range\n      newProperties: null\n    }\n\nexport type SetSelectionOperation = ExtendedType<\n  'SetSelectionOperation',\n  BaseSetSelectionOperation\n>\n\nexport type BaseSplitNodeOperation = {\n  type: 'split_node'\n  path: Path\n  position: number\n  properties: Partial<Node>\n}\n\nexport type SplitNodeOperation = ExtendedType<\n  'SplitNodeOperation',\n  BaseSplitNodeOperation\n>\n\nexport type NodeOperation =\n  | InsertNodeOperation\n  | MergeNodeOperation\n  | MoveNodeOperation\n  | RemoveNodeOperation\n  | SetNodeOperation\n  | SplitNodeOperation\n\nexport type SelectionOperation = SetSelectionOperation\n\nexport type TextOperation = InsertTextOperation | RemoveTextOperation\n\n/**\n * `Operation` objects define the low-level instructions that Slate editors use\n * to apply changes to their internal state. Representing all changes as\n * operations is what allows Slate editors to easily implement history,\n * collaboration, and other features.\n */\n\nexport type BaseOperation = NodeOperation | SelectionOperation | TextOperation\nexport type Operation = ExtendedType<'Operation', BaseOperation>\n\nexport interface OperationInterface {\n  isNodeOperation: (value: any) => value is NodeOperation\n  isOperation: (value: any) => value is Operation\n  isOperationList: (value: any) => value is Operation[]\n  isSelectionOperation: (value: any) => value is SelectionOperation\n  isTextOperation: (value: any) => value is TextOperation\n  inverse: (op: Operation) => Operation\n}\n\nexport const Operation: OperationInterface = {\n  /**\n   * Check of a value is a `NodeOperation` object.\n   */\n\n  isNodeOperation(value: any): value is NodeOperation {\n    return Operation.isOperation(value) && value.type.endsWith('_node')\n  },\n\n  /**\n   * Check of a value is an `Operation` object.\n   */\n\n  isOperation(value: any): value is Operation {\n    if (!isPlainObject(value)) {\n      return false\n    }\n\n    switch (value.type) {\n      case 'insert_node':\n        return Path.isPath(value.path) && Node.isNode(value.node)\n      case 'insert_text':\n        return (\n          typeof value.offset === 'number' &&\n          typeof value.text === 'string' &&\n          Path.isPath(value.path)\n        )\n      case 'merge_node':\n        return (\n          typeof value.position === 'number' &&\n          Path.isPath(value.path) &&\n          isPlainObject(value.properties)\n        )\n      case 'move_node':\n        return Path.isPath(value.path) && Path.isPath(value.newPath)\n      case 'remove_node':\n        return Path.isPath(value.path) && Node.isNode(value.node)\n      case 'remove_text':\n        return (\n          typeof value.offset === 'number' &&\n          typeof value.text === 'string' &&\n          Path.isPath(value.path)\n        )\n      case 'set_node':\n        return (\n          Path.isPath(value.path) &&\n          isPlainObject(value.properties) &&\n          isPlainObject(value.newProperties)\n        )\n      case 'set_selection':\n        return (\n          (value.properties === null && Range.isRange(value.newProperties)) ||\n          (value.newProperties === null && Range.isRange(value.properties)) ||\n          (isPlainObject(value.properties) &&\n            isPlainObject(value.newProperties))\n        )\n      case 'split_node':\n        return (\n          Path.isPath(value.path) &&\n          typeof value.position === 'number' &&\n          isPlainObject(value.properties)\n        )\n      default:\n        return false\n    }\n  },\n\n  /**\n   * Check if a value is a list of `Operation` objects.\n   */\n\n  isOperationList(value: any): value is Operation[] {\n    return (\n      Array.isArray(value) && value.every(val => Operation.isOperation(val))\n    )\n  },\n\n  /**\n   * Check of a value is a `SelectionOperation` object.\n   */\n\n  isSelectionOperation(value: any): value is SelectionOperation {\n    return Operation.isOperation(value) && value.type.endsWith('_selection')\n  },\n\n  /**\n   * Check of a value is a `TextOperation` object.\n   */\n\n  isTextOperation(value: any): value is TextOperation {\n    return Operation.isOperation(value) && value.type.endsWith('_text')\n  },\n\n  /**\n   * Invert an operation, returning a new operation that will exactly undo the\n   * original when applied.\n   */\n\n  inverse(op: Operation): Operation {\n    switch (op.type) {\n      case 'insert_node': {\n        return { ...op, type: 'remove_node' }\n      }\n\n      case 'insert_text': {\n        return { ...op, type: 'remove_text' }\n      }\n\n      case 'merge_node': {\n        return { ...op, type: 'split_node', path: Path.previous(op.path) }\n      }\n\n      case 'move_node': {\n        const { newPath, path } = op\n\n        // PERF: in this case the move operation is a no-op anyways.\n        if (Path.equals(newPath, path)) {\n          return op\n        }\n\n        // If the move happens completely within a single parent the path and\n        // newPath are stable with respect to each other.\n        if (Path.isSibling(path, newPath)) {\n          return { ...op, path: newPath, newPath: path }\n        }\n\n        // If the move does not happen within a single parent it is possible\n        // for the move to impact the true path to the location where the node\n        // was removed from and where it was inserted. We have to adjust for this\n        // and find the original path. We can accomplish this (only in non-sibling)\n        // moves by looking at the impact of the move operation on the node\n        // after the original move path.\n        const inversePath = Path.transform(path, op)!\n        const inverseNewPath = Path.transform(Path.next(path), op)!\n        return { ...op, path: inversePath, newPath: inverseNewPath }\n      }\n\n      case 'remove_node': {\n        return { ...op, type: 'insert_node' }\n      }\n\n      case 'remove_text': {\n        return { ...op, type: 'insert_text' }\n      }\n\n      case 'set_node': {\n        const { properties, newProperties } = op\n        return { ...op, properties: newProperties, newProperties: properties }\n      }\n\n      case 'set_selection': {\n        const { properties, newProperties } = op\n\n        if (properties == null) {\n          return {\n            ...op,\n            properties: newProperties as Range,\n            newProperties: null,\n          }\n        } else if (newProperties == null) {\n          return {\n            ...op,\n            properties: null,\n            newProperties: properties as Range,\n          }\n        } else {\n          return { ...op, properties: newProperties, newProperties: properties }\n        }\n      }\n\n      case 'split_node': {\n        return { ...op, type: 'merge_node', path: Path.next(op.path) }\n      }\n    }\n  },\n}\n", "import { produce } from 'immer'\nimport { Operation } from '..'\n\n/**\n * `Path` arrays are a list of indexes that describe a node's exact position in\n * a Slate node tree. Although they are usually relative to the root `Editor`\n * object, they can be relative to any `Node` object.\n */\n\nexport type Path = number[]\n\nexport interface PathInterface {\n  ancestors: (path: Path, options?: { reverse?: boolean }) => Path[]\n  common: (path: Path, another: Path) => Path\n  compare: (path: Path, another: Path) => -1 | 0 | 1\n  endsAfter: (path: Path, another: Path) => boolean\n  endsAt: (path: Path, another: Path) => boolean\n  endsBefore: (path: Path, another: Path) => boolean\n  equals: (path: Path, another: Path) => boolean\n  hasPrevious: (path: Path) => boolean\n  isAfter: (path: Path, another: Path) => boolean\n  isAncestor: (path: Path, another: Path) => boolean\n  isBefore: (path: Path, another: Path) => boolean\n  isChild: (path: Path, another: Path) => boolean\n  isCommon: (path: Path, another: Path) => boolean\n  isDescendant: (path: Path, another: Path) => boolean\n  isParent: (path: Path, another: Path) => boolean\n  isPath: (value: any) => value is Path\n  isSibling: (path: Path, another: Path) => boolean\n  levels: (\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Path[]\n  next: (path: Path) => Path\n  operationCanTransformPath: (operation: Operation) => boolean\n  parent: (path: Path) => Path\n  previous: (path: Path) => Path\n  relative: (path: Path, ancestor: Path) => Path\n  transform: (\n    path: Path,\n    operation: Operation,\n    options?: { affinity?: 'forward' | 'backward' | null }\n  ) => Path | null\n}\n\nexport const Path: PathInterface = {\n  /**\n   * Get a list of ancestor paths for a given path.\n   *\n   * The paths are sorted from deepest to shallowest ancestor. However, if the\n   * `reverse: true` option is passed, they are reversed.\n   */\n\n  ancestors(path: Path, options: { reverse?: boolean } = {}): Path[] {\n    const { reverse = false } = options\n    let paths = Path.levels(path, options)\n\n    if (reverse) {\n      paths = paths.slice(1)\n    } else {\n      paths = paths.slice(0, -1)\n    }\n\n    return paths\n  },\n\n  /**\n   * Get the common ancestor path of two paths.\n   */\n\n  common(path: Path, another: Path): Path {\n    const common: Path = []\n\n    for (let i = 0; i < path.length && i < another.length; i++) {\n      const av = path[i]\n      const bv = another[i]\n\n      if (av !== bv) {\n        break\n      }\n\n      common.push(av)\n    }\n\n    return common\n  },\n\n  /**\n   * Compare a path to another, returning an integer indicating whether the path\n   * was before, at, or after the other.\n   *\n   * Note: Two paths of unequal length can still receive a `0` result if one is\n   * directly above or below the other. If you want exact matching, use\n   * [[Path.equals]] instead.\n   */\n\n  compare(path: Path, another: Path): -1 | 0 | 1 {\n    const min = Math.min(path.length, another.length)\n\n    for (let i = 0; i < min; i++) {\n      if (path[i] < another[i]) return -1\n      if (path[i] > another[i]) return 1\n    }\n\n    return 0\n  },\n\n  /**\n   * Check if a path ends after one of the indexes in another.\n   */\n\n  endsAfter(path: Path, another: Path): boolean {\n    const i = path.length - 1\n    const as = path.slice(0, i)\n    const bs = another.slice(0, i)\n    const av = path[i]\n    const bv = another[i]\n    return Path.equals(as, bs) && av > bv\n  },\n\n  /**\n   * Check if a path ends at one of the indexes in another.\n   */\n\n  endsAt(path: Path, another: Path): boolean {\n    const i = path.length\n    const as = path.slice(0, i)\n    const bs = another.slice(0, i)\n    return Path.equals(as, bs)\n  },\n\n  /**\n   * Check if a path ends before one of the indexes in another.\n   */\n\n  endsBefore(path: Path, another: Path): boolean {\n    const i = path.length - 1\n    const as = path.slice(0, i)\n    const bs = another.slice(0, i)\n    const av = path[i]\n    const bv = another[i]\n    return Path.equals(as, bs) && av < bv\n  },\n\n  /**\n   * Check if a path is exactly equal to another.\n   */\n\n  equals(path: Path, another: Path): boolean {\n    return (\n      path.length === another.length && path.every((n, i) => n === another[i])\n    )\n  },\n\n  /**\n   * Check if the path of previous sibling node exists\n   */\n\n  hasPrevious(path: Path): boolean {\n    return path[path.length - 1] > 0\n  },\n\n  /**\n   * Check if a path is after another.\n   */\n\n  isAfter(path: Path, another: Path): boolean {\n    return Path.compare(path, another) === 1\n  },\n\n  /**\n   * Check if a path is an ancestor of another.\n   */\n\n  isAncestor(path: Path, another: Path): boolean {\n    return path.length < another.length && Path.compare(path, another) === 0\n  },\n\n  /**\n   * Check if a path is before another.\n   */\n\n  isBefore(path: Path, another: Path): boolean {\n    return Path.compare(path, another) === -1\n  },\n\n  /**\n   * Check if a path is a child of another.\n   */\n\n  isChild(path: Path, another: Path): boolean {\n    return (\n      path.length === another.length + 1 && Path.compare(path, another) === 0\n    )\n  },\n\n  /**\n   * Check if a path is equal to or an ancestor of another.\n   */\n\n  isCommon(path: Path, another: Path): boolean {\n    return path.length <= another.length && Path.compare(path, another) === 0\n  },\n\n  /**\n   * Check if a path is a descendant of another.\n   */\n\n  isDescendant(path: Path, another: Path): boolean {\n    return path.length > another.length && Path.compare(path, another) === 0\n  },\n\n  /**\n   * Check if a path is the parent of another.\n   */\n\n  isParent(path: Path, another: Path): boolean {\n    return (\n      path.length + 1 === another.length && Path.compare(path, another) === 0\n    )\n  },\n\n  /**\n   * Check is a value implements the `Path` interface.\n   */\n\n  isPath(value: any): value is Path {\n    return (\n      Array.isArray(value) &&\n      (value.length === 0 || typeof value[0] === 'number')\n    )\n  },\n\n  /**\n   * Check if a path is a sibling of another.\n   */\n\n  isSibling(path: Path, another: Path): boolean {\n    if (path.length !== another.length) {\n      return false\n    }\n\n    const as = path.slice(0, -1)\n    const bs = another.slice(0, -1)\n    const al = path[path.length - 1]\n    const bl = another[another.length - 1]\n    return al !== bl && Path.equals(as, bs)\n  },\n\n  /**\n   * Get a list of paths at every level down to a path. Note: this is the same\n   * as `Path.ancestors`, but including the path itself.\n   *\n   * The paths are sorted from shallowest to deepest. However, if the `reverse:\n   * true` option is passed, they are reversed.\n   */\n\n  levels(\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Path[] {\n    const { reverse = false } = options\n    const list: Path[] = []\n\n    for (let i = 0; i <= path.length; i++) {\n      list.push(path.slice(0, i))\n    }\n\n    if (reverse) {\n      list.reverse()\n    }\n\n    return list\n  },\n\n  /**\n   * Given a path, get the path to the next sibling node.\n   */\n\n  next(path: Path): Path {\n    if (path.length === 0) {\n      throw new Error(\n        `Cannot get the next path of a root path [${path}], because it has no next index.`\n      )\n    }\n\n    const last = path[path.length - 1]\n    return path.slice(0, -1).concat(last + 1)\n  },\n\n  /**\n   * Returns whether this operation can affect paths or not. Used as an\n   * optimization when updating dirty paths during normalization\n   *\n   * NOTE: This *must* be kept in sync with the implementation of 'transform'\n   * below\n   */\n  operationCanTransformPath(operation: Operation): boolean {\n    switch (operation.type) {\n      case 'insert_node':\n      case 'remove_node':\n      case 'merge_node':\n      case 'split_node':\n      case 'move_node':\n        return true\n      default:\n        return false\n    }\n  },\n\n  /**\n   * Given a path, return a new path referring to the parent node above it.\n   */\n\n  parent(path: Path): Path {\n    if (path.length === 0) {\n      throw new Error(`Cannot get the parent path of the root path [${path}].`)\n    }\n\n    return path.slice(0, -1)\n  },\n\n  /**\n   * Given a path, get the path to the previous sibling node.\n   */\n\n  previous(path: Path): Path {\n    if (path.length === 0) {\n      throw new Error(\n        `Cannot get the previous path of a root path [${path}], because it has no previous index.`\n      )\n    }\n\n    const last = path[path.length - 1]\n\n    if (last <= 0) {\n      throw new Error(\n        `Cannot get the previous path of a first child path [${path}] because it would result in a negative index.`\n      )\n    }\n\n    return path.slice(0, -1).concat(last - 1)\n  },\n\n  /**\n   * Get a path relative to an ancestor.\n   */\n\n  relative(path: Path, ancestor: Path): Path {\n    if (!Path.isAncestor(ancestor, path) && !Path.equals(path, ancestor)) {\n      throw new Error(\n        `Cannot get the relative path of [${path}] inside ancestor [${ancestor}], because it is not above or equal to the path.`\n      )\n    }\n\n    return path.slice(ancestor.length)\n  },\n\n  /**\n   * Transform a path by an operation.\n   */\n\n  transform(\n    path: Path | null,\n    operation: Operation,\n    options: { affinity?: 'forward' | 'backward' | null } = {}\n  ): Path | null {\n    return produce(path, p => {\n      const { affinity = 'forward' } = options\n\n      // PERF: Exit early if the operation is guaranteed not to have an effect.\n      if (!path || path?.length === 0) {\n        return\n      }\n\n      if (p === null) {\n        return null\n      }\n\n      switch (operation.type) {\n        case 'insert_node': {\n          const { path: op } = operation\n\n          if (\n            Path.equals(op, p) ||\n            Path.endsBefore(op, p) ||\n            Path.isAncestor(op, p)\n          ) {\n            p[op.length - 1] += 1\n          }\n\n          break\n        }\n\n        case 'remove_node': {\n          const { path: op } = operation\n\n          if (Path.equals(op, p) || Path.isAncestor(op, p)) {\n            return null\n          } else if (Path.endsBefore(op, p)) {\n            p[op.length - 1] -= 1\n          }\n\n          break\n        }\n\n        case 'merge_node': {\n          const { path: op, position } = operation\n\n          if (Path.equals(op, p) || Path.endsBefore(op, p)) {\n            p[op.length - 1] -= 1\n          } else if (Path.isAncestor(op, p)) {\n            p[op.length - 1] -= 1\n            p[op.length] += position\n          }\n\n          break\n        }\n\n        case 'split_node': {\n          const { path: op, position } = operation\n\n          if (Path.equals(op, p)) {\n            if (affinity === 'forward') {\n              p[p.length - 1] += 1\n            } else if (affinity === 'backward') {\n              // Nothing, because it still refers to the right path.\n            } else {\n              return null\n            }\n          } else if (Path.endsBefore(op, p)) {\n            p[op.length - 1] += 1\n          } else if (Path.isAncestor(op, p) && path[op.length] >= position) {\n            p[op.length - 1] += 1\n            p[op.length] -= position\n          }\n\n          break\n        }\n\n        case 'move_node': {\n          const { path: op, newPath: onp } = operation\n\n          // If the old and new path are the same, it's a no-op.\n          if (Path.equals(op, onp)) {\n            return\n          }\n\n          if (Path.isAncestor(op, p) || Path.equals(op, p)) {\n            const copy = onp.slice()\n\n            if (Path.endsBefore(op, onp) && op.length < onp.length) {\n              copy[op.length - 1] -= 1\n            }\n\n            return copy.concat(p.slice(op.length))\n          } else if (\n            Path.isSibling(op, onp) &&\n            (Path.isAncestor(onp, p) || Path.equals(onp, p))\n          ) {\n            if (Path.endsBefore(op, p)) {\n              p[op.length - 1] -= 1\n            } else {\n              p[op.length - 1] += 1\n            }\n          } else if (\n            Path.endsBefore(onp, p) ||\n            Path.equals(onp, p) ||\n            Path.isAncestor(onp, p)\n          ) {\n            if (Path.endsBefore(op, p)) {\n              p[op.length - 1] -= 1\n            }\n\n            p[onp.length - 1] += 1\n          } else if (Path.endsBefore(op, p)) {\n            if (Path.equals(onp, p)) {\n              p[onp.length - 1] += 1\n            }\n\n            p[op.length - 1] -= 1\n          }\n\n          break\n        }\n      }\n    })\n  },\n}\n", "import { Operation, Path } from '..'\n\n/**\n * `PathRef` objects keep a specific path in a document synced over time as new\n * operations are applied to the editor. You can access their `current` property\n * at any time for the up-to-date path value.\n */\n\nexport interface PathRef {\n  current: Path | null\n  affinity: 'forward' | 'backward' | null\n  unref(): Path | null\n}\n\nexport interface PathRefInterface {\n  transform: (ref: PathRef, op: Operation) => void\n}\n\nexport const PathRef: PathRefInterface = {\n  /**\n   * Transform the path ref's current value by an operation.\n   */\n\n  transform(ref: PathRef, op: Operation): void {\n    const { current, affinity } = ref\n\n    if (current == null) {\n      return\n    }\n\n    const path = Path.transform(current, op, { affinity })\n    ref.current = path\n\n    if (path == null) {\n      ref.unref()\n    }\n  },\n}\n", "import { isPlainObject } from 'is-plain-object'\nimport { produce } from 'immer'\nimport { ExtendedType, Operation, Path } from '..'\n\n/**\n * `Point` objects refer to a specific location in a text node in a Slate\n * document. Its path refers to the location of the node in the tree, and its\n * offset refers to the distance into the node's string of text. Points can\n * only refer to `Text` nodes.\n */\n\nexport interface BasePoint {\n  path: Path\n  offset: number\n}\n\nexport type Point = ExtendedType<'Point', BasePoint>\n\nexport interface PointInterface {\n  compare: (point: Point, another: Point) => -1 | 0 | 1\n  isAfter: (point: Point, another: Point) => boolean\n  isBefore: (point: Point, another: Point) => boolean\n  equals: (point: Point, another: Point) => boolean\n  isPoint: (value: any) => value is Point\n  transform: (\n    point: Point,\n    op: Operation,\n    options?: { affinity?: 'forward' | 'backward' | null }\n  ) => Point | null\n}\n\nexport const Point: PointInterface = {\n  /**\n   * Compare a point to another, returning an integer indicating whether the\n   * point was before, at, or after the other.\n   */\n\n  compare(point: Point, another: Point): -1 | 0 | 1 {\n    const result = Path.compare(point.path, another.path)\n\n    if (result === 0) {\n      if (point.offset < another.offset) return -1\n      if (point.offset > another.offset) return 1\n      return 0\n    }\n\n    return result\n  },\n\n  /**\n   * Check if a point is after another.\n   */\n\n  isAfter(point: Point, another: Point): boolean {\n    return Point.compare(point, another) === 1\n  },\n\n  /**\n   * Check if a point is before another.\n   */\n\n  isBefore(point: Point, another: Point): boolean {\n    return Point.compare(point, another) === -1\n  },\n\n  /**\n   * Check if a point is exactly equal to another.\n   */\n\n  equals(point: Point, another: Point): boolean {\n    // PERF: ensure the offsets are equal first since they are cheaper to check.\n    return (\n      point.offset === another.offset && Path.equals(point.path, another.path)\n    )\n  },\n\n  /**\n   * Check if a value implements the `Point` interface.\n   */\n\n  isPoint(value: any): value is Point {\n    return (\n      isPlainObject(value) &&\n      typeof value.offset === 'number' &&\n      Path.isPath(value.path)\n    )\n  },\n\n  /**\n   * Transform a point by an operation.\n   */\n\n  transform(\n    point: Point | null,\n    op: Operation,\n    options: { affinity?: 'forward' | 'backward' | null } = {}\n  ): Point | null {\n    return produce(point, p => {\n      if (p === null) {\n        return null\n      }\n      const { affinity = 'forward' } = options\n      const { path, offset } = p\n\n      switch (op.type) {\n        case 'insert_node':\n        case 'move_node': {\n          p.path = Path.transform(path, op, options)!\n          break\n        }\n\n        case 'insert_text': {\n          if (Path.equals(op.path, path) && op.offset <= offset) {\n            p.offset += op.text.length\n          }\n\n          break\n        }\n\n        case 'merge_node': {\n          if (Path.equals(op.path, path)) {\n            p.offset += op.position\n          }\n\n          p.path = Path.transform(path, op, options)!\n          break\n        }\n\n        case 'remove_text': {\n          if (Path.equals(op.path, path) && op.offset <= offset) {\n            p.offset -= Math.min(offset - op.offset, op.text.length)\n          }\n\n          break\n        }\n\n        case 'remove_node': {\n          if (Path.equals(op.path, path) || Path.isAncestor(op.path, path)) {\n            return null\n          }\n\n          p.path = Path.transform(path, op, options)!\n          break\n        }\n\n        case 'split_node': {\n          if (Path.equals(op.path, path)) {\n            if (op.position === offset && affinity == null) {\n              return null\n            } else if (\n              op.position < offset ||\n              (op.position === offset && affinity === 'forward')\n            ) {\n              p.offset -= op.position\n\n              p.path = Path.transform(path, op, {\n                ...options,\n                affinity: 'forward',\n              })!\n            }\n          } else {\n            p.path = Path.transform(path, op, options)!\n          }\n\n          break\n        }\n      }\n    })\n  },\n}\n\n/**\n * `PointEntry` objects are returned when iterating over `Point` objects that\n * belong to a range.\n */\n\nexport type PointEntry = [Point, 'anchor' | 'focus']\n", "import { Operation, Point } from '..'\n\n/**\n * `PointRef` objects keep a specific point in a document synced over time as new\n * operations are applied to the editor. You can access their `current` property\n * at any time for the up-to-date point value.\n */\n\nexport interface PointRef {\n  current: Point | null\n  affinity: 'forward' | 'backward' | null\n  unref(): Point | null\n}\n\nexport interface PointRefInterface {\n  transform: (ref: PointRef, op: Operation) => void\n}\n\nexport const PointRef: PointRefInterface = {\n  /**\n   * Transform the point ref's current value by an operation.\n   */\n\n  transform(ref: PointRef, op: Operation): void {\n    const { current, affinity } = ref\n\n    if (current == null) {\n      return\n    }\n\n    const point = Point.transform(current, op, { affinity })\n    ref.current = point\n\n    if (point == null) {\n      ref.unref()\n    }\n  },\n}\n", "import { produce } from 'immer'\nimport { isPlainObject } from 'is-plain-object'\nimport { ExtendedType, Operation, Path, Point, PointEntry } from '..'\n\n/**\n * `Range` objects are a set of points that refer to a specific span of a Slate\n * document. They can define a span inside a single node or a can span across\n * multiple nodes.\n */\n\nexport interface BaseRange {\n  anchor: Point\n  focus: Point\n}\n\nexport type Range = ExtendedType<'Range', BaseRange>\n\nexport interface RangeInterface {\n  edges: (\n    range: Range,\n    options?: {\n      reverse?: boolean\n    }\n  ) => [Point, Point]\n  end: (range: Range) => Point\n  equals: (range: Range, another: Range) => boolean\n  includes: (range: Range, target: Path | Point | Range) => boolean\n  intersection: (range: Range, another: Range) => Range | null\n  isBackward: (range: Range) => boolean\n  isCollapsed: (range: Range) => boolean\n  isExpanded: (range: Range) => boolean\n  isForward: (range: Range) => boolean\n  isRange: (value: any) => value is Range\n  points: (range: Range) => Generator<PointEntry, void, undefined>\n  start: (range: Range) => Point\n  transform: (\n    range: Range,\n    op: Operation,\n    options?: {\n      affinity?: 'forward' | 'backward' | 'outward' | 'inward' | null\n    }\n  ) => Range | null\n}\n\nexport const Range: RangeInterface = {\n  /**\n   * Get the start and end points of a range, in the order in which they appear\n   * in the document.\n   */\n\n  edges(\n    range: Range,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): [Point, Point] {\n    const { reverse = false } = options\n    const { anchor, focus } = range\n    return Range.isBackward(range) === reverse\n      ? [anchor, focus]\n      : [focus, anchor]\n  },\n\n  /**\n   * Get the end point of a range.\n   */\n\n  end(range: Range): Point {\n    const [, end] = Range.edges(range)\n    return end\n  },\n\n  /**\n   * Check if a range is exactly equal to another.\n   */\n\n  equals(range: Range, another: Range): boolean {\n    return (\n      Point.equals(range.anchor, another.anchor) &&\n      Point.equals(range.focus, another.focus)\n    )\n  },\n\n  /**\n   * Check if a range includes a path, a point or part of another range.\n   */\n\n  includes(range: Range, target: Path | Point | Range): boolean {\n    if (Range.isRange(target)) {\n      if (\n        Range.includes(range, target.anchor) ||\n        Range.includes(range, target.focus)\n      ) {\n        return true\n      }\n\n      const [rs, re] = Range.edges(range)\n      const [ts, te] = Range.edges(target)\n      return Point.isBefore(rs, ts) && Point.isAfter(re, te)\n    }\n\n    const [start, end] = Range.edges(range)\n    let isAfterStart = false\n    let isBeforeEnd = false\n\n    if (Point.isPoint(target)) {\n      isAfterStart = Point.compare(target, start) >= 0\n      isBeforeEnd = Point.compare(target, end) <= 0\n    } else {\n      isAfterStart = Path.compare(target, start.path) >= 0\n      isBeforeEnd = Path.compare(target, end.path) <= 0\n    }\n\n    return isAfterStart && isBeforeEnd\n  },\n\n  /**\n   * Get the intersection of a range with another.\n   */\n\n  intersection(range: Range, another: Range): Range | null {\n    const { anchor, focus, ...rest } = range\n    const [s1, e1] = Range.edges(range)\n    const [s2, e2] = Range.edges(another)\n    const start = Point.isBefore(s1, s2) ? s2 : s1\n    const end = Point.isBefore(e1, e2) ? e1 : e2\n\n    if (Point.isBefore(end, start)) {\n      return null\n    } else {\n      return { anchor: start, focus: end, ...rest }\n    }\n  },\n\n  /**\n   * Check if a range is backward, meaning that its anchor point appears in the\n   * document _after_ its focus point.\n   */\n\n  isBackward(range: Range): boolean {\n    const { anchor, focus } = range\n    return Point.isAfter(anchor, focus)\n  },\n\n  /**\n   * Check if a range is collapsed, meaning that both its anchor and focus\n   * points refer to the exact same position in the document.\n   */\n\n  isCollapsed(range: Range): boolean {\n    const { anchor, focus } = range\n    return Point.equals(anchor, focus)\n  },\n\n  /**\n   * Check if a range is expanded.\n   *\n   * This is the opposite of [[Range.isCollapsed]] and is provided for legibility.\n   */\n\n  isExpanded(range: Range): boolean {\n    return !Range.isCollapsed(range)\n  },\n\n  /**\n   * Check if a range is forward.\n   *\n   * This is the opposite of [[Range.isBackward]] and is provided for legibility.\n   */\n\n  isForward(range: Range): boolean {\n    return !Range.isBackward(range)\n  },\n\n  /**\n   * Check if a value implements the [[Range]] interface.\n   */\n\n  isRange(value: any): value is Range {\n    return (\n      isPlainObject(value) &&\n      Point.isPoint(value.anchor) &&\n      Point.isPoint(value.focus)\n    )\n  },\n\n  /**\n   * Iterate through all of the point entries in a range.\n   */\n\n  *points(range: Range): Generator<PointEntry, void, undefined> {\n    yield [range.anchor, 'anchor']\n    yield [range.focus, 'focus']\n  },\n\n  /**\n   * Get the start point of a range.\n   */\n\n  start(range: Range): Point {\n    const [start] = Range.edges(range)\n    return start\n  },\n\n  /**\n   * Transform a range by an operation.\n   */\n\n  transform(\n    range: Range | null,\n    op: Operation,\n    options: {\n      affinity?: 'forward' | 'backward' | 'outward' | 'inward' | null\n    } = {}\n  ): Range | null {\n    return produce(range, r => {\n      if (r === null) {\n        return null\n      }\n      const { affinity = 'inward' } = options\n      let affinityAnchor: 'forward' | 'backward' | null\n      let affinityFocus: 'forward' | 'backward' | null\n\n      if (affinity === 'inward') {\n        // If the range is collapsed, make sure to use the same affinity to\n        // avoid the two points passing each other and expanding in the opposite\n        // direction\n        const isCollapsed = Range.isCollapsed(r)\n        if (Range.isForward(r)) {\n          affinityAnchor = 'forward'\n          affinityFocus = isCollapsed ? affinityAnchor : 'backward'\n        } else {\n          affinityAnchor = 'backward'\n          affinityFocus = isCollapsed ? affinityAnchor : 'forward'\n        }\n      } else if (affinity === 'outward') {\n        if (Range.isForward(r)) {\n          affinityAnchor = 'backward'\n          affinityFocus = 'forward'\n        } else {\n          affinityAnchor = 'forward'\n          affinityFocus = 'backward'\n        }\n      } else {\n        affinityAnchor = affinity\n        affinityFocus = affinity\n      }\n      const anchor = Point.transform(r.anchor, op, { affinity: affinityAnchor })\n      const focus = Point.transform(r.focus, op, { affinity: affinityFocus })\n\n      if (!anchor || !focus) {\n        return null\n      }\n\n      r.anchor = anchor\n      r.focus = focus\n    })\n  },\n}\n", "import { Operation, Range } from '..'\n\n/**\n * `RangeRef` objects keep a specific range in a document synced over time as new\n * operations are applied to the editor. You can access their `current` property\n * at any time for the up-to-date range value.\n */\n\nexport interface RangeRef {\n  current: Range | null\n  affinity: 'forward' | 'backward' | 'outward' | 'inward' | null\n  unref(): Range | null\n}\n\nexport interface RangeRefInterface {\n  transform: (ref: RangeRef, op: Operation) => void\n}\n\nexport const RangeRef: RangeRefInterface = {\n  /**\n   * Transform the range ref's current value by an operation.\n   */\n\n  transform(ref: RangeRef, op: Operation): void {\n    const { current, affinity } = ref\n\n    if (current == null) {\n      return\n    }\n\n    const path = Range.transform(current, op, { affinity })\n    ref.current = path\n\n    if (path == null) {\n      ref.unref()\n    }\n  },\n}\n", "import { isPlainObject } from 'is-plain-object'\n\n/*\n  Custom deep equal comparison for Slate nodes.\n\n  We don't need general purpose deep equality;\n  Slate only supports plain values, Arrays, and nested objects.\n  Complex values nested inside Arrays are not supported.\n\n  Slate objects are designed to be serialised, so\n  missing keys are deliberately normalised to undefined.\n */\nexport const isDeepEqual = (\n  node: Record<string, any>,\n  another: Record<string, any>\n): boolean => {\n  for (const key in node) {\n    const a = node[key]\n    const b = another[key]\n    if (isPlainObject(a) && isPlainObject(b)) {\n      if (!isDeepEqual(a, b)) return false\n    } else if (Array.isArray(a) && Array.isArray(b)) {\n      if (a.length !== b.length) return false\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) return false\n      }\n    } else if (a !== b) {\n      return false\n    }\n  }\n\n  /*\n    Deep object equality is only necessary in one direction; in the reverse direction\n    we are only looking for keys that are missing.\n    As above, undefined keys are normalised to missing.\n  */\n\n  for (const key in another) {\n    if (node[key] === undefined && another[key] !== undefined) {\n      return false\n    }\n  }\n\n  return true\n}\n", "import { isPlainObject } from 'is-plain-object'\nimport { Range } from '..'\nimport { ExtendedType } from './custom-types'\nimport { isDeepEqual } from '../utils/deep-equal'\n\n/**\n * `Text` objects represent the nodes that contain the actual text content of a\n * Slate document along with any formatting properties. They are always leaf\n * nodes in the document tree as they cannot contain any children.\n */\n\nexport interface BaseText {\n  text: string\n}\n\nexport type Text = ExtendedType<'Text', BaseText>\n\nexport interface TextInterface {\n  equals: (text: Text, another: Text, options?: { loose?: boolean }) => boolean\n  isText: (value: any) => value is Text\n  isTextList: (value: any) => value is Text[]\n  isTextProps: (props: any) => props is Partial<Text>\n  matches: (text: Text, props: Partial<Text>) => boolean\n  decorations: (node: Text, decorations: Range[]) => Text[]\n}\n\nexport const Text: TextInterface = {\n  /**\n   * Check if two text nodes are equal.\n   *\n   * When loose is set, the text is not compared. This is\n   * used to check whether sibling text nodes can be merged.\n   */\n  equals(\n    text: Text,\n    another: Text,\n    options: { loose?: boolean } = {}\n  ): boolean {\n    const { loose = false } = options\n\n    function omitText(obj: Record<any, any>) {\n      const { text, ...rest } = obj\n\n      return rest\n    }\n\n    return isDeepEqual(\n      loose ? omitText(text) : text,\n      loose ? omitText(another) : another\n    )\n  },\n\n  /**\n   * Check if a value implements the `Text` interface.\n   */\n\n  isText(value: any): value is Text {\n    return isPlainObject(value) && typeof value.text === 'string'\n  },\n\n  /**\n   * Check if a value is a list of `Text` objects.\n   */\n\n  isTextList(value: any): value is Text[] {\n    return Array.isArray(value) && value.every(val => Text.isText(val))\n  },\n\n  /**\n   * Check if some props are a partial of Text.\n   */\n\n  isTextProps(props: any): props is Partial<Text> {\n    return (props as Partial<Text>).text !== undefined\n  },\n\n  /**\n   * Check if an text matches set of properties.\n   *\n   * Note: this is for matching custom properties, and it does not ensure that\n   * the `text` property are two nodes equal.\n   */\n\n  matches(text: Text, props: Partial<Text>): boolean {\n    for (const key in props) {\n      if (key === 'text') {\n        continue\n      }\n\n      if (!text.hasOwnProperty(key) || text[key] !== props[key]) {\n        return false\n      }\n    }\n\n    return true\n  },\n\n  /**\n   * Get the leaves for a text node given decorations.\n   */\n\n  decorations(node: Text, decorations: Range[]): Text[] {\n    let leaves: Text[] = [{ ...node }]\n\n    for (const dec of decorations) {\n      const { anchor, focus, ...rest } = dec\n      const [start, end] = Range.edges(dec)\n      const next = []\n      let o = 0\n\n      for (const leaf of leaves) {\n        const { length } = leaf.text\n        const offset = o\n        o += length\n\n        // If the range encompases the entire leaf, add the range.\n        if (start.offset <= offset && end.offset >= o) {\n          Object.assign(leaf, rest)\n          next.push(leaf)\n          continue\n        }\n\n        // If the range expanded and match the leaf, or starts after, or ends before it, continue.\n        if (\n          (start.offset !== end.offset &&\n            (start.offset === o || end.offset === offset)) ||\n          start.offset > o ||\n          end.offset < offset ||\n          (end.offset === offset && offset !== 0)\n        ) {\n          next.push(leaf)\n          continue\n        }\n\n        // Otherwise we need to split the leaf, at the start, end, or both,\n        // and add the range to the middle intersecting section. Do the end\n        // split first since we don't need to update the offset that way.\n        let middle = leaf\n        let before\n        let after\n\n        if (end.offset < o) {\n          const off = end.offset - offset\n          after = { ...middle, text: middle.text.slice(off) }\n          middle = { ...middle, text: middle.text.slice(0, off) }\n        }\n\n        if (start.offset > offset) {\n          const off = start.offset - offset\n          before = { ...middle, text: middle.text.slice(0, off) }\n          middle = { ...middle, text: middle.text.slice(off) }\n        }\n\n        Object.assign(middle, rest)\n\n        if (before) {\n          next.push(before)\n        }\n\n        next.push(middle)\n\n        if (after) {\n          next.push(after)\n        }\n      }\n\n      leaves = next\n    }\n\n    return leaves\n  },\n}\n", "import { createDraft, finishDraft, isDraft } from 'immer'\nimport {\n  Node,\n  Editor,\n  Selection,\n  Range,\n  Point,\n  Text,\n  Element,\n  Operation,\n  Descendant,\n  NodeEntry,\n  Path,\n  Ancestor,\n} from '..'\n\nexport interface GeneralTransforms {\n  transform: (editor: Editor, op: Operation) => void\n}\n\nconst applyToDraft = (editor: Editor, selection: Selection, op: Operation) => {\n  switch (op.type) {\n    case 'insert_node': {\n      const { path, node } = op\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n\n      if (index > parent.children.length) {\n        throw new Error(\n          `Cannot apply an \"insert_node\" operation at path [${path}] because the destination is past the end of the node.`\n        )\n      }\n\n      parent.children.splice(index, 0, node)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'insert_text': {\n      const { path, offset, text } = op\n      if (text.length === 0) break\n      const node = Node.leaf(editor, path)\n      const before = node.text.slice(0, offset)\n      const after = node.text.slice(offset)\n      node.text = before + text + after\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'merge_node': {\n      const { path } = op\n      const node = Node.get(editor, path)\n      const prevPath = Path.previous(path)\n      const prev = Node.get(editor, prevPath)\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n\n      if (Text.isText(node) && Text.isText(prev)) {\n        prev.text += node.text\n      } else if (!Text.isText(node) && !Text.isText(prev)) {\n        prev.children.push(...node.children)\n      } else {\n        throw new Error(\n          `Cannot apply a \"merge_node\" operation at path [${path}] to nodes of different interfaces: ${node} ${prev}`\n        )\n      }\n\n      parent.children.splice(index, 1)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'move_node': {\n      const { path, newPath } = op\n\n      if (Path.isAncestor(path, newPath)) {\n        throw new Error(\n          `Cannot move a path [${path}] to new path [${newPath}] because the destination is inside itself.`\n        )\n      }\n\n      const node = Node.get(editor, path)\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n\n      // This is tricky, but since the `path` and `newPath` both refer to\n      // the same snapshot in time, there's a mismatch. After either\n      // removing the original position, the second step's path can be out\n      // of date. So instead of using the `op.newPath` directly, we\n      // transform `op.path` to ascertain what the `newPath` would be after\n      // the operation was applied.\n      parent.children.splice(index, 1)\n      const truePath = Path.transform(path, op)!\n      const newParent = Node.get(editor, Path.parent(truePath)) as Ancestor\n      const newIndex = truePath[truePath.length - 1]\n\n      newParent.children.splice(newIndex, 0, node)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'remove_node': {\n      const { path } = op\n      const index = path[path.length - 1]\n      const parent = Node.parent(editor, path)\n      parent.children.splice(index, 1)\n\n      // Transform all of the points in the value, but if the point was in the\n      // node that was removed we need to update the range or remove it.\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          const result = Point.transform(point, op)\n\n          if (selection != null && result != null) {\n            selection[key] = result\n          } else {\n            let prev: NodeEntry<Text> | undefined\n            let next: NodeEntry<Text> | undefined\n\n            for (const [n, p] of Node.texts(editor)) {\n              if (Path.compare(p, path) === -1) {\n                prev = [n, p]\n              } else {\n                next = [n, p]\n                break\n              }\n            }\n\n            let preferNext = false\n            if (prev && next) {\n              if (Path.equals(next[1], path)) {\n                preferNext = !Path.hasPrevious(next[1])\n              } else {\n                preferNext =\n                  Path.common(prev[1], path).length <\n                  Path.common(next[1], path).length\n              }\n            }\n\n            if (prev && !preferNext) {\n              point.path = prev[1]\n              point.offset = prev[0].text.length\n            } else if (next) {\n              point.path = next[1]\n              point.offset = 0\n            } else {\n              selection = null\n            }\n          }\n        }\n      }\n\n      break\n    }\n\n    case 'remove_text': {\n      const { path, offset, text } = op\n      if (text.length === 0) break\n      const node = Node.leaf(editor, path)\n      const before = node.text.slice(0, offset)\n      const after = node.text.slice(offset + text.length)\n      node.text = before + after\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'set_node': {\n      const { path, properties, newProperties } = op\n\n      if (path.length === 0) {\n        throw new Error(`Cannot set properties on the root node!`)\n      }\n\n      const node = Node.get(editor, path)\n\n      for (const key in newProperties) {\n        if (key === 'children' || key === 'text') {\n          throw new Error(`Cannot set the \"${key}\" property of nodes!`)\n        }\n\n        const value = newProperties[key]\n\n        if (value == null) {\n          delete node[key]\n        } else {\n          node[key] = value\n        }\n      }\n\n      // properties that were previously defined, but are now missing, must be deleted\n      for (const key in properties) {\n        if (!newProperties.hasOwnProperty(key)) {\n          delete node[key]\n        }\n      }\n\n      break\n    }\n\n    case 'set_selection': {\n      const { newProperties } = op\n\n      if (newProperties == null) {\n        selection = newProperties\n      } else {\n        if (selection == null) {\n          if (!Range.isRange(newProperties)) {\n            throw new Error(\n              `Cannot apply an incomplete \"set_selection\" operation properties ${JSON.stringify(\n                newProperties\n              )} when there is no current selection.`\n            )\n          }\n\n          selection = { ...newProperties }\n        }\n\n        for (const key in newProperties) {\n          const value = newProperties[key]\n\n          if (value == null) {\n            if (key === 'anchor' || key === 'focus') {\n              throw new Error(`Cannot remove the \"${key}\" selection property`)\n            }\n\n            delete selection[key]\n          } else {\n            selection[key] = value\n          }\n        }\n      }\n\n      break\n    }\n\n    case 'split_node': {\n      const { path, position, properties } = op\n\n      if (path.length === 0) {\n        throw new Error(\n          `Cannot apply a \"split_node\" operation at path [${path}] because the root node cannot be split.`\n        )\n      }\n\n      const node = Node.get(editor, path)\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n      let newNode: Descendant\n\n      if (Text.isText(node)) {\n        const before = node.text.slice(0, position)\n        const after = node.text.slice(position)\n        node.text = before\n        newNode = {\n          ...(properties as Partial<Text>),\n          text: after,\n        }\n      } else {\n        const before = node.children.slice(0, position)\n        const after = node.children.slice(position)\n        node.children = before\n\n        newNode = {\n          ...(properties as Partial<Element>),\n          children: after,\n        }\n      }\n\n      parent.children.splice(index + 1, 0, newNode)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n  }\n  return selection\n}\n\nexport const GeneralTransforms: GeneralTransforms = {\n  /**\n   * Transform the editor by an operation.\n   */\n\n  transform(editor: Editor, op: Operation): void {\n    editor.children = createDraft(editor.children)\n    let selection = editor.selection && createDraft(editor.selection)\n\n    try {\n      selection = applyToDraft(editor, selection, op)\n    } finally {\n      editor.children = finishDraft(editor.children)\n\n      if (selection) {\n        editor.selection = isDraft(selection)\n          ? (finishDraft(selection) as Range)\n          : selection\n      } else {\n        editor.selection = null\n      }\n    }\n  },\n}\n", "import {\n  Editor,\n  Element,\n  Location,\n  Node,\n  Path,\n  Point,\n  Range,\n  Text,\n  Transforms,\n  NodeEntry,\n  Ancestor,\n} from '..'\nimport { NodeMatch } from '../interfaces/editor'\n\nexport interface NodeTransforms {\n  insertNodes: <T extends Node>(\n    editor: Editor,\n    nodes: Node | Node[],\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      select?: boolean\n      voids?: boolean\n    }\n  ) => void\n  liftNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => void\n  mergeNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  moveNodes: <T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      to: Path\n      voids?: boolean\n    }\n  ) => void\n  removeNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  setNodes: <T extends Node>(\n    editor: Editor,\n    props: Partial<T>,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      hanging?: boolean\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n  splitNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      always?: boolean\n      height?: number\n      voids?: boolean\n    }\n  ) => void\n  unsetNodes: <T extends Node>(\n    editor: Editor,\n    props: string | string[],\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n  unwrapNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n  wrapNodes: <T extends Node>(\n    editor: Editor,\n    element: Element,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n}\n\nexport const NodeTransforms: NodeTransforms = {\n  /**\n   * Insert nodes at a specific location in the Editor.\n   */\n\n  insertNodes<T extends Node>(\n    editor: Editor,\n    nodes: Node | Node[],\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      select?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { hanging = false, voids = false, mode = 'lowest' } = options\n      let { at, match, select } = options\n\n      if (Node.isNode(nodes)) {\n        nodes = [nodes]\n      }\n\n      if (nodes.length === 0) {\n        return\n      }\n\n      const [node] = nodes\n\n      // By default, use the selection as the target location. But if there is\n      // no selection, insert at the end of the document since that is such a\n      // common use case when inserting from a non-selected state.\n      if (!at) {\n        if (editor.selection) {\n          at = editor.selection\n        } else if (editor.children.length > 0) {\n          at = Editor.end(editor, [])\n        } else {\n          at = [0]\n        }\n\n        select = true\n      }\n\n      if (select == null) {\n        select = false\n      }\n\n      if (Range.isRange(at)) {\n        if (!hanging) {\n          at = Editor.unhangRange(editor, at)\n        }\n\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const [, end] = Range.edges(at)\n          const pointRef = Editor.pointRef(editor, end)\n          Transforms.delete(editor, { at })\n          at = pointRef.unref()!\n        }\n      }\n\n      if (Point.isPoint(at)) {\n        if (match == null) {\n          if (Text.isText(node)) {\n            match = n => Text.isText(n)\n          } else if (editor.isInline(node)) {\n            match = n => Text.isText(n) || Editor.isInline(editor, n)\n          } else {\n            match = n => Editor.isBlock(editor, n)\n          }\n        }\n\n        const [entry] = Editor.nodes(editor, {\n          at: at.path,\n          match,\n          mode,\n          voids,\n        })\n\n        if (entry) {\n          const [, matchPath] = entry\n          const pathRef = Editor.pathRef(editor, matchPath)\n          const isAtEnd = Editor.isEnd(editor, at, matchPath)\n          Transforms.splitNodes(editor, { at, match, mode, voids })\n          const path = pathRef.unref()!\n          at = isAtEnd ? Path.next(path) : path\n        } else {\n          return\n        }\n      }\n\n      const parentPath = Path.parent(at)\n      let index = at[at.length - 1]\n\n      if (!voids && Editor.void(editor, { at: parentPath })) {\n        return\n      }\n\n      for (const node of nodes) {\n        const path = parentPath.concat(index)\n        index++\n        editor.apply({ type: 'insert_node', path, node })\n        at = Path.next(at)\n      }\n      at = Path.previous(at)\n\n      if (select) {\n        const point = Editor.end(editor, at)\n\n        if (point) {\n          Transforms.select(editor, point)\n        }\n      }\n    })\n  },\n\n  /**\n   * Lift nodes at a specific location upwards in the document tree, splitting\n   * their parent in two if necessary.\n   */\n\n  liftNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { at = editor.selection, mode = 'lowest', voids = false } = options\n      let { match } = options\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (!at) {\n        return\n      }\n\n      const matches = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(matches, ([, p]) => Editor.pathRef(editor, p))\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n\n        if (path.length < 2) {\n          throw new Error(\n            `Cannot lift node at a path [${path}] because it has a depth of less than \\`2\\`.`\n          )\n        }\n\n        const parentNodeEntry = Editor.node(editor, Path.parent(path))\n        const [parent, parentPath] = parentNodeEntry as NodeEntry<Ancestor>\n        const index = path[path.length - 1]\n        const { length } = parent.children\n\n        if (length === 1) {\n          const toPath = Path.next(parentPath)\n          Transforms.moveNodes(editor, { at: path, to: toPath, voids })\n          Transforms.removeNodes(editor, { at: parentPath, voids })\n        } else if (index === 0) {\n          Transforms.moveNodes(editor, { at: path, to: parentPath, voids })\n        } else if (index === length - 1) {\n          const toPath = Path.next(parentPath)\n          Transforms.moveNodes(editor, { at: path, to: toPath, voids })\n        } else {\n          const splitPath = Path.next(path)\n          const toPath = Path.next(parentPath)\n          Transforms.splitNodes(editor, { at: splitPath, voids })\n          Transforms.moveNodes(editor, { at: path, to: toPath, voids })\n        }\n      }\n    })\n  },\n\n  /**\n   * Merge a node at a location with the previous node of the same depth,\n   * removing any empty containing nodes after the merge if necessary.\n   */\n\n  mergeNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      let { match, at = editor.selection } = options\n      const { hanging = false, voids = false, mode = 'lowest' } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        if (Path.isPath(at)) {\n          const [parent] = Editor.parent(editor, at)\n          match = n => parent.children.includes(n)\n        } else {\n          match = n => Editor.isBlock(editor, n)\n        }\n      }\n\n      if (!hanging && Range.isRange(at)) {\n        at = Editor.unhangRange(editor, at)\n      }\n\n      if (Range.isRange(at)) {\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const [, end] = Range.edges(at)\n          const pointRef = Editor.pointRef(editor, end)\n          Transforms.delete(editor, { at })\n          at = pointRef.unref()!\n\n          if (options.at == null) {\n            Transforms.select(editor, at)\n          }\n        }\n      }\n\n      const [current] = Editor.nodes(editor, { at, match, voids, mode })\n      const prev = Editor.previous(editor, { at, match, voids, mode })\n\n      if (!current || !prev) {\n        return\n      }\n\n      const [node, path] = current\n      const [prevNode, prevPath] = prev\n\n      if (path.length === 0 || prevPath.length === 0) {\n        return\n      }\n\n      const newPath = Path.next(prevPath)\n      const commonPath = Path.common(path, prevPath)\n      const isPreviousSibling = Path.isSibling(path, prevPath)\n      const levels = Array.from(Editor.levels(editor, { at: path }), ([n]) => n)\n        .slice(commonPath.length)\n        .slice(0, -1)\n\n      // Determine if the merge will leave an ancestor of the path empty as a\n      // result, in which case we'll want to remove it after merging.\n      const emptyAncestor = Editor.above(editor, {\n        at: path,\n        mode: 'highest',\n        match: n => levels.includes(n) && hasSingleChildNest(editor, n),\n      })\n\n      const emptyRef = emptyAncestor && Editor.pathRef(editor, emptyAncestor[1])\n      let properties\n      let position\n\n      // Ensure that the nodes are equivalent, and figure out what the position\n      // and extra properties of the merge will be.\n      if (Text.isText(node) && Text.isText(prevNode)) {\n        const { text, ...rest } = node\n        position = prevNode.text.length\n        properties = rest as Partial<Text>\n      } else if (Element.isElement(node) && Element.isElement(prevNode)) {\n        const { children, ...rest } = node\n        position = prevNode.children.length\n        properties = rest as Partial<Element>\n      } else {\n        throw new Error(\n          `Cannot merge the node at path [${path}] with the previous sibling because it is not the same kind: ${JSON.stringify(\n            node\n          )} ${JSON.stringify(prevNode)}`\n        )\n      }\n\n      // If the node isn't already the next sibling of the previous node, move\n      // it so that it is before merging.\n      if (!isPreviousSibling) {\n        Transforms.moveNodes(editor, { at: path, to: newPath, voids })\n      }\n\n      // If there was going to be an empty ancestor of the node that was merged,\n      // we remove it from the tree.\n      if (emptyRef) {\n        Transforms.removeNodes(editor, { at: emptyRef.current!, voids })\n      }\n\n      // If the target node that we're merging with is empty, remove it instead\n      // of merging the two. This is a common rich text editor behavior to\n      // prevent losing formatting when deleting entire nodes when you have a\n      // hanging selection.\n      // if prevNode is first child in parent,don't remove it.\n      if (\n        (Element.isElement(prevNode) && Editor.isEmpty(editor, prevNode)) ||\n        (Text.isText(prevNode) &&\n          prevNode.text === '' &&\n          prevPath[prevPath.length - 1] !== 0)\n      ) {\n        Transforms.removeNodes(editor, { at: prevPath, voids })\n      } else {\n        editor.apply({\n          type: 'merge_node',\n          path: newPath,\n          position,\n          properties,\n        })\n      }\n\n      if (emptyRef) {\n        emptyRef.unref()\n      }\n    })\n  },\n\n  /**\n   * Move the nodes at a location to a new location.\n   */\n\n  moveNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      to: Path\n      voids?: boolean\n    }\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const {\n        to,\n        at = editor.selection,\n        mode = 'lowest',\n        voids = false,\n      } = options\n      let { match } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      const toRef = Editor.pathRef(editor, to)\n      const targets = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(targets, ([, p]) => Editor.pathRef(editor, p))\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n        const newPath = toRef.current!\n\n        if (path.length !== 0) {\n          editor.apply({ type: 'move_node', path, newPath })\n        }\n\n        if (\n          toRef.current &&\n          Path.isSibling(newPath, path) &&\n          Path.isAfter(newPath, path)\n        ) {\n          // When performing a sibling move to a later index, the path at the destination is shifted\n          // to before the insertion point instead of after. To ensure our group of nodes are inserted\n          // in the correct order we increment toRef to account for that\n          toRef.current = Path.next(toRef.current)\n        }\n      }\n\n      toRef.unref()\n    })\n  },\n\n  /**\n   * Remove the nodes at a specific location in the document.\n   */\n\n  removeNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { hanging = false, voids = false, mode = 'lowest' } = options\n      let { at = editor.selection, match } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (!hanging && Range.isRange(at)) {\n        at = Editor.unhangRange(editor, at)\n      }\n\n      const depths = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(depths, ([, p]) => Editor.pathRef(editor, p))\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n\n        if (path) {\n          const [node] = Editor.node(editor, path)\n          editor.apply({ type: 'remove_node', path, node })\n        }\n      }\n    })\n  },\n\n  /**\n   * Set new properties on the nodes at a location.\n   */\n\n  setNodes<T extends Node>(\n    editor: Editor,\n    props: Partial<Node>,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      hanging?: boolean\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      let { match, at = editor.selection } = options\n      const {\n        hanging = false,\n        mode = 'lowest',\n        split = false,\n        voids = false,\n      } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (!hanging && Range.isRange(at)) {\n        at = Editor.unhangRange(editor, at)\n      }\n\n      if (split && Range.isRange(at)) {\n        if (\n          Range.isCollapsed(at) &&\n          Editor.leaf(editor, at.anchor)[0].text.length > 0\n        ) {\n          // If the range is collapsed in a non-empty node and 'split' is true, there's nothing to\n          // set that won't get normalized away\n          return\n        }\n        const rangeRef = Editor.rangeRef(editor, at, { affinity: 'inward' })\n        const [start, end] = Range.edges(at)\n        const splitMode = mode === 'lowest' ? 'lowest' : 'highest'\n        const endAtEndOfNode = Editor.isEnd(editor, end, end.path)\n        Transforms.splitNodes(editor, {\n          at: end,\n          match,\n          mode: splitMode,\n          voids,\n          always: !endAtEndOfNode,\n        })\n        const startAtStartOfNode = Editor.isStart(editor, start, start.path)\n        Transforms.splitNodes(editor, {\n          at: start,\n          match,\n          mode: splitMode,\n          voids,\n          always: !startAtStartOfNode,\n        })\n        at = rangeRef.unref()!\n\n        if (options.at == null) {\n          Transforms.select(editor, at)\n        }\n      }\n\n      for (const [node, path] of Editor.nodes(editor, {\n        at,\n        match,\n        mode,\n        voids,\n      })) {\n        const properties: Partial<Node> = {}\n        const newProperties: Partial<Node> = {}\n\n        // You can't set properties on the editor node.\n        if (path.length === 0) {\n          continue\n        }\n\n        let hasChanges = false\n\n        for (const k in props) {\n          if (k === 'children' || k === 'text') {\n            continue\n          }\n\n          if (props[k] !== node[k]) {\n            hasChanges = true\n            // Omit new properties from the old properties list\n            if (node.hasOwnProperty(k)) properties[k] = node[k]\n            // Omit properties that have been removed from the new properties list\n            if (props[k] != null) newProperties[k] = props[k]\n          }\n        }\n\n        if (hasChanges) {\n          editor.apply({\n            type: 'set_node',\n            path,\n            properties,\n            newProperties,\n          })\n        }\n      }\n    })\n  },\n\n  /**\n   * Split the nodes at a specific location.\n   */\n\n  splitNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      always?: boolean\n      height?: number\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { mode = 'lowest', voids = false } = options\n      let { match, at = editor.selection, height = 0, always = false } = options\n\n      if (match == null) {\n        match = n => Editor.isBlock(editor, n)\n      }\n\n      if (Range.isRange(at)) {\n        at = deleteRange(editor, at)\n      }\n\n      // If the target is a path, the default height-skipping and position\n      // counters need to account for us potentially splitting at a non-leaf.\n      if (Path.isPath(at)) {\n        const path = at\n        const point = Editor.point(editor, path)\n        const [parent] = Editor.parent(editor, path)\n        match = n => n === parent\n        height = point.path.length - path.length + 1\n        at = point\n        always = true\n      }\n\n      if (!at) {\n        return\n      }\n\n      const beforeRef = Editor.pointRef(editor, at, {\n        affinity: 'backward',\n      })\n      const [highest] = Editor.nodes(editor, { at, match, mode, voids })\n\n      if (!highest) {\n        return\n      }\n\n      const voidMatch = Editor.void(editor, { at, mode: 'highest' })\n      const nudge = 0\n\n      if (!voids && voidMatch) {\n        const [voidNode, voidPath] = voidMatch\n\n        if (Element.isElement(voidNode) && editor.isInline(voidNode)) {\n          let after = Editor.after(editor, voidPath)\n\n          if (!after) {\n            const text = { text: '' }\n            const afterPath = Path.next(voidPath)\n            Transforms.insertNodes(editor, text, { at: afterPath, voids })\n            after = Editor.point(editor, afterPath)!\n          }\n\n          at = after\n          always = true\n        }\n\n        const siblingHeight = at.path.length - voidPath.length\n        height = siblingHeight + 1\n        always = true\n      }\n\n      const afterRef = Editor.pointRef(editor, at)\n      const depth = at.path.length - height\n      const [, highestPath] = highest\n      const lowestPath = at.path.slice(0, depth)\n      let position = height === 0 ? at.offset : at.path[depth] + nudge\n\n      for (const [node, path] of Editor.levels(editor, {\n        at: lowestPath,\n        reverse: true,\n        voids,\n      })) {\n        let split = false\n\n        if (\n          path.length < highestPath.length ||\n          path.length === 0 ||\n          (!voids && Editor.isVoid(editor, node))\n        ) {\n          break\n        }\n\n        const point = beforeRef.current!\n        const isEnd = Editor.isEnd(editor, point, path)\n\n        if (always || !beforeRef || !Editor.isEdge(editor, point, path)) {\n          split = true\n          const properties = Node.extractProps(node)\n          editor.apply({\n            type: 'split_node',\n            path,\n            position,\n            properties,\n          })\n        }\n\n        position = path[path.length - 1] + (split || isEnd ? 1 : 0)\n      }\n\n      if (options.at == null) {\n        const point = afterRef.current || Editor.end(editor, [])\n        Transforms.select(editor, point)\n      }\n\n      beforeRef.unref()\n      afterRef.unref()\n    })\n  },\n\n  /**\n   * Unset properties on the nodes at a location.\n   */\n\n  unsetNodes<T extends Node>(\n    editor: Editor,\n    props: string | string[],\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    if (!Array.isArray(props)) {\n      props = [props]\n    }\n\n    const obj = {}\n\n    for (const key of props) {\n      obj[key] = null\n    }\n\n    Transforms.setNodes(editor, obj, options)\n  },\n\n  /**\n   * Unwrap the nodes at a location from a parent node, splitting the parent if\n   * necessary to ensure that only the content in the range is unwrapped.\n   */\n\n  unwrapNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { mode = 'lowest', split = false, voids = false } = options\n      let { at = editor.selection, match } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (Path.isPath(at)) {\n        at = Editor.range(editor, at)\n      }\n\n      const rangeRef = Range.isRange(at) ? Editor.rangeRef(editor, at) : null\n      const matches = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(\n        matches,\n        ([, p]) => Editor.pathRef(editor, p)\n        // unwrapNode will call liftNode which does not support splitting the node when nested.\n        // If we do not reverse the order and call it from top to the bottom, it will remove all blocks\n        // that wrap target node. So we reverse the order.\n      ).reverse()\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n        const [node] = Editor.node(editor, path)\n        let range = Editor.range(editor, path)\n\n        if (split && rangeRef) {\n          range = Range.intersection(rangeRef.current!, range)!\n        }\n\n        Transforms.liftNodes(editor, {\n          at: range,\n          match: n => Element.isAncestor(node) && node.children.includes(n),\n          voids,\n        })\n      }\n\n      if (rangeRef) {\n        rangeRef.unref()\n      }\n    })\n  },\n\n  /**\n   * Wrap the nodes at a location in a new container node, splitting the edges\n   * of the range first to ensure that only the content in the range is wrapped.\n   */\n\n  wrapNodes<T extends Node>(\n    editor: Editor,\n    element: Element,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { mode = 'lowest', split = false, voids = false } = options\n      let { match, at = editor.selection } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        if (Path.isPath(at)) {\n          match = matchPath(editor, at)\n        } else if (editor.isInline(element)) {\n          match = n => Editor.isInline(editor, n) || Text.isText(n)\n        } else {\n          match = n => Editor.isBlock(editor, n)\n        }\n      }\n\n      if (split && Range.isRange(at)) {\n        const [start, end] = Range.edges(at)\n        const rangeRef = Editor.rangeRef(editor, at, {\n          affinity: 'inward',\n        })\n        Transforms.splitNodes(editor, { at: end, match, voids })\n        Transforms.splitNodes(editor, { at: start, match, voids })\n        at = rangeRef.unref()!\n\n        if (options.at == null) {\n          Transforms.select(editor, at)\n        }\n      }\n\n      const roots = Array.from(\n        Editor.nodes(editor, {\n          at,\n          match: editor.isInline(element)\n            ? n => Editor.isBlock(editor, n)\n            : n => Editor.isEditor(n),\n          mode: 'lowest',\n          voids,\n        })\n      )\n\n      for (const [, rootPath] of roots) {\n        const a = Range.isRange(at)\n          ? Range.intersection(at, Editor.range(editor, rootPath))\n          : at\n\n        if (!a) {\n          continue\n        }\n\n        const matches = Array.from(\n          Editor.nodes(editor, { at: a, match, mode, voids })\n        )\n\n        if (matches.length > 0) {\n          const [first] = matches\n          const last = matches[matches.length - 1]\n          const [, firstPath] = first\n          const [, lastPath] = last\n\n          if (firstPath.length === 0 && lastPath.length === 0) {\n            // if there's no matching parent - usually means the node is an editor - don't do anything\n            continue\n          }\n\n          const commonPath = Path.equals(firstPath, lastPath)\n            ? Path.parent(firstPath)\n            : Path.common(firstPath, lastPath)\n\n          const range = Editor.range(editor, firstPath, lastPath)\n          const commonNodeEntry = Editor.node(editor, commonPath)\n          const [commonNode] = commonNodeEntry\n          const depth = commonPath.length + 1\n          const wrapperPath = Path.next(lastPath.slice(0, depth))\n          const wrapper = { ...element, children: [] }\n          Transforms.insertNodes(editor, wrapper, { at: wrapperPath, voids })\n\n          Transforms.moveNodes(editor, {\n            at: range,\n            match: n =>\n              Element.isAncestor(commonNode) && commonNode.children.includes(n),\n            to: wrapperPath.concat(0),\n            voids,\n          })\n        }\n      }\n    })\n  },\n}\n\nconst hasSingleChildNest = (editor: Editor, node: Node): boolean => {\n  if (Element.isElement(node)) {\n    const element = node as Element\n    if (Editor.isVoid(editor, node)) {\n      return true\n    } else if (element.children.length === 1) {\n      return hasSingleChildNest(editor, element.children[0])\n    } else {\n      return false\n    }\n  } else if (Editor.isEditor(node)) {\n    return false\n  } else {\n    return true\n  }\n}\n\n/**\n * Convert a range into a point by deleting it's content.\n */\n\nconst deleteRange = (editor: Editor, range: Range): Point | null => {\n  if (Range.isCollapsed(range)) {\n    return range.anchor\n  } else {\n    const [, end] = Range.edges(range)\n    const pointRef = Editor.pointRef(editor, end)\n    Transforms.delete(editor, { at: range })\n    return pointRef.unref()\n  }\n}\n\nconst matchPath = (editor: Editor, path: Path): ((node: Node) => boolean) => {\n  const [node] = Editor.node(editor, path)\n  return n => n === node\n}\n", "import { Editor, Location, Point, Range, Transforms } from '..'\n\nexport interface SelectionTransforms {\n  collapse: (\n    editor: Editor,\n    options?: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    }\n  ) => void\n  deselect: (editor: Editor) => void\n  move: (\n    editor: Editor,\n    options?: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line'\n      reverse?: boolean\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    }\n  ) => void\n  select: (editor: Editor, target: Location) => void\n  setPoint: (\n    editor: Editor,\n    props: Partial<Point>,\n    options?: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    }\n  ) => void\n  setSelection: (editor: Editor, props: Partial<Range>) => void\n}\n\nexport const SelectionTransforms: SelectionTransforms = {\n  /**\n   * Collapse the selection.\n   */\n\n  collapse(\n    editor: Editor,\n    options: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    } = {}\n  ): void {\n    const { edge = 'anchor' } = options\n    const { selection } = editor\n\n    if (!selection) {\n      return\n    } else if (edge === 'anchor') {\n      Transforms.select(editor, selection.anchor)\n    } else if (edge === 'focus') {\n      Transforms.select(editor, selection.focus)\n    } else if (edge === 'start') {\n      const [start] = Range.edges(selection)\n      Transforms.select(editor, start)\n    } else if (edge === 'end') {\n      const [, end] = Range.edges(selection)\n      Transforms.select(editor, end)\n    }\n  },\n\n  /**\n   * Unset the selection.\n   */\n\n  deselect(editor: Editor): void {\n    const { selection } = editor\n\n    if (selection) {\n      editor.apply({\n        type: 'set_selection',\n        properties: selection,\n        newProperties: null,\n      })\n    }\n  },\n\n  /**\n   * Move the selection's point forward or backward.\n   */\n\n  move(\n    editor: Editor,\n    options: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line'\n      reverse?: boolean\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    } = {}\n  ): void {\n    const { selection } = editor\n    const { distance = 1, unit = 'character', reverse = false } = options\n    let { edge = null } = options\n\n    if (!selection) {\n      return\n    }\n\n    if (edge === 'start') {\n      edge = Range.isBackward(selection) ? 'focus' : 'anchor'\n    }\n\n    if (edge === 'end') {\n      edge = Range.isBackward(selection) ? 'anchor' : 'focus'\n    }\n\n    const { anchor, focus } = selection\n    const opts = { distance, unit }\n    const props: Partial<Range> = {}\n\n    if (edge == null || edge === 'anchor') {\n      const point = reverse\n        ? Editor.before(editor, anchor, opts)\n        : Editor.after(editor, anchor, opts)\n\n      if (point) {\n        props.anchor = point\n      }\n    }\n\n    if (edge == null || edge === 'focus') {\n      const point = reverse\n        ? Editor.before(editor, focus, opts)\n        : Editor.after(editor, focus, opts)\n\n      if (point) {\n        props.focus = point\n      }\n    }\n\n    Transforms.setSelection(editor, props)\n  },\n\n  /**\n   * Set the selection to a new value.\n   */\n\n  select(editor: Editor, target: Location): void {\n    const { selection } = editor\n    target = Editor.range(editor, target)\n\n    if (selection) {\n      Transforms.setSelection(editor, target)\n      return\n    }\n\n    if (!Range.isRange(target)) {\n      throw new Error(\n        `When setting the selection and the current selection is \\`null\\` you must provide at least an \\`anchor\\` and \\`focus\\`, but you passed: ${JSON.stringify(\n          target\n        )}`\n      )\n    }\n\n    editor.apply({\n      type: 'set_selection',\n      properties: selection,\n      newProperties: target,\n    })\n  },\n\n  /**\n   * Set new properties on one of the selection's points.\n   */\n\n  setPoint(\n    editor: Editor,\n    props: Partial<Point>,\n    options: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    } = {}\n  ): void {\n    const { selection } = editor\n    let { edge = 'both' } = options\n\n    if (!selection) {\n      return\n    }\n\n    if (edge === 'start') {\n      edge = Range.isBackward(selection) ? 'focus' : 'anchor'\n    }\n\n    if (edge === 'end') {\n      edge = Range.isBackward(selection) ? 'anchor' : 'focus'\n    }\n\n    const { anchor, focus } = selection\n    const point = edge === 'anchor' ? anchor : focus\n\n    Transforms.setSelection(editor, {\n      [edge === 'anchor' ? 'anchor' : 'focus']: { ...point, ...props },\n    })\n  },\n\n  /**\n   * Set new properties on the selection.\n   */\n\n  setSelection(editor: Editor, props: Partial<Range>): void {\n    const { selection } = editor\n    const oldProps: Partial<Range> | null = {}\n    const newProps: Partial<Range> = {}\n\n    if (!selection) {\n      return\n    }\n\n    for (const k in props) {\n      if (\n        (k === 'anchor' &&\n          props.anchor != null &&\n          !Point.equals(props.anchor, selection.anchor)) ||\n        (k === 'focus' &&\n          props.focus != null &&\n          !Point.equals(props.focus, selection.focus)) ||\n        (k !== 'anchor' && k !== 'focus' && props[k] !== selection[k])\n      ) {\n        oldProps[k] = selection[k]\n        newProps[k] = props[k]\n      }\n    }\n\n    if (Object.keys(oldProps).length > 0) {\n      editor.apply({\n        type: 'set_selection',\n        properties: oldProps,\n        newProperties: newProps,\n      })\n    }\n  },\n}\n", "import {\n  Editor,\n  Element,\n  Location,\n  Node,\n  NodeEntry,\n  Path,\n  Text,\n  Point,\n  Range,\n  Transforms,\n} from '..'\n\nexport interface TextTransforms {\n  delete: (\n    editor: Editor,\n    options?: {\n      at?: Location\n      distance?: number\n      unit?: 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  insertFragment: (\n    editor: Editor,\n    fragment: Node[],\n    options?: {\n      at?: Location\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  insertText: (\n    editor: Editor,\n    text: string,\n    options?: {\n      at?: Location\n      voids?: boolean\n    }\n  ) => void\n}\n\nexport const TextTransforms: TextTransforms = {\n  /**\n   * Delete content in the editor.\n   */\n\n  delete(\n    editor: Editor,\n    options: {\n      at?: Location\n      distance?: number\n      unit?: 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const {\n        reverse = false,\n        unit = 'character',\n        distance = 1,\n        voids = false,\n      } = options\n      let { at = editor.selection, hanging = false } = options\n\n      if (!at) {\n        return\n      }\n\n      if (Range.isRange(at) && Range.isCollapsed(at)) {\n        at = at.anchor\n      }\n\n      if (Point.isPoint(at)) {\n        const furthestVoid = Editor.void(editor, { at, mode: 'highest' })\n\n        if (!voids && furthestVoid) {\n          const [, voidPath] = furthestVoid\n          at = voidPath\n        } else {\n          const opts = { unit, distance }\n          const target = reverse\n            ? Editor.before(editor, at, opts) || Editor.start(editor, [])\n            : Editor.after(editor, at, opts) || Editor.end(editor, [])\n          at = { anchor: at, focus: target }\n          hanging = true\n        }\n      }\n\n      if (Path.isPath(at)) {\n        Transforms.removeNodes(editor, { at, voids })\n        return\n      }\n\n      if (Range.isCollapsed(at)) {\n        return\n      }\n\n      if (!hanging) {\n        const [, end] = Range.edges(at)\n        const endOfDoc = Editor.end(editor, [])\n\n        if (!Point.equals(end, endOfDoc)) {\n          at = Editor.unhangRange(editor, at, { voids })\n        }\n      }\n\n      let [start, end] = Range.edges(at)\n      const startBlock = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n        at: start,\n        voids,\n      })\n      const endBlock = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n        at: end,\n        voids,\n      })\n      const isAcrossBlocks =\n        startBlock && endBlock && !Path.equals(startBlock[1], endBlock[1])\n      const isSingleText = Path.equals(start.path, end.path)\n      const startVoid = voids\n        ? null\n        : Editor.void(editor, { at: start, mode: 'highest' })\n      const endVoid = voids\n        ? null\n        : Editor.void(editor, { at: end, mode: 'highest' })\n\n      // If the start or end points are inside an inline void, nudge them out.\n      if (startVoid) {\n        const before = Editor.before(editor, start)\n\n        if (\n          before &&\n          startBlock &&\n          Path.isAncestor(startBlock[1], before.path)\n        ) {\n          start = before\n        }\n      }\n\n      if (endVoid) {\n        const after = Editor.after(editor, end)\n\n        if (after && endBlock && Path.isAncestor(endBlock[1], after.path)) {\n          end = after\n        }\n      }\n\n      // Get the highest nodes that are completely inside the range, as well as\n      // the start and end nodes.\n      const matches: NodeEntry[] = []\n      let lastPath: Path | undefined\n\n      for (const entry of Editor.nodes(editor, { at, voids })) {\n        const [node, path] = entry\n\n        if (lastPath && Path.compare(path, lastPath) === 0) {\n          continue\n        }\n\n        if (\n          (!voids && Editor.isVoid(editor, node)) ||\n          (!Path.isCommon(path, start.path) && !Path.isCommon(path, end.path))\n        ) {\n          matches.push(entry)\n          lastPath = path\n        }\n      }\n\n      const pathRefs = Array.from(matches, ([, p]) => Editor.pathRef(editor, p))\n      const startRef = Editor.pointRef(editor, start)\n      const endRef = Editor.pointRef(editor, end)\n\n      if (!isSingleText && !startVoid) {\n        const point = startRef.current!\n        const [node] = Editor.leaf(editor, point)\n        const { path } = point\n        const { offset } = start\n        const text = node.text.slice(offset)\n        if (text.length > 0)\n          editor.apply({ type: 'remove_text', path, offset, text })\n      }\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n        Transforms.removeNodes(editor, { at: path, voids })\n      }\n\n      if (!endVoid) {\n        const point = endRef.current!\n        const [node] = Editor.leaf(editor, point)\n        const { path } = point\n        const offset = isSingleText ? start.offset : 0\n        const text = node.text.slice(offset, end.offset)\n        if (text.length > 0)\n          editor.apply({ type: 'remove_text', path, offset, text })\n      }\n\n      if (\n        !isSingleText &&\n        isAcrossBlocks &&\n        endRef.current &&\n        startRef.current\n      ) {\n        Transforms.mergeNodes(editor, {\n          at: endRef.current,\n          hanging: true,\n          voids,\n        })\n      }\n\n      const point = reverse\n        ? startRef.unref() || endRef.unref()\n        : endRef.unref() || startRef.unref()\n\n      if (options.at == null && point) {\n        Transforms.select(editor, point)\n      }\n    })\n  },\n\n  /**\n   * Insert a fragment at a specific location in the editor.\n   */\n\n  insertFragment(\n    editor: Editor,\n    fragment: Node[],\n    options: {\n      at?: Location\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { hanging = false, voids = false } = options\n      let { at = editor.selection } = options\n\n      if (!fragment.length) {\n        return\n      }\n\n      if (!at) {\n        return\n      } else if (Range.isRange(at)) {\n        if (!hanging) {\n          at = Editor.unhangRange(editor, at)\n        }\n\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const [, end] = Range.edges(at)\n\n          if (!voids && Editor.void(editor, { at: end })) {\n            return\n          }\n\n          const pointRef = Editor.pointRef(editor, end)\n          Transforms.delete(editor, { at })\n          at = pointRef.unref()!\n        }\n      } else if (Path.isPath(at)) {\n        at = Editor.start(editor, at)\n      }\n\n      if (!voids && Editor.void(editor, { at })) {\n        return\n      }\n\n      // If the insert point is at the edge of an inline node, move it outside\n      // instead since it will need to be split otherwise.\n      const inlineElementMatch = Editor.above(editor, {\n        at,\n        match: n => Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })\n\n      if (inlineElementMatch) {\n        const [, inlinePath] = inlineElementMatch\n\n        if (Editor.isEnd(editor, at, inlinePath)) {\n          const after = Editor.after(editor, inlinePath)!\n          at = after\n        } else if (Editor.isStart(editor, at, inlinePath)) {\n          const before = Editor.before(editor, inlinePath)!\n          at = before\n        }\n      }\n\n      const blockMatch = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n        at,\n        voids,\n      })!\n      const [, blockPath] = blockMatch\n      const isBlockStart = Editor.isStart(editor, at, blockPath)\n      const isBlockEnd = Editor.isEnd(editor, at, blockPath)\n      const isBlockEmpty = isBlockStart && isBlockEnd\n      const mergeStart = !isBlockStart || (isBlockStart && isBlockEnd)\n      const mergeEnd = !isBlockEnd\n      const [, firstPath] = Node.first({ children: fragment }, [])\n      const [, lastPath] = Node.last({ children: fragment }, [])\n\n      const matches: NodeEntry[] = []\n      const matcher = ([n, p]: NodeEntry) => {\n        const isRoot = p.length === 0\n        if (isRoot) {\n          return false\n        }\n\n        if (isBlockEmpty) {\n          return true\n        }\n\n        if (\n          mergeStart &&\n          Path.isAncestor(p, firstPath) &&\n          Element.isElement(n) &&\n          !editor.isVoid(n) &&\n          !editor.isInline(n)\n        ) {\n          return false\n        }\n\n        if (\n          mergeEnd &&\n          Path.isAncestor(p, lastPath) &&\n          Element.isElement(n) &&\n          !editor.isVoid(n) &&\n          !editor.isInline(n)\n        ) {\n          return false\n        }\n\n        return true\n      }\n\n      for (const entry of Node.nodes(\n        { children: fragment },\n        { pass: matcher }\n      )) {\n        if (matcher(entry)) {\n          matches.push(entry)\n        }\n      }\n\n      const starts = []\n      const middles = []\n      const ends = []\n      let starting = true\n      let hasBlocks = false\n\n      for (const [node] of matches) {\n        if (Element.isElement(node) && !editor.isInline(node)) {\n          starting = false\n          hasBlocks = true\n          middles.push(node)\n        } else if (starting) {\n          starts.push(node)\n        } else {\n          ends.push(node)\n        }\n      }\n\n      const [inlineMatch] = Editor.nodes(editor, {\n        at,\n        match: n => Text.isText(n) || Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })!\n\n      const [, inlinePath] = inlineMatch\n      const isInlineStart = Editor.isStart(editor, at, inlinePath)\n      const isInlineEnd = Editor.isEnd(editor, at, inlinePath)\n\n      const middleRef = Editor.pathRef(\n        editor,\n        isBlockEnd ? Path.next(blockPath) : blockPath\n      )\n\n      const endRef = Editor.pathRef(\n        editor,\n        isInlineEnd ? Path.next(inlinePath) : inlinePath\n      )\n\n      const blockPathRef = Editor.pathRef(editor, blockPath)\n\n      Transforms.splitNodes(editor, {\n        at,\n        match: n =>\n          hasBlocks\n            ? Editor.isBlock(editor, n)\n            : Text.isText(n) || Editor.isInline(editor, n),\n        mode: hasBlocks ? 'lowest' : 'highest',\n        voids,\n      })\n\n      const startRef = Editor.pathRef(\n        editor,\n        !isInlineStart || (isInlineStart && isInlineEnd)\n          ? Path.next(inlinePath)\n          : inlinePath\n      )\n\n      Transforms.insertNodes(editor, starts, {\n        at: startRef.current!,\n        match: n => Text.isText(n) || Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })\n\n      if (isBlockEmpty && middles.length) {\n        Transforms.delete(editor, { at: blockPathRef.unref()!, voids })\n      }\n\n      Transforms.insertNodes(editor, middles, {\n        at: middleRef.current!,\n        match: n => Editor.isBlock(editor, n),\n        mode: 'lowest',\n        voids,\n      })\n\n      Transforms.insertNodes(editor, ends, {\n        at: endRef.current!,\n        match: n => Text.isText(n) || Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })\n\n      if (!options.at) {\n        let path\n\n        if (ends.length > 0) {\n          path = Path.previous(endRef.current!)\n        } else if (middles.length > 0) {\n          path = Path.previous(middleRef.current!)\n        } else {\n          path = Path.previous(startRef.current!)\n        }\n\n        const end = Editor.end(editor, path)\n        Transforms.select(editor, end)\n      }\n\n      startRef.unref()\n      middleRef.unref()\n      endRef.unref()\n    })\n  },\n\n  /**\n   * Insert a string of text in the Editor.\n   */\n\n  insertText(\n    editor: Editor,\n    text: string,\n    options: {\n      at?: Location\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { voids = false } = options\n      let { at = editor.selection } = options\n\n      if (!at) {\n        return\n      }\n\n      if (Path.isPath(at)) {\n        at = Editor.range(editor, at)\n      }\n\n      if (Range.isRange(at)) {\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const end = Range.end(at)\n          if (!voids && Editor.void(editor, { at: end })) {\n            return\n          }\n          const start = Range.start(at)\n          const pointRef = Editor.pointRef(editor, start)\n          Transforms.delete(editor, { at, voids })\n          at = pointRef.unref()!\n          Transforms.setSelection(editor, { anchor: at, focus: at })\n        }\n      }\n\n      if (!voids && Editor.void(editor, { at })) {\n        return\n      }\n\n      const { path, offset } = at\n      if (text.length > 0)\n        editor.apply({ type: 'insert_text', path, offset, text })\n    })\n  },\n}\n", "import { GeneralTransforms } from './general'\nimport { NodeTransforms } from './node'\nimport { SelectionTransforms } from './selection'\nimport { TextTransforms } from './text'\n\nexport const Transforms: GeneralTransforms &\n  NodeTransforms &\n  SelectionTransforms &\n  TextTransforms = {\n  ...GeneralTransforms,\n  ...NodeTransforms,\n  ...SelectionTransforms,\n  ...TextTransforms,\n}\n"], "names": ["DIRTY_PATHS", "WeakMap", "DIRTY_PATH_KEYS", "FLUSHING", "NORMALIZING", "PATH_REFS", "POINT_REFS", "RANGE_REFS", "createEditor", "editor", "children", "operations", "selection", "marks", "isInline", "isVoid", "onChange", "apply", "op", "Editor", "pathRefs", "ref", "PathRef", "transform", "pointRefs", "PointRef", "rangeRefs", "RangeRef", "old<PERSON><PERSON><PERSON><PERSON><PERSON>s", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "dirtyPaths", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "path", "key", "join", "has", "push", "Path", "operationCanTransformPath", "newPath", "newDirty<PERSON><PERSON><PERSON>", "getDirty<PERSON><PERSON>s", "set", "Transforms", "normalize", "type", "Promise", "resolve", "then", "addMark", "value", "Range", "isExpanded", "setNodes", "match", "Text", "isText", "split", "deleteBackward", "unit", "isCollapsed", "reverse", "deleteForward", "deleteFragment", "direction", "getFragment", "Node", "fragment", "insertBreak", "splitNodes", "always", "insertFragment", "insertNode", "node", "insertNodes", "insertText", "text", "normalizeNode", "entry", "Element", "isElement", "length", "child", "at", "concat", "voids", "shouldHaveInlines", "isEditor", "n", "i", "currentNode", "prev", "isLast", "isInlineOrText", "removeNodes", "<PERSON><PERSON><PERSON><PERSON>", "equals", "loose", "mergeNodes", "removeMark", "unsetNodes", "levels", "descendants", "Array", "from", "nodes", "p", "ancestors", "previousPath", "previous", "oldAncestors", "newAncestors", "ancestor", "newParent", "newIndex", "resultPath", "nextPath", "next", "getCharacterDistance", "str", "isRTL", "isLTR", "codepoints", "codepointsIteratorRTL", "left", "CodepointType", "None", "right", "distance", "gb11", "gb12Or13", "char", "code", "codePointAt", "getCodepointType", "intersects", "ZWJ", "ExtPict", "endsWithEmojiZWJ", "substring", "RI", "endsWithOddNumberOfRIs", "isBoundaryPair", "SPACE", "PUNCTUATION", "CHAMELEON", "getWordDistance", "dist", "started", "charDist", "splitByCharacterDistance", "remaining", "isWordCharacter", "slice", "test", "nextChar", "nextRemaining", "end", "char1", "char<PERSON>t", "isLowSurrogate", "charCodeAt", "char2", "isHighSurrogate", "charCode", "reExtend", "rePrepend", "reSpacingMark", "reL", "reV", "reT", "reLV", "reLVT", "reExtPict", "Any", "search", "Extend", "Prepend", "SpacingMark", "L", "V", "T", "LV", "LVT", "x", "y", "NonBoundaryPairs", "findIndex", "r", "endingEmojiZWJ", "endingRIs", "numRIs", "isPlainObject", "isNodeList", "isAncestor", "isElementList", "isArray", "every", "val", "isElementProps", "props", "undefined", "isElementType", "elementVal", "elementKey", "matches", "element", "IS_EDITOR_CACHE", "above", "options", "mode", "after", "anchor", "point", "edge", "focus", "range", "d", "target", "positions", "before", "start", "edges", "first", "hasBlocks", "some", "isBlock", "hasInlines", "hasTexts", "cachedIsEditor", "isRange", "Operation", "isOperationList", "isEnd", "Point", "isEdge", "isStart", "isEmpty", "isNormalizing", "offset", "last", "leaf", "rest", "block", "prevNode", "prevPath", "blockPath", "pointAfterLocation", "to", "span", "isPath", "Error", "parent", "includes", "universal", "Span", "isSpan", "nodeEntries", "pass", "hit", "isLower", "compare", "emit", "force", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popDirtyPath", "pop", "allPaths", "allPathKeys", "map", "withoutNormalizing", "<PERSON><PERSON><PERSON>", "_", "max", "m", "parentPath", "depth", "firstPath", "last<PERSON><PERSON>", "common", "isPoint", "<PERSON><PERSON><PERSON>", "pathRef", "affinity", "current", "unref", "refs", "pointRef", "isNewBlock", "blockText", "leafTextRemaining", "leafTextOffset", "e", "s", "string", "<PERSON><PERSON><PERSON><PERSON>", "calcDistance", "pointBeforeLocation", "rangeRef", "setNormalizing", "t", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endBlock", "skip", "isBefore", "fn", "Location", "isLocation", "IS_NODE_LIST_CACHE", "root", "index", "JSON", "stringify", "c", "child<PERSON><PERSON>", "another", "descendant", "elements", "extractProps", "properties", "newRoot", "produce", "splice", "isNode", "cachedResult", "isTextProps", "visited", "isAfter", "nextIndex", "texts", "isNodeOperation", "isOperation", "endsWith", "position", "newProperties", "isSelectionOperation", "isTextOperation", "inverse", "is<PERSON><PERSON>ling", "inversePath", "inverseNewPath", "paths", "av", "bv", "min", "Math", "endsAfter", "as", "bs", "endsAt", "endsBefore", "has<PERSON>revious", "<PERSON><PERSON><PERSON><PERSON>", "isCommon", "isDescendant", "isParent", "al", "bl", "list", "operation", "relative", "onp", "copy", "result", "isBackward", "rs", "re", "ts", "te", "isAfterStart", "isBeforeEnd", "intersection", "s1", "e1", "s2", "e2", "isForward", "points", "affinityAnchor", "affinityFocus", "isDeepEqual", "a", "b", "omitText", "obj", "isTextList", "hasOwnProperty", "decorations", "leaves", "dec", "o", "Object", "assign", "middle", "off", "applyToDraft", "truePath", "preferNext", "newNode", "GeneralTransforms", "createDraft", "finishDraft", "isDraft", "NodeTransforms", "hanging", "select", "matchPath", "isAtEnd", "liftNodes", "parentNodeEntry", "to<PERSON><PERSON>", "moveNodes", "splitPath", "commonPath", "isPreviousSibling", "emptyAncestor", "hasSingleChildNest", "emptyRef", "toRef", "targets", "depths", "splitMode", "endAtEndOfNode", "startAtStartOfNode", "has<PERSON><PERSON><PERSON>", "k", "height", "deleteRange", "beforeRef", "highest", "voidMatch", "nudge", "voidNode", "voidPath", "after<PERSON><PERSON>", "siblingHeight", "afterRef", "highestPath", "lowestPath", "unwrapNodes", "wrapNodes", "roots", "rootPath", "commonNodeEntry", "commonNode", "wrapperPath", "wrapper", "SelectionTransforms", "collapse", "deselect", "move", "opts", "setSelection", "setPoint", "oldProps", "newProps", "keys", "TextTransforms", "furthestVoid", "endOfDoc", "startBlock", "isAcrossBlocks", "isSingleText", "startVoid", "endVoid", "startRef", "endRef", "inlineElementMatch", "inlinePath", "blockMatch", "isBlockStart", "isBlockEnd", "isBlockEmpty", "mergeStart", "mergeEnd", "matcher", "isRoot", "starts", "middles", "ends", "starting", "inlineMatch", "isInlineStart", "isInlineEnd", "middleRef", "blockPathRef"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAAS,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE;AACrC,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AACxD;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACvD,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,cAAc,GAAG,iBAAiB,CAAC;AACnC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACT5E,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACjC,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACvD,CAAC;AACD;AACA,cAAc,GAAG,kBAAkB,CAAC;AACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACP5E,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5H,CAAC;AACD;AACA,cAAc,GAAG,gBAAgB,CAAC;AAClC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACH5E,SAAS,2BAA2B,CAAC,CAAC,EAAE,MAAM,EAAE;AAChD,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO;AACjB,EAAE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAChE,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,EAAE,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;AAC9D,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClH,CAAC;AACD;AACA,cAAc,GAAG,2BAA2B,CAAC;AAC7C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACZ5E,SAAS,kBAAkB,GAAG;AAC9B,EAAE,MAAM,IAAI,SAAS,CAAC,sIAAsI,CAAC,CAAC;AAC9J,CAAC;AACD;AACA,cAAc,GAAG,kBAAkB,CAAC;AACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACG5E,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACjC,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAClH,CAAC;AACD;AACA,cAAc,GAAG,kBAAkB,CAAC;AACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACb5E,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;AACrC,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACL5E,SAAS,qBAAqB,CAAC,GAAG,EAAE,CAAC,EAAE;AACvC,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;AAC3G;AACA,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,OAAO;AACzB,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAChB,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACjB;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACb;AACA,EAAE,IAAI;AACN,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE;AACtE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM;AACxC,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,EAAE,GAAG,IAAI,CAAC;AACd,IAAI,EAAE,GAAG,GAAG,CAAC;AACb,GAAG,SAAS;AACZ,IAAI,IAAI;AACR,MAAM,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;AACtD,KAAK,SAAS;AACd,MAAM,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC;AACvB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,cAAc,GAAG,qBAAqB,CAAC;AACvC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;AC/B5E,SAAS,gBAAgB,GAAG;AAC5B,EAAE,MAAM,IAAI,SAAS,CAAC,2IAA2I,CAAC,CAAC;AACnK,CAAC;AACD;AACA,cAAc,GAAG,gBAAgB,CAAC;AAClC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACG5E,SAAS,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE;AAChC,EAAE,OAAO,cAAc,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AACxH,CAAC;AACD;AACA,cAAc,GAAG,cAAc,CAAC;AAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACb5E,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AAC1C,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;AAClB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACpC,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC,CAAC;AACP,GAAG,MAAM;AACT,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;ACdrE,IAAMA,WAAW,GAA4B,IAAIC,OAAJ,EAA7C;AACA,IAAMC,eAAe,GAAiC,IAAID,OAAJ,EAAtD;AACA,IAAME,QAAQ,GAA6B,IAAIF,OAAJ,EAA3C;AACA,IAAMG,WAAW,GAA6B,IAAIH,OAAJ,EAA9C;AACA,IAAMI,SAAS,GAAkC,IAAIJ,OAAJ,EAAjD;AACA,IAAMK,UAAU,GAAmC,IAAIL,OAAJ,EAAnD;AACA,IAAMM,UAAU,GAAmC,IAAIN,OAAJ,EAAnD;;;;;;;;;;;ACSP;;;;IAIaO,YAAY,GAAG,SAAfA,YAAe;AAC1B,MAAMC,MAAM,GAAW;AACrBC,IAAAA,QAAQ,EAAE,EADW;AAErBC,IAAAA,UAAU,EAAE,EAFS;AAGrBC,IAAAA,SAAS,EAAE,IAHU;AAIrBC,IAAAA,KAAK,EAAE,IAJc;AAKrBC,IAAAA,QAAQ,EAAE;AAAA,aAAM,KAAN;AAAA,KALW;AAMrBC,IAAAA,MAAM,EAAE;AAAA,aAAM,KAAN;AAAA,KANa;AAOrBC,IAAAA,QAAQ,EAAE,sBAPW;AASrBC,IAAAA,KAAK,EAAE,eAACC,EAAD;mDACaC,MAAM,CAACC,QAAP,CAAgBX,MAAhB;;;;AAAlB,4DAA2C;AAAA,cAAhCY,GAAgC;AACzCC,UAAAA,OAAO,CAACC,SAAR,CAAkBF,GAAlB,EAAuBH,EAAvB;AACD;;;;;;;oDAEiBC,MAAM,CAACK,SAAP,CAAiBf,MAAjB;;;;AAAlB,+DAA4C;AAAA,cAAjCY,IAAiC;AAC1CI,UAAAA,QAAQ,CAACF,SAAT,CAAmBF,IAAnB,EAAwBH,EAAxB;AACD;;;;;;;oDAEiBC,MAAM,CAACO,SAAP,CAAiBjB,MAAjB;;;;AAAlB,+DAA4C;AAAA,cAAjCY,KAAiC;AAC1CM,UAAAA,QAAQ,CAACJ,SAAT,CAAmBF,KAAnB,EAAwBH,EAAxB;AACD;;;;;;;AAED,UAAMU,aAAa,GAAG5B,WAAW,CAAC6B,GAAZ,CAAgBpB,MAAhB,KAA2B,EAAjD;AACA,UAAMqB,gBAAgB,GAAG5B,eAAe,CAAC2B,GAAhB,CAAoBpB,MAApB,KAA+B,IAAIsB,GAAJ,EAAxD;AACA,UAAIC,UAAJ;AACA,UAAIC,aAAJ;;AAEA,UAAMC,GAAG,GAAG,SAANA,GAAM,CAACC,IAAD;AACV,YAAIA,IAAJ,EAAU;AACR,cAAMC,GAAG,GAAGD,IAAI,CAACE,IAAL,CAAU,GAAV,CAAZ;;AAEA,cAAI,CAACJ,aAAa,CAACK,GAAd,CAAkBF,GAAlB,CAAL,EAA6B;AAC3BH,YAAAA,aAAa,CAACC,GAAd,CAAkBE,GAAlB;AACAJ,YAAAA,UAAU,CAACO,IAAX,CAAgBJ,IAAhB;AACD;AACF;AACF,OATD;;AAWA,UAAIK,IAAI,CAACC,yBAAL,CAA+BvB,EAA/B,CAAJ,EAAwC;AACtCc,QAAAA,UAAU,GAAG,EAAb;AACAC,QAAAA,aAAa,GAAG,IAAIF,GAAJ,EAAhB;;AAFsC,sDAGnBH,aAHmB;AAAA;;AAAA;AAGtC,iEAAkC;AAAA,gBAAvBO,IAAuB;AAChC,gBAAMO,OAAO,GAAGF,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,CAAhB;AACAgB,YAAAA,GAAG,CAACQ,OAAD,CAAH;AACD;AANqC;AAAA;AAAA;AAAA;AAAA;AAOvC,OAPD,MAOO;AACLV,QAAAA,UAAU,GAAGJ,aAAb;AACAK,QAAAA,aAAa,GAAGH,gBAAhB;AACD;;AAED,UAAMa,aAAa,GAAGC,aAAa,CAAC1B,EAAD,CAAnC;;oDACmByB;;;;AAAnB,+DAAkC;AAAA,cAAvBR,KAAuB;AAChCD,UAAAA,GAAG,CAACC,KAAD,CAAH;AACD;;;;;;;AAEDnC,MAAAA,WAAW,CAAC6C,GAAZ,CAAgBpC,MAAhB,EAAwBuB,UAAxB;AACA9B,MAAAA,eAAe,CAAC2C,GAAhB,CAAoBpC,MAApB,EAA4BwB,aAA5B;AACAa,MAAAA,UAAU,CAACvB,SAAX,CAAqBd,MAArB,EAA6BS,EAA7B;AACAT,MAAAA,MAAM,CAACE,UAAP,CAAkB4B,IAAlB,CAAuBrB,EAAvB;AACAC,MAAAA,MAAM,CAAC4B,SAAP,CAAiBtC,MAAjB;;AAGA,UAAIS,EAAE,CAAC8B,IAAH,KAAY,eAAhB,EAAiC;AAC/BvC,QAAAA,MAAM,CAACI,KAAP,GAAe,IAAf;AACD;;AAED,UAAI,CAACV,QAAQ,CAAC0B,GAAT,CAAapB,MAAb,CAAL,EAA2B;AACzBN,QAAAA,QAAQ,CAAC0C,GAAT,CAAapC,MAAb,EAAqB,IAArB;AAEAwC,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB;AACrBhD,UAAAA,QAAQ,CAAC0C,GAAT,CAAapC,MAAb,EAAqB,KAArB;AACAA,UAAAA,MAAM,CAACO,QAAP;AACAP,UAAAA,MAAM,CAACE,UAAP,GAAoB,EAApB;AACD,SAJD;AAKD;AACF,KA3EoB;AA6ErByC,IAAAA,OAAO,EAAE,iBAAChB,GAAD,EAAciB,KAAd;AACP,UAAQzC,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,UAAIA,SAAJ,EAAe;AACb,YAAI0C,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAJ,EAAiC;AAC/BkC,UAAAA,UAAU,CAACU,QAAX,CACE/C,MADF,sBAEK2B,GAFL,EAEWiB,KAFX,GAGE;AAAEI,YAAAA,KAAK,EAAEC,IAAI,CAACC,MAAd;AAAsBC,YAAAA,KAAK,EAAE;AAA7B,WAHF;AAKD,SAND,MAMO;AACL,cAAM/C,KAAK,uCACLM,MAAM,CAACN,KAAP,CAAaJ,MAAb,KAAwB,EADnB,2BAER2B,GAFQ,EAEFiB,KAFE,EAAX;;AAKA5C,UAAAA,MAAM,CAACI,KAAP,GAAeA,KAAf;;AACA,cAAI,CAACV,QAAQ,CAAC0B,GAAT,CAAapB,MAAb,CAAL,EAA2B;AACzBA,YAAAA,MAAM,CAACO,QAAP;AACD;AACF;AACF;AACF,KAnGoB;AAqGrB6C,IAAAA,cAAc,EAAE,wBAACC,IAAD;AACd,UAAQlD,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,UAAIA,SAAS,IAAI0C,KAAK,CAACS,WAAN,CAAkBnD,SAAlB,CAAjB,EAA+C;AAC7CkC,QAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAEqD,UAAAA,IAAI,EAAJA,IAAF;AAAQE,UAAAA,OAAO,EAAE;AAAjB,SAA1B;AACD;AACF,KA3GoB;AA6GrBC,IAAAA,aAAa,EAAE,uBAACH,IAAD;AACb,UAAQlD,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,UAAIA,SAAS,IAAI0C,KAAK,CAACS,WAAN,CAAkBnD,SAAlB,CAAjB,EAA+C;AAC7CkC,QAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAEqD,UAAAA,IAAI,EAAJA;AAAF,SAA1B;AACD;AACF,KAnHoB;AAqHrBI,IAAAA,cAAc,EAAE,wBAACC,SAAD;AACd,UAAQvD,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,UAAIA,SAAS,IAAI0C,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAjB,EAA8C;AAC5CkC,QAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAEuD,UAAAA,OAAO,EAAEG,SAAS,KAAK;AAAzB,SAA1B;AACD;AACF,KA3HoB;AA6HrBC,IAAAA,WAAW,EAAE;AACX,UAAQxD,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,UAAIA,SAAJ,EAAe;AACb,eAAOyD,IAAI,CAACC,QAAL,CAAc7D,MAAd,EAAsBG,SAAtB,CAAP;AACD;;AACD,aAAO,EAAP;AACD,KApIoB;AAsIrB2D,IAAAA,WAAW,EAAE;AACXzB,MAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAAEgE,QAAAA,MAAM,EAAE;AAAV,OAA9B;AACD,KAxIoB;AA0IrBC,IAAAA,cAAc,EAAE,wBAACJ,QAAD;AACdxB,MAAAA,UAAU,CAAC4B,cAAX,CAA0BjE,MAA1B,EAAkC6D,QAAlC;AACD,KA5IoB;AA8IrBK,IAAAA,UAAU,EAAE,oBAACC,IAAD;AACV9B,MAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+BmE,IAA/B;AACD,KAhJoB;AAkJrBE,IAAAA,UAAU,EAAE,oBAACC,IAAD;AACV,UAAQnE,SAAR,GAA6BH,MAA7B,CAAQG,SAAR;AAAA,UAAmBC,KAAnB,GAA6BJ,MAA7B,CAAmBI,KAAnB;;AAEA,UAAID,SAAJ,EAAe;AACb,YAAIC,KAAJ,EAAW;AACT,cAAM+D,IAAI;AAAKG,YAAAA,IAAI,EAAJA;AAAL,aAAclE,KAAd,CAAV;;AACAiC,UAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+BmE,IAA/B;AACD,SAHD,MAGO;AACL9B,UAAAA,UAAU,CAACgC,UAAX,CAAsBrE,MAAtB,EAA8BsE,IAA9B;AACD;;AAEDtE,QAAAA,MAAM,CAACI,KAAP,GAAe,IAAf;AACD;AACF,KA/JoB;AAiKrBmE,IAAAA,aAAa,EAAE,uBAACC,KAAD;AACb,kCAAqBA,KAArB;AAAA,UAAOL,IAAP;AAAA,UAAazC,IAAb;;;AAGA,UAAIuB,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAJ,EAAuB;AACrB;AACD;;;AAGD,UAAIM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2BA,IAAI,CAAClE,QAAL,CAAc0E,MAAd,KAAyB,CAAxD,EAA2D;AACzD,YAAMC,KAAK,GAAG;AAAEN,UAAAA,IAAI,EAAE;AAAR,SAAd;AACAjC,QAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+B4E,KAA/B,EAAsC;AACpCC,UAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAY,CAAZ,CADgC;AAEpCC,UAAAA,KAAK,EAAE;AAF6B,SAAtC;AAIA;AACD;;;AAGD,UAAMC,iBAAiB,GAAGtE,MAAM,CAACuE,QAAP,CAAgBd,IAAhB,IACtB,KADsB,GAEtBM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,MACCnE,MAAM,CAACK,QAAP,CAAgB8D,IAAhB,KACCA,IAAI,CAAClE,QAAL,CAAc0E,MAAd,KAAyB,CAD1B,IAEC1B,IAAI,CAACC,MAAL,CAAYiB,IAAI,CAAClE,QAAL,CAAc,CAAd,CAAZ,CAFD,IAGCD,MAAM,CAACK,QAAP,CAAgB8D,IAAI,CAAClE,QAAL,CAAc,CAAd,CAAhB,CAJF,CAFJ;AASA;;AACA,UAAIiF,CAAC,GAAG,CAAR;;AAEA,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhB,IAAI,CAAClE,QAAL,CAAc0E,MAAlC,EAA0CQ,CAAC,IAAID,CAAC,EAAhD,EAAoD;AAClD,YAAME,WAAW,GAAGxB,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,IAAjB,CAApB;AACA,YAAIuB,IAAI,CAACC,MAAL,CAAYkC,WAAZ,CAAJ,EAA8B;AAC9B,YAAMR,MAAK,GAAGT,IAAI,CAAClE,QAAL,CAAckF,CAAd,CAAd;AACA,YAAME,IAAI,GAAGD,WAAW,CAACnF,QAAZ,CAAqBiF,CAAC,GAAG,CAAzB,CAAb;AACA,YAAMI,MAAM,GAAGH,CAAC,KAAKhB,IAAI,CAAClE,QAAL,CAAc0E,MAAd,GAAuB,CAA5C;AACA,YAAMY,cAAc,GAClBtC,IAAI,CAACC,MAAL,CAAY0B,MAAZ,KACCH,OAAO,CAACC,SAAR,CAAkBE,MAAlB,KAA4B5E,MAAM,CAACK,QAAP,CAAgBuE,MAAhB,CAF/B,CANkD;AAWlD;AACA;AACA;;AACA,YAAIW,cAAc,KAAKP,iBAAvB,EAA0C;AACxC3C,UAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAAE6E,YAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAYI,CAAZ,CAAN;AAAsBH,YAAAA,KAAK,EAAE;AAA7B,WAA/B;AACAG,UAAAA,CAAC;AACF,SAHD,MAGO,IAAIT,OAAO,CAACC,SAAR,CAAkBE,MAAlB,CAAJ,EAA8B;AACnC;AACA,cAAI5E,MAAM,CAACK,QAAP,CAAgBuE,MAAhB,CAAJ,EAA4B;AAC1B,gBAAIS,IAAI,IAAI,IAAR,IAAgB,CAACpC,IAAI,CAACC,MAAL,CAAYmC,IAAZ,CAArB,EAAwC;AACtC,kBAAMI,QAAQ,GAAG;AAAEnB,gBAAAA,IAAI,EAAE;AAAR,eAAjB;AACAjC,cAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+ByF,QAA/B,EAAyC;AACvCZ,gBAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAYI,CAAZ,CADmC;AAEvCH,gBAAAA,KAAK,EAAE;AAFgC,eAAzC;AAIAG,cAAAA,CAAC;AACF,aAPD,MAOO,IAAII,MAAJ,EAAY;AACjB,kBAAMG,SAAQ,GAAG;AAAEnB,gBAAAA,IAAI,EAAE;AAAR,eAAjB;AACAjC,cAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+ByF,SAA/B,EAAyC;AACvCZ,gBAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAYI,CAAC,GAAG,CAAhB,CADmC;AAEvCH,gBAAAA,KAAK,EAAE;AAFgC,eAAzC;AAIAG,cAAAA,CAAC;AACF;AACF;AACF,SAnBM,MAmBA;AACL;AACA,cAAIG,IAAI,IAAI,IAAR,IAAgBpC,IAAI,CAACC,MAAL,CAAYmC,IAAZ,CAApB,EAAuC;AACrC,gBAAIpC,IAAI,CAACyC,MAAL,CAAYd,MAAZ,EAAmBS,IAAnB,EAAyB;AAAEM,cAAAA,KAAK,EAAE;AAAT,aAAzB,CAAJ,EAA+C;AAC7CtD,cAAAA,UAAU,CAACuD,UAAX,CAAsB5F,MAAtB,EAA8B;AAAE6E,gBAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAYI,CAAZ,CAAN;AAAsBH,gBAAAA,KAAK,EAAE;AAA7B,eAA9B;AACAG,cAAAA,CAAC;AACF,aAHD,MAGO,IAAIG,IAAI,CAACf,IAAL,KAAc,EAAlB,EAAsB;AAC3BjC,cAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAC7B6E,gBAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAYI,CAAC,GAAG,CAAhB,CADyB;AAE7BH,gBAAAA,KAAK,EAAE;AAFsB,eAA/B;AAIAG,cAAAA,CAAC;AACF,aANM,MAMA,IAAIN,MAAK,CAACN,IAAN,KAAe,EAAnB,EAAuB;AAC5BjC,cAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAC7B6E,gBAAAA,EAAE,EAAEnD,IAAI,CAACoD,MAAL,CAAYI,CAAZ,CADyB;AAE7BH,gBAAAA,KAAK,EAAE;AAFsB,eAA/B;AAIAG,cAAAA,CAAC;AACF;AACF;AACF;AACF;AACF,KA1PoB;AA4PrBW,IAAAA,UAAU,EAAE,oBAAClE,GAAD;AACV,UAAQxB,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,UAAIA,SAAJ,EAAe;AACb,YAAI0C,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAJ,EAAiC;AAC/BkC,UAAAA,UAAU,CAACyD,UAAX,CAAsB9F,MAAtB,EAA8B2B,GAA9B,EAAmC;AACjCqB,YAAAA,KAAK,EAAEC,IAAI,CAACC,MADqB;AAEjCC,YAAAA,KAAK,EAAE;AAF0B,WAAnC;AAID,SALD,MAKO;AACL,cAAM/C,KAAK,uBAASM,MAAM,CAACN,KAAP,CAAaJ,MAAb,KAAwB,EAAjC,CAAX;;AACA,iBAAOI,KAAK,CAACuB,GAAD,CAAZ;AACA3B,UAAAA,MAAM,CAACI,KAAP,GAAeA,KAAf;;AACA,cAAI,CAACV,QAAQ,CAAC0B,GAAT,CAAapB,MAAb,CAAL,EAA2B;AACzBA,YAAAA,MAAM,CAACO,QAAP;AACD;AACF;AACF;AACF;AA9QoB,GAAvB;AAiRA,SAAOP,MAAP;AACD;AAED;;;;AAIA,IAAMmC,aAAa,GAAG,SAAhBA,aAAgB,CAAC1B,EAAD;AACpB,UAAQA,EAAE,CAAC8B,IAAX;AACE,SAAK,aAAL;AACA,SAAK,aAAL;AACA,SAAK,UAAL;AAAiB;AACf,YAAQb,IAAR,GAAiBjB,EAAjB,CAAQiB,IAAR;AACA,eAAOK,IAAI,CAACgE,MAAL,CAAYrE,IAAZ,CAAP;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAQyC,IAAR,GAAuB1D,EAAvB,CAAQ0D,IAAR;AAAA,YAAczC,MAAd,GAAuBjB,EAAvB,CAAciB,IAAd;AACA,YAAMqE,MAAM,GAAGhE,IAAI,CAACgE,MAAL,CAAYrE,MAAZ,CAAf;AACA,YAAMsE,WAAW,GAAG/C,IAAI,CAACC,MAAL,CAAYiB,IAAZ,IAChB,EADgB,GAEhB8B,KAAK,CAACC,IAAN,CAAWtC,IAAI,CAACuC,KAAL,CAAWhC,IAAX,CAAX,EAA6B;AAAA;AAAA,cAAIiC,CAAJ;;AAAA,iBAAW1E,MAAI,CAACoD,MAAL,CAAYsB,CAAZ,CAAX;AAAA,SAA7B,CAFJ;AAIA,4CAAWL,MAAX,sBAAsBC,WAAtB;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAQtE,MAAR,GAAiBjB,EAAjB,CAAQiB,IAAR;AACA,YAAM2E,SAAS,GAAGtE,IAAI,CAACsE,SAAL,CAAe3E,MAAf,CAAlB;AACA,YAAM4E,YAAY,GAAGvE,IAAI,CAACwE,QAAL,CAAc7E,MAAd,CAArB;AACA,4CAAW2E,SAAX,IAAsBC,YAAtB;AACD;;AAED,SAAK,WAAL;AAAkB;AAChB,YAAQ5E,MAAR,GAA0BjB,EAA1B,CAAQiB,IAAR;AAAA,YAAcO,OAAd,GAA0BxB,EAA1B,CAAcwB,OAAd;;AAEA,YAAIF,IAAI,CAAC2D,MAAL,CAAYhE,MAAZ,EAAkBO,OAAlB,CAAJ,EAAgC;AAC9B,iBAAO,EAAP;AACD;;AAED,YAAMuE,YAAY,GAAW,EAA7B;AACA,YAAMC,YAAY,GAAW,EAA7B;;AARgB,sDAUO1E,IAAI,CAACsE,SAAL,CAAe3E,MAAf,CAVP;AAAA;;AAAA;AAUhB,iEAA6C;AAAA,gBAAlCgF,QAAkC;AAC3C,gBAAMN,CAAC,GAAGrE,IAAI,CAACjB,SAAL,CAAe4F,QAAf,EAAyBjG,EAAzB,CAAV;AACA+F,YAAAA,YAAY,CAAC1E,IAAb,CAAkBsE,CAAlB;AACD;AAbe;AAAA;AAAA;AAAA;AAAA;;AAAA,sDAeOrE,IAAI,CAACsE,SAAL,CAAepE,OAAf,CAfP;AAAA;;AAAA;AAehB,iEAAgD;AAAA,gBAArCyE,SAAqC;;AAC9C,gBAAMN,EAAC,GAAGrE,IAAI,CAACjB,SAAL,CAAe4F,SAAf,EAAyBjG,EAAzB,CAAV;;AACAgG,YAAAA,YAAY,CAAC3E,IAAb,CAAkBsE,EAAlB;AACD;AAlBe;AAAA;AAAA;AAAA;AAAA;;AAoBhB,YAAMO,SAAS,GAAGF,YAAY,CAACA,YAAY,CAAC9B,MAAb,GAAsB,CAAvB,CAA9B;AACA,YAAMiC,QAAQ,GAAG3E,OAAO,CAACA,OAAO,CAAC0C,MAAR,GAAiB,CAAlB,CAAxB;AACA,YAAMkC,UAAU,GAAGF,SAAS,CAAC7B,MAAV,CAAiB8B,QAAjB,CAAnB;AAEA,yBAAWJ,YAAX,EAA4BC,YAA5B,GAA0CI,UAA1C;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAQnF,MAAR,GAAiBjB,EAAjB,CAAQiB,IAAR;;AACA,YAAM2E,UAAS,GAAGtE,IAAI,CAACsE,SAAL,CAAe3E,MAAf,CAAlB;;AACA,kCAAW2E,UAAX;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAQ3E,MAAR,GAAiBjB,EAAjB,CAAQiB,IAAR;;AACA,YAAMqE,OAAM,GAAGhE,IAAI,CAACgE,MAAL,CAAYrE,MAAZ,CAAf;;AACA,YAAMoF,QAAQ,GAAG/E,IAAI,CAACgF,IAAL,CAAUrF,MAAV,CAAjB;AACA,4CAAWqE,OAAX,IAAmBe,QAAnB;AACD;;AAED;AAAS;AACP,eAAO,EAAP;AACD;AAnEH;AAqED,CAtED;;;AC9SA,SAAS,6BAA6B,CAAC,MAAM,EAAE,QAAQ,EAAE;AACzD,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS;AAC7C,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA,cAAc,GAAG,6BAA6B,CAAC;AAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACd5E,SAAS,wBAAwB,CAAC,MAAM,EAAE,QAAQ,EAAE;AACpD,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAG,4BAA4B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9D,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA,EAAE,IAAI,MAAM,CAAC,qBAAqB,EAAE;AACpC,IAAI,IAAI,gBAAgB,GAAG,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AAChE;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,MAAM,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS;AAC/C,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,SAAS;AAC7E,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA,cAAc,GAAG,wBAAwB,CAAC;AAC1C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;;;;;;ACtB5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAIO,IAAME,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACC,GAAD;MAAcC,4EAAQ;AACxD,MAAMC,KAAK,GAAG,CAACD,KAAf;AACA,MAAME,UAAU,GAAGF,KAAK,GAAGG,qBAAqB,CAACJ,GAAD,CAAxB,GAAgCA,GAAxD;AAEA,MAAIK,IAAI,GAAkBC,aAAa,CAACC,IAAxC;AACA,MAAIC,KAAK,GAAkBF,aAAa,CAACC,IAAzC;AACA,MAAIE,QAAQ,GAAG,CAAf;;AAEA,MAAIC,IAAI,GAAmB,IAA3B;;AACA,MAAIC,QAAQ,GAAmB,IAA/B;;+CAEmBR;;;;AAAnB,wDAA+B;AAAA,UAApBS,KAAoB;;AAC7B,UAAMC,IAAI,GAAGD,KAAI,CAACE,WAAL,CAAiB,CAAjB,CAAb;;AACA,UAAI,CAACD,IAAL,EAAW;AAEX,UAAMvF,IAAI,GAAGyF,gBAAgB,CAACH,KAAD,EAAOC,IAAP,CAA7B;;AAJ6B,iBAKZX,KAAK,GAAG,CAACM,KAAD,EAAQlF,IAAR,CAAH,GAAmB,CAACA,IAAD,EAAO+E,IAAP,CALZ;;AAAA;;AAK3BA,MAAAA,IAL2B;AAKrBG,MAAAA,KALqB;;AAO7B,UACEQ,UAAU,CAACX,IAAD,EAAOC,aAAa,CAACW,GAArB,CAAV,IACAD,UAAU,CAACR,KAAD,EAAQF,aAAa,CAACY,OAAtB,CAFZ,EAGE;AACA,YAAIhB,KAAJ,EAAW;AACTQ,UAAAA,IAAI,GAAGS,gBAAgB,CAACnB,GAAG,CAACoB,SAAJ,CAAc,CAAd,EAAiBX,QAAjB,CAAD,CAAvB;AACD,SAFD,MAEO;AACLC,UAAAA,IAAI,GAAGS,gBAAgB,CAACnB,GAAG,CAACoB,SAAJ,CAAc,CAAd,EAAiBpB,GAAG,CAACtC,MAAJ,GAAa+C,QAA9B,CAAD,CAAvB;AACD;;AACD,YAAI,CAACC,IAAL,EAAW;AACZ;;AAED,UACEM,UAAU,CAACX,IAAD,EAAOC,aAAa,CAACe,EAArB,CAAV,IACAL,UAAU,CAACR,KAAD,EAAQF,aAAa,CAACe,EAAtB,CAFZ,EAGE;AACA,YAAIV,QAAQ,KAAK,IAAjB,EAAuB;AACrBA,UAAAA,QAAQ,GAAG,CAACA,QAAZ;AACD,SAFD,MAEO;AACL,cAAIT,KAAJ,EAAW;AACTS,YAAAA,QAAQ,GAAG,IAAX;AACD,WAFD,MAEO;AACLA,YAAAA,QAAQ,GAAGW,sBAAsB,CAC/BtB,GAAG,CAACoB,SAAJ,CAAc,CAAd,EAAiBpB,GAAG,CAACtC,MAAJ,GAAa+C,QAA9B,CAD+B,CAAjC;AAGD;AACF;;AACD,YAAI,CAACE,QAAL,EAAe;AAChB;;AAED,UACEN,IAAI,KAAKC,aAAa,CAACC,IAAvB,IACAC,KAAK,KAAKF,aAAa,CAACC,IADxB,IAEAgB,cAAc,CAAClB,IAAD,EAAOG,KAAP,CAHhB,EAIE;AACA;AACD;;AAEDC,MAAAA,QAAQ,IAAIG,KAAI,CAAClD,MAAjB;AACD;;;;;;;AAED,SAAO+C,QAAQ,IAAI,CAAnB;AACD,CA5DM;AA8DP,IAAMe,KAAK,GAAG,IAAd;AACA,IAAMC,WAAW,GAAG,oyCAApB;AACA,IAAMC,SAAS,GAAG,iBAAlB;AAEA;;;;AAIO,IAAMC,eAAe,GAAG,SAAlBA,eAAkB,CAACtE,IAAD;MAAe4C,4EAAQ;AACpD,MAAI2B,IAAI,GAAG,CAAX;AACA,MAAIC,OAAO,GAAG,KAAd;;AAEA,SAAOxE,IAAI,CAACK,MAAL,GAAc,CAArB,EAAwB;AACtB,QAAMoE,QAAQ,GAAG/B,oBAAoB,CAAC1C,IAAD,EAAO4C,KAAP,CAArC;;AACA,gCAA0B8B,wBAAwB,CAAC1E,IAAD,EAAOyE,QAAP,EAAiB7B,KAAjB,CAAlD;AAAA;AAAA,QAAOW,MAAP;AAAA,QAAaoB,SAAb;;AAEA,QAAIC,eAAe,CAACrB,MAAD,EAAOoB,SAAP,EAAkB/B,KAAlB,CAAnB,EAA6C;AAC3C4B,MAAAA,OAAO,GAAG,IAAV;AACAD,MAAAA,IAAI,IAAIE,QAAR;AACD,KAHD,MAGO,IAAI,CAACD,OAAL,EAAc;AACnBD,MAAAA,IAAI,IAAIE,QAAR;AACD,KAFM,MAEA;AACL;AACD;;AAEDzE,IAAAA,IAAI,GAAG2E,SAAP;AACD;;AAED,SAAOJ,IAAP;AACD,CArBM;AAuBP;;;;;AAKO,IAAMG,wBAAwB,GAAG,SAA3BA,wBAA2B,CACtC/B,GADsC,EAEtC4B,IAFsC,EAGtC3B,KAHsC;AAKtC,MAAIA,KAAJ,EAAW;AACT,QAAMrC,EAAE,GAAGoC,GAAG,CAACtC,MAAJ,GAAakE,IAAxB;AACA,WAAO,CAAC5B,GAAG,CAACkC,KAAJ,CAAUtE,EAAV,EAAcoC,GAAG,CAACtC,MAAlB,CAAD,EAA4BsC,GAAG,CAACkC,KAAJ,CAAU,CAAV,EAAatE,EAAb,CAA5B,CAAP;AACD;;AAED,SAAO,CAACoC,GAAG,CAACkC,KAAJ,CAAU,CAAV,EAAaN,IAAb,CAAD,EAAqB5B,GAAG,CAACkC,KAAJ,CAAUN,IAAV,CAArB,CAAP;AACD,CAXM;AAaP;;;;;AAKA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CACtBrB,MADsB,EAEtBoB,SAFsB;MAGtB/B,4EAAQ;;AAER,MAAIuB,KAAK,CAACW,IAAN,CAAWvB,MAAX,CAAJ,EAAsB;AACpB,WAAO,KAAP;AACD;AAGD;;;AACA,MAAIc,SAAS,CAACS,IAAV,CAAevB,MAAf,CAAJ,EAA0B;AACxB,QAAMkB,QAAQ,GAAG/B,oBAAoB,CAACiC,SAAD,EAAY/B,KAAZ,CAArC;;AACA,iCAAkC8B,wBAAwB,CACxDC,SADwD,EAExDF,QAFwD,EAGxD7B,KAHwD,CAA1D;AAAA;AAAA,QAAOmC,QAAP;AAAA,QAAiBC,aAAjB;;AAMA,QAAIJ,eAAe,CAACG,QAAD,EAAWC,aAAX,EAA0BpC,KAA1B,CAAnB,EAAqD;AACnD,aAAO,IAAP;AACD;AACF;;AAED,MAAIwB,WAAW,CAACU,IAAZ,CAAiBvB,MAAjB,CAAJ,EAA4B;AAC1B,WAAO,KAAP;AACD;;AAED,SAAO,IAAP;AACD,CA7BD;AA+BA;;;;;AAIO,IAAMR,qBAAqB,GAAG,UAAxBA,qBAAwB,CAAUJ,GAAV;AACnC,MAAMsC,GAAG,GAAGtC,GAAG,CAACtC,MAAJ,GAAa,CAAzB;;AAEA,OAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,GAAG,CAACtC,MAAxB,EAAgCQ,CAAC,EAAjC,EAAqC;AACnC,QAAMqE,KAAK,GAAGvC,GAAG,CAACwC,MAAJ,CAAWF,GAAG,GAAGpE,CAAjB,CAAd;;AAEA,QAAIuE,cAAc,CAACF,KAAK,CAACG,UAAN,CAAiB,CAAjB,CAAD,CAAlB,EAAyC;AACvC,UAAMC,KAAK,GAAG3C,GAAG,CAACwC,MAAJ,CAAWF,GAAG,GAAGpE,CAAN,GAAU,CAArB,CAAd;;AACA,UAAI0E,eAAe,CAACD,KAAK,CAACD,UAAN,CAAiB,CAAjB,CAAD,CAAnB,EAA0C;AACxC,cAAMC,KAAK,GAAGJ,KAAd;AAEArE,QAAAA,CAAC;AACD;AACD;AACF;;AAED,UAAMqE,KAAN;AACD;AACF,CAlBM;AAoBP;;;;;;AAMA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,QAAD;AACtB,SAAOA,QAAQ,IAAI,MAAZ,IAAsBA,QAAQ,IAAI,MAAzC;AACD,CAFD;AAIA;;;;;;;AAMA,IAAMJ,cAAc,GAAG,SAAjBA,cAAiB,CAACI,QAAD;AACrB,SAAOA,QAAQ,IAAI,MAAZ,IAAsBA,QAAQ,IAAI,MAAzC;AACD,CAFD;;AAIA,IAAKvC,aAAL;;AAAA,WAAKA;AACHA,EAAAA,wCAAA,SAAA;AACAA,EAAAA,0CAAA,WAAA;AACAA,EAAAA,uCAAA,QAAA;AACAA,EAAAA,sCAAA,OAAA;AACAA,EAAAA,2CAAA,YAAA;AACAA,EAAAA,gDAAA,gBAAA;AACAA,EAAAA,sCAAA,MAAA;AACAA,EAAAA,sCAAA,MAAA;AACAA,EAAAA,uCAAA,MAAA;AACAA,EAAAA,wCAAA,OAAA;AACAA,EAAAA,yCAAA,QAAA;AACAA,EAAAA,8CAAA,YAAA;AACAA,EAAAA,0CAAA,QAAA;AACD,CAdD,EAAKA,aAAa,KAAbA,aAAa,KAAA,CAAlB;;AAgBA,IAAMwC,QAAQ,GAAG,6nHAAjB;AACA,IAAMC,SAAS,GAAG,iJAAlB;AACA,IAAMC,aAAa,GAAG,8lDAAtB;AACA,IAAMC,GAAG,GAAG,gCAAZ;AACA,IAAMC,GAAG,GAAG,gCAAZ;AACA,IAAMC,GAAG,GAAG,gCAAZ;AACA,IAAMC,IAAI,GAAG,g2EAAb;AACA,IAAMC,KAAK,GAAG,ykKAAd;AACA,IAAMC,SAAS,GAAG,oyBAAlB;;AAEA,IAAMvC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACH,MAAD,EAAeC,IAAf;AACvB,MAAIvF,IAAI,GAAGgF,aAAa,CAACiD,GAAzB;;AACA,MAAI3C,MAAI,CAAC4C,MAAL,CAAYV,QAAZ,MAA0B,CAAC,CAA/B,EAAkC;AAChCxH,IAAAA,IAAI,IAAIgF,aAAa,CAACmD,MAAtB;AACD;;AACD,MAAI5C,IAAI,KAAK,MAAb,EAAqB;AACnBvF,IAAAA,IAAI,IAAIgF,aAAa,CAACW,GAAtB;AACD;;AACD,MAAIJ,IAAI,IAAI,OAAR,IAAmBA,IAAI,IAAI,OAA/B,EAAwC;AACtCvF,IAAAA,IAAI,IAAIgF,aAAa,CAACe,EAAtB;AACD;;AACD,MAAIT,MAAI,CAAC4C,MAAL,CAAYT,SAAZ,MAA2B,CAAC,CAAhC,EAAmC;AACjCzH,IAAAA,IAAI,IAAIgF,aAAa,CAACoD,OAAtB;AACD;;AACD,MAAI9C,MAAI,CAAC4C,MAAL,CAAYR,aAAZ,MAA+B,CAAC,CAApC,EAAuC;AACrC1H,IAAAA,IAAI,IAAIgF,aAAa,CAACqD,WAAtB;AACD;;AACD,MAAI/C,MAAI,CAAC4C,MAAL,CAAYP,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AAC3B3H,IAAAA,IAAI,IAAIgF,aAAa,CAACsD,CAAtB;AACD;;AACD,MAAIhD,MAAI,CAAC4C,MAAL,CAAYN,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AAC3B5H,IAAAA,IAAI,IAAIgF,aAAa,CAACuD,CAAtB;AACD;;AACD,MAAIjD,MAAI,CAAC4C,MAAL,CAAYL,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AAC3B7H,IAAAA,IAAI,IAAIgF,aAAa,CAACwD,CAAtB;AACD;;AACD,MAAIlD,MAAI,CAAC4C,MAAL,CAAYJ,IAAZ,MAAsB,CAAC,CAA3B,EAA8B;AAC5B9H,IAAAA,IAAI,IAAIgF,aAAa,CAACyD,EAAtB;AACD;;AACD,MAAInD,MAAI,CAAC4C,MAAL,CAAYH,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;AAC7B/H,IAAAA,IAAI,IAAIgF,aAAa,CAAC0D,GAAtB;AACD;;AACD,MAAIpD,MAAI,CAAC4C,MAAL,CAAYF,SAAZ,MAA2B,CAAC,CAAhC,EAAmC;AACjChI,IAAAA,IAAI,IAAIgF,aAAa,CAACY,OAAtB;AACD;;AAED,SAAO5F,IAAP;AACD,CArCD;;AAuCA,SAAS0F,UAAT,CAAoBiD,CAApB,EAAsCC,CAAtC;AACE,SAAO,CAACD,CAAC,GAAGC,CAAL,MAAY,CAAnB;AACD;;AAED,IAAMC,gBAAgB,GAAqC;AAEzD,CACE7D,aAAa,CAACsD,CADhB,EAEEtD,aAAa,CAACsD,CAAd,GAAkBtD,aAAa,CAACuD,CAAhC,GAAoCvD,aAAa,CAACyD,EAAlD,GAAuDzD,aAAa,CAAC0D,GAFvE,CAFyD;AAOzD,CAAC1D,aAAa,CAACyD,EAAd,GAAmBzD,aAAa,CAACuD,CAAlC,EAAqCvD,aAAa,CAACuD,CAAd,GAAkBvD,aAAa,CAACwD,CAArE,CAPyD;AASzD,CAACxD,aAAa,CAAC0D,GAAd,GAAoB1D,aAAa,CAACwD,CAAnC,EAAsCxD,aAAa,CAACwD,CAApD,CATyD;AAWzD,CAACxD,aAAa,CAACiD,GAAf,EAAoBjD,aAAa,CAACmD,MAAd,GAAuBnD,aAAa,CAACW,GAAzD,CAXyD;AAazD,CAACX,aAAa,CAACiD,GAAf,EAAoBjD,aAAa,CAACqD,WAAlC,CAbyD;AAezD,CAACrD,aAAa,CAACoD,OAAf,EAAwBpD,aAAa,CAACiD,GAAtC,CAfyD;AAiBzD,CAACjD,aAAa,CAACW,GAAf,EAAoBX,aAAa,CAACY,OAAlC,CAjByD;AAmBzD,CAACZ,aAAa,CAACe,EAAf,EAAmBf,aAAa,CAACe,EAAjC,CAnByD,CAA3D;;AAsBA,SAASE,cAAT,CAAwBlB,IAAxB,EAA6CG,KAA7C;AACE,SACE2D,gBAAgB,CAACC,SAAjB,CACE,UAAAC,CAAC;AAAA,WAAIrD,UAAU,CAACX,IAAD,EAAOgE,CAAC,CAAC,CAAD,CAAR,CAAV,IAA0BrD,UAAU,CAACR,KAAD,EAAQ6D,CAAC,CAAC,CAAD,CAAT,CAAxC;AAAA,GADH,MAEM,CAAC,CAHT;AAKD;;AAED,IAAMC,cAAc,GAAG,m6IAAvB;;AACA,IAAMnD,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACnB,GAAD;AACvB,SAAOA,GAAG,CAACwD,MAAJ,CAAWc,cAAX,MAA+B,CAAC,CAAvC;AACD,CAFD;;AAIA,IAAMC,SAAS,GAAG,8BAAlB;;AACA,IAAMjD,sBAAsB,GAAG,SAAzBA,sBAAyB,CAACtB,GAAD;AAC7B,MAAMjE,KAAK,GAAGiE,GAAG,CAACjE,KAAJ,CAAUwI,SAAV,CAAd;;AACA,MAAIxI,KAAK,KAAK,IAAd,EAAoB;AAClB,WAAO,KAAP;AACD,GAFD,MAEO;AACL;AACA,QAAMyI,MAAM,GAAGzI,KAAK,CAAC,CAAD,CAAL,CAAS2B,MAAT,GAAkB,CAAjC;AACA,WAAO8G,MAAM,GAAG,CAAT,KAAe,CAAtB;AACD;AACF,CATD;;AC1RA;;;;AAGA,IAAM/G,SAAS,GAAG,SAAZA,SAAY,CAAC9B,KAAD;AAChB,SACE8I,2BAAa,CAAC9I,KAAD,CAAb,IACAgB,IAAI,CAAC+H,UAAL,CAAgB/I,KAAK,CAAC3C,QAAtB,CADA,IAEA,CAACS,MAAM,CAACuE,QAAP,CAAgBrC,KAAhB,CAHH;AAKD,CAND;;IAQa6B,OAAO,GAAqB;AACvC;;;AAIAmH,EAAAA,UALuC,sBAK5BhJ,KAL4B;AAMrC,WAAO8I,2BAAa,CAAC9I,KAAD,CAAb,IAAwBgB,IAAI,CAAC+H,UAAL,CAAgB/I,KAAK,CAAC3C,QAAtB,CAA/B;AACD,GAPsC;;AASvC;;;AAIAyE,EAAAA,SAAS,EAATA,SAbuC;;AAcvC;;;AAIAmH,EAAAA,aAlBuC,yBAkBzBjJ,KAlByB;AAmBrC,WAAOqD,KAAK,CAAC6F,OAAN,CAAclJ,KAAd,KAAwBA,KAAK,CAACmJ,KAAN,CAAY,UAAAC,GAAG;AAAA,aAAIvH,OAAO,CAACC,SAAR,CAAkBsH,GAAlB,CAAJ;AAAA,KAAf,CAA/B;AACD,GApBsC;;AAsBvC;;;AAIAC,EAAAA,cA1BuC,0BA0BxBC,KA1BwB;AA2BrC,WAAQA,KAA0B,CAACjM,QAA3B,KAAwCkM,SAAhD;AACD,GA5BsC;;AA8BvC;;;;AAKAC,EAAAA,aAAa,EAAE,uBACbxJ,KADa,EAEbyJ,UAFa;QAGbC,iFAAqB;AAErB,WAAO5H,SAAS,CAAC9B,KAAD,CAAT,IAAoBA,KAAK,CAAC0J,UAAD,CAAL,KAAsBD,UAAjD;AACD,GAzCsC;;AA2CvC;;;;;;AAOAE,EAAAA,OAlDuC,mBAkD/BC,OAlD+B,EAkDbN,KAlDa;AAmDrC,SAAK,IAAMvK,GAAX,IAAkBuK,KAAlB,EAAyB;AACvB,UAAIvK,GAAG,KAAK,UAAZ,EAAwB;AACtB;AACD;;AAED,UAAI6K,OAAO,CAAC7K,GAAD,CAAP,KAAiBuK,KAAK,CAACvK,GAAD,CAA1B,EAAiC;AAC/B,eAAO,KAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD;AA9DsC;;;;;;;;;;;;;;ACwPzC,IAAM8K,eAAe,GAAG,IAAIjN,OAAJ,EAAxB;IAEakB,MAAM,GAAoB;AACrC;;;AAIAgM,EAAAA,KALqC,iBAMnC1M,MANmC;QAOnC2M,8EAKI;AAEJ,yBAKIA,OALJ,CACE5H,KADF;AAAA,QACEA,KADF,+BACU,KADV;AAAA,wBAKI4H,OALJ,CAEEC,IAFF;AAAA,QAEEA,IAFF,8BAES,QAFT;AAAA,sBAKID,OALJ,CAGE9H,EAHF;AAAA,QAGEA,EAHF,4BAGO7E,MAAM,CAACG,SAHd;AAAA,QAIE6C,KAJF,GAKI2J,OALJ,CAIE3J,KAJF;;AAOA,QAAI,CAAC6B,EAAL,EAAS;AACP;AACD;;AAED,QAAMnD,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,CAAb;AACA,QAAMtB,OAAO,GAAGqJ,IAAI,KAAK,QAAzB;;iDAEqBlM,MAAM,CAACqF,MAAP,CAAc/F,MAAd,EAAsB;AACzC6E,MAAAA,EAAE,EAAEnD,IADqC;AAEzCqD,MAAAA,KAAK,EAALA,KAFyC;AAGzC/B,MAAAA,KAAK,EAALA,KAHyC;AAIzCO,MAAAA,OAAO,EAAPA;AAJyC,KAAtB;;;;AAArB,0DAKI;AAAA;AAAA,YALQ2B,CAKR;AAAA,YALWkB,CAKX;;AACF,YAAI,CAACnD,IAAI,CAACC,MAAL,CAAYgC,CAAZ,CAAD,IAAmB,CAACnD,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkB0E,CAAlB,CAAxB,EAA8C;AAC5C,iBAAO,CAAClB,CAAD,EAAIkB,CAAJ,CAAP;AACD;AACF;;;;;;AACF,GAtCoC;;AAwCrC;;;;;;AAOAzD,EAAAA,OA/CqC,mBA+C7B3C,MA/C6B,EA+Cb2B,GA/Ca,EA+CAiB,KA/CA;AAgDnC5C,IAAAA,MAAM,CAAC2C,OAAP,CAAehB,GAAf,EAAoBiB,KAApB;AACD,GAjDoC;;AAmDrC;;;AAIAiK,EAAAA,KAvDqC,iBAwDnC7M,MAxDmC,EAyDnC6E,EAzDmC;QA0DnC8H,8EAII;AAEJ,QAAMG,MAAM,GAAGpM,MAAM,CAACqM,KAAP,CAAa/M,MAAb,EAAqB6E,EAArB,EAAyB;AAAEmI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAf;AACA,QAAMC,KAAK,GAAGvM,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB,EAAnB,CAAd;AACA,QAAMkN,KAAK,GAAG;AAAEJ,MAAAA,MAAM,EAANA,MAAF;AAAUG,MAAAA,KAAK,EAALA;AAAV,KAAd;AACA,4BAAyBN,OAAzB,CAAQjF,QAAR;AAAA,QAAQA,QAAR,kCAAmB,CAAnB;AACA,QAAIyF,CAAC,GAAG,CAAR;AACA,QAAIC,MAAJ;;kDAEgB1M,MAAM,CAAC2M,SAAP,CAAiBrN,MAAjB,sCACX2M,OADW;AAEd9H,MAAAA,EAAE,EAAEqI;AAFU;;;;AAAhB,6DAGI;AAAA,YAHO9G,CAGP;;AACF,YAAI+G,CAAC,GAAGzF,QAAR,EAAkB;AAChB;AACD;;AAED,YAAIyF,CAAC,KAAK,CAAV,EAAa;AACXC,UAAAA,MAAM,GAAGhH,CAAT;AACD;;AAED+G,QAAAA,CAAC;AACF;;;;;;;AAED,WAAOC,MAAP;AACD,GAvFoC;;AAyFrC;;;AAIAE,EAAAA,MA7FqC,kBA8FnCtN,MA9FmC,EA+FnC6E,EA/FmC;QAgGnC8H,8EAII;AAEJ,QAAMG,MAAM,GAAGpM,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB,EAArB,CAAf;AACA,QAAMiN,KAAK,GAAGvM,MAAM,CAACqM,KAAP,CAAa/M,MAAb,EAAqB6E,EAArB,EAAyB;AAAEmI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAd;AACA,QAAME,KAAK,GAAG;AAAEJ,MAAAA,MAAM,EAANA,MAAF;AAAUG,MAAAA,KAAK,EAALA;AAAV,KAAd;AACA,6BAAyBN,OAAzB,CAAQjF,QAAR;AAAA,QAAQA,QAAR,mCAAmB,CAAnB;AACA,QAAIyF,CAAC,GAAG,CAAR;AACA,QAAIC,MAAJ;;kDAEgB1M,MAAM,CAAC2M,SAAP,CAAiBrN,MAAjB,sCACX2M,OADW;AAEd9H,MAAAA,EAAE,EAAEqI,KAFU;AAGd3J,MAAAA,OAAO,EAAE;AAHK;;;;AAAhB,6DAII;AAAA,YAJO6C,CAIP;;AACF,YAAI+G,CAAC,GAAGzF,QAAR,EAAkB;AAChB;AACD;;AAED,YAAIyF,CAAC,KAAK,CAAV,EAAa;AACXC,UAAAA,MAAM,GAAGhH,CAAT;AACD;;AAED+G,QAAAA,CAAC;AACF;;;;;;;AAED,WAAOC,MAAP;AACD,GA9HoC;;AAgIrC;;;AAIAhK,EAAAA,cApIqC,0BAqInCpD,MArImC;QAsInC2M,8EAEI;AAEJ,wBAA+BA,OAA/B,CAAQtJ,IAAR;AAAA,QAAQA,IAAR,8BAAe,WAAf;AACArD,IAAAA,MAAM,CAACoD,cAAP,CAAsBC,IAAtB;AACD,GA5IoC;;AA8IrC;;;AAIAG,EAAAA,aAlJqC,yBAmJnCxD,MAnJmC;QAoJnC2M,8EAEI;AAEJ,yBAA+BA,OAA/B,CAAQtJ,IAAR;AAAA,QAAQA,IAAR,+BAAe,WAAf;AACArD,IAAAA,MAAM,CAACwD,aAAP,CAAqBH,IAArB;AACD,GA1JoC;;AA4JrC;;;AAIAI,EAAAA,cAhKqC,0BAiKnCzD,MAjKmC;QAkKnC2M,8EAEI;AAEJ,6BAAkCA,OAAlC,CAAQjJ,SAAR;AAAA,QAAQA,SAAR,mCAAoB,SAApB;AACA1D,IAAAA,MAAM,CAACyD,cAAP,CAAsBC,SAAtB;AACD,GAxKoC;;AA0KrC;;;AAIA8J,EAAAA,KA9KqC,iBA8K/BxN,MA9K+B,EA8Kf6E,EA9Ke;AA+KnC,WAAO,CAACnE,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB6E,EAArB,CAAD,EAA2BnE,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB6E,EAAnB,CAA3B,CAAP;AACD,GAhLoC;;AAkLrC;;;AAIA0E,EAAAA,GAtLqC,eAsLjCvJ,MAtLiC,EAsLjB6E,EAtLiB;AAuLnC,WAAOnE,MAAM,CAACqM,KAAP,CAAa/M,MAAb,EAAqB6E,EAArB,EAAyB;AAAEmI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAP;AACD,GAxLoC;;AA0LrC;;;AAIAS,EAAAA,KA9LqC,iBA8L/BzN,MA9L+B,EA8Lf6E,EA9Le;AA+LnC,QAAMnD,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB;AAAEmI,MAAAA,IAAI,EAAE;AAAR,KAAxB,CAAb;AACA,WAAOtM,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB0B,IAApB,CAAP;AACD,GAjMoC;;AAmMrC;;;AAIAmC,EAAAA,QAvMqC,oBAuM5B7D,MAvM4B,EAuMZ6E,EAvMY;AAwMnC,QAAMqI,KAAK,GAAGxM,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB6E,EAArB,CAAd;AACA,QAAMhB,QAAQ,GAAGD,IAAI,CAACC,QAAL,CAAc7D,MAAd,EAAsBkN,KAAtB,CAAjB;AACA,WAAOrJ,QAAP;AACD,GA3MoC;;AA4MrC;;;AAIA6J,EAAAA,SAhNqC,qBAgN3B1N,MAhN2B,EAgNXwM,OAhNW;AAiNnC,WAAOA,OAAO,CAACvM,QAAR,CAAiB0N,IAAjB,CAAsB,UAAAzI,CAAC;AAAA,aAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,KAAvB,CAAP;AACD,GAlNoC;;AAoNrC;;;AAIA2I,EAAAA,UAxNqC,sBAwN1B7N,MAxN0B,EAwNVwM,OAxNU;AAyNnC,WAAOA,OAAO,CAACvM,QAAR,CAAiB0N,IAAjB,CACL,UAAAzI,CAAC;AAAA,aAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAAtB;AAAA,KADI,CAAP;AAGD,GA5NoC;;AA8NrC;;;AAIA4I,EAAAA,QAlOqC,oBAkO5B9N,MAlO4B,EAkOZwM,OAlOY;AAmOnC,WAAOA,OAAO,CAACvM,QAAR,CAAiB8L,KAAjB,CAAuB,UAAA7G,CAAC;AAAA,aAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,CAAJ;AAAA,KAAxB,CAAP;AACD,GApOoC;;AAsOrC;;;;;AAMApB,EAAAA,WA5OqC,uBA4OzB9D,MA5OyB;AA6OnCA,IAAAA,MAAM,CAAC8D,WAAP;AACD,GA9OoC;;AAgPrC;;;;;AAMAG,EAAAA,cAtPqC,0BAsPtBjE,MAtPsB,EAsPN6D,QAtPM;AAuPnC7D,IAAAA,MAAM,CAACiE,cAAP,CAAsBJ,QAAtB;AACD,GAxPoC;;AA0PrC;;;;;AAMAK,EAAAA,UAhQqC,sBAgQ1BlE,MAhQ0B,EAgQVmE,IAhQU;AAiQnCnE,IAAAA,MAAM,CAACkE,UAAP,CAAkBC,IAAlB;AACD,GAlQoC;;AAoQrC;;;;;AAMAE,EAAAA,UA1QqC,sBA0Q1BrE,MA1Q0B,EA0QVsE,IA1QU;AA2QnCtE,IAAAA,MAAM,CAACqE,UAAP,CAAkBC,IAAlB;AACD,GA5QoC;;AA8QrC;;;AAIAsJ,EAAAA,OAlRqC,mBAkR7B5N,MAlR6B,EAkRb4C,KAlRa;AAmRnC,WAAO6B,OAAO,CAACC,SAAR,CAAkB9B,KAAlB,KAA4B,CAAC5C,MAAM,CAACK,QAAP,CAAgBuC,KAAhB,CAApC;AACD,GApRoC;;AAsRrC;;;AAIAqC,EAAAA,QA1RqC,oBA0R5BrC,KA1R4B;AA2RnC,QAAI,CAAC8I,2BAAa,CAAC9I,KAAD,CAAlB,EAA2B,OAAO,KAAP;AAC3B,QAAMmL,cAAc,GAAGtB,eAAe,CAACrL,GAAhB,CAAoBwB,KAApB,CAAvB;;AACA,QAAImL,cAAc,KAAK5B,SAAvB,EAAkC;AAChC,aAAO4B,cAAP;AACD;;AACD,QAAM9I,QAAQ,GACZ,OAAOrC,KAAK,CAACD,OAAb,KAAyB,UAAzB,IACA,OAAOC,KAAK,CAACpC,KAAb,KAAuB,UADvB,IAEA,OAAOoC,KAAK,CAACQ,cAAb,KAAgC,UAFhC,IAGA,OAAOR,KAAK,CAACY,aAAb,KAA+B,UAH/B,IAIA,OAAOZ,KAAK,CAACa,cAAb,KAAgC,UAJhC,IAKA,OAAOb,KAAK,CAACkB,WAAb,KAA6B,UAL7B,IAMA,OAAOlB,KAAK,CAACqB,cAAb,KAAgC,UANhC,IAOA,OAAOrB,KAAK,CAACsB,UAAb,KAA4B,UAP5B,IAQA,OAAOtB,KAAK,CAACyB,UAAb,KAA4B,UAR5B,IASA,OAAOzB,KAAK,CAACvC,QAAb,KAA0B,UAT1B,IAUA,OAAOuC,KAAK,CAACtC,MAAb,KAAwB,UAVxB,IAWA,OAAOsC,KAAK,CAAC2B,aAAb,KAA+B,UAX/B,IAYA,OAAO3B,KAAK,CAACrC,QAAb,KAA0B,UAZ1B,IAaA,OAAOqC,KAAK,CAACiD,UAAb,KAA4B,UAb5B,KAcCjD,KAAK,CAACxC,KAAN,KAAgB,IAAhB,IAAwBsL,2BAAa,CAAC9I,KAAK,CAACxC,KAAP,CAdtC,MAeCwC,KAAK,CAACzC,SAAN,KAAoB,IAApB,IAA4B0C,KAAK,CAACmL,OAAN,CAAcpL,KAAK,CAACzC,SAApB,CAf7B,KAgBAyD,IAAI,CAAC+H,UAAL,CAAgB/I,KAAK,CAAC3C,QAAtB,CAhBA,IAiBAgO,SAAS,CAACC,eAAV,CAA0BtL,KAAK,CAAC1C,UAAhC,CAlBF;AAmBAuM,IAAAA,eAAe,CAACrK,GAAhB,CAAoBQ,KAApB,EAA2BqC,QAA3B;AACA,WAAOA,QAAP;AACD,GArToC;;AAuTrC;;;AAIAkJ,EAAAA,KA3TqC,iBA2T/BnO,MA3T+B,EA2Tf+M,KA3Te,EA2TDlI,EA3TC;AA4TnC,QAAM0E,GAAG,GAAG7I,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB6E,EAAnB,CAAZ;AACA,WAAOuJ,KAAK,CAAC1I,MAAN,CAAaqH,KAAb,EAAoBxD,GAApB,CAAP;AACD,GA9ToC;;AAgUrC;;;AAIA8E,EAAAA,MApUqC,kBAoU9BrO,MApU8B,EAoUd+M,KApUc,EAoUAlI,EApUA;AAqUnC,WAAOnE,MAAM,CAAC4N,OAAP,CAAetO,MAAf,EAAuB+M,KAAvB,EAA8BlI,EAA9B,KAAqCnE,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqB+M,KAArB,EAA4BlI,EAA5B,CAA5C;AACD,GAtUoC;;AAwUrC;;;AAIA0J,EAAAA,OA5UqC,mBA4U7BvO,MA5U6B,EA4UbwM,OA5Ua;AA6UnC,QAAQvM,QAAR,GAAqBuM,OAArB,CAAQvM,QAAR;;AACA,mCAAgBA,QAAhB;AAAA,QAAOwN,KAAP;;AACA,WACExN,QAAQ,CAAC0E,MAAT,KAAoB,CAApB,IACC1E,QAAQ,CAAC0E,MAAT,KAAoB,CAApB,IACC1B,IAAI,CAACC,MAAL,CAAYuK,KAAZ,CADD,IAECA,KAAK,CAACnJ,IAAN,KAAe,EAFhB,IAGC,CAACtE,MAAM,CAACM,MAAP,CAAckM,OAAd,CALL;AAOD,GAtVoC;;AAwVrC;;;AAIAnM,EAAAA,QA5VqC,oBA4V5BL,MA5V4B,EA4VZ4C,KA5VY;AA6VnC,WAAO6B,OAAO,CAACC,SAAR,CAAkB9B,KAAlB,KAA4B5C,MAAM,CAACK,QAAP,CAAgBuC,KAAhB,CAAnC;AACD,GA9VoC;;AAgWrC;;;AAIA4L,EAAAA,aApWqC,yBAoWvBxO,MApWuB;AAqWnC,QAAMwO,aAAa,GAAG7O,WAAW,CAACyB,GAAZ,CAAgBpB,MAAhB,CAAtB;AACA,WAAOwO,aAAa,KAAKrC,SAAlB,GAA8B,IAA9B,GAAqCqC,aAA5C;AACD,GAvWoC;;AAyWrC;;;AAIAF,EAAAA,OA7WqC,mBA6W7BtO,MA7W6B,EA6Wb+M,KA7Wa,EA6WClI,EA7WD;AA8WnC;AACA,QAAIkI,KAAK,CAAC0B,MAAN,KAAiB,CAArB,EAAwB;AACtB,aAAO,KAAP;AACD;;AAED,QAAMlB,KAAK,GAAG7M,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB6E,EAArB,CAAd;AACA,WAAOuJ,KAAK,CAAC1I,MAAN,CAAaqH,KAAb,EAAoBQ,KAApB,CAAP;AACD,GArXoC;;AAuXrC;;;AAIAjN,EAAAA,MA3XqC,kBA2X9BN,MA3X8B,EA2Xd4C,KA3Xc;AA4XnC,WAAO6B,OAAO,CAACC,SAAR,CAAkB9B,KAAlB,KAA4B5C,MAAM,CAACM,MAAP,CAAcsC,KAAd,CAAnC;AACD,GA7XoC;;AA+XrC;;;AAIA8L,EAAAA,IAnYqC,gBAmYhC1O,MAnYgC,EAmYhB6E,EAnYgB;AAoYnC,QAAMnD,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB;AAAEmI,MAAAA,IAAI,EAAE;AAAR,KAAxB,CAAb;AACA,WAAOtM,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB0B,IAApB,CAAP;AACD,GAtYoC;;AAwYrC;;;AAIAiN,EAAAA,IA5YqC,gBA6YnC3O,MA7YmC,EA8YnC6E,EA9YmC;QA+YnC8H,8EAGI;AAEJ,QAAMjL,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB8H,OAAxB,CAAb;AACA,QAAMxI,IAAI,GAAGP,IAAI,CAAC+K,IAAL,CAAU3O,MAAV,EAAkB0B,IAAlB,CAAb;AACA,WAAO,CAACyC,IAAD,EAAOzC,IAAP,CAAP;AACD,GAvZoC;;AAyZrC;;;AAICqE,EAAAA,MA7ZoC,mBA8ZnC/F,MA9ZmC;QA+ZnC2M,8EAKI;AAEJ,uBAAkEA,OAAlE,CAAQ9H,EAAR;AAAA,QAAQA,EAAR,6BAAa7E,MAAM,CAACG,SAApB;AAAA,2BAAkEwM,OAAlE,CAA+BpJ,OAA/B;AAAA,QAA+BA,OAA/B,iCAAyC,KAAzC;AAAA,0BAAkEoJ,OAAlE,CAAgD5H,KAAhD;AAAA,QAAgDA,KAAhD,gCAAwD,KAAxD;AACA,QAAM/B,KAAN,GAAgB2J,OAAhB,CAAM3J,KAAN;;AAEA,QAAIA,KAAK,IAAI,IAAb,EAAmB;AACjBA,MAAAA,KAAK,GAAG;AAAA,eAAM,IAAN;AAAA,OAAR;AACD;;AAED,QAAI,CAAC6B,EAAL,EAAS;AACP;AACD;;AAED,QAAMkB,MAAM,GAAmB,EAA/B;AACA,QAAMrE,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,CAAb;;kDAEqBjB,IAAI,CAACmC,MAAL,CAAY/F,MAAZ,EAAoB0B,IAApB;;;;AAArB,6DAAgD;AAAA;AAAA,YAApCwD,CAAoC;AAAA,YAAjCkB,CAAiC;;AAC9C,YAAI,CAACpD,KAAK,CAACkC,CAAD,EAAIkB,CAAJ,CAAV,EAAkB;AAChB;AACD;;AAEDL,QAAAA,MAAM,CAACjE,IAAP,CAAY,CAACoD,CAAD,EAAIkB,CAAJ,CAAZ;;AAEA,YAAI,CAACrB,KAAD,IAAUrE,MAAM,CAACJ,MAAP,CAAcN,MAAd,EAAsBkF,CAAtB,CAAd,EAAwC;AACtC;AACD;AACF;;;;;;;AAED,QAAI3B,OAAJ,EAAa;AACXwC,MAAAA,MAAM,CAACxC,OAAP;AACD;;AAED,WAAOwC,MAAP;AACD,GArcoC;;AAucrC;;;AAIA3F,EAAAA,KA3cqC,iBA2c/BJ,MA3c+B;AA4cnC,QAAQI,KAAR,GAA6BJ,MAA7B,CAAQI,KAAR;AAAA,QAAeD,SAAf,GAA6BH,MAA7B,CAAeG,SAAf;;AAEA,QAAI,CAACA,SAAL,EAAgB;AACd,aAAO,IAAP;AACD;;AAED,QAAIC,KAAJ,EAAW;AACT,aAAOA,KAAP;AACD;;AAED,QAAIyC,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAJ,EAAiC;AAC/B,0BAAgBO,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAEgD,QAAAA,KAAK,EAAEC,IAAI,CAACC;AAAd,OAArB,CAAhB;AAAA;AAAA,UAAOF,KAAP;;AAEA,UAAIA,KAAJ,EAAW;AACT,oCAAeA,KAAf;AAAA,YAAOmB,KAAP;;AACA,QAA0BA,KAA1B,CAAQG,IAAR;AAAA,gBAAiBsK,KAAjB,4BAA0BzK,KAA1B;;AACA,eAAOyK,KAAP;AACD,OAJD,MAIO;AACL,eAAO,EAAP;AACD;AACF;;AAED,QAAQ9B,MAAR,GAAmB3M,SAAnB,CAAQ2M,MAAR;AACA,QAAQpL,IAAR,GAAiBoL,MAAjB,CAAQpL,IAAR;;AACA,uBAAahB,MAAM,CAACiO,IAAP,CAAY3O,MAAZ,EAAoB0B,IAApB,CAAb;AAAA;AAAA,QAAKyC,IAAL;;AAEA,QAAI2I,MAAM,CAAC2B,MAAP,KAAkB,CAAtB,EAAyB;AACvB,UAAMpJ,IAAI,GAAG3E,MAAM,CAAC6F,QAAP,CAAgBvG,MAAhB,EAAwB;AAAE6E,QAAAA,EAAE,EAAEnD,IAAN;AAAYsB,QAAAA,KAAK,EAAEC,IAAI,CAACC;AAAxB,OAAxB,CAAb;AACA,UAAM2L,KAAK,GAAGnO,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AACjCgD,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA;AADyB,OAArB,CAAd;;AAIA,UAAIG,IAAI,IAAIwJ,KAAZ,EAAmB;AACjB,mCAA6BxJ,IAA7B;AAAA,YAAOyJ,QAAP;AAAA,YAAiBC,QAAjB;;AACA,oCAAsBF,KAAtB;AAAA,YAASG,SAAT;;AAEA,YAAIjN,IAAI,CAAC6J,UAAL,CAAgBoD,SAAhB,EAA2BD,QAA3B,CAAJ,EAA0C;AACxC5K,UAAAA,IAAI,GAAG2K,QAAP;AACD;AACF;AACF;;AAED,iBAA0B3K,IAA1B;AAAA,eAAQG,IAAR;AAAA,YAAiBsK,IAAjB;;AACA,WAAOA,IAAP;AACD,GAxfoC;;AA0frC;;;AAIA7H,EAAAA,IA9fqC,gBA+fnC/G,MA/fmC;QAggBnC2M,8EAKI;AAEJ,yBAA2CA,OAA3C,CAAQC,IAAR;AAAA,QAAQA,IAAR,+BAAe,QAAf;AAAA,0BAA2CD,OAA3C,CAAyB5H,KAAzB;AAAA,QAAyBA,KAAzB,gCAAiC,KAAjC;AACA,QAAM/B,KAAN,GAAuC2J,OAAvC,CAAM3J,KAAN;AAAA,uBAAuC2J,OAAvC,CAAa9H,EAAb;AAAA,QAAaA,EAAb,6BAAkB7E,MAAM,CAACG,SAAzB;;AAEA,QAAI,CAAC0E,EAAL,EAAS;AACP;AACD;;AAED,QAAMoK,kBAAkB,GAAGvO,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqB6E,EAArB,EAAyB;AAAEE,MAAAA,KAAK,EAALA;AAAF,KAAzB,CAA3B;AAEA,QAAI,CAACkK,kBAAL,EAAyB;;AAEzB,uBAAevO,MAAM,CAACgO,IAAP,CAAY1O,MAAZ,EAAoB,EAApB,CAAf;AAAA;AAAA,QAASkP,EAAT;;AAEA,QAAMC,IAAI,GAAS,CAACF,kBAAkB,CAACvN,IAApB,EAA0BwN,EAA1B,CAAnB;;AAEA,QAAInN,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,KAAmBA,EAAE,CAACF,MAAH,KAAc,CAArC,EAAwC;AACtC,YAAM,IAAI0K,KAAJ,gDAAN;AACD;;AAED,QAAIrM,KAAK,IAAI,IAAb,EAAmB;AACjB,UAAIjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB,6BAAiBnE,MAAM,CAAC4O,MAAP,CAActP,MAAd,EAAsB6E,EAAtB,CAAjB;AAAA;AAAA,YAAOyK,MAAP;;AACAtM,QAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,iBAAIoK,MAAM,CAACrP,QAAP,CAAgBsP,QAAhB,CAAyBrK,CAAzB,CAAJ;AAAA,SAAT;AACD,OAHD,MAGO;AACLlC,QAAAA,KAAK,GAAG;AAAA,iBAAM,IAAN;AAAA,SAAR;AACD;AACF;;AAED,yBAAetC,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,MAAAA,EAAE,EAAEsK,IAAN;AAAYnM,MAAAA,KAAK,EAALA,KAAZ;AAAmB4J,MAAAA,IAAI,EAAJA,IAAnB;AAAyB7H,MAAAA,KAAK,EAALA;AAAzB,KAArB,CAAf;AAAA;AAAA,QAAOgC,IAAP;;AACA,WAAOA,IAAP;AACD,GAriBoC;;AAuiBrC;;;AAIA5C,EAAAA,IA3iBqC,gBA4iBnCnE,MA5iBmC,EA6iBnC6E,EA7iBmC;QA8iBnC8H,8EAGI;AAEJ,QAAMjL,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB8H,OAAxB,CAAb;AACA,QAAMxI,IAAI,GAAGP,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,IAAjB,CAAb;AACA,WAAO,CAACyC,IAAD,EAAOzC,IAAP,CAAP;AACD,GAtjBoC;;AAwjBrC;;;AAICyE,EAAAA,KA5jBoC,kBA6jBnCnG,MA7jBmC;QA8jBnC2M,8EAOI;AAEJ,uBAMIA,OANJ,CACE9H,EADF;AAAA,QACEA,EADF,6BACO7E,MAAM,CAACG,SADd;AAAA,yBAMIwM,OANJ,CAEEC,IAFF;AAAA,QAEEA,IAFF,+BAES,KAFT;AAAA,6BAMID,OANJ,CAGE6C,SAHF;AAAA,QAGEA,SAHF,mCAGc,KAHd;AAAA,4BAMI7C,OANJ,CAIEpJ,OAJF;AAAA,QAIEA,OAJF,kCAIY,KAJZ;AAAA,0BAMIoJ,OANJ,CAKE5H,KALF;AAAA,QAKEA,KALF,gCAKU,KALV;AAOA,QAAM/B,KAAN,GAAgB2J,OAAhB,CAAM3J,KAAN;;AAEA,QAAI,CAACA,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG;AAAA,eAAM,IAAN;AAAA,OAAR;AACD;;AAED,QAAI,CAAC6B,EAAL,EAAS;AACP;AACD;;AAED,QAAIqB,IAAJ;AACA,QAAIgJ,EAAJ;;AAEA,QAAIO,IAAI,CAACC,MAAL,CAAY7K,EAAZ,CAAJ,EAAqB;AACnBqB,MAAAA,IAAI,GAAGrB,EAAE,CAAC,CAAD,CAAT;AACAqK,MAAAA,EAAE,GAAGrK,EAAE,CAAC,CAAD,CAAP;AACD,KAHD,MAGO;AACL,UAAM4I,KAAK,GAAG/M,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB;AAAEmI,QAAAA,IAAI,EAAE;AAAR,OAAxB,CAAd;AACA,UAAM0B,IAAI,GAAGhO,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB;AAAEmI,QAAAA,IAAI,EAAE;AAAR,OAAxB,CAAb;AACA9G,MAAAA,IAAI,GAAG3C,OAAO,GAAGmL,IAAH,GAAUjB,KAAxB;AACAyB,MAAAA,EAAE,GAAG3L,OAAO,GAAGkK,KAAH,GAAWiB,IAAvB;AACD;;AAED,QAAMiB,WAAW,GAAG/L,IAAI,CAACuC,KAAL,CAAWnG,MAAX,EAAmB;AACrCuD,MAAAA,OAAO,EAAPA,OADqC;AAErC2C,MAAAA,IAAI,EAAJA,IAFqC;AAGrCgJ,MAAAA,EAAE,EAAFA,EAHqC;AAIrCU,MAAAA,IAAI,EAAE;AAAA;AAAA,YAAE1K,CAAF;;AAAA,eAAUH,KAAK,GAAG,KAAH,GAAWrE,MAAM,CAACJ,MAAP,CAAcN,MAAd,EAAsBkF,CAAtB,CAA1B;AAAA;AAJ+B,KAAnB,CAApB;AAOA,QAAMqH,OAAO,GAAmB,EAAhC;AACA,QAAIsD,GAAJ;;kDAE2BF;;;;AAA3B,6DAAwC;AAAA;AAAA,YAA5BxL,IAA4B;AAAA,YAAtBzC,IAAsB;;AACtC,YAAMoO,OAAO,GAAGD,GAAG,IAAI9N,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmBmO,GAAG,CAAC,CAAD,CAAtB,MAA+B,CAAtD,CADsC;;AAItC,YAAIjD,IAAI,KAAK,SAAT,IAAsBkD,OAA1B,EAAmC;AACjC;AACD;;AAED,YAAI,CAAC9M,KAAK,CAACmB,IAAD,EAAOzC,IAAP,CAAV,EAAwB;AACtB;AACA;AACA;AACA,cAAI8N,SAAS,IAAI,CAACM,OAAd,IAAyB7M,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAA7B,EAAgD;AAC9C;AACD,WAFD,MAEO;AACL;AACD;AACF,SAjBqC;;;AAoBtC,YAAIyI,IAAI,KAAK,QAAT,IAAqBkD,OAAzB,EAAkC;AAChCD,UAAAA,GAAG,GAAG,CAAC1L,IAAD,EAAOzC,IAAP,CAAN;AACA;AACD,SAvBqC;;;AA0BtC,YAAMsO,IAAI,GACRpD,IAAI,KAAK,QAAT,GAAoBiD,GAApB,GAA0B,CAAC1L,IAAD,EAAOzC,IAAP,CAD5B;;AAGA,YAAIsO,IAAJ,EAAU;AACR,cAAIR,SAAJ,EAAe;AACbjD,YAAAA,OAAO,CAACzK,IAAR,CAAakO,IAAb;AACD,WAFD,MAEO;AACL,kBAAMA,IAAN;AACD;AACF;;AAEDH,QAAAA,GAAG,GAAG,CAAC1L,IAAD,EAAOzC,IAAP,CAAN;AACD;;;;;;;;AAGD,QAAIkL,IAAI,KAAK,QAAT,IAAqBiD,GAAzB,EAA8B;AAC5B,UAAIL,SAAJ,EAAe;AACbjD,QAAAA,OAAO,CAACzK,IAAR,CAAa+N,GAAb;AACD,OAFD,MAEO;AACL,cAAMA,GAAN;AACD;AACF;AAGD;;;AACA,QAAIL,SAAJ,EAAe;AACb,aAAOjD,OAAP;AACD;AACF,GArqBoC;;AAsqBrC;;;AAIAjK,EAAAA,SA1qBqC,qBA2qBnCtC,MA3qBmC;QA4qBnC2M,8EAEI;AAEJ,yBAA0BA,OAA1B,CAAQsD,KAAR;AAAA,QAAQA,KAAR,+BAAgB,KAAhB;;AACA,QAAM9N,aAAa,GAAG,SAAhBA,aAAgB,CAACnC,MAAD;AACpB,aAAOT,WAAW,CAAC6B,GAAZ,CAAgBpB,MAAhB,KAA2B,EAAlC;AACD,KAFD;;AAIA,QAAMkQ,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAClQ,MAAD;AACvB,aAAOP,eAAe,CAAC2B,GAAhB,CAAoBpB,MAApB,KAA+B,IAAIsB,GAAJ,EAAtC;AACD,KAFD;;AAIA,QAAM6O,YAAY,GAAG,SAAfA,YAAe,CAACnQ,MAAD;AACnB,UAAM0B,IAAI,GAAGS,aAAa,CAACnC,MAAD,CAAb,CAAsBoQ,GAAtB,EAAb;AACA,UAAMzO,GAAG,GAAGD,IAAI,CAACE,IAAL,CAAU,GAAV,CAAZ;AACAsO,MAAAA,gBAAgB,CAAClQ,MAAD,CAAhB,WAAgC2B,GAAhC;AACA,aAAOD,IAAP;AACD,KALD;;AAOA,QAAI,CAAChB,MAAM,CAAC8N,aAAP,CAAqBxO,MAArB,CAAL,EAAmC;AACjC;AACD;;AAED,QAAIiQ,KAAJ,EAAW;AACT,UAAMI,QAAQ,GAAGpK,KAAK,CAACC,IAAN,CAAWtC,IAAI,CAACuC,KAAL,CAAWnG,MAAX,CAAX,EAA+B;AAAA;AAAA,YAAIoG,CAAJ;;AAAA,eAAWA,CAAX;AAAA,OAA/B,CAAjB;AACA,UAAMkK,WAAW,GAAG,IAAIhP,GAAJ,CAAQ+O,QAAQ,CAACE,GAAT,CAAa,UAAAnK,CAAC;AAAA,eAAIA,CAAC,CAACxE,IAAF,CAAO,GAAP,CAAJ;AAAA,OAAd,CAAR,CAApB;AACArC,MAAAA,WAAW,CAAC6C,GAAZ,CAAgBpC,MAAhB,EAAwBqQ,QAAxB;AACA5Q,MAAAA,eAAe,CAAC2C,GAAhB,CAAoBpC,MAApB,EAA4BsQ,WAA5B;AACD;;AAED,QAAInO,aAAa,CAACnC,MAAD,CAAb,CAAsB2E,MAAtB,KAAiC,CAArC,EAAwC;AACtC;AACD;;AAEDjE,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC;;;;;oDAKwBmC,aAAa,CAACnC,MAAD;;;;AAArC,+DAA+C;AAAA,cAApCyQ,UAAoC;;AAC7C,cAAI7M,IAAI,CAAC/B,GAAL,CAAS7B,MAAT,EAAiByQ,UAAjB,CAAJ,EAAiC;AAC/B,gBAAMjM,MAAK,GAAG9D,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoByQ,UAApB,CAAd;;AACA,yCAAkBjM,MAAlB;AAAA,gBAAOL,IAAP;AAAA,gBAAauM,CAAb;AAEA;;;;;;;;AAOA,gBAAIjM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2BA,IAAI,CAAClE,QAAL,CAAc0E,MAAd,KAAyB,CAAxD,EAA2D;AACzD3E,cAAAA,MAAM,CAACuE,aAAP,CAAqBC,MAArB;AACD;AACF;AACF;;;;;;;AAED,UAAMmM,GAAG,GAAGxO,aAAa,CAACnC,MAAD,CAAb,CAAsB2E,MAAtB,GAA+B,EAA3C;;AACA,UAAIiM,CAAC,GAAG,CAAR;;AAEA,aAAOzO,aAAa,CAACnC,MAAD,CAAb,CAAsB2E,MAAtB,KAAiC,CAAxC,EAA2C;AACzC,YAAIiM,CAAC,GAAGD,GAAR,EAAa;AACX,gBAAM,IAAItB,KAAJ,yEAC8CsB,GAD9C,2HAAN;AAGD;;AAED,YAAMF,SAAS,GAAGN,YAAY,CAACnQ,MAAD,CAA9B,CAPyC;;AAUzC,YAAI4D,IAAI,CAAC/B,GAAL,CAAS7B,MAAT,EAAiByQ,SAAjB,CAAJ,EAAiC;AAC/B,cAAMjM,KAAK,GAAG9D,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoByQ,SAApB,CAAd;AACAzQ,UAAAA,MAAM,CAACuE,aAAP,CAAqBC,KAArB;AACD;;AACDoM,QAAAA,CAAC;AACF;AACF,KA3CD;AA4CD,GA3vBoC;;AA6vBrC;;;AAIAtB,EAAAA,MAjwBqC,kBAkwBnCtP,MAlwBmC,EAmwBnC6E,EAnwBmC;QAowBnC8H,8EAGI;AAEJ,QAAMjL,IAAI,GAAGhB,MAAM,CAACgB,IAAP,CAAY1B,MAAZ,EAAoB6E,EAApB,EAAwB8H,OAAxB,CAAb;AACA,QAAMkE,UAAU,GAAG9O,IAAI,CAACuN,MAAL,CAAY5N,IAAZ,CAAnB;AACA,QAAM8C,KAAK,GAAG9D,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB6Q,UAApB,CAAd;AACA,WAAOrM,KAAP;AACD,GA7wBoC;;AA+wBrC;;;AAIA9C,EAAAA,IAnxBqC,gBAoxBnC1B,MApxBmC,EAqxBnC6E,EArxBmC;QAsxBnC8H,8EAGI;AAEJ,QAAQmE,KAAR,GAAwBnE,OAAxB,CAAQmE,KAAR;AAAA,QAAe9D,IAAf,GAAwBL,OAAxB,CAAeK,IAAf;;AAEA,QAAIjL,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB,UAAImI,IAAI,KAAK,OAAb,EAAsB;AACpB,0BAAsBpJ,IAAI,CAAC6J,KAAL,CAAWzN,MAAX,EAAmB6E,EAAnB,CAAtB;AAAA;AAAA,YAASkM,SAAT;;AACAlM,QAAAA,EAAE,GAAGkM,SAAL;AACD,OAHD,MAGO,IAAI/D,IAAI,KAAK,KAAb,EAAoB;AACzB,yBAAqBpJ,IAAI,CAAC8K,IAAL,CAAU1O,MAAV,EAAkB6E,EAAlB,CAArB;AAAA;AAAA,YAASmM,QAAT;;AACAnM,QAAAA,EAAE,GAAGmM,QAAL;AACD;AACF;;AAED,QAAInO,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AACrB,UAAImI,IAAI,KAAK,OAAb,EAAsB;AACpBnI,QAAAA,EAAE,GAAGhC,KAAK,CAAC0K,KAAN,CAAY1I,EAAZ,CAAL;AACD,OAFD,MAEO,IAAImI,IAAI,KAAK,KAAb,EAAoB;AACzBnI,QAAAA,EAAE,GAAGhC,KAAK,CAAC0G,GAAN,CAAU1E,EAAV,CAAL;AACD,OAFM,MAEA;AACLA,QAAAA,EAAE,GAAG9C,IAAI,CAACkP,MAAL,CAAYpM,EAAE,CAACiI,MAAH,CAAUpL,IAAtB,EAA4BmD,EAAE,CAACoI,KAAH,CAASvL,IAArC,CAAL;AACD;AACF;;AAED,QAAI0M,KAAK,CAAC8C,OAAN,CAAcrM,EAAd,CAAJ,EAAuB;AACrBA,MAAAA,EAAE,GAAGA,EAAE,CAACnD,IAAR;AACD;;AAED,QAAIoP,KAAK,IAAI,IAAb,EAAmB;AACjBjM,MAAAA,EAAE,GAAGA,EAAE,CAACsE,KAAH,CAAS,CAAT,EAAY2H,KAAZ,CAAL;AACD;;AAED,WAAOjM,EAAP;AACD,GA1zBoC;AA4zBrCsM,EAAAA,OA5zBqC,mBA4zB7BnR,MA5zB6B,EA4zBb0B,IA5zBa;AA6zBnC,WAAOkC,IAAI,CAAC/B,GAAL,CAAS7B,MAAT,EAAiB0B,IAAjB,CAAP;AACD,GA9zBoC;;AAg0BrC;;;;AAKA0P,EAAAA,OAr0BqC,mBAs0BnCpR,MAt0BmC,EAu0BnC0B,IAv0BmC;QAw0BnCiL,8EAEI;AAEJ,4BAAiCA,OAAjC,CAAQ0E,QAAR;AAAA,QAAQA,QAAR,kCAAmB,SAAnB;AACA,QAAMzQ,GAAG,GAAY;AACnB0Q,MAAAA,OAAO,EAAE5P,IADU;AAEnB2P,MAAAA,QAAQ,EAARA,QAFmB;AAGnBE,MAAAA,KAHmB;AAIjB,YAAQD,OAAR,GAAoB1Q,GAApB,CAAQ0Q,OAAR;AACA,YAAM3Q,QAAQ,GAAGD,MAAM,CAACC,QAAP,CAAgBX,MAAhB,CAAjB;AACAW,QAAAA,QAAQ,UAAR,CAAgBC,GAAhB;AACAA,QAAAA,GAAG,CAAC0Q,OAAJ,GAAc,IAAd;AACA,eAAOA,OAAP;AACD;AATkB,KAArB;AAYA,QAAME,IAAI,GAAG9Q,MAAM,CAACC,QAAP,CAAgBX,MAAhB,CAAb;AACAwR,IAAAA,IAAI,CAAC/P,GAAL,CAASb,GAAT;AACA,WAAOA,GAAP;AACD,GA51BoC;;AA81BrC;;;AAIAD,EAAAA,QAl2BqC,oBAk2B5BX,MAl2B4B;AAm2BnC,QAAIwR,IAAI,GAAG5R,SAAS,CAACwB,GAAV,CAAcpB,MAAd,CAAX;;AAEA,QAAI,CAACwR,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIlQ,GAAJ,EAAP;AACA1B,MAAAA,SAAS,CAACwC,GAAV,CAAcpC,MAAd,EAAsBwR,IAAtB;AACD;;AAED,WAAOA,IAAP;AACD,GA32BoC;;AA62BrC;;;AAIAzE,EAAAA,KAj3BqC,iBAk3BnC/M,MAl3BmC,EAm3BnC6E,EAn3BmC;QAo3BnC8H,8EAEI;AAEJ,wBAA2BA,OAA3B,CAAQK,IAAR;AAAA,QAAQA,IAAR,8BAAe,OAAf;;AAEA,QAAIjL,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB,UAAInD,IAAJ;;AAEA,UAAIsL,IAAI,KAAK,KAAb,EAAoB;AAClB,0BAAqBpJ,IAAI,CAAC8K,IAAL,CAAU1O,MAAV,EAAkB6E,EAAlB,CAArB;AAAA;AAAA,YAASmM,QAAT;;AACAtP,QAAAA,IAAI,GAAGsP,QAAP;AACD,OAHD,MAGO;AACL,2BAAsBpN,IAAI,CAAC6J,KAAL,CAAWzN,MAAX,EAAmB6E,EAAnB,CAAtB;AAAA;AAAA,YAASkM,SAAT;;AACArP,QAAAA,IAAI,GAAGqP,SAAP;AACD;;AAED,UAAM5M,IAAI,GAAGP,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,IAAjB,CAAb;;AAEA,UAAI,CAACuB,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAL,EAAwB;AACtB,cAAM,IAAIkL,KAAJ,0BACcrC,IADd,yCACiDnI,EADjD,iCAC0EmI,IAD1E,iBAAN;AAGD;;AAED,aAAO;AAAEtL,QAAAA,IAAI,EAAJA,IAAF;AAAQ+M,QAAAA,MAAM,EAAEzB,IAAI,KAAK,KAAT,GAAiB7I,IAAI,CAACG,IAAL,CAAUK,MAA3B,GAAoC;AAApD,OAAP;AACD;;AAED,QAAI9B,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AACrB,yBAAqBhC,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAArB;AAAA;AAAA,UAAO0I,KAAP;AAAA,UAAchE,GAAd;;AACA,aAAOyD,IAAI,KAAK,OAAT,GAAmBO,KAAnB,GAA2BhE,GAAlC;AACD;;AAED,WAAO1E,EAAP;AACD,GAt5BoC;;AAw5BrC;;;;AAKA4M,EAAAA,QA75BqC,oBA85BnCzR,MA95BmC,EA+5BnC+M,KA/5BmC;QAg6BnCJ,8EAEI;AAEJ,6BAAiCA,OAAjC,CAAQ0E,QAAR;AAAA,QAAQA,QAAR,mCAAmB,SAAnB;AACA,QAAMzQ,GAAG,GAAa;AACpB0Q,MAAAA,OAAO,EAAEvE,KADW;AAEpBsE,MAAAA,QAAQ,EAARA,QAFoB;AAGpBE,MAAAA,KAHoB;AAIlB,YAAQD,OAAR,GAAoB1Q,GAApB,CAAQ0Q,OAAR;AACA,YAAMvQ,SAAS,GAAGL,MAAM,CAACK,SAAP,CAAiBf,MAAjB,CAAlB;AACAe,QAAAA,SAAS,UAAT,CAAiBH,GAAjB;AACAA,QAAAA,GAAG,CAAC0Q,OAAJ,GAAc,IAAd;AACA,eAAOA,OAAP;AACD;AATmB,KAAtB;AAYA,QAAME,IAAI,GAAG9Q,MAAM,CAACK,SAAP,CAAiBf,MAAjB,CAAb;AACAwR,IAAAA,IAAI,CAAC/P,GAAL,CAASb,GAAT;AACA,WAAOA,GAAP;AACD,GAp7BoC;;AAs7BrC;;;AAIAG,EAAAA,SA17BqC,qBA07B3Bf,MA17B2B;AA27BnC,QAAIwR,IAAI,GAAG3R,UAAU,CAACuB,GAAX,CAAepB,MAAf,CAAX;;AAEA,QAAI,CAACwR,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIlQ,GAAJ,EAAP;AACAzB,MAAAA,UAAU,CAACuC,GAAX,CAAepC,MAAf,EAAuBwR,IAAvB;AACD;;AAED,WAAOA,IAAP;AACD,GAn8BoC;;AAq8BrC;;;;;;;;;;;;AAaCnE,EAAAA,SAl9BoC,sBAm9BnCrN,MAn9BmC;QAo9BnC2M,8EAKI;AAEJ,uBAKIA,OALJ,CACE9H,EADF;AAAA,QACEA,EADF,6BACO7E,MAAM,CAACG,SADd;AAAA,yBAKIwM,OALJ,CAEEtJ,IAFF;AAAA,QAEEA,IAFF,+BAES,QAFT;AAAA,4BAKIsJ,OALJ,CAGEpJ,OAHF;AAAA,QAGEA,OAHF,kCAGY,KAHZ;AAAA,0BAKIoJ,OALJ,CAIE5H,KAJF;AAAA,QAIEA,KAJF,gCAIU,KAJV;;AAOA,QAAI,CAACF,EAAL,EAAS;AACP;AACD;AAED;;;;;;;;;;;;;;;;;;;AAkBA,QAAMqI,KAAK,GAAGxM,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB6E,EAArB,CAAd;;AACA,wBAAqBhC,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAArB;AAAA;AAAA,QAAOK,KAAP;AAAA,QAAchE,GAAd;;AACA,QAAMkE,KAAK,GAAGlK,OAAO,GAAGgG,GAAH,GAASgE,KAA9B;AACA,QAAImE,UAAU,GAAG,KAAjB;AACA,QAAIC,SAAS,GAAG,EAAhB;AACA,QAAIjK,QAAQ,GAAG,CAAf;;AACA,QAAIkK,iBAAiB,GAAG,CAAxB;AACA,QAAIC,cAAc,GAAG,CAArB;AAGA;AACA;AACA;AACA;AACA;;kDAC2BnR,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,MAAAA,EAAE,EAAFA,EAAF;AAAMtB,MAAAA,OAAO,EAAPA,OAAN;AAAewB,MAAAA,KAAK,EAALA;AAAf,KAArB;;;;AAA3B,6DAAyE;AAAA;AAAA,YAA7DZ,IAA6D;AAAA,YAAvDzC,IAAuD;;AACvE;;;AAGA,YAAI+C,OAAO,CAACC,SAAR,CAAkBP,IAAlB,CAAJ,EAA6B;AAC3B;AACA;AACA;AACA,cAAI,CAACY,KAAD,IAAU/E,MAAM,CAACM,MAAP,CAAc6D,IAAd,CAAd,EAAmC;AACjC,kBAAMzD,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB0B,IAArB,CAAN;AACA;AACD,WAP0B;AAU3B;AACA;;;AACA,cAAI1B,MAAM,CAACK,QAAP,CAAgB8D,IAAhB,CAAJ,EAA2B,SAZA;;AAe3B,cAAIzD,MAAM,CAACmN,UAAP,CAAkB7N,MAAlB,EAA0BmE,IAA1B,CAAJ,EAAqC;AACnC;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAM2N,CAAC,GAAG/P,IAAI,CAAC6J,UAAL,CAAgBlK,IAAhB,EAAsB6H,GAAG,CAAC7H,IAA1B,IACN6H,GADM,GAEN7I,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB0B,IAAnB,CAFJ;AAGA,gBAAMqQ,CAAC,GAAGhQ,IAAI,CAAC6J,UAAL,CAAgBlK,IAAhB,EAAsB6L,KAAK,CAAC7L,IAA5B,IACN6L,KADM,GAEN7M,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB0B,IAArB,CAFJ;AAIAiQ,YAAAA,SAAS,GAAGjR,MAAM,CAACsR,MAAP,CAAchS,MAAd,EAAsB;AAAE8M,cAAAA,MAAM,EAAEiF,CAAV;AAAa9E,cAAAA,KAAK,EAAE6E;AAApB,aAAtB,EAA+C;AAAE/M,cAAAA,KAAK,EAALA;AAAF,aAA/C,CAAZ;AACA2M,YAAAA,UAAU,GAAG,IAAb;AACD;AACF;AAED;;;;;;AAIA,YAAIzO,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAJ,EAAuB;AACrB,cAAM8N,OAAO,GAAGlQ,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkB+L,KAAK,CAAC/L,IAAxB,CAAhB,CADqB;AAIrB;AACA;AACA;AAEA;;AACA,cAAIuQ,OAAJ,EAAa;AACXL,YAAAA,iBAAiB,GAAGrO,OAAO,GACvBkK,KAAK,CAACgB,MADiB,GAEvBtK,IAAI,CAACG,IAAL,CAAUK,MAAV,GAAmB8I,KAAK,CAACgB,MAF7B;AAGAoD,YAAAA,cAAc,GAAGpE,KAAK,CAACgB,MAAvB,CAJW;AAKZ,WALD,MAKO;AACLmD,YAAAA,iBAAiB,GAAGzN,IAAI,CAACG,IAAL,CAAUK,MAA9B;AACAkN,YAAAA,cAAc,GAAGtO,OAAO,GAAGqO,iBAAH,GAAuB,CAA/C;AACD,WAjBoB;;;AAoBrB,cAAIK,OAAO,IAAIP,UAAX,IAAyBrO,IAAI,KAAK,QAAtC,EAAgD;AAC9C,kBAAM;AAAE3B,cAAAA,IAAI,EAAJA,IAAF;AAAQ+M,cAAAA,MAAM,EAAEoD;AAAhB,aAAN;AACAH,YAAAA,UAAU,GAAG,KAAb;AACD,WAvBoB;;;AA0BrB,iBAAO,IAAP,EAAa;AACX;AACA;AACA;AACA,gBAAIhK,QAAQ,KAAK,CAAjB,EAAoB;AAClB,kBAAIiK,SAAS,KAAK,EAAlB,EAAsB;AACtBjK,cAAAA,QAAQ,GAAGwK,YAAY,CAACP,SAAD,EAAYtO,IAAZ,EAAkBE,OAAlB,CAAvB,CAFkB;AAIlB;;AACAoO,cAAAA,SAAS,GAAG3I,wBAAwB,CAClC2I,SADkC,EAElCjK,QAFkC,EAGlCnE,OAHkC,CAAxB,CAIV,CAJU,CAAZ;AAKD,aAdU;;;AAiBXsO,YAAAA,cAAc,GAAGtO,OAAO,GACpBsO,cAAc,GAAGnK,QADG,GAEpBmK,cAAc,GAAGnK,QAFrB;AAGAkK,YAAAA,iBAAiB,GAAGA,iBAAiB,GAAGlK,QAAxC,CApBW;AAuBX;AACA;;AACA,gBAAIkK,iBAAiB,GAAG,CAAxB,EAA2B;AACzBlK,cAAAA,QAAQ,GAAG,CAACkK,iBAAZ;AACA;AACD,aA5BU;AA+BX;AACA;;;AACAlK,YAAAA,QAAQ,GAAG,CAAX;AACA,kBAAM;AAAEhG,cAAAA,IAAI,EAAJA,IAAF;AAAQ+M,cAAAA,MAAM,EAAEoD;AAAhB,aAAN;AACD;AACF;AACF;AAED;AACA;AAEA;AACA;;;;;;;;AACA,aAASK,YAAT,CAAsB5N,IAAtB,EAAoCjB,IAApC,EAAkDE,OAAlD;AACE,UAAIF,IAAI,KAAK,WAAb,EAA0B;AACxB,eAAO2D,oBAAoB,CAAC1C,IAAD,EAAOf,OAAP,CAA3B;AACD,OAFD,MAEO,IAAIF,IAAI,KAAK,MAAb,EAAqB;AAC1B,eAAOuF,eAAe,CAACtE,IAAD,EAAOf,OAAP,CAAtB;AACD,OAFM,MAEA,IAAIF,IAAI,KAAK,MAAT,IAAmBA,IAAI,KAAK,OAAhC,EAAyC;AAC9C,eAAOiB,IAAI,CAACK,MAAZ;AACD;;AACD,aAAO,CAAP;AACD;AACF,GAtoCoC;;AAwoCrC;;;AAIA4B,EAAAA,QA5oCqC,oBA6oCnCvG,MA7oCmC;QA8oCnC2M,8EAKI;AAEJ,yBAA2CA,OAA3C,CAAQC,IAAR;AAAA,QAAQA,IAAR,+BAAe,QAAf;AAAA,0BAA2CD,OAA3C,CAAyB5H,KAAzB;AAAA,QAAyBA,KAAzB,gCAAiC,KAAjC;AACA,QAAM/B,KAAN,GAAuC2J,OAAvC,CAAM3J,KAAN;AAAA,uBAAuC2J,OAAvC,CAAa9H,EAAb;AAAA,QAAaA,EAAb,6BAAkB7E,MAAM,CAACG,SAAzB;;AAEA,QAAI,CAAC0E,EAAL,EAAS;AACP;AACD;;AAED,QAAMsN,mBAAmB,GAAGzR,MAAM,CAAC4M,MAAP,CAActN,MAAd,EAAsB6E,EAAtB,EAA0B;AAAEE,MAAAA,KAAK,EAALA;AAAF,KAA1B,CAA5B;;AAEA,QAAI,CAACoN,mBAAL,EAA0B;AACxB;AACD;;AAED,wBAAezR,MAAM,CAAC+M,KAAP,CAAazN,MAAb,EAAqB,EAArB,CAAf;AAAA;AAAA,QAASkP,EAAT;AAGA;;;AACA,QAAMC,IAAI,GAAS,CAACgD,mBAAmB,CAACzQ,IAArB,EAA2BwN,EAA3B,CAAnB;;AAEA,QAAInN,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,KAAmBA,EAAE,CAACF,MAAH,KAAc,CAArC,EAAwC;AACtC,YAAM,IAAI0K,KAAJ,oDAAN;AACD;;AAED,QAAIrM,KAAK,IAAI,IAAb,EAAmB;AACjB,UAAIjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB,8BAAiBnE,MAAM,CAAC4O,MAAP,CAActP,MAAd,EAAsB6E,EAAtB,CAAjB;AAAA;AAAA,YAAOyK,MAAP;;AACAtM,QAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,iBAAIoK,MAAM,CAACrP,QAAP,CAAgBsP,QAAhB,CAAyBrK,CAAzB,CAAJ;AAAA,SAAT;AACD,OAHD,MAGO;AACLlC,QAAAA,KAAK,GAAG;AAAA,iBAAM,IAAN;AAAA,SAAR;AACD;AACF;;AAED,yBAAmBtC,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AACtCuD,MAAAA,OAAO,EAAE,IAD6B;AAEtCsB,MAAAA,EAAE,EAAEsK,IAFkC;AAGtCnM,MAAAA,KAAK,EAALA,KAHsC;AAItC4J,MAAAA,IAAI,EAAJA,IAJsC;AAKtC7H,MAAAA,KAAK,EAALA;AALsC,KAArB,CAAnB;AAAA;AAAA,QAAOwB,QAAP;;AAQA,WAAOA,QAAP;AACD,GA9rCoC;;AAgsCrC;;;AAIA2G,EAAAA,KApsCqC,iBAosC/BlN,MApsC+B,EAosCf6E,EApsCe,EAosCDqK,EApsCC;AAqsCnC,QAAIrM,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,KAAqB,CAACqK,EAA1B,EAA8B;AAC5B,aAAOrK,EAAP;AACD;;AAED,QAAM0I,KAAK,GAAG7M,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB6E,EAArB,CAAd;AACA,QAAM0E,GAAG,GAAG7I,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmBkP,EAAE,IAAIrK,EAAzB,CAAZ;AACA,WAAO;AAAEiI,MAAAA,MAAM,EAAES,KAAV;AAAiBN,MAAAA,KAAK,EAAE1D;AAAxB,KAAP;AACD,GA5sCoC;;AA8sCrC;;;;AAKA6I,EAAAA,QAntCqC,oBAotCnCpS,MAptCmC,EAqtCnCkN,KArtCmC;QAstCnCP,8EAEI;AAEJ,6BAAiCA,OAAjC,CAAQ0E,QAAR;AAAA,QAAQA,QAAR,mCAAmB,SAAnB;AACA,QAAMzQ,GAAG,GAAa;AACpB0Q,MAAAA,OAAO,EAAEpE,KADW;AAEpBmE,MAAAA,QAAQ,EAARA,QAFoB;AAGpBE,MAAAA,KAHoB;AAIlB,YAAQD,OAAR,GAAoB1Q,GAApB,CAAQ0Q,OAAR;AACA,YAAMrQ,SAAS,GAAGP,MAAM,CAACO,SAAP,CAAiBjB,MAAjB,CAAlB;AACAiB,QAAAA,SAAS,UAAT,CAAiBL,GAAjB;AACAA,QAAAA,GAAG,CAAC0Q,OAAJ,GAAc,IAAd;AACA,eAAOA,OAAP;AACD;AATmB,KAAtB;AAYA,QAAME,IAAI,GAAG9Q,MAAM,CAACO,SAAP,CAAiBjB,MAAjB,CAAb;AACAwR,IAAAA,IAAI,CAAC/P,GAAL,CAASb,GAAT;AACA,WAAOA,GAAP;AACD,GA1uCoC;;AA4uCrC;;;AAIAK,EAAAA,SAhvCqC,qBAgvC3BjB,MAhvC2B;AAivCnC,QAAIwR,IAAI,GAAG1R,UAAU,CAACsB,GAAX,CAAepB,MAAf,CAAX;;AAEA,QAAI,CAACwR,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIlQ,GAAJ,EAAP;AACAxB,MAAAA,UAAU,CAACsC,GAAX,CAAepC,MAAf,EAAuBwR,IAAvB;AACD;;AAED,WAAOA,IAAP;AACD,GAzvCoC;;AA2vCrC;;;;;;;AAQA3L,EAAAA,UAnwCqC,sBAmwC1B7F,MAnwC0B,EAmwCV2B,GAnwCU;AAowCnC3B,IAAAA,MAAM,CAAC6F,UAAP,CAAkBlE,GAAlB;AACD,GArwCoC;;AAuwCrC;;;;;;AAMA0Q,EAAAA,cA7wCqC,0BA6wCtBrS,MA7wCsB,EA6wCNwO,aA7wCM;AA8wCnC7O,IAAAA,WAAW,CAACyC,GAAZ,CAAgBpC,MAAhB,EAAwBwO,aAAxB;AACD,GA/wCoC;;AAixCrC;;;AAIAjB,EAAAA,KArxCqC,iBAqxC/BvN,MArxC+B,EAqxCf6E,EArxCe;AAsxCnC,WAAOnE,MAAM,CAACqM,KAAP,CAAa/M,MAAb,EAAqB6E,EAArB,EAAyB;AAAEmI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAP;AACD,GAvxCoC;;AAyxCrC;;;;;;AAOAgF,EAAAA,MAhyCqC,kBAiyCnChS,MAjyCmC,EAkyCnC6E,EAlyCmC;QAmyCnC8H,8EAEI;AAEJ,0BAA0BA,OAA1B,CAAQ5H,KAAR;AAAA,QAAQA,KAAR,gCAAgB,KAAhB;AACA,QAAMmI,KAAK,GAAGxM,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB6E,EAArB,CAAd;;AACA,wBAAqBhC,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAArB;AAAA;AAAA,QAAOK,KAAP;AAAA,QAAchE,GAAd;;AACA,QAAIjF,IAAI,GAAG,EAAX;;kDAE2B5D,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAC9C6E,MAAAA,EAAE,EAAEqI,KAD0C;AAE9ClK,MAAAA,KAAK,EAAEC,IAAI,CAACC,MAFkC;AAG9C6B,MAAAA,KAAK,EAALA;AAH8C,KAArB;;;;AAA3B,6DAII;AAAA;AAAA,YAJQZ,IAIR;AAAA,YAJczC,IAId;;AACF,YAAI4Q,CAAC,GAAGnO,IAAI,CAACG,IAAb;;AAEA,YAAIvC,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkB6H,GAAG,CAAC7H,IAAtB,CAAJ,EAAiC;AAC/B4Q,UAAAA,CAAC,GAAGA,CAAC,CAACnJ,KAAF,CAAQ,CAAR,EAAWI,GAAG,CAACkF,MAAf,CAAJ;AACD;;AAED,YAAI1M,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkB6L,KAAK,CAAC7L,IAAxB,CAAJ,EAAmC;AACjC4Q,UAAAA,CAAC,GAAGA,CAAC,CAACnJ,KAAF,CAAQoE,KAAK,CAACkB,MAAd,CAAJ;AACD;;AAEDnK,QAAAA,IAAI,IAAIgO,CAAR;AACD;;;;;;;AAED,WAAOhO,IAAP;AACD,GA/zCoC;;AAi0CrC;;;AAIAiO,EAAAA,WAr0CqC,uBAs0CnCvS,MAt0CmC,EAu0CnCkN,KAv0CmC;QAw0CnCP,8EAEI;AAEJ,0BAA0BA,OAA1B,CAAQ5H,KAAR;AAAA,QAAQA,KAAR,gCAAgB,KAAhB;;AACA,wBAAmBlC,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAAnB;AAAA;AAAA,QAAKK,KAAL;AAAA,QAAYhE,GAAZ;;;AAGA,QAAIgE,KAAK,CAACkB,MAAN,KAAiB,CAAjB,IAAsBlF,GAAG,CAACkF,MAAJ,KAAe,CAArC,IAA0C5L,KAAK,CAACS,WAAN,CAAkB4J,KAAlB,CAA9C,EAAwE;AACtE,aAAOA,KAAP;AACD;;AAED,QAAMsF,QAAQ,GAAG9R,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AACpC6E,MAAAA,EAAE,EAAE0E,GADgC;AAEpCvG,MAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,eAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA;AAF4B,KAArB,CAAjB;AAIA,QAAM8J,SAAS,GAAGwD,QAAQ,GAAGA,QAAQ,CAAC,CAAD,CAAX,GAAiB,EAA3C;AACA,QAAM/E,KAAK,GAAG/M,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqBuN,KAArB,CAAd;AACA,QAAMD,MAAM,GAAG;AAAER,MAAAA,MAAM,EAAEW,KAAV;AAAiBR,MAAAA,KAAK,EAAE1D;AAAxB,KAAf;AACA,QAAIkJ,IAAI,GAAG,IAAX;;kDAE2B/R,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAC9C6E,MAAAA,EAAE,EAAEyI,MAD0C;AAE9CtK,MAAAA,KAAK,EAAEC,IAAI,CAACC,MAFkC;AAG9CK,MAAAA,OAAO,EAAE,IAHqC;AAI9CwB,MAAAA,KAAK,EAALA;AAJ8C,KAArB;;;;AAA3B,6DAKI;AAAA;AAAA,YALQZ,IAKR;AAAA,YALczC,IAKd;;AACF,YAAI+Q,IAAJ,EAAU;AACRA,UAAAA,IAAI,GAAG,KAAP;AACA;AACD;;AAED,YAAItO,IAAI,CAACG,IAAL,KAAc,EAAd,IAAoBvC,IAAI,CAAC2Q,QAAL,CAAchR,IAAd,EAAoBsN,SAApB,CAAxB,EAAwD;AACtDzF,UAAAA,GAAG,GAAG;AAAE7H,YAAAA,IAAI,EAAJA,IAAF;AAAQ+M,YAAAA,MAAM,EAAEtK,IAAI,CAACG,IAAL,CAAUK;AAA1B,WAAN;AACA;AACD;AACF;;;;;;;AAED,WAAO;AAAEmI,MAAAA,MAAM,EAAES,KAAV;AAAiBN,MAAAA,KAAK,EAAE1D;AAAxB,KAAP;AACD,GA/2CoC;;AAi3CrC;;;AAj3CqC,yBAs3CnCvJ,MAt3CmC;QAu3CnC2M,8EAII;AAEJ,WAAOjM,MAAM,CAACgM,KAAP,CAAa1M,MAAb,sCACF2M,OADE;AAEL3J,MAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,eAAIxE,MAAM,CAACJ,MAAP,CAAcN,MAAd,EAAsBkF,CAAtB,CAAJ;AAAA;AAFH,OAAP;AAID,GAj4CoC;;AAm4CrC;;;AAIAsL,EAAAA,kBAv4CqC,8BAu4ClBxQ,MAv4CkB,EAu4CF2S,EAv4CE;AAw4CnC,QAAM/P,KAAK,GAAGlC,MAAM,CAAC8N,aAAP,CAAqBxO,MAArB,CAAd;AACAU,IAAAA,MAAM,CAAC2R,cAAP,CAAsBrS,MAAtB,EAA8B,KAA9B;;AACA,QAAI;AACF2S,MAAAA,EAAE;AACH,KAFD,SAEU;AACRjS,MAAAA,MAAM,CAAC2R,cAAP,CAAsBrS,MAAtB,EAA8B4C,KAA9B;AACD;;AACDlC,IAAAA,MAAM,CAAC4B,SAAP,CAAiBtC,MAAjB;AACD;AAh5CoC;;IChR1B4S,QAAQ,GAAsB;AACzC;;;AAIAC,EAAAA,UALyC,sBAK9BjQ,KAL8B;AAMvC,WAAOb,IAAI,CAACqN,MAAL,CAAYxM,KAAZ,KAAsBwL,KAAK,CAAC8C,OAAN,CAActO,KAAd,CAAtB,IAA8CC,KAAK,CAACmL,OAAN,CAAcpL,KAAd,CAArD;AACD;AAPwC;IAqB9B6M,IAAI,GAAkB;AACjC;;;AAIAC,EAAAA,MALiC,kBAK1B9M,KAL0B;AAM/B,WACEqD,KAAK,CAAC6F,OAAN,CAAclJ,KAAd,KAAwBA,KAAK,CAAC+B,MAAN,KAAiB,CAAzC,IAA8C/B,KAAK,CAACmJ,KAAN,CAAYhK,IAAI,CAACqN,MAAjB,CADhD;AAGD;AATgC;;;;;;;;;;ACkDnC,IAAM0D,kBAAkB,GAAG,IAAItT,OAAJ,EAA3B;IAEaoE,IAAI,GAAkB;AACjC;;;AAIA8C,EAAAA,QALiC,oBAKxBqM,IALwB,EAKZrR,IALY;AAM/B,QAAMyC,IAAI,GAAGP,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAerR,IAAf,CAAb;;AAEA,QAAIuB,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAJ,EAAuB;AACrB,YAAM,IAAIkL,KAAJ,iDACqC3N,IADrC,yDACwFyC,IADxF,EAAN;AAGD;;AAED,WAAOA,IAAP;AACD,GAfgC;;AAiBjC;;;;;;AAOCkC,EAAAA,SAxBgC,sBAyB/B0M,IAzB+B,EA0B/BrR,IA1B+B;QA2B/BiL,8EAEI;;iDAEY5K,IAAI,CAACsE,SAAL,CAAe3E,IAAf,EAAqBiL,OAArB;;;;AAAhB,0DAA+C;AAAA,YAApCvG,CAAoC;AAC7C,YAAMlB,CAAC,GAAGtB,IAAI,CAAC8C,QAAL,CAAcqM,IAAd,EAAoB3M,CAApB,CAAV;AACA,YAAM5B,KAAK,GAAwB,CAACU,CAAD,EAAIkB,CAAJ,CAAnC;AACA,cAAM5B,KAAN;AACD;;;;;;AACF,GApCgC;;AAsCjC;;;AAIAI,EAAAA,KA1CiC,iBA0C3BmO,IA1C2B,EA0CfC,KA1Ce;AA2C/B,QAAI/P,IAAI,CAACC,MAAL,CAAY6P,IAAZ,CAAJ,EAAuB;AACrB,YAAM,IAAI1D,KAAJ,gDACoC4D,IAAI,CAACC,SAAL,CAAeH,IAAf,CADpC,EAAN;AAGD;;AAED,QAAMI,CAAC,GAAGJ,IAAI,CAAC9S,QAAL,CAAc+S,KAAd,CAAV;;AAEA,QAAIG,CAAC,IAAI,IAAT,EAAe;AACb,YAAM,IAAI9D,KAAJ,sCAC2B2D,KAD3B,wBAC+CC,IAAI,CAACC,SAAL,CACjDH,IADiD,CAD/C,EAAN;AAKD;;AAED,WAAOI,CAAP;AACD,GA5DgC;;AA8DjC;;;AAIClT,EAAAA,QAlEgC,qBAmE/B8S,IAnE+B,EAoE/BrR,IApE+B;QAqE/BiL,8EAEI;AAEJ,2BAA4BA,OAA5B,CAAQpJ,OAAR;AAAA,QAAQA,OAAR,iCAAkB,KAAlB;AACA,QAAMmD,QAAQ,GAAG9C,IAAI,CAAC8C,QAAL,CAAcqM,IAAd,EAAoBrR,IAApB,CAAjB;AACA,QAAQzB,QAAR,GAAqByG,QAArB,CAAQzG,QAAR;AACA,QAAI+S,KAAK,GAAGzP,OAAO,GAAGtD,QAAQ,CAAC0E,MAAT,GAAkB,CAArB,GAAyB,CAA5C;;AAEA,WAAOpB,OAAO,GAAGyP,KAAK,IAAI,CAAZ,GAAgBA,KAAK,GAAG/S,QAAQ,CAAC0E,MAA/C,EAAuD;AACrD,UAAMC,KAAK,GAAGhB,IAAI,CAACgB,KAAL,CAAW8B,QAAX,EAAqBsM,KAArB,CAAd;AACA,UAAMI,SAAS,GAAG1R,IAAI,CAACoD,MAAL,CAAYkO,KAAZ,CAAlB;AACA,YAAM,CAACpO,KAAD,EAAQwO,SAAR,CAAN;AACAJ,MAAAA,KAAK,GAAGzP,OAAO,GAAGyP,KAAK,GAAG,CAAX,GAAeA,KAAK,GAAG,CAAtC;AACD;AACF,GApFgC;;AAsFjC;;;AAIA/B,EAAAA,MA1FiC,kBA0F1B8B,IA1F0B,EA0FdrR,IA1Fc,EA0FF2R,OA1FE;AA2F/B,QAAMjN,CAAC,GAAGrE,IAAI,CAACkP,MAAL,CAAYvP,IAAZ,EAAkB2R,OAAlB,CAAV;AACA,QAAMnO,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAV;AACA,WAAO,CAAClB,CAAD,EAAIkB,CAAJ,CAAP;AACD,GA9FgC;;AAgGjC;;;AAIAkN,EAAAA,UApGiC,sBAoGtBP,IApGsB,EAoGVrR,IApGU;AAqG/B,QAAMyC,IAAI,GAAGP,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAerR,IAAf,CAAb;;AAEA,QAAIhB,MAAM,CAACuE,QAAP,CAAgBd,IAAhB,CAAJ,EAA2B;AACzB,YAAM,IAAIkL,KAAJ,mDACuC3N,IADvC,kEACmGyC,IADnG,EAAN;AAGD;;AAED,WAAOA,IAAP;AACD,GA9GgC;;AAgHjC;;;AAIC6B,EAAAA,WApHgC,wBAqH/B+M,IArH+B;QAsH/BpG,8EAKI;;kDAEuB/I,IAAI,CAACuC,KAAL,CAAW4M,IAAX,EAAiBpG,OAAjB;;;;AAA3B,6DAAsD;AAAA;AAAA,YAA1CxI,IAA0C;AAAA,YAApCzC,IAAoC;;AACpD,YAAIA,IAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB;AACA;AACA,gBAAM,CAACR,IAAD,EAAOzC,IAAP,CAAN;AACD;AACF;;;;;;AACF,GApIgC;;AAsIjC;;;;;AAMC6R,EAAAA,QA5IgC,qBA6I/BR,IA7I+B;QA8I/BpG,8EAKI;;kDAEuB/I,IAAI,CAACuC,KAAL,CAAW4M,IAAX,EAAiBpG,OAAjB;;;;AAA3B,6DAAsD;AAAA;AAAA,YAA1CxI,IAA0C;AAAA,YAApCzC,IAAoC;;AACpD,YAAI+C,OAAO,CAACC,SAAR,CAAkBP,IAAlB,CAAJ,EAA6B;AAC3B,gBAAM,CAACA,IAAD,EAAOzC,IAAP,CAAN;AACD;AACF;;;;;;AACF,GA1JgC;;AA4JjC;;;AAIA8R,EAAAA,YAhKiC,wBAgKpBrP,IAhKoB;AAiK/B,QAAIM,OAAO,CAACmH,UAAR,CAAmBzH,IAAnB,CAAJ,EAA8B;AAC5B,MAAoCA,IAApC,CAAQlE,QAAR;AAAA,cAAqBwT,UAArB,4BAAoCtP,IAApC;;AAEA,aAAOsP,UAAP;AACD,KAJD,MAIO;AACL,MAAgCtP,IAAhC,CAAQG,IAAR;AAAA,cAAiBmP,WAAjB,4BAAgCtP,IAAhC;;AAEA,aAAOsP,WAAP;AACD;AACF,GA1KgC;;AA4KjC;;;AAIAhG,EAAAA,KAhLiC,iBAgL3BsF,IAhL2B,EAgLfrR,IAhLe;AAiL/B,QAAM0E,CAAC,GAAG1E,IAAI,CAACyH,KAAL,EAAV;AACA,QAAIjE,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAR;;AAEA,WAAOlB,CAAP,EAAU;AACR,UAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBA,CAAC,CAACjF,QAAF,CAAW0E,MAAX,KAAsB,CAA5C,EAA+C;AAC7C;AACD,OAFD,MAEO;AACLO,QAAAA,CAAC,GAAGA,CAAC,CAACjF,QAAF,CAAW,CAAX,CAAJ;AACAmG,QAAAA,CAAC,CAACtE,IAAF,CAAO,CAAP;AACD;AACF;;AAED,WAAO,CAACoD,CAAD,EAAIkB,CAAJ,CAAP;AACD,GA9LgC;;AAgMjC;;;AAIAvC,EAAAA,QApMiC,oBAoMxBkP,IApMwB,EAoMZ7F,KApMY;AAqM/B,QAAIjK,IAAI,CAACC,MAAL,CAAY6P,IAAZ,CAAJ,EAAuB;AACrB,YAAM,IAAI1D,KAAJ,iEACqD4D,IAAI,CAACC,SAAL,CACvDH,IADuD,CADrD,EAAN;AAKD;;AAED,QAAMW,OAAO,GAAGC,aAAO,CAAC;AAAE1T,MAAAA,QAAQ,EAAE8S,IAAI,CAAC9S;AAAjB,KAAD,EAA8B,UAAAqL,CAAC;AACpD,yBAAqBzI,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAArB;AAAA;AAAA,UAAOK,KAAP;AAAA,UAAchE,GAAd;;AACA,UAAMoG,WAAW,GAAG/L,IAAI,CAACuC,KAAL,CAAWmF,CAAX,EAAc;AAChC/H,QAAAA,OAAO,EAAE,IADuB;AAEhCqM,QAAAA,IAAI,EAAE;AAAA;AAAA,cAAIlO,IAAJ;;AAAA,iBAAc,CAACmB,KAAK,CAAC0M,QAAN,CAAerC,KAAf,EAAsBxL,IAAtB,CAAf;AAAA;AAF0B,OAAd,CAApB;;oDAKuBiO;;;;AAAvB,+DAAoC;AAAA;AAAA,cAAtBjO,IAAsB;;AAClC,cAAI,CAACmB,KAAK,CAAC0M,QAAN,CAAerC,KAAf,EAAsBxL,IAAtB,CAAL,EAAkC;AAChC,gBAAM4N,MAAM,GAAG1L,IAAI,CAAC0L,MAAL,CAAYhE,CAAZ,EAAe5J,IAAf,CAAf;AACA,gBAAMsR,KAAK,GAAGtR,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB;AACA2K,YAAAA,MAAM,CAACrP,QAAP,CAAgB2T,MAAhB,CAAuBZ,KAAvB,EAA8B,CAA9B;AACD;;AAED,cAAIjR,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkB6H,GAAG,CAAC7H,IAAtB,CAAJ,EAAiC;AAC/B,gBAAMiN,IAAI,GAAG/K,IAAI,CAAC+K,IAAL,CAAUrD,CAAV,EAAa5J,IAAb,CAAb;AACAiN,YAAAA,IAAI,CAACrK,IAAL,GAAYqK,IAAI,CAACrK,IAAL,CAAU6E,KAAV,CAAgB,CAAhB,EAAmBI,GAAG,CAACkF,MAAvB,CAAZ;AACD;;AAED,cAAI1M,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkB6L,KAAK,CAAC7L,IAAxB,CAAJ,EAAmC;AACjC,gBAAMiN,KAAI,GAAG/K,IAAI,CAAC+K,IAAL,CAAUrD,CAAV,EAAa5J,IAAb,CAAb;;AACAiN,YAAAA,KAAI,CAACrK,IAAL,GAAYqK,KAAI,CAACrK,IAAL,CAAU6E,KAAV,CAAgBoE,KAAK,CAACkB,MAAtB,CAAZ;AACD;AACF;;;;;;;AAED,UAAI/N,MAAM,CAACuE,QAAP,CAAgBqG,CAAhB,CAAJ,EAAwB;AACtBA,QAAAA,CAAC,CAACnL,SAAF,GAAc,IAAd;AACD;AACF,KA5BsB,CAAvB;AA8BA,WAAOuT,OAAO,CAACzT,QAAf;AACD,GA5OgC;;AA8OjC;;;;AAKAmB,EAAAA,GAnPiC,eAmP7B2R,IAnP6B,EAmPjBrR,IAnPiB;AAoP/B,QAAIyC,IAAI,GAAG4O,IAAX;;AAEA,SAAK,IAAI5N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzD,IAAI,CAACiD,MAAzB,EAAiCQ,CAAC,EAAlC,EAAsC;AACpC,UAAMiB,CAAC,GAAG1E,IAAI,CAACyD,CAAD,CAAd;;AAEA,UAAIlC,IAAI,CAACC,MAAL,CAAYiB,IAAZ,KAAqB,CAACA,IAAI,CAAClE,QAAL,CAAcmG,CAAd,CAA1B,EAA4C;AAC1C,cAAM,IAAIiJ,KAAJ,6CACiC3N,IADjC,wBACmDuR,IAAI,CAACC,SAAL,CACrDH,IADqD,CADnD,EAAN;AAKD;;AAED5O,MAAAA,IAAI,GAAGA,IAAI,CAAClE,QAAL,CAAcmG,CAAd,CAAP;AACD;;AAED,WAAOjC,IAAP;AACD,GArQgC;;AAuQjC;;;AAIAtC,EAAAA,GA3QiC,eA2Q7BkR,IA3Q6B,EA2QjBrR,IA3QiB;AA4Q/B,QAAIyC,IAAI,GAAG4O,IAAX;;AAEA,SAAK,IAAI5N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzD,IAAI,CAACiD,MAAzB,EAAiCQ,CAAC,EAAlC,EAAsC;AACpC,UAAMiB,CAAC,GAAG1E,IAAI,CAACyD,CAAD,CAAd;;AAEA,UAAIlC,IAAI,CAACC,MAAL,CAAYiB,IAAZ,KAAqB,CAACA,IAAI,CAAClE,QAAL,CAAcmG,CAAd,CAA1B,EAA4C;AAC1C,eAAO,KAAP;AACD;;AAEDjC,MAAAA,IAAI,GAAGA,IAAI,CAAClE,QAAL,CAAcmG,CAAd,CAAP;AACD;;AAED,WAAO,IAAP;AACD,GAzRgC;;AA2RjC;;;AAIAyN,EAAAA,MA/RiC,kBA+R1BjR,KA/R0B;AAgS/B,WACEK,IAAI,CAACC,MAAL,CAAYN,KAAZ,KAAsB6B,OAAO,CAACC,SAAR,CAAkB9B,KAAlB,CAAtB,IAAkDlC,MAAM,CAACuE,QAAP,CAAgBrC,KAAhB,CADpD;AAGD,GAnSgC;;AAqSjC;;;AAIA+I,EAAAA,UAzSiC,sBAyStB/I,KAzSsB;AA0S/B,QAAI,CAACqD,KAAK,CAAC6F,OAAN,CAAclJ,KAAd,CAAL,EAA2B;AACzB,aAAO,KAAP;AACD;;AACD,QAAMkR,YAAY,GAAGhB,kBAAkB,CAAC1R,GAAnB,CAAuBwB,KAAvB,CAArB;;AACA,QAAIkR,YAAY,KAAK3H,SAArB,EAAgC;AAC9B,aAAO2H,YAAP;AACD;;AACD,QAAMnI,UAAU,GAAG/I,KAAK,CAACmJ,KAAN,CAAY,UAAAC,GAAG;AAAA,aAAIpI,IAAI,CAACiQ,MAAL,CAAY7H,GAAZ,CAAJ;AAAA,KAAf,CAAnB;AACA8G,IAAAA,kBAAkB,CAAC1Q,GAAnB,CAAuBQ,KAAvB,EAA8B+I,UAA9B;AACA,WAAOA,UAAP;AACD,GApTgC;;AAsTjC;;;AAIA+C,EAAAA,IA1TiC,gBA0T5BqE,IA1T4B,EA0ThBrR,IA1TgB;AA2T/B,QAAM0E,CAAC,GAAG1E,IAAI,CAACyH,KAAL,EAAV;AACA,QAAIjE,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAR;;AAEA,WAAOlB,CAAP,EAAU;AACR,UAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBA,CAAC,CAACjF,QAAF,CAAW0E,MAAX,KAAsB,CAA5C,EAA+C;AAC7C;AACD,OAFD,MAEO;AACL,YAAMQ,CAAC,GAAGD,CAAC,CAACjF,QAAF,CAAW0E,MAAX,GAAoB,CAA9B;AACAO,QAAAA,CAAC,GAAGA,CAAC,CAACjF,QAAF,CAAWkF,CAAX,CAAJ;AACAiB,QAAAA,CAAC,CAACtE,IAAF,CAAOqD,CAAP;AACD;AACF;;AAED,WAAO,CAACD,CAAD,EAAIkB,CAAJ,CAAP;AACD,GAzUgC;;AA2UjC;;;AAIAuI,EAAAA,IA/UiC,gBA+U5BoE,IA/U4B,EA+UhBrR,IA/UgB;AAgV/B,QAAMyC,IAAI,GAAGP,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAerR,IAAf,CAAb;;AAEA,QAAI,CAACuB,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAL,EAAwB;AACtB,YAAM,IAAIkL,KAAJ,6CACiC3N,IADjC,qDACgFyC,IADhF,EAAN;AAGD;;AAED,WAAOA,IAAP;AACD,GAzVgC;;AA2VjC;;;;;;AAOC4B,EAAAA,MAlWgC,mBAmW/BgN,IAnW+B,EAoW/BrR,IApW+B;QAqW/BiL,8EAEI;;kDAEY5K,IAAI,CAACgE,MAAL,CAAYrE,IAAZ,EAAkBiL,OAAlB;;;;AAAhB,6DAA4C;AAAA,YAAjCvG,CAAiC;AAC1C,YAAMlB,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAV;AACA,cAAM,CAAClB,CAAD,EAAIkB,CAAJ,CAAN;AACD;;;;;;AACF,GA7WgC;;AA+WjC;;;AAIAmG,EAAAA,OAnXiC,mBAmXzBpI,IAnXyB,EAmXb+H,KAnXa;AAoX/B,WACGzH,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KACCM,OAAO,CAACwH,cAAR,CAAuBC,KAAvB,CADD,IAECzH,OAAO,CAAC8H,OAAR,CAAgBpI,IAAhB,EAAsB+H,KAAtB,CAFF,IAGCjJ,IAAI,CAACC,MAAL,CAAYiB,IAAZ,KACClB,IAAI,CAAC8Q,WAAL,CAAiB7H,KAAjB,CADD,IAECjJ,IAAI,CAACsJ,OAAL,CAAapI,IAAb,EAAmB+H,KAAnB,CANJ;AAQD,GA5XgC;;AA8XjC;;;;;AAMC/F,EAAAA,KApYgC,kBAqY/B4M,IArY+B;QAsY/BpG,8EAKI;AAEJ,QAAQiD,IAAR,GAAkCjD,OAAlC,CAAQiD,IAAR;AAAA,4BAAkCjD,OAAlC,CAAcpJ,OAAd;AAAA,QAAcA,OAAd,kCAAwB,KAAxB;AACA,wBAA0BoJ,OAA1B,CAAQzG,IAAR;AAAA,QAAQA,IAAR,8BAAe,EAAf;AAAA,QAAmBgJ,EAAnB,GAA0BvC,OAA1B,CAAmBuC,EAAnB;AACA,QAAM8E,OAAO,GAAG,IAAI1S,GAAJ,EAAhB;AACA,QAAI8E,CAAC,GAAS,EAAd;AACA,QAAIlB,CAAC,GAAG6N,IAAR;;AAEA,WAAO,IAAP,EAAa;AACX,UAAI7D,EAAE,KAAK3L,OAAO,GAAGxB,IAAI,CAAC2Q,QAAL,CAActM,CAAd,EAAiB8I,EAAjB,CAAH,GAA0BnN,IAAI,CAACkS,OAAL,CAAa7N,CAAb,EAAgB8I,EAAhB,CAAtC,CAAN,EAAkE;AAChE;AACD;;AAED,UAAI,CAAC8E,OAAO,CAACnS,GAAR,CAAYqD,CAAZ,CAAL,EAAqB;AACnB,cAAM,CAACA,CAAD,EAAIkB,CAAJ,CAAN;AACD,OAPU;;;AAUX,UACE,CAAC4N,OAAO,CAACnS,GAAR,CAAYqD,CAAZ,CAAD,IACA,CAACjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,CADD,IAEAA,CAAC,CAACjF,QAAF,CAAW0E,MAAX,KAAsB,CAFtB,KAGCiL,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAAC,CAAC1K,CAAD,EAAIkB,CAAJ,CAAD,CAAJ,KAAiB,KAHlC,CADF,EAKE;AACA4N,QAAAA,OAAO,CAACvS,GAAR,CAAYyD,CAAZ;AACA,YAAIgP,SAAS,GAAG3Q,OAAO,GAAG2B,CAAC,CAACjF,QAAF,CAAW0E,MAAX,GAAoB,CAAvB,GAA2B,CAAlD;;AAEA,YAAI5C,IAAI,CAAC6J,UAAL,CAAgBxF,CAAhB,EAAmBF,IAAnB,CAAJ,EAA8B;AAC5BgO,UAAAA,SAAS,GAAGhO,IAAI,CAACE,CAAC,CAACzB,MAAH,CAAhB;AACD;;AAEDyB,QAAAA,CAAC,GAAGA,CAAC,CAACtB,MAAF,CAASoP,SAAT,CAAJ;AACAhP,QAAAA,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAJ;AACA;AACD,OA1BU;;;AA6BX,UAAIA,CAAC,CAACzB,MAAF,KAAa,CAAjB,EAAoB;AAClB;AACD,OA/BU;;;AAkCX,UAAI,CAACpB,OAAL,EAAc;AACZ,YAAMtB,OAAO,GAAGF,IAAI,CAACgF,IAAL,CAAUX,CAAV,CAAhB;;AAEA,YAAIxC,IAAI,CAAC/B,GAAL,CAASkR,IAAT,EAAe9Q,OAAf,CAAJ,EAA6B;AAC3BmE,UAAAA,CAAC,GAAGnE,OAAJ;AACAiD,UAAAA,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAJ;AACA;AACD;AACF,OA1CU;;;AA6CX,UAAI7C,OAAO,IAAI6C,CAAC,CAACA,CAAC,CAACzB,MAAF,GAAW,CAAZ,CAAD,KAAoB,CAAnC,EAAsC;AACpC,YAAM1C,QAAO,GAAGF,IAAI,CAACwE,QAAL,CAAcH,CAAd,CAAhB;;AACAA,QAAAA,CAAC,GAAGnE,QAAJ;AACAiD,QAAAA,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAJ;AACA;AACD,OAlDU;;;AAqDXA,MAAAA,CAAC,GAAGrE,IAAI,CAACuN,MAAL,CAAYlJ,CAAZ,CAAJ;AACAlB,MAAAA,CAAC,GAAGtB,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAe3M,CAAf,CAAJ;AACA4N,MAAAA,OAAO,CAACvS,GAAR,CAAYyD,CAAZ;AACD;AACF,GA5cgC;;AA8cjC;;;AAIAoK,EAAAA,MAldiC,kBAkd1ByD,IAld0B,EAkddrR,IAldc;AAmd/B,QAAMmP,UAAU,GAAG9O,IAAI,CAACuN,MAAL,CAAY5N,IAAZ,CAAnB;AACA,QAAM0E,CAAC,GAAGxC,IAAI,CAACxC,GAAL,CAAS2R,IAAT,EAAelC,UAAf,CAAV;;AAEA,QAAI5N,IAAI,CAACC,MAAL,CAAYkD,CAAZ,CAAJ,EAAoB;AAClB,YAAM,IAAIiJ,KAAJ,0CAC8B3N,IAD9B,8CAAN;AAGD;;AAED,WAAO0E,CAAP;AACD,GA7dgC;;AA+djC;;;;;;;AAQA4L,EAAAA,MAveiC,kBAue1B7N,IAve0B;AAwe/B,QAAIlB,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAJ,EAAuB;AACrB,aAAOA,IAAI,CAACG,IAAZ;AACD,KAFD,MAEO;AACL,aAAOH,IAAI,CAAClE,QAAL,CAAcsQ,GAAd,CAAkB3M,IAAI,CAACoO,MAAvB,EAA+BpQ,IAA/B,CAAoC,EAApC,CAAP;AACD;AACF,GA7egC;;AA+ejC;;;AAICuS,EAAAA,KAnfgC,kBAof/BpB,IApf+B;QAqf/BpG,8EAKI;;kDAEuB/I,IAAI,CAACuC,KAAL,CAAW4M,IAAX,EAAiBpG,OAAjB;;;;AAA3B,6DAAsD;AAAA;AAAA,YAA1CxI,IAA0C;AAAA,YAApCzC,IAAoC;;AACpD,YAAIuB,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAJ,EAAuB;AACrB,gBAAM,CAACA,IAAD,EAAOzC,IAAP,CAAN;AACD;AACF;;;;;;AACF;AAjgBgC;;;;;IC2DtBuM,SAAS,GAAuB;AAC3C;;;AAIAmG,EAAAA,eAL2C,2BAK3BxR,KAL2B;AAMzC,WAAOqL,SAAS,CAACoG,WAAV,CAAsBzR,KAAtB,KAAgCA,KAAK,CAACL,IAAN,CAAW+R,QAAX,CAAoB,OAApB,CAAvC;AACD,GAP0C;;AAS3C;;;AAIAD,EAAAA,WAb2C,uBAa/BzR,KAb+B;AAczC,QAAI,CAAC8I,2BAAa,CAAC9I,KAAD,CAAlB,EAA2B;AACzB,aAAO,KAAP;AACD;;AAED,YAAQA,KAAK,CAACL,IAAd;AACE,WAAK,aAAL;AACE,eAAOR,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,KAA2BkC,IAAI,CAACiQ,MAAL,CAAYjR,KAAK,CAACuB,IAAlB,CAAlC;;AACF,WAAK,aAAL;AACE,eACE,OAAOvB,KAAK,CAAC6L,MAAb,KAAwB,QAAxB,IACA,OAAO7L,KAAK,CAAC0B,IAAb,KAAsB,QADtB,IAEAvC,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,CAHF;;AAKF,WAAK,YAAL;AACE,eACE,OAAOkB,KAAK,CAAC2R,QAAb,KAA0B,QAA1B,IACAxS,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,CADA,IAEAgK,2BAAa,CAAC9I,KAAK,CAAC6Q,UAAP,CAHf;;AAKF,WAAK,WAAL;AACE,eAAO1R,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,KAA2BK,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAACX,OAAlB,CAAlC;;AACF,WAAK,aAAL;AACE,eAAOF,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,KAA2BkC,IAAI,CAACiQ,MAAL,CAAYjR,KAAK,CAACuB,IAAlB,CAAlC;;AACF,WAAK,aAAL;AACE,eACE,OAAOvB,KAAK,CAAC6L,MAAb,KAAwB,QAAxB,IACA,OAAO7L,KAAK,CAAC0B,IAAb,KAAsB,QADtB,IAEAvC,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,CAHF;;AAKF,WAAK,UAAL;AACE,eACEK,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,KACAgK,2BAAa,CAAC9I,KAAK,CAAC6Q,UAAP,CADb,IAEA/H,2BAAa,CAAC9I,KAAK,CAAC4R,aAAP,CAHf;;AAKF,WAAK,eAAL;AACE,eACG5R,KAAK,CAAC6Q,UAAN,KAAqB,IAArB,IAA6B5Q,KAAK,CAACmL,OAAN,CAAcpL,KAAK,CAAC4R,aAApB,CAA9B,IACC5R,KAAK,CAAC4R,aAAN,KAAwB,IAAxB,IAAgC3R,KAAK,CAACmL,OAAN,CAAcpL,KAAK,CAAC6Q,UAApB,CADjC,IAEC/H,2BAAa,CAAC9I,KAAK,CAAC6Q,UAAP,CAAb,IACC/H,2BAAa,CAAC9I,KAAK,CAAC4R,aAAP,CAJjB;;AAMF,WAAK,YAAL;AACE,eACEzS,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,KACA,OAAOkB,KAAK,CAAC2R,QAAb,KAA0B,QAD1B,IAEA7I,2BAAa,CAAC9I,KAAK,CAAC6Q,UAAP,CAHf;;AAKF;AACE,eAAO,KAAP;AA7CJ;AA+CD,GAjE0C;;AAmE3C;;;AAIAvF,EAAAA,eAvE2C,2BAuE3BtL,KAvE2B;AAwEzC,WACEqD,KAAK,CAAC6F,OAAN,CAAclJ,KAAd,KAAwBA,KAAK,CAACmJ,KAAN,CAAY,UAAAC,GAAG;AAAA,aAAIiC,SAAS,CAACoG,WAAV,CAAsBrI,GAAtB,CAAJ;AAAA,KAAf,CAD1B;AAGD,GA3E0C;;AA6E3C;;;AAIAyI,EAAAA,oBAjF2C,gCAiFtB7R,KAjFsB;AAkFzC,WAAOqL,SAAS,CAACoG,WAAV,CAAsBzR,KAAtB,KAAgCA,KAAK,CAACL,IAAN,CAAW+R,QAAX,CAAoB,YAApB,CAAvC;AACD,GAnF0C;;AAqF3C;;;AAIAI,EAAAA,eAzF2C,2BAyF3B9R,KAzF2B;AA0FzC,WAAOqL,SAAS,CAACoG,WAAV,CAAsBzR,KAAtB,KAAgCA,KAAK,CAACL,IAAN,CAAW+R,QAAX,CAAoB,OAApB,CAAvC;AACD,GA3F0C;;AA6F3C;;;;AAKAK,EAAAA,OAlG2C,mBAkGnClU,EAlGmC;AAmGzC,YAAQA,EAAE,CAAC8B,IAAX;AACE,WAAK,aAAL;AAAoB;AAClB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,aAAL;AAAoB;AAClB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,YAAL;AAAmB;AACjB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE,YAAtB;AAAoCb,YAAAA,IAAI,EAAEK,IAAI,CAACwE,QAAL,CAAc9F,EAAE,CAACiB,IAAjB;AAA1C;AACD;;AAED,WAAK,WAAL;AAAkB;AAChB,cAAQO,OAAR,GAA0BxB,EAA1B,CAAQwB,OAAR;AAAA,cAAiBP,IAAjB,GAA0BjB,EAA1B,CAAiBiB,IAAjB,CADgB;;AAIhB,cAAIK,IAAI,CAAC2D,MAAL,CAAYzD,OAAZ,EAAqBP,IAArB,CAAJ,EAAgC;AAC9B,mBAAOjB,EAAP;AACD,WANe;AAShB;;;AACA,cAAIsB,IAAI,CAAC6S,SAAL,CAAelT,IAAf,EAAqBO,OAArB,CAAJ,EAAmC;AACjC,uDAAYxB,EAAZ;AAAgBiB,cAAAA,IAAI,EAAEO,OAAtB;AAA+BA,cAAAA,OAAO,EAAEP;AAAxC;AACD,WAZe;AAehB;AACA;AACA;AACA;AACA;;;AACA,cAAMmT,WAAW,GAAG9S,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,CAApB;AACA,cAAMqU,cAAc,GAAG/S,IAAI,CAACjB,SAAL,CAAeiB,IAAI,CAACgF,IAAL,CAAUrF,IAAV,CAAf,EAAgCjB,EAAhC,CAAvB;AACA,qDAAYA,EAAZ;AAAgBiB,YAAAA,IAAI,EAAEmT,WAAtB;AAAmC5S,YAAAA,OAAO,EAAE6S;AAA5C;AACD;;AAED,WAAK,aAAL;AAAoB;AAClB,qDAAYrU,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,aAAL;AAAoB;AAClB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,UAAL;AAAiB;AACf,cAAQkR,UAAR,GAAsChT,EAAtC,CAAQgT,UAAR;AAAA,cAAoBe,aAApB,GAAsC/T,EAAtC,CAAoB+T,aAApB;AACA,qDAAY/T,EAAZ;AAAgBgT,YAAAA,UAAU,EAAEe,aAA5B;AAA2CA,YAAAA,aAAa,EAAEf;AAA1D;AACD;;AAED,WAAK,eAAL;AAAsB;AACpB,cAAQA,WAAR,GAAsChT,EAAtC,CAAQgT,UAAR;AAAA,cAAoBe,cAApB,GAAsC/T,EAAtC,CAAoB+T,aAApB;;AAEA,cAAIf,WAAU,IAAI,IAAlB,EAAwB;AACtB,uDACKhT,EADL;AAEEgT,cAAAA,UAAU,EAAEe,cAFd;AAGEA,cAAAA,aAAa,EAAE;AAHjB;AAKD,WAND,MAMO,IAAIA,cAAa,IAAI,IAArB,EAA2B;AAChC,uDACK/T,EADL;AAEEgT,cAAAA,UAAU,EAAE,IAFd;AAGEe,cAAAA,aAAa,EAAEf;AAHjB;AAKD,WANM,MAMA;AACL,uDAAYhT,EAAZ;AAAgBgT,cAAAA,UAAU,EAAEe,cAA5B;AAA2CA,cAAAA,aAAa,EAAEf;AAA1D;AACD;AACF;;AAED,WAAK,YAAL;AAAmB;AACjB,qDAAYhT,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE,YAAtB;AAAoCb,YAAAA,IAAI,EAAEK,IAAI,CAACgF,IAAL,CAAUtG,EAAE,CAACiB,IAAb;AAA1C;AACD;AAzEH;AA2ED;AA9K0C;;ICtGhCK,IAAI,GAAkB;AACjC;;;;;;AAOAsE,EAAAA,SARiC,qBAQvB3E,IARuB;QAQXiL,8EAAiC;AACrD,2BAA4BA,OAA5B,CAAQpJ,OAAR;AAAA,QAAQA,OAAR,iCAAkB,KAAlB;AACA,QAAIwR,KAAK,GAAGhT,IAAI,CAACgE,MAAL,CAAYrE,IAAZ,EAAkBiL,OAAlB,CAAZ;;AAEA,QAAIpJ,OAAJ,EAAa;AACXwR,MAAAA,KAAK,GAAGA,KAAK,CAAC5L,KAAN,CAAY,CAAZ,CAAR;AACD,KAFD,MAEO;AACL4L,MAAAA,KAAK,GAAGA,KAAK,CAAC5L,KAAN,CAAY,CAAZ,EAAe,CAAC,CAAhB,CAAR;AACD;;AAED,WAAO4L,KAAP;AACD,GAnBgC;;AAqBjC;;;AAIA9D,EAAAA,MAzBiC,kBAyB1BvP,IAzB0B,EAyBd2R,OAzBc;AA0B/B,QAAMpC,MAAM,GAAS,EAArB;;AAEA,SAAK,IAAI9L,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzD,IAAI,CAACiD,MAAT,IAAmBQ,CAAC,GAAGkO,OAAO,CAAC1O,MAA/C,EAAuDQ,CAAC,EAAxD,EAA4D;AAC1D,UAAM6P,EAAE,GAAGtT,IAAI,CAACyD,CAAD,CAAf;AACA,UAAM8P,EAAE,GAAG5B,OAAO,CAAClO,CAAD,CAAlB;;AAEA,UAAI6P,EAAE,KAAKC,EAAX,EAAe;AACb;AACD;;AAEDhE,MAAAA,MAAM,CAACnP,IAAP,CAAYkT,EAAZ;AACD;;AAED,WAAO/D,MAAP;AACD,GAxCgC;;AA0CjC;;;;;;;;AASAlB,EAAAA,OAnDiC,mBAmDzBrO,IAnDyB,EAmDb2R,OAnDa;AAoD/B,QAAM6B,GAAG,GAAGC,IAAI,CAACD,GAAL,CAASxT,IAAI,CAACiD,MAAd,EAAsB0O,OAAO,CAAC1O,MAA9B,CAAZ;;AAEA,SAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+P,GAApB,EAAyB/P,CAAC,EAA1B,EAA8B;AAC5B,UAAIzD,IAAI,CAACyD,CAAD,CAAJ,GAAUkO,OAAO,CAAClO,CAAD,CAArB,EAA0B,OAAO,CAAC,CAAR;AAC1B,UAAIzD,IAAI,CAACyD,CAAD,CAAJ,GAAUkO,OAAO,CAAClO,CAAD,CAArB,EAA0B,OAAO,CAAP;AAC3B;;AAED,WAAO,CAAP;AACD,GA5DgC;;AA8DjC;;;AAIAiQ,EAAAA,SAlEiC,qBAkEvB1T,IAlEuB,EAkEX2R,OAlEW;AAmE/B,QAAMlO,CAAC,GAAGzD,IAAI,CAACiD,MAAL,GAAc,CAAxB;AACA,QAAM0Q,EAAE,GAAG3T,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAchE,CAAd,CAAX;AACA,QAAMmQ,EAAE,GAAGjC,OAAO,CAAClK,KAAR,CAAc,CAAd,EAAiBhE,CAAjB,CAAX;AACA,QAAM6P,EAAE,GAAGtT,IAAI,CAACyD,CAAD,CAAf;AACA,QAAM8P,EAAE,GAAG5B,OAAO,CAAClO,CAAD,CAAlB;AACA,WAAOpD,IAAI,CAAC2D,MAAL,CAAY2P,EAAZ,EAAgBC,EAAhB,KAAuBN,EAAE,GAAGC,EAAnC;AACD,GAzEgC;;AA2EjC;;;AAIAM,EAAAA,MA/EiC,kBA+E1B7T,IA/E0B,EA+Ed2R,OA/Ec;AAgF/B,QAAMlO,CAAC,GAAGzD,IAAI,CAACiD,MAAf;AACA,QAAM0Q,EAAE,GAAG3T,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAchE,CAAd,CAAX;AACA,QAAMmQ,EAAE,GAAGjC,OAAO,CAAClK,KAAR,CAAc,CAAd,EAAiBhE,CAAjB,CAAX;AACA,WAAOpD,IAAI,CAAC2D,MAAL,CAAY2P,EAAZ,EAAgBC,EAAhB,CAAP;AACD,GApFgC;;AAsFjC;;;AAIAE,EAAAA,UA1FiC,sBA0FtB9T,IA1FsB,EA0FV2R,OA1FU;AA2F/B,QAAMlO,CAAC,GAAGzD,IAAI,CAACiD,MAAL,GAAc,CAAxB;AACA,QAAM0Q,EAAE,GAAG3T,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAchE,CAAd,CAAX;AACA,QAAMmQ,EAAE,GAAGjC,OAAO,CAAClK,KAAR,CAAc,CAAd,EAAiBhE,CAAjB,CAAX;AACA,QAAM6P,EAAE,GAAGtT,IAAI,CAACyD,CAAD,CAAf;AACA,QAAM8P,EAAE,GAAG5B,OAAO,CAAClO,CAAD,CAAlB;AACA,WAAOpD,IAAI,CAAC2D,MAAL,CAAY2P,EAAZ,EAAgBC,EAAhB,KAAuBN,EAAE,GAAGC,EAAnC;AACD,GAjGgC;;AAmGjC;;;AAIAvP,EAAAA,MAvGiC,kBAuG1BhE,IAvG0B,EAuGd2R,OAvGc;AAwG/B,WACE3R,IAAI,CAACiD,MAAL,KAAgB0O,OAAO,CAAC1O,MAAxB,IAAkCjD,IAAI,CAACqK,KAAL,CAAW,UAAC7G,CAAD,EAAIC,CAAJ;AAAA,aAAUD,CAAC,KAAKmO,OAAO,CAAClO,CAAD,CAAvB;AAAA,KAAX,CADpC;AAGD,GA3GgC;;AA6GjC;;;AAIAsQ,EAAAA,WAjHiC,uBAiHrB/T,IAjHqB;AAkH/B,WAAOA,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAJ,GAAwB,CAA/B;AACD,GAnHgC;;AAqHjC;;;AAIAsP,EAAAA,OAzHiC,mBAyHzBvS,IAzHyB,EAyHb2R,OAzHa;AA0H/B,WAAOtR,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CAAvC;AACD,GA3HgC;;AA6HjC;;;AAIAzH,EAAAA,UAjIiC,sBAiItBlK,IAjIsB,EAiIV2R,OAjIU;AAkI/B,WAAO3R,IAAI,CAACiD,MAAL,GAAc0O,OAAO,CAAC1O,MAAtB,IAAgC5C,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CAAvE;AACD,GAnIgC;;AAqIjC;;;AAIAX,EAAAA,QAzIiC,oBAyIxBhR,IAzIwB,EAyIZ2R,OAzIY;AA0I/B,WAAOtR,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CAAC,CAAxC;AACD,GA3IgC;;AA6IjC;;;AAIAqC,EAAAA,OAjJiC,mBAiJzBhU,IAjJyB,EAiJb2R,OAjJa;AAkJ/B,WACE3R,IAAI,CAACiD,MAAL,KAAgB0O,OAAO,CAAC1O,MAAR,GAAiB,CAAjC,IAAsC5C,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CADxE;AAGD,GArJgC;;AAuJjC;;;AAIAsC,EAAAA,QA3JiC,oBA2JxBjU,IA3JwB,EA2JZ2R,OA3JY;AA4J/B,WAAO3R,IAAI,CAACiD,MAAL,IAAe0O,OAAO,CAAC1O,MAAvB,IAAiC5C,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CAAxE;AACD,GA7JgC;;AA+JjC;;;AAIAuC,EAAAA,YAnKiC,wBAmKpBlU,IAnKoB,EAmKR2R,OAnKQ;AAoK/B,WAAO3R,IAAI,CAACiD,MAAL,GAAc0O,OAAO,CAAC1O,MAAtB,IAAgC5C,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CAAvE;AACD,GArKgC;;AAuKjC;;;AAIAwC,EAAAA,QA3KiC,oBA2KxBnU,IA3KwB,EA2KZ2R,OA3KY;AA4K/B,WACE3R,IAAI,CAACiD,MAAL,GAAc,CAAd,KAAoB0O,OAAO,CAAC1O,MAA5B,IAAsC5C,IAAI,CAACgO,OAAL,CAAarO,IAAb,EAAmB2R,OAAnB,MAAgC,CADxE;AAGD,GA/KgC;;AAiLjC;;;AAIAjE,EAAAA,MArLiC,kBAqL1BxM,KArL0B;AAsL/B,WACEqD,KAAK,CAAC6F,OAAN,CAAclJ,KAAd,MACCA,KAAK,CAAC+B,MAAN,KAAiB,CAAjB,IAAsB,OAAO/B,KAAK,CAAC,CAAD,CAAZ,KAAoB,QAD3C,CADF;AAID,GA1LgC;;AA4LjC;;;AAIAgS,EAAAA,SAhMiC,qBAgMvBlT,IAhMuB,EAgMX2R,OAhMW;AAiM/B,QAAI3R,IAAI,CAACiD,MAAL,KAAgB0O,OAAO,CAAC1O,MAA5B,EAAoC;AAClC,aAAO,KAAP;AACD;;AAED,QAAM0Q,EAAE,GAAG3T,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAX;AACA,QAAMmM,EAAE,GAAGjC,OAAO,CAAClK,KAAR,CAAc,CAAd,EAAiB,CAAC,CAAlB,CAAX;AACA,QAAM2M,EAAE,GAAGpU,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAf;AACA,QAAMoR,EAAE,GAAG1C,OAAO,CAACA,OAAO,CAAC1O,MAAR,GAAiB,CAAlB,CAAlB;AACA,WAAOmR,EAAE,KAAKC,EAAP,IAAahU,IAAI,CAAC2D,MAAL,CAAY2P,EAAZ,EAAgBC,EAAhB,CAApB;AACD,GA1MgC;;AA4MjC;;;;;;;AAQAvP,EAAAA,MApNiC,kBAqN/BrE,IArN+B;QAsN/BiL,8EAEI;AAEJ,4BAA4BA,OAA5B,CAAQpJ,OAAR;AAAA,QAAQA,OAAR,kCAAkB,KAAlB;AACA,QAAMyS,IAAI,GAAW,EAArB;;AAEA,SAAK,IAAI7Q,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIzD,IAAI,CAACiD,MAA1B,EAAkCQ,CAAC,EAAnC,EAAuC;AACrC6Q,MAAAA,IAAI,CAAClU,IAAL,CAAUJ,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAchE,CAAd,CAAV;AACD;;AAED,QAAI5B,OAAJ,EAAa;AACXyS,MAAAA,IAAI,CAACzS,OAAL;AACD;;AAED,WAAOyS,IAAP;AACD,GAtOgC;;AAwOjC;;;AAIAjP,EAAAA,IA5OiC,gBA4O5BrF,IA5O4B;AA6O/B,QAAIA,IAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI0K,KAAJ,oDACwC3N,IADxC,sCAAN;AAGD;;AAED,QAAMgN,IAAI,GAAGhN,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAjB;AACA,WAAOjD,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,EAAkBrE,MAAlB,CAAyB4J,IAAI,GAAG,CAAhC,CAAP;AACD,GArPgC;;AAuPjC;;;;;;;AAOA1M,EAAAA,yBA9PiC,qCA8PPiU,SA9PO;AA+P/B,YAAQA,SAAS,CAAC1T,IAAlB;AACE,WAAK,aAAL;AACA,WAAK,aAAL;AACA,WAAK,YAAL;AACA,WAAK,YAAL;AACA,WAAK,WAAL;AACE,eAAO,IAAP;;AACF;AACE,eAAO,KAAP;AARJ;AAUD,GAzQgC;;AA2QjC;;;AAIA+M,EAAAA,MA/QiC,kBA+Q1B5N,IA/Q0B;AAgR/B,QAAIA,IAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI0K,KAAJ,wDAA0D3N,IAA1D,QAAN;AACD;;AAED,WAAOA,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAP;AACD,GArRgC;;AAuRjC;;;AAIA5C,EAAAA,QA3RiC,oBA2RxB7E,IA3RwB;AA4R/B,QAAIA,IAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI0K,KAAJ,wDAC4C3N,IAD5C,0CAAN;AAGD;;AAED,QAAMgN,IAAI,GAAGhN,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAjB;;AAEA,QAAI+J,IAAI,IAAI,CAAZ,EAAe;AACb,YAAM,IAAIW,KAAJ,+DACmD3N,IADnD,oDAAN;AAGD;;AAED,WAAOA,IAAI,CAACyH,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,EAAkBrE,MAAlB,CAAyB4J,IAAI,GAAG,CAAhC,CAAP;AACD,GA3SgC;;AA6SjC;;;AAIAwH,EAAAA,QAjTiC,oBAiTxBxU,IAjTwB,EAiTZgF,QAjTY;AAkT/B,QAAI,CAAC3E,IAAI,CAAC6J,UAAL,CAAgBlF,QAAhB,EAA0BhF,IAA1B,CAAD,IAAoC,CAACK,IAAI,CAAC2D,MAAL,CAAYhE,IAAZ,EAAkBgF,QAAlB,CAAzC,EAAsE;AACpE,YAAM,IAAI2I,KAAJ,4CACgC3N,IADhC,gCAC0DgF,QAD1D,sDAAN;AAGD;;AAED,WAAOhF,IAAI,CAACyH,KAAL,CAAWzC,QAAQ,CAAC/B,MAApB,CAAP;AACD,GAzTgC;;AA2TjC;;;AAIA7D,EAAAA,SA/TiC,qBAgU/BY,IAhU+B,EAiU/BuU,SAjU+B;QAkU/BtJ,8EAAwD;AAExD,WAAOgH,aAAO,CAACjS,IAAD,EAAO,UAAA0E,CAAC;AACpB,8BAAiCuG,OAAjC,CAAQ0E,QAAR;AAAA,UAAQA,QAAR,kCAAmB,SAAnB;;AAGA,UAAI,CAAC3P,IAAD,IAAS,CAAAA,IAAI,SAAJ,IAAAA,IAAI,WAAJ,YAAAA,IAAI,CAAEiD,MAAN,MAAiB,CAA9B,EAAiC;AAC/B;AACD;;AAED,UAAIyB,CAAC,KAAK,IAAV,EAAgB;AACd,eAAO,IAAP;AACD;;AAED,cAAQ6P,SAAS,CAAC1T,IAAlB;AACE,aAAK,aAAL;AAAoB;AAClB,gBAAc9B,EAAd,GAAqBwV,SAArB,CAAQvU,IAAR;;AAEA,gBACEK,IAAI,CAAC2D,MAAL,CAAYjF,EAAZ,EAAgB2F,CAAhB,KACArE,IAAI,CAACyT,UAAL,CAAgB/U,EAAhB,EAAoB2F,CAApB,CADA,IAEArE,IAAI,CAAC6J,UAAL,CAAgBnL,EAAhB,EAAoB2F,CAApB,CAHF,EAIE;AACAA,cAAAA,CAAC,CAAC3F,EAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAED;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAclE,GAAd,GAAqBwV,SAArB,CAAQvU,IAAR;;AAEA,gBAAIK,IAAI,CAAC2D,MAAL,CAAYjF,GAAZ,EAAgB2F,CAAhB,KAAsBrE,IAAI,CAAC6J,UAAL,CAAgBnL,GAAhB,EAAoB2F,CAApB,CAA1B,EAAkD;AAChD,qBAAO,IAAP;AACD,aAFD,MAEO,IAAIrE,IAAI,CAACyT,UAAL,CAAgB/U,GAAhB,EAAoB2F,CAApB,CAAJ,EAA4B;AACjCA,cAAAA,CAAC,CAAC3F,GAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAED;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAclE,IAAd,GAA+BwV,SAA/B,CAAQvU,IAAR;AAAA,gBAAkB6S,QAAlB,GAA+B0B,SAA/B,CAAkB1B,QAAlB;;AAEA,gBAAIxS,IAAI,CAAC2D,MAAL,CAAYjF,IAAZ,EAAgB2F,CAAhB,KAAsBrE,IAAI,CAACyT,UAAL,CAAgB/U,IAAhB,EAAoB2F,CAApB,CAA1B,EAAkD;AAChDA,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD,aAFD,MAEO,IAAI5C,IAAI,CAAC6J,UAAL,CAAgBnL,IAAhB,EAAoB2F,CAApB,CAAJ,EAA4B;AACjCA,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACAyB,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAJ,CAAD,IAAgB4P,QAAhB;AACD;;AAED;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAc9T,IAAd,GAA+BwV,SAA/B,CAAQvU,IAAR;AAAA,gBAAkB6S,SAAlB,GAA+B0B,SAA/B,CAAkB1B,QAAlB;;AAEA,gBAAIxS,IAAI,CAAC2D,MAAL,CAAYjF,IAAZ,EAAgB2F,CAAhB,CAAJ,EAAwB;AACtB,kBAAIiL,QAAQ,KAAK,SAAjB,EAA4B;AAC1BjL,gBAAAA,CAAC,CAACA,CAAC,CAACzB,MAAF,GAAW,CAAZ,CAAD,IAAmB,CAAnB;AACD,eAFD,MAEO,IAAI0M,QAAQ,KAAK,UAAjB,EAA6B,CAA7B,MAEA;AACL,uBAAO,IAAP;AACD;AACF,aARD,MAQO,IAAItP,IAAI,CAACyT,UAAL,CAAgB/U,IAAhB,EAAoB2F,CAApB,CAAJ,EAA4B;AACjCA,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD,aAFM,MAEA,IAAI5C,IAAI,CAAC6J,UAAL,CAAgBnL,IAAhB,EAAoB2F,CAApB,KAA0B1E,IAAI,CAACjB,IAAE,CAACkE,MAAJ,CAAJ,IAAmB4P,SAAjD,EAA2D;AAChEnO,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACAyB,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAJ,CAAD,IAAgB4P,SAAhB;AACD;;AAED;AACD;;AAED,aAAK,WAAL;AAAkB;AAChB,gBAAc9T,IAAd,GAAmCwV,SAAnC,CAAQvU,IAAR;AAAA,gBAA2ByU,GAA3B,GAAmCF,SAAnC,CAAkBhU,OAAlB,CADgB;;AAIhB,gBAAIF,IAAI,CAAC2D,MAAL,CAAYjF,IAAZ,EAAgB0V,GAAhB,CAAJ,EAA0B;AACxB;AACD;;AAED,gBAAIpU,IAAI,CAAC6J,UAAL,CAAgBnL,IAAhB,EAAoB2F,CAApB,KAA0BrE,IAAI,CAAC2D,MAAL,CAAYjF,IAAZ,EAAgB2F,CAAhB,CAA9B,EAAkD;AAChD,kBAAMgQ,IAAI,GAAGD,GAAG,CAAChN,KAAJ,EAAb;;AAEA,kBAAIpH,IAAI,CAACyT,UAAL,CAAgB/U,IAAhB,EAAoB0V,GAApB,KAA4B1V,IAAE,CAACkE,MAAH,GAAYwR,GAAG,CAACxR,MAAhD,EAAwD;AACtDyR,gBAAAA,IAAI,CAAC3V,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAJ,IAAuB,CAAvB;AACD;;AAED,qBAAOyR,IAAI,CAACtR,MAAL,CAAYsB,CAAC,CAAC+C,KAAF,CAAQ1I,IAAE,CAACkE,MAAX,CAAZ,CAAP;AACD,aARD,MAQO,IACL5C,IAAI,CAAC6S,SAAL,CAAenU,IAAf,EAAmB0V,GAAnB,MACCpU,IAAI,CAAC6J,UAAL,CAAgBuK,GAAhB,EAAqB/P,CAArB,KAA2BrE,IAAI,CAAC2D,MAAL,CAAYyQ,GAAZ,EAAiB/P,CAAjB,CAD5B,CADK,EAGL;AACA,kBAAIrE,IAAI,CAACyT,UAAL,CAAgB/U,IAAhB,EAAoB2F,CAApB,CAAJ,EAA4B;AAC1BA,gBAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD,eAFD,MAEO;AACLyB,gBAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;AACF,aATM,MASA,IACL5C,IAAI,CAACyT,UAAL,CAAgBW,GAAhB,EAAqB/P,CAArB,KACArE,IAAI,CAAC2D,MAAL,CAAYyQ,GAAZ,EAAiB/P,CAAjB,CADA,IAEArE,IAAI,CAAC6J,UAAL,CAAgBuK,GAAhB,EAAqB/P,CAArB,CAHK,EAIL;AACA,kBAAIrE,IAAI,CAACyT,UAAL,CAAgB/U,IAAhB,EAAoB2F,CAApB,CAAJ,EAA4B;AAC1BA,gBAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAEDyB,cAAAA,CAAC,CAAC+P,GAAG,CAACxR,MAAJ,GAAa,CAAd,CAAD,IAAqB,CAArB;AACD,aAVM,MAUA,IAAI5C,IAAI,CAACyT,UAAL,CAAgB/U,IAAhB,EAAoB2F,CAApB,CAAJ,EAA4B;AACjC,kBAAIrE,IAAI,CAAC2D,MAAL,CAAYyQ,GAAZ,EAAiB/P,CAAjB,CAAJ,EAAyB;AACvBA,gBAAAA,CAAC,CAAC+P,GAAG,CAACxR,MAAJ,GAAa,CAAd,CAAD,IAAqB,CAArB;AACD;;AAEDyB,cAAAA,CAAC,CAAC3F,IAAE,CAACkE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAED;AACD;AAzGH;AA2GD,KAvHa,CAAd;AAwHD;AA5bgC;;IC7BtB9D,OAAO,GAAqB;AACvC;;;AAIAC,EAAAA,SALuC,qBAK7BF,GAL6B,EAKfH,EALe;AAMrC,QAAQ6Q,OAAR,GAA8B1Q,GAA9B,CAAQ0Q,OAAR;AAAA,QAAiBD,QAAjB,GAA8BzQ,GAA9B,CAAiByQ,QAAjB;;AAEA,QAAIC,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAM5P,IAAI,GAAGK,IAAI,CAACjB,SAAL,CAAewQ,OAAf,EAAwB7Q,EAAxB,EAA4B;AAAE4Q,MAAAA,QAAQ,EAARA;AAAF,KAA5B,CAAb;AACAzQ,IAAAA,GAAG,CAAC0Q,OAAJ,GAAc5P,IAAd;;AAEA,QAAIA,IAAI,IAAI,IAAZ,EAAkB;AAChBd,MAAAA,GAAG,CAAC2Q,KAAJ;AACD;AACF;AAlBsC;;;;;ICa5BnD,KAAK,GAAmB;AACnC;;;;AAKA2B,EAAAA,OANmC,mBAM3BhD,KAN2B,EAMbsG,OANa;AAOjC,QAAMgD,MAAM,GAAGtU,IAAI,CAACgO,OAAL,CAAahD,KAAK,CAACrL,IAAnB,EAAyB2R,OAAO,CAAC3R,IAAjC,CAAf;;AAEA,QAAI2U,MAAM,KAAK,CAAf,EAAkB;AAChB,UAAItJ,KAAK,CAAC0B,MAAN,GAAe4E,OAAO,CAAC5E,MAA3B,EAAmC,OAAO,CAAC,CAAR;AACnC,UAAI1B,KAAK,CAAC0B,MAAN,GAAe4E,OAAO,CAAC5E,MAA3B,EAAmC,OAAO,CAAP;AACnC,aAAO,CAAP;AACD;;AAED,WAAO4H,MAAP;AACD,GAhBkC;;AAkBnC;;;AAIApC,EAAAA,OAtBmC,mBAsB3BlH,KAtB2B,EAsBbsG,OAtBa;AAuBjC,WAAOjF,KAAK,CAAC2B,OAAN,CAAchD,KAAd,EAAqBsG,OAArB,MAAkC,CAAzC;AACD,GAxBkC;;AA0BnC;;;AAIAX,EAAAA,QA9BmC,oBA8B1B3F,KA9B0B,EA8BZsG,OA9BY;AA+BjC,WAAOjF,KAAK,CAAC2B,OAAN,CAAchD,KAAd,EAAqBsG,OAArB,MAAkC,CAAC,CAA1C;AACD,GAhCkC;;AAkCnC;;;AAIA3N,EAAAA,MAtCmC,kBAsC5BqH,KAtC4B,EAsCdsG,OAtCc;AAuCjC;AACA,WACEtG,KAAK,CAAC0B,MAAN,KAAiB4E,OAAO,CAAC5E,MAAzB,IAAmC1M,IAAI,CAAC2D,MAAL,CAAYqH,KAAK,CAACrL,IAAlB,EAAwB2R,OAAO,CAAC3R,IAAhC,CADrC;AAGD,GA3CkC;;AA6CnC;;;AAIAwP,EAAAA,OAjDmC,mBAiD3BtO,KAjD2B;AAkDjC,WACE8I,2BAAa,CAAC9I,KAAD,CAAb,IACA,OAAOA,KAAK,CAAC6L,MAAb,KAAwB,QADxB,IAEA1M,IAAI,CAACqN,MAAL,CAAYxM,KAAK,CAAClB,IAAlB,CAHF;AAKD,GAvDkC;;AAyDnC;;;AAIAZ,EAAAA,SA7DmC,qBA8DjCiM,KA9DiC,EA+DjCtM,EA/DiC;QAgEjCkM,8EAAwD;AAExD,WAAOgH,aAAO,CAAC5G,KAAD,EAAQ,UAAA3G,CAAC;AACrB,UAAIA,CAAC,KAAK,IAAV,EAAgB;AACd,eAAO,IAAP;AACD;;AACD,8BAAiCuG,OAAjC,CAAQ0E,QAAR;AAAA,UAAQA,QAAR,kCAAmB,SAAnB;AACA,UAAQ3P,IAAR,GAAyB0E,CAAzB,CAAQ1E,IAAR;AAAA,UAAc+M,MAAd,GAAyBrI,CAAzB,CAAcqI,MAAd;;AAEA,cAAQhO,EAAE,CAAC8B,IAAX;AACE,aAAK,aAAL;AACA,aAAK,WAAL;AAAkB;AAChB6D,YAAAA,CAAC,CAAC1E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBkM,OAAzB,CAAT;AACA;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAI5K,IAAI,CAAC2D,MAAL,CAAYjF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,KAA8BjB,EAAE,CAACgO,MAAH,IAAaA,MAA/C,EAAuD;AACrDrI,cAAAA,CAAC,CAACqI,MAAF,IAAYhO,EAAE,CAAC6D,IAAH,CAAQK,MAApB;AACD;;AAED;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAI5C,IAAI,CAAC2D,MAAL,CAAYjF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,CAAJ,EAAgC;AAC9B0E,cAAAA,CAAC,CAACqI,MAAF,IAAYhO,EAAE,CAAC8T,QAAf;AACD;;AAEDnO,YAAAA,CAAC,CAAC1E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBkM,OAAzB,CAAT;AACA;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAI5K,IAAI,CAAC2D,MAAL,CAAYjF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,KAA8BjB,EAAE,CAACgO,MAAH,IAAaA,MAA/C,EAAuD;AACrDrI,cAAAA,CAAC,CAACqI,MAAF,IAAY0G,IAAI,CAACD,GAAL,CAASzG,MAAM,GAAGhO,EAAE,CAACgO,MAArB,EAA6BhO,EAAE,CAAC6D,IAAH,CAAQK,MAArC,CAAZ;AACD;;AAED;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAI5C,IAAI,CAAC2D,MAAL,CAAYjF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,KAA8BK,IAAI,CAAC6J,UAAL,CAAgBnL,EAAE,CAACiB,IAAnB,EAAyBA,IAAzB,CAAlC,EAAkE;AAChE,qBAAO,IAAP;AACD;;AAED0E,YAAAA,CAAC,CAAC1E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBkM,OAAzB,CAAT;AACA;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAI5K,IAAI,CAAC2D,MAAL,CAAYjF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,CAAJ,EAAgC;AAC9B,kBAAIjB,EAAE,CAAC8T,QAAH,KAAgB9F,MAAhB,IAA0B4C,QAAQ,IAAI,IAA1C,EAAgD;AAC9C,uBAAO,IAAP;AACD,eAFD,MAEO,IACL5Q,EAAE,CAAC8T,QAAH,GAAc9F,MAAd,IACChO,EAAE,CAAC8T,QAAH,KAAgB9F,MAAhB,IAA0B4C,QAAQ,KAAK,SAFnC,EAGL;AACAjL,gBAAAA,CAAC,CAACqI,MAAF,IAAYhO,EAAE,CAAC8T,QAAf;AAEAnO,gBAAAA,CAAC,CAAC1E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,sCACJkM,OADI;AAEP0E,kBAAAA,QAAQ,EAAE;AAFH,mBAAT;AAID;AACF,aAdD,MAcO;AACLjL,cAAAA,CAAC,CAAC1E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBkM,OAAzB,CAAT;AACD;;AAED;AACD;AA7DH;AA+DD,KAtEa,CAAd;AAuED;AAzIkC;;ICbxB3L,QAAQ,GAAsB;AACzC;;;AAIAF,EAAAA,SALyC,qBAK/BF,GAL+B,EAKhBH,EALgB;AAMvC,QAAQ6Q,OAAR,GAA8B1Q,GAA9B,CAAQ0Q,OAAR;AAAA,QAAiBD,QAAjB,GAA8BzQ,GAA9B,CAAiByQ,QAAjB;;AAEA,QAAIC,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAMvE,KAAK,GAAGqB,KAAK,CAACtN,SAAN,CAAgBwQ,OAAhB,EAAyB7Q,EAAzB,EAA6B;AAAE4Q,MAAAA,QAAQ,EAARA;AAAF,KAA7B,CAAd;AACAzQ,IAAAA,GAAG,CAAC0Q,OAAJ,GAAcvE,KAAd;;AAEA,QAAIA,KAAK,IAAI,IAAb,EAAmB;AACjBnM,MAAAA,GAAG,CAAC2Q,KAAJ;AACD;AACF;AAlBwC;;;;;;;IC0B9B1O,KAAK,GAAmB;AACnC;;;;AAKA2K,EAAAA,KANmC,iBAOjCN,KAPiC;QAQjCP,8EAEI;AAEJ,2BAA4BA,OAA5B,CAAQpJ,OAAR;AAAA,QAAQA,OAAR,iCAAkB,KAAlB;AACA,QAAQuJ,MAAR,GAA0BI,KAA1B,CAAQJ,MAAR;AAAA,QAAgBG,KAAhB,GAA0BC,KAA1B,CAAgBD,KAAhB;AACA,WAAOpK,KAAK,CAACyT,UAAN,CAAiBpJ,KAAjB,MAA4B3J,OAA5B,GACH,CAACuJ,MAAD,EAASG,KAAT,CADG,GAEH,CAACA,KAAD,EAAQH,MAAR,CAFJ;AAGD,GAjBkC;;AAmBnC;;;AAIAvD,EAAAA,GAvBmC,eAuB/B2D,KAvB+B;AAwBjC,uBAAgBrK,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAAhB;AAAA;AAAA,QAAS3D,GAAT;;AACA,WAAOA,GAAP;AACD,GA1BkC;;AA4BnC;;;AAIA7D,EAAAA,MAhCmC,kBAgC5BwH,KAhC4B,EAgCdmG,OAhCc;AAiCjC,WACEjF,KAAK,CAAC1I,MAAN,CAAawH,KAAK,CAACJ,MAAnB,EAA2BuG,OAAO,CAACvG,MAAnC,KACAsB,KAAK,CAAC1I,MAAN,CAAawH,KAAK,CAACD,KAAnB,EAA0BoG,OAAO,CAACpG,KAAlC,CAFF;AAID,GArCkC;;AAuCnC;;;AAIAsC,EAAAA,QA3CmC,oBA2C1BrC,KA3C0B,EA2CZE,MA3CY;AA4CjC,QAAIvK,KAAK,CAACmL,OAAN,CAAcZ,MAAd,CAAJ,EAA2B;AACzB,UACEvK,KAAK,CAAC0M,QAAN,CAAerC,KAAf,EAAsBE,MAAM,CAACN,MAA7B,KACAjK,KAAK,CAAC0M,QAAN,CAAerC,KAAf,EAAsBE,MAAM,CAACH,KAA7B,CAFF,EAGE;AACA,eAAO,IAAP;AACD;;AAED,0BAAiBpK,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAAjB;AAAA;AAAA,UAAOqJ,EAAP;AAAA,UAAWC,EAAX;;AACA,0BAAiB3T,KAAK,CAAC2K,KAAN,CAAYJ,MAAZ,CAAjB;AAAA;AAAA,UAAOqJ,EAAP;AAAA,UAAWC,EAAX;;AACA,aAAOtI,KAAK,CAACsE,QAAN,CAAe6D,EAAf,EAAmBE,EAAnB,KAA0BrI,KAAK,CAAC6F,OAAN,CAAcuC,EAAd,EAAkBE,EAAlB,CAAjC;AACD;;AAED,wBAAqB7T,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAArB;AAAA;AAAA,QAAOK,KAAP;AAAA,QAAchE,GAAd;;AACA,QAAIoN,YAAY,GAAG,KAAnB;AACA,QAAIC,WAAW,GAAG,KAAlB;;AAEA,QAAIxI,KAAK,CAAC8C,OAAN,CAAc9D,MAAd,CAAJ,EAA2B;AACzBuJ,MAAAA,YAAY,GAAGvI,KAAK,CAAC2B,OAAN,CAAc3C,MAAd,EAAsBG,KAAtB,KAAgC,CAA/C;AACAqJ,MAAAA,WAAW,GAAGxI,KAAK,CAAC2B,OAAN,CAAc3C,MAAd,EAAsB7D,GAAtB,KAA8B,CAA5C;AACD,KAHD,MAGO;AACLoN,MAAAA,YAAY,GAAG5U,IAAI,CAACgO,OAAL,CAAa3C,MAAb,EAAqBG,KAAK,CAAC7L,IAA3B,KAAoC,CAAnD;AACAkV,MAAAA,WAAW,GAAG7U,IAAI,CAACgO,OAAL,CAAa3C,MAAb,EAAqB7D,GAAG,CAAC7H,IAAzB,KAAkC,CAAhD;AACD;;AAED,WAAOiV,YAAY,IAAIC,WAAvB;AACD,GAtEkC;;AAwEnC;;;AAIAC,EAAAA,YA5EmC,wBA4EtB3J,KA5EsB,EA4ERmG,OA5EQ;AA6EjC,IAAmCnG,KAAnC,CAAQJ,MAAR;AAAA,QAAmCI,KAAnC,CAAgBD,KAAhB;AAAA,YAA0B2B,IAA1B,4BAAmC1B,KAAnC;;AACA,wBAAiBrK,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAAjB;AAAA;AAAA,QAAO4J,EAAP;AAAA,QAAWC,EAAX;;AACA,yBAAiBlU,KAAK,CAAC2K,KAAN,CAAY6F,OAAZ,CAAjB;AAAA;AAAA,QAAO2D,EAAP;AAAA,QAAWC,EAAX;;AACA,QAAM1J,KAAK,GAAGa,KAAK,CAACsE,QAAN,CAAeoE,EAAf,EAAmBE,EAAnB,IAAyBA,EAAzB,GAA8BF,EAA5C;AACA,QAAMvN,GAAG,GAAG6E,KAAK,CAACsE,QAAN,CAAeqE,EAAf,EAAmBE,EAAnB,IAAyBF,EAAzB,GAA8BE,EAA1C;;AAEA,QAAI7I,KAAK,CAACsE,QAAN,CAAenJ,GAAf,EAAoBgE,KAApB,CAAJ,EAAgC;AAC9B,aAAO,IAAP;AACD,KAFD,MAEO;AACL;AAAST,QAAAA,MAAM,EAAES,KAAjB;AAAwBN,QAAAA,KAAK,EAAE1D;AAA/B,SAAuCqF,IAAvC;AACD;AACF,GAxFkC;;AA0FnC;;;;AAKA0H,EAAAA,UA/FmC,sBA+FxBpJ,KA/FwB;AAgGjC,QAAQJ,MAAR,GAA0BI,KAA1B,CAAQJ,MAAR;AAAA,QAAgBG,KAAhB,GAA0BC,KAA1B,CAAgBD,KAAhB;AACA,WAAOmB,KAAK,CAAC6F,OAAN,CAAcnH,MAAd,EAAsBG,KAAtB,CAAP;AACD,GAlGkC;;AAoGnC;;;;AAKA3J,EAAAA,WAzGmC,uBAyGvB4J,KAzGuB;AA0GjC,QAAQJ,MAAR,GAA0BI,KAA1B,CAAQJ,MAAR;AAAA,QAAgBG,KAAhB,GAA0BC,KAA1B,CAAgBD,KAAhB;AACA,WAAOmB,KAAK,CAAC1I,MAAN,CAAaoH,MAAb,EAAqBG,KAArB,CAAP;AACD,GA5GkC;;AA8GnC;;;;;AAMAnK,EAAAA,UApHmC,sBAoHxBoK,KApHwB;AAqHjC,WAAO,CAACrK,KAAK,CAACS,WAAN,CAAkB4J,KAAlB,CAAR;AACD,GAtHkC;;AAwHnC;;;;;AAMAgK,EAAAA,SA9HmC,qBA8HzBhK,KA9HyB;AA+HjC,WAAO,CAACrK,KAAK,CAACyT,UAAN,CAAiBpJ,KAAjB,CAAR;AACD,GAhIkC;;AAkInC;;;AAIAc,EAAAA,OAtImC,mBAsI3BpL,KAtI2B;AAuIjC,WACE8I,2BAAa,CAAC9I,KAAD,CAAb,IACAwL,KAAK,CAAC8C,OAAN,CAActO,KAAK,CAACkK,MAApB,CADA,IAEAsB,KAAK,CAAC8C,OAAN,CAActO,KAAK,CAACqK,KAApB,CAHF;AAKD,GA5IkC;;AA8InC;;;AAICkK,EAAAA,MAlJkC,mBAkJ3BjK,KAlJ2B;AAmJjC,UAAM,CAACA,KAAK,CAACJ,MAAP,EAAe,QAAf,CAAN;AACA,UAAM,CAACI,KAAK,CAACD,KAAP,EAAc,OAAd,CAAN;AACD,GArJkC;;AAuJnC;;;AAIAM,EAAAA,KA3JmC,iBA2J7BL,KA3J6B;AA4JjC,yBAAgBrK,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAAhB;AAAA;AAAA,QAAOK,KAAP;;AACA,WAAOA,KAAP;AACD,GA9JkC;;AAgKnC;;;AAIAzM,EAAAA,SApKmC,qBAqKjCoM,KArKiC,EAsKjCzM,EAtKiC;QAuKjCkM,8EAEI;AAEJ,WAAOgH,aAAO,CAACzG,KAAD,EAAQ,UAAA5B,CAAC;AACrB,UAAIA,CAAC,KAAK,IAAV,EAAgB;AACd,eAAO,IAAP;AACD;;AACD,8BAAgCqB,OAAhC,CAAQ0E,QAAR;AAAA,UAAQA,QAAR,kCAAmB,QAAnB;AACA,UAAI+F,cAAJ;AACA,UAAIC,aAAJ;;AAEA,UAAIhG,QAAQ,KAAK,QAAjB,EAA2B;AACzB;AACA;AACA;AACA,YAAM/N,WAAW,GAAGT,KAAK,CAACS,WAAN,CAAkBgI,CAAlB,CAApB;;AACA,YAAIzI,KAAK,CAACqU,SAAN,CAAgB5L,CAAhB,CAAJ,EAAwB;AACtB8L,UAAAA,cAAc,GAAG,SAAjB;AACAC,UAAAA,aAAa,GAAG/T,WAAW,GAAG8T,cAAH,GAAoB,UAA/C;AACD,SAHD,MAGO;AACLA,UAAAA,cAAc,GAAG,UAAjB;AACAC,UAAAA,aAAa,GAAG/T,WAAW,GAAG8T,cAAH,GAAoB,SAA/C;AACD;AACF,OAZD,MAYO,IAAI/F,QAAQ,KAAK,SAAjB,EAA4B;AACjC,YAAIxO,KAAK,CAACqU,SAAN,CAAgB5L,CAAhB,CAAJ,EAAwB;AACtB8L,UAAAA,cAAc,GAAG,UAAjB;AACAC,UAAAA,aAAa,GAAG,SAAhB;AACD,SAHD,MAGO;AACLD,UAAAA,cAAc,GAAG,SAAjB;AACAC,UAAAA,aAAa,GAAG,UAAhB;AACD;AACF,OARM,MAQA;AACLD,QAAAA,cAAc,GAAG/F,QAAjB;AACAgG,QAAAA,aAAa,GAAGhG,QAAhB;AACD;;AACD,UAAMvE,MAAM,GAAGsB,KAAK,CAACtN,SAAN,CAAgBwK,CAAC,CAACwB,MAAlB,EAA0BrM,EAA1B,EAA8B;AAAE4Q,QAAAA,QAAQ,EAAE+F;AAAZ,OAA9B,CAAf;AACA,UAAMnK,KAAK,GAAGmB,KAAK,CAACtN,SAAN,CAAgBwK,CAAC,CAAC2B,KAAlB,EAAyBxM,EAAzB,EAA6B;AAAE4Q,QAAAA,QAAQ,EAAEgG;AAAZ,OAA7B,CAAd;;AAEA,UAAI,CAACvK,MAAD,IAAW,CAACG,KAAhB,EAAuB;AACrB,eAAO,IAAP;AACD;;AAED3B,MAAAA,CAAC,CAACwB,MAAF,GAAWA,MAAX;AACAxB,MAAAA,CAAC,CAAC2B,KAAF,GAAUA,KAAV;AACD,KAzCa,CAAd;AA0CD;AArNkC;;IC1BxB/L,QAAQ,GAAsB;AACzC;;;AAIAJ,EAAAA,SALyC,qBAK/BF,GAL+B,EAKhBH,EALgB;AAMvC,QAAQ6Q,OAAR,GAA8B1Q,GAA9B,CAAQ0Q,OAAR;AAAA,QAAiBD,QAAjB,GAA8BzQ,GAA9B,CAAiByQ,QAAjB;;AAEA,QAAIC,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAM5P,IAAI,GAAGmB,KAAK,CAAC/B,SAAN,CAAgBwQ,OAAhB,EAAyB7Q,EAAzB,EAA6B;AAAE4Q,MAAAA,QAAQ,EAARA;AAAF,KAA7B,CAAb;AACAzQ,IAAAA,GAAG,CAAC0Q,OAAJ,GAAc5P,IAAd;;AAEA,QAAIA,IAAI,IAAI,IAAZ,EAAkB;AAChBd,MAAAA,GAAG,CAAC2Q,KAAJ;AACD;AACF;AAlBwC;;AChB3C;;;;;;;;;;;AAUO,IAAM+F,WAAW,GAAG,SAAdA,WAAc,CACzBnT,IADyB,EAEzBkP,OAFyB;AAIzB,OAAK,IAAM1R,GAAX,IAAkBwC,IAAlB,EAAwB;AACtB,QAAMoT,CAAC,GAAGpT,IAAI,CAACxC,GAAD,CAAd;AACA,QAAM6V,CAAC,GAAGnE,OAAO,CAAC1R,GAAD,CAAjB;;AACA,QAAI+J,2BAAa,CAAC6L,CAAD,CAAb,IAAoB7L,2BAAa,CAAC8L,CAAD,CAArC,EAA0C;AACxC,UAAI,CAACF,WAAW,CAACC,CAAD,EAAIC,CAAJ,CAAhB,EAAwB,OAAO,KAAP;AACzB,KAFD,MAEO,IAAIvR,KAAK,CAAC6F,OAAN,CAAcyL,CAAd,KAAoBtR,KAAK,CAAC6F,OAAN,CAAc0L,CAAd,CAAxB,EAA0C;AAC/C,UAAID,CAAC,CAAC5S,MAAF,KAAa6S,CAAC,CAAC7S,MAAnB,EAA2B,OAAO,KAAP;;AAC3B,WAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoS,CAAC,CAAC5S,MAAtB,EAA8BQ,CAAC,EAA/B,EAAmC;AACjC,YAAIoS,CAAC,CAACpS,CAAD,CAAD,KAASqS,CAAC,CAACrS,CAAD,CAAd,EAAmB,OAAO,KAAP;AACpB;AACF,KALM,MAKA,IAAIoS,CAAC,KAAKC,CAAV,EAAa;AAClB,aAAO,KAAP;AACD;AACF;AAED;;;;;;;AAMA,OAAK,IAAM7V,IAAX,IAAkB0R,OAAlB,EAA2B;AACzB,QAAIlP,IAAI,CAACxC,IAAD,CAAJ,KAAcwK,SAAd,IAA2BkH,OAAO,CAAC1R,IAAD,CAAP,KAAiBwK,SAAhD,EAA2D;AACzD,aAAO,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD,CAhCM;;;;;;;;;;;;;;ICcMlJ,IAAI,GAAkB;AACjC;;;;;;AAMAyC,EAAAA,MAPiC,kBAQ/BpB,IAR+B,EAS/B+O,OAT+B;QAU/B1G,8EAA+B;AAE/B,yBAA0BA,OAA1B,CAAQhH,KAAR;AAAA,QAAQA,KAAR,+BAAgB,KAAhB;;AAEA,aAAS8R,QAAT,CAAkBC,GAAlB;AACE,MAA0BA,GAA1B,CAAQpT,IAAR;AAAA,cAAiBsK,IAAjB,4BAA0B8I,GAA1B;;AAEA,aAAO9I,IAAP;AACD;;AAED,WAAO0I,WAAW,CAChB3R,KAAK,GAAG8R,QAAQ,CAACnT,IAAD,CAAX,GAAoBA,IADT,EAEhBqB,KAAK,GAAG8R,QAAQ,CAACpE,OAAD,CAAX,GAAuBA,OAFZ,CAAlB;AAID,GAxBgC;;AA0BjC;;;AAIAnQ,EAAAA,MA9BiC,kBA8B1BN,KA9B0B;AA+B/B,WAAO8I,2BAAa,CAAC9I,KAAD,CAAb,IAAwB,OAAOA,KAAK,CAAC0B,IAAb,KAAsB,QAArD;AACD,GAhCgC;;AAkCjC;;;AAIAqT,EAAAA,UAtCiC,sBAsCtB/U,KAtCsB;AAuC/B,WAAOqD,KAAK,CAAC6F,OAAN,CAAclJ,KAAd,KAAwBA,KAAK,CAACmJ,KAAN,CAAY,UAAAC,GAAG;AAAA,aAAI/I,IAAI,CAACC,MAAL,CAAY8I,GAAZ,CAAJ;AAAA,KAAf,CAA/B;AACD,GAxCgC;;AA0CjC;;;AAIA+H,EAAAA,WA9CiC,uBA8CrB7H,KA9CqB;AA+C/B,WAAQA,KAAuB,CAAC5H,IAAxB,KAAiC6H,SAAzC;AACD,GAhDgC;;AAkDjC;;;;;;AAOAI,EAAAA,OAzDiC,mBAyDzBjI,IAzDyB,EAyDb4H,KAzDa;AA0D/B,SAAK,IAAMvK,GAAX,IAAkBuK,KAAlB,EAAyB;AACvB,UAAIvK,GAAG,KAAK,MAAZ,EAAoB;AAClB;AACD;;AAED,UAAI,CAAC2C,IAAI,CAACsT,cAAL,CAAoBjW,GAApB,CAAD,IAA6B2C,IAAI,CAAC3C,GAAD,CAAJ,KAAcuK,KAAK,CAACvK,GAAD,CAApD,EAA2D;AACzD,eAAO,KAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD,GArEgC;;AAuEjC;;;AAIAkW,EAAAA,WA3EiC,uBA2ErB1T,IA3EqB,EA2ET0T,YA3ES;AA4E/B,QAAIC,MAAM,GAAW,qBAAM3T,IAAN,EAArB;;iDAEkB0T;;;;AAAlB,0DAA+B;AAAA,YAApBE,GAAoB;;AAC7B,YAAQjL,MAAR,GAAmCiL,GAAnC,CAAQjL,MAAR;AAAA,YAAgBG,KAAhB,GAAmC8K,GAAnC,CAAgB9K,KAAhB;AAAA,YAA0B2B,IAA1B,4BAAmCmJ,GAAnC;;AACA,2BAAqBlV,KAAK,CAAC2K,KAAN,CAAYuK,GAAZ,CAArB;AAAA;AAAA,YAAOxK,KAAP;AAAA,YAAchE,GAAd;;AACA,YAAMxC,IAAI,GAAG,EAAb;AACA,YAAIiR,CAAC,GAAG,CAAR;;AAJ6B,sDAMVF,MANU;AAAA;;AAAA;AAM7B,iEAA2B;AAAA,gBAAhBnJ,IAAgB;AACzB,gBAAQhK,MAAR,GAAmBgK,IAAI,CAACrK,IAAxB,CAAQK,MAAR;AACA,gBAAM8J,MAAM,GAAGuJ,CAAf;AACAA,YAAAA,CAAC,IAAIrT,MAAL,CAHyB;;AAMzB,gBAAI4I,KAAK,CAACkB,MAAN,IAAgBA,MAAhB,IAA0BlF,GAAG,CAACkF,MAAJ,IAAcuJ,CAA5C,EAA+C;AAC7CC,cAAAA,MAAM,CAACC,MAAP,CAAcvJ,IAAd,EAAoBC,IAApB;AACA7H,cAAAA,IAAI,CAACjF,IAAL,CAAU6M,IAAV;AACA;AACD,aAVwB;;;AAazB,gBACGpB,KAAK,CAACkB,MAAN,KAAiBlF,GAAG,CAACkF,MAArB,KACElB,KAAK,CAACkB,MAAN,KAAiBuJ,CAAjB,IAAsBzO,GAAG,CAACkF,MAAJ,KAAeA,MADvC,CAAD,IAEAlB,KAAK,CAACkB,MAAN,GAAeuJ,CAFf,IAGAzO,GAAG,CAACkF,MAAJ,GAAaA,MAHb,IAIClF,GAAG,CAACkF,MAAJ,KAAeA,MAAf,IAAyBA,MAAM,KAAK,CALvC,EAME;AACA1H,cAAAA,IAAI,CAACjF,IAAL,CAAU6M,IAAV;AACA;AACD,aAtBwB;AAyBzB;AACA;;;AACA,gBAAIwJ,MAAM,GAAGxJ,IAAb;AACA,gBAAIrB,MAAM,SAAV;AACA,gBAAIT,KAAK,SAAT;;AAEA,gBAAItD,GAAG,CAACkF,MAAJ,GAAauJ,CAAjB,EAAoB;AAClB,kBAAMI,GAAG,GAAG7O,GAAG,CAACkF,MAAJ,GAAaA,MAAzB;AACA5B,cAAAA,KAAK,uCAAQsL,MAAR;AAAgB7T,gBAAAA,IAAI,EAAE6T,MAAM,CAAC7T,IAAP,CAAY6E,KAAZ,CAAkBiP,GAAlB;AAAtB,gBAAL;AACAD,cAAAA,MAAM,uCAAQA,MAAR;AAAgB7T,gBAAAA,IAAI,EAAE6T,MAAM,CAAC7T,IAAP,CAAY6E,KAAZ,CAAkB,CAAlB,EAAqBiP,GAArB;AAAtB,gBAAN;AACD;;AAED,gBAAI7K,KAAK,CAACkB,MAAN,GAAeA,MAAnB,EAA2B;AACzB,kBAAM2J,IAAG,GAAG7K,KAAK,CAACkB,MAAN,GAAeA,MAA3B;;AACAnB,cAAAA,MAAM,uCAAQ6K,MAAR;AAAgB7T,gBAAAA,IAAI,EAAE6T,MAAM,CAAC7T,IAAP,CAAY6E,KAAZ,CAAkB,CAAlB,EAAqBiP,IAArB;AAAtB,gBAAN;AACAD,cAAAA,MAAM,uCAAQA,MAAR;AAAgB7T,gBAAAA,IAAI,EAAE6T,MAAM,CAAC7T,IAAP,CAAY6E,KAAZ,CAAkBiP,IAAlB;AAAtB,gBAAN;AACD;;AAEDH,YAAAA,MAAM,CAACC,MAAP,CAAcC,MAAd,EAAsBvJ,IAAtB;;AAEA,gBAAItB,MAAJ,EAAY;AACVvG,cAAAA,IAAI,CAACjF,IAAL,CAAUwL,MAAV;AACD;;AAEDvG,YAAAA,IAAI,CAACjF,IAAL,CAAUqW,MAAV;;AAEA,gBAAItL,KAAJ,EAAW;AACT9F,cAAAA,IAAI,CAACjF,IAAL,CAAU+K,KAAV;AACD;AACF;AA5D4B;AAAA;AAAA;AAAA;AAAA;;AA8D7BiL,QAAAA,MAAM,GAAG/Q,IAAT;AACD;;;;;;;AAED,WAAO+Q,MAAP;AACD;AAhJgC;;;;;;;;;;;;ACNnC,IAAMO,YAAY,GAAG,SAAfA,YAAe,CAACrY,MAAD,EAAiBG,SAAjB,EAAuCM,EAAvC;AACnB,UAAQA,EAAE,CAAC8B,IAAX;AACE,SAAK,aAAL;AAAoB;AAClB,YAAQb,IAAR,GAAuBjB,EAAvB,CAAQiB,IAAR;AAAA,YAAcyC,IAAd,GAAuB1D,EAAvB,CAAc0D,IAAd;AACA,YAAMmL,MAAM,GAAG1L,IAAI,CAAC0L,MAAL,CAAYtP,MAAZ,EAAoB0B,IAApB,CAAf;AACA,YAAMsR,KAAK,GAAGtR,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB;;AAEA,YAAIqO,KAAK,GAAG1D,MAAM,CAACrP,QAAP,CAAgB0E,MAA5B,EAAoC;AAClC,gBAAM,IAAI0K,KAAJ,8DACgD3N,IADhD,4DAAN;AAGD;;AAED4N,QAAAA,MAAM,CAACrP,QAAP,CAAgB2T,MAAhB,CAAuBZ,KAAvB,EAA8B,CAA9B,EAAiC7O,IAAjC;;AAEA,YAAIhE,SAAJ,EAAe;AAAA,uDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,gEAAoD;AAAA;AAAA,kBAAxC4M,KAAwC;AAAA,kBAAjCpL,GAAiC;;AAClDxB,cAAAA,SAAS,CAACwB,GAAD,CAAT,GAAiByM,KAAK,CAACtN,SAAN,CAAgBiM,KAAhB,EAAuBtM,EAAvB,CAAjB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAId;;AAED;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAQiB,KAAR,GAA+BjB,EAA/B,CAAQiB,IAAR;AAAA,YAAc+M,MAAd,GAA+BhO,EAA/B,CAAcgO,MAAd;AAAA,YAAsBnK,IAAtB,GAA+B7D,EAA/B,CAAsB6D,IAAtB;AACA,YAAIA,IAAI,CAACK,MAAL,KAAgB,CAApB,EAAuB;;AACvB,YAAMR,KAAI,GAAGP,IAAI,CAAC+K,IAAL,CAAU3O,MAAV,EAAkB0B,KAAlB,CAAb;;AACA,YAAM4L,MAAM,GAAGnJ,KAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgB,CAAhB,EAAmBsF,MAAnB,CAAf;;AACA,YAAM5B,KAAK,GAAG1I,KAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgBsF,MAAhB,CAAd;;AACAtK,QAAAA,KAAI,CAACG,IAAL,GAAYgJ,MAAM,GAAGhJ,IAAT,GAAgBuI,KAA5B;;AAEA,YAAI1M,SAAJ,EAAe;AAAA,wDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,mEAAoD;AAAA;AAAA,kBAAxC4M,MAAwC;AAAA,kBAAjCpL,IAAiC;;AAClDxB,cAAAA,SAAS,CAACwB,IAAD,CAAT,GAAiByM,KAAK,CAACtN,SAAN,CAAgBiM,MAAhB,EAAuBtM,EAAvB,CAAjB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAId;;AAED;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAQiB,MAAR,GAAiBjB,EAAjB,CAAQiB,IAAR;;AACA,YAAMyC,MAAI,GAAGP,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AACA,YAAMqN,QAAQ,GAAGhN,IAAI,CAACwE,QAAL,CAAc7E,MAAd,CAAjB;AACA,YAAM2D,IAAI,GAAGzB,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB+O,QAAjB,CAAb;;AACA,YAAMO,OAAM,GAAG1L,IAAI,CAAC0L,MAAL,CAAYtP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA,YAAMsR,MAAK,GAAGtR,MAAI,CAACA,MAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB;;AAEA,YAAI1B,IAAI,CAACC,MAAL,CAAYiB,MAAZ,KAAqBlB,IAAI,CAACC,MAAL,CAAYmC,IAAZ,CAAzB,EAA4C;AAC1CA,UAAAA,IAAI,CAACf,IAAL,IAAaH,MAAI,CAACG,IAAlB;AACD,SAFD,MAEO,IAAI,CAACrB,IAAI,CAACC,MAAL,CAAYiB,MAAZ,CAAD,IAAsB,CAAClB,IAAI,CAACC,MAAL,CAAYmC,IAAZ,CAA3B,EAA8C;AAAA;;AACnD,4BAAAA,IAAI,CAACpF,QAAL,EAAc6B,IAAd,0CAAsBqC,MAAI,CAAClE,QAA3B;AACD,SAFM,MAEA;AACL,gBAAM,IAAIoP,KAAJ,4DAC8C3N,MAD9C,iDACyFyC,MADzF,cACiGkB,IADjG,EAAN;AAGD;;AAEDiK,QAAAA,OAAM,CAACrP,QAAP,CAAgB2T,MAAhB,CAAuBZ,MAAvB,EAA8B,CAA9B;;AAEA,YAAI7S,SAAJ,EAAe;AAAA,wDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,mEAAoD;AAAA;AAAA,kBAAxC4M,OAAwC;AAAA,kBAAjCpL,KAAiC;;AAClDxB,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiByM,KAAK,CAACtN,SAAN,CAAgBiM,OAAhB,EAAuBtM,EAAvB,CAAjB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAId;;AAED;AACD;;AAED,SAAK,WAAL;AAAkB;AAChB,YAAQiB,MAAR,GAA0BjB,EAA1B,CAAQiB,IAAR;AAAA,YAAcO,OAAd,GAA0BxB,EAA1B,CAAcwB,OAAd;;AAEA,YAAIF,IAAI,CAAC6J,UAAL,CAAgBlK,MAAhB,EAAsBO,OAAtB,CAAJ,EAAoC;AAClC,gBAAM,IAAIoN,KAAJ,+BACmB3N,MADnB,4BACyCO,OADzC,iDAAN;AAGD;;AAED,YAAMkC,MAAI,GAAGP,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AACA,YAAM4N,QAAM,GAAG1L,IAAI,CAAC0L,MAAL,CAAYtP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA,YAAMsR,OAAK,GAAGtR,MAAI,CAACA,MAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB,CAXgB;AAchB;AACA;AACA;AACA;AACA;;AACA2K,QAAAA,QAAM,CAACrP,QAAP,CAAgB2T,MAAhB,CAAuBZ,OAAvB,EAA8B,CAA9B;;AACA,YAAMsF,QAAQ,GAAGvW,IAAI,CAACjB,SAAL,CAAeY,MAAf,EAAqBjB,EAArB,CAAjB;AACA,YAAMkG,SAAS,GAAG/C,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB+B,IAAI,CAACuN,MAAL,CAAYgJ,QAAZ,CAAjB,CAAlB;AACA,YAAM1R,QAAQ,GAAG0R,QAAQ,CAACA,QAAQ,CAAC3T,MAAT,GAAkB,CAAnB,CAAzB;AAEAgC,QAAAA,SAAS,CAAC1G,QAAV,CAAmB2T,MAAnB,CAA0BhN,QAA1B,EAAoC,CAApC,EAAuCzC,MAAvC;;AAEA,YAAIhE,SAAJ,EAAe;AAAA,wDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,mEAAoD;AAAA;AAAA,kBAAxC4M,OAAwC;AAAA,kBAAjCpL,KAAiC;;AAClDxB,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiByM,KAAK,CAACtN,SAAN,CAAgBiM,OAAhB,EAAuBtM,EAAvB,CAAjB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAId;;AAED;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAQiB,MAAR,GAAiBjB,EAAjB,CAAQiB,IAAR;AACA,YAAMsR,OAAK,GAAGtR,MAAI,CAACA,MAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB;;AACA,YAAM2K,QAAM,GAAG1L,IAAI,CAAC0L,MAAL,CAAYtP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA4N,QAAAA,QAAM,CAACrP,QAAP,CAAgB2T,MAAhB,CAAuBZ,OAAvB,EAA8B,CAA9B,EAJkB;AAOlB;;;AACA,YAAI7S,SAAJ,EAAe;AAAA,wDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,mEAAoD;AAAA;AAAA,kBAAxC4M,OAAwC;AAAA,kBAAjCpL,KAAiC;;AAClD,kBAAM0U,MAAM,GAAGjI,KAAK,CAACtN,SAAN,CAAgBiM,OAAhB,EAAuBtM,EAAvB,CAAf;;AAEA,kBAAIN,SAAS,IAAI,IAAb,IAAqBkW,MAAM,IAAI,IAAnC,EAAyC;AACvClW,gBAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiB0U,MAAjB;AACD,eAFD,MAEO;AACL,oBAAIhR,KAAiC,SAArC;;AACA,oBAAI0B,IAAiC,SAArC;;AAFK,8DAIgBnD,IAAI,CAACuQ,KAAL,CAAWnU,MAAX,CAJhB;AAAA;;AAAA;AAIL,yEAAyC;AAAA;AAAA,wBAA7BkF,CAA6B;AAAA,wBAA1BkB,CAA0B;;AACvC,wBAAIrE,IAAI,CAACgO,OAAL,CAAa3J,CAAb,EAAgB1E,MAAhB,MAA0B,CAAC,CAA/B,EAAkC;AAChC2D,sBAAAA,KAAI,GAAG,CAACH,CAAD,EAAIkB,CAAJ,CAAP;AACD,qBAFD,MAEO;AACLW,sBAAAA,IAAI,GAAG,CAAC7B,CAAD,EAAIkB,CAAJ,CAAP;AACA;AACD;AACF;AAXI;AAAA;AAAA;AAAA;AAAA;;AAaL,oBAAImS,UAAU,GAAG,KAAjB;;AACA,oBAAIlT,KAAI,IAAI0B,IAAZ,EAAkB;AAChB,sBAAIhF,IAAI,CAAC2D,MAAL,CAAYqB,IAAI,CAAC,CAAD,CAAhB,EAAqBrF,MAArB,CAAJ,EAAgC;AAC9B6W,oBAAAA,UAAU,GAAG,CAACxW,IAAI,CAAC0T,WAAL,CAAiB1O,IAAI,CAAC,CAAD,CAArB,CAAd;AACD,mBAFD,MAEO;AACLwR,oBAAAA,UAAU,GACRxW,IAAI,CAACkP,MAAL,CAAY5L,KAAI,CAAC,CAAD,CAAhB,EAAqB3D,MAArB,EAA2BiD,MAA3B,GACA5C,IAAI,CAACkP,MAAL,CAAYlK,IAAI,CAAC,CAAD,CAAhB,EAAqBrF,MAArB,EAA2BiD,MAF7B;AAGD;AACF;;AAED,oBAAIU,KAAI,IAAI,CAACkT,UAAb,EAAyB;AACvBxL,kBAAAA,OAAK,CAACrL,IAAN,GAAa2D,KAAI,CAAC,CAAD,CAAjB;AACA0H,kBAAAA,OAAK,CAAC0B,MAAN,GAAepJ,KAAI,CAAC,CAAD,CAAJ,CAAQf,IAAR,CAAaK,MAA5B;AACD,iBAHD,MAGO,IAAIoC,IAAJ,EAAU;AACfgG,kBAAAA,OAAK,CAACrL,IAAN,GAAaqF,IAAI,CAAC,CAAD,CAAjB;AACAgG,kBAAAA,OAAK,CAAC0B,MAAN,GAAe,CAAf;AACD,iBAHM,MAGA;AACLtO,kBAAAA,SAAS,GAAG,IAAZ;AACD;AACF;AACF;AAxCY;AAAA;AAAA;AAAA;AAAA;AAyCd;;AAED;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAQuB,MAAR,GAA+BjB,EAA/B,CAAQiB,IAAR;AAAA,YAAc+M,OAAd,GAA+BhO,EAA/B,CAAcgO,MAAd;AAAA,YAAsBnK,KAAtB,GAA+B7D,EAA/B,CAAsB6D,IAAtB;AACA,YAAIA,KAAI,CAACK,MAAL,KAAgB,CAApB,EAAuB;;AACvB,YAAMR,MAAI,GAAGP,IAAI,CAAC+K,IAAL,CAAU3O,MAAV,EAAkB0B,MAAlB,CAAb;;AACA,YAAM4L,OAAM,GAAGnJ,MAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgB,CAAhB,EAAmBsF,OAAnB,CAAf;;AACA,YAAM5B,MAAK,GAAG1I,MAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgBsF,OAAM,GAAGnK,KAAI,CAACK,MAA9B,CAAd;;AACAR,QAAAA,MAAI,CAACG,IAAL,GAAYgJ,OAAM,GAAGT,MAArB;;AAEA,YAAI1M,SAAJ,EAAe;AAAA,wDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,mEAAoD;AAAA;AAAA,kBAAxC4M,OAAwC;AAAA,kBAAjCpL,KAAiC;;AAClDxB,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiByM,KAAK,CAACtN,SAAN,CAAgBiM,OAAhB,EAAuBtM,EAAvB,CAAjB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAId;;AAED;AACD;;AAED,SAAK,UAAL;AAAiB;AACf,YAAQiB,MAAR,GAA4CjB,EAA5C,CAAQiB,IAAR;AAAA,YAAc+R,UAAd,GAA4ChT,EAA5C,CAAcgT,UAAd;AAAA,YAA0Be,aAA1B,GAA4C/T,EAA5C,CAA0B+T,aAA1B;;AAEA,YAAI9S,MAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB,gBAAM,IAAI0K,KAAJ,2CAAN;AACD;;AAED,YAAMlL,MAAI,GAAGP,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AAEA,aAAK,IAAMC,KAAX,IAAkB6S,aAAlB,EAAiC;AAC/B,cAAI7S,KAAG,KAAK,UAAR,IAAsBA,KAAG,KAAK,MAAlC,EAA0C;AACxC,kBAAM,IAAI0N,KAAJ,4BAA6B1N,KAA7B,2BAAN;AACD;;AAED,cAAMiB,KAAK,GAAG4R,aAAa,CAAC7S,KAAD,CAA3B;;AAEA,cAAIiB,KAAK,IAAI,IAAb,EAAmB;AACjB,mBAAOuB,MAAI,CAACxC,KAAD,CAAX;AACD,WAFD,MAEO;AACLwC,YAAAA,MAAI,CAACxC,KAAD,CAAJ,GAAYiB,KAAZ;AACD;AACF,SArBc;;;AAwBf,aAAK,IAAMjB,KAAX,IAAkB8R,UAAlB,EAA8B;AAC5B,cAAI,CAACe,aAAa,CAACoD,cAAd,CAA6BjW,KAA7B,CAAL,EAAwC;AACtC,mBAAOwC,MAAI,CAACxC,KAAD,CAAX;AACD;AACF;;AAED;AACD;;AAED,SAAK,eAAL;AAAsB;AACpB,YAAQ6S,cAAR,GAA0B/T,EAA1B,CAAQ+T,aAAR;;AAEA,YAAIA,cAAa,IAAI,IAArB,EAA2B;AACzBrU,UAAAA,SAAS,GAAGqU,cAAZ;AACD,SAFD,MAEO;AACL,cAAIrU,SAAS,IAAI,IAAjB,EAAuB;AACrB,gBAAI,CAAC0C,KAAK,CAACmL,OAAN,CAAcwG,cAAd,CAAL,EAAmC;AACjC,oBAAM,IAAInF,KAAJ,6EAC+D4D,IAAI,CAACC,SAAL,CACjEsB,cADiE,CAD/D,0CAAN;AAKD;;AAEDrU,YAAAA,SAAS,uBAAQqU,cAAR,CAAT;AACD;;AAED,eAAK,IAAM7S,KAAX,IAAkB6S,cAAlB,EAAiC;AAC/B,gBAAM5R,MAAK,GAAG4R,cAAa,CAAC7S,KAAD,CAA3B;;AAEA,gBAAIiB,MAAK,IAAI,IAAb,EAAmB;AACjB,kBAAIjB,KAAG,KAAK,QAAR,IAAoBA,KAAG,KAAK,OAAhC,EAAyC;AACvC,sBAAM,IAAI0N,KAAJ,+BAAgC1N,KAAhC,2BAAN;AACD;;AAED,qBAAOxB,SAAS,CAACwB,KAAD,CAAhB;AACD,aAND,MAMO;AACLxB,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiBiB,MAAjB;AACD;AACF;AACF;;AAED;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAQlB,MAAR,GAAuCjB,EAAvC,CAAQiB,IAAR;AAAA,YAAc6S,QAAd,GAAuC9T,EAAvC,CAAc8T,QAAd;AAAA,YAAwBd,WAAxB,GAAuChT,EAAvC,CAAwBgT,UAAxB;;AAEA,YAAI/R,MAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB,gBAAM,IAAI0K,KAAJ,4DAC8C3N,MAD9C,8CAAN;AAGD;;AAED,YAAMyC,MAAI,GAAGP,IAAI,CAACxC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AACA,YAAM4N,QAAM,GAAG1L,IAAI,CAAC0L,MAAL,CAAYtP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA,YAAMsR,OAAK,GAAGtR,MAAI,CAACA,MAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB;AACA,YAAI6T,OAAJ;;AAEA,YAAIvV,IAAI,CAACC,MAAL,CAAYiB,MAAZ,CAAJ,EAAuB;AACrB,cAAMmJ,QAAM,GAAGnJ,MAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgB,CAAhB,EAAmBoL,QAAnB,CAAf;;AACA,cAAM1H,OAAK,GAAG1I,MAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgBoL,QAAhB,CAAd;;AACApQ,UAAAA,MAAI,CAACG,IAAL,GAAYgJ,QAAZ;AACAkL,UAAAA,OAAO,uCACD/E,WADC;AAELnP,YAAAA,IAAI,EAAEuI;AAFD,YAAP;AAID,SARD,MAQO;AACL,cAAMS,QAAM,GAAGnJ,MAAI,CAAClE,QAAL,CAAckJ,KAAd,CAAoB,CAApB,EAAuBoL,QAAvB,CAAf;;AACA,cAAM1H,OAAK,GAAG1I,MAAI,CAAClE,QAAL,CAAckJ,KAAd,CAAoBoL,QAApB,CAAd;;AACApQ,UAAAA,MAAI,CAAClE,QAAL,GAAgBqN,QAAhB;AAEAkL,UAAAA,OAAO,uCACD/E,WADC;AAELxT,YAAAA,QAAQ,EAAE4M;AAFL,YAAP;AAID;;AAEDyC,QAAAA,QAAM,CAACrP,QAAP,CAAgB2T,MAAhB,CAAuBZ,OAAK,GAAG,CAA/B,EAAkC,CAAlC,EAAqCwF,OAArC;;AAEA,YAAIrY,SAAJ,EAAe;AAAA,wDACc0C,KAAK,CAACsU,MAAN,CAAahX,SAAb,CADd;AAAA;;AAAA;AACb,mEAAoD;AAAA;AAAA,kBAAxC4M,OAAwC;AAAA,kBAAjCpL,KAAiC;;AAClDxB,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiByM,KAAK,CAACtN,SAAN,CAAgBiM,OAAhB,EAAuBtM,EAAvB,CAAjB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAId;;AAED;AACD;AA9RH;;AAgSA,SAAON,SAAP;AACD,CAlSD;;AAoSO,IAAMsY,iBAAiB,GAAsB;AAClD;;;AAIA3X,EAAAA,SALkD,qBAKxCd,MALwC,EAKxBS,EALwB;AAMhDT,IAAAA,MAAM,CAACC,QAAP,GAAkByY,iBAAW,CAAC1Y,MAAM,CAACC,QAAR,CAA7B;AACA,QAAIE,SAAS,GAAGH,MAAM,CAACG,SAAP,IAAoBuY,iBAAW,CAAC1Y,MAAM,CAACG,SAAR,CAA/C;;AAEA,QAAI;AACFA,MAAAA,SAAS,GAAGkY,YAAY,CAACrY,MAAD,EAASG,SAAT,EAAoBM,EAApB,CAAxB;AACD,KAFD,SAEU;AACRT,MAAAA,MAAM,CAACC,QAAP,GAAkB0Y,iBAAW,CAAC3Y,MAAM,CAACC,QAAR,CAA7B;;AAEA,UAAIE,SAAJ,EAAe;AACbH,QAAAA,MAAM,CAACG,SAAP,GAAmByY,aAAO,CAACzY,SAAD,CAAP,GACdwY,iBAAW,CAACxY,SAAD,CADG,GAEfA,SAFJ;AAGD,OAJD,MAIO;AACLH,QAAAA,MAAM,CAACG,SAAP,GAAmB,IAAnB;AACD;AACF;AACF;AAtBiD,CAA7C;;;;;;;;;;;;;;AC5LA,IAAM0Y,cAAc,GAAmB;AAC5C;;;AAIAzU,EAAAA,WAL4C,uBAM1CpE,MAN0C,EAO1CmG,KAP0C;QAQ1CwG,8EAOI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,6BAA4D2M,OAA5D,CAAQmM,OAAR;AAAA,UAAQA,OAAR,iCAAkB,KAAlB;AAAA,2BAA4DnM,OAA5D,CAAyB5H,KAAzB;AAAA,UAAyBA,KAAzB,+BAAiC,KAAjC;AAAA,0BAA4D4H,OAA5D,CAAwCC,IAAxC;AAAA,UAAwCA,IAAxC,8BAA+C,QAA/C;AACA,UAAM/H,EAAN,GAA4B8H,OAA5B,CAAM9H,EAAN;AAAA,UAAU7B,KAAV,GAA4B2J,OAA5B,CAAU3J,KAAV;AAAA,UAAiB+V,MAAjB,GAA4BpM,OAA5B,CAAiBoM,MAAjB;;AAEA,UAAInV,IAAI,CAACiQ,MAAL,CAAY1N,KAAZ,CAAJ,EAAwB;AACtBA,QAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;AACD;;AAED,UAAIA,KAAK,CAACxB,MAAN,KAAiB,CAArB,EAAwB;AACtB;AACD;;AAED,mBAAewB,KAAf;AAAA;AAAA,UAAOhC,IAAP;AAGA;AACA;;;AACA,UAAI,CAACU,EAAL,EAAS;AACP,YAAI7E,MAAM,CAACG,SAAX,EAAsB;AACpB0E,UAAAA,EAAE,GAAG7E,MAAM,CAACG,SAAZ;AACD,SAFD,MAEO,IAAIH,MAAM,CAACC,QAAP,CAAgB0E,MAAhB,GAAyB,CAA7B,EAAgC;AACrCE,UAAAA,EAAE,GAAGnE,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB,EAAnB,CAAL;AACD,SAFM,MAEA;AACL6E,UAAAA,EAAE,GAAG,CAAC,CAAD,CAAL;AACD;;AAEDkU,QAAAA,MAAM,GAAG,IAAT;AACD;;AAED,UAAIA,MAAM,IAAI,IAAd,EAAoB;AAClBA,QAAAA,MAAM,GAAG,KAAT;AACD;;AAED,UAAIlW,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AACrB,YAAI,CAACiU,OAAL,EAAc;AACZjU,UAAAA,EAAE,GAAGnE,MAAM,CAAC6R,WAAP,CAAmBvS,MAAnB,EAA2B6E,EAA3B,CAAL;AACD;;AAED,YAAIhC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACiI,MAAR;AACD,SAFD,MAEO;AACL,6BAAgBjK,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAAhB;AAAA;AAAA,cAAS0E,GAAT;;AACA,cAAMkI,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuJ,GAAxB,CAAjB;AACAlH,UAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAE6E,YAAAA,EAAE,EAAFA;AAAF,WAA1B;AACAA,UAAAA,EAAE,GAAG4M,QAAQ,CAACF,KAAT,EAAL;AACD;AACF;;AAED,UAAInD,KAAK,CAAC8C,OAAN,CAAcrM,EAAd,CAAJ,EAAuB;AACrB,YAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjB,cAAIC,IAAI,CAACC,MAAL,CAAYiB,IAAZ,CAAJ,EAAuB;AACrBnB,YAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,qBAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,CAAJ;AAAA,aAAT;AACD,WAFD,MAEO,IAAIlF,MAAM,CAACK,QAAP,CAAgB8D,IAAhB,CAAJ,EAA2B;AAChCnB,YAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,qBAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAAtB;AAAA,aAAT;AACD,WAFM,MAEA;AACLlC,YAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,qBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,aAAT;AACD;AACF;;AAED,4BAAgBxE,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AACnC6E,UAAAA,EAAE,EAAEA,EAAE,CAACnD,IAD4B;AAEnCsB,UAAAA,KAAK,EAALA,KAFmC;AAGnC4J,UAAAA,IAAI,EAAJA,IAHmC;AAInC7H,UAAAA,KAAK,EAALA;AAJmC,SAArB,CAAhB;AAAA;AAAA,YAAOP,KAAP;;AAOA,YAAIA,KAAJ,EAAW;AACT,sCAAsBA,KAAtB;AAAA,cAASwU,UAAT;;AACA,cAAM5H,OAAO,GAAG1Q,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBgZ,UAAvB,CAAhB;AACA,cAAMC,OAAO,GAAGvY,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqB6E,EAArB,EAAyBmU,UAAzB,CAAhB;AACA3W,UAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAAE6E,YAAAA,EAAE,EAAFA,EAAF;AAAM7B,YAAAA,KAAK,EAALA,KAAN;AAAa4J,YAAAA,IAAI,EAAJA,IAAb;AAAmB7H,YAAAA,KAAK,EAALA;AAAnB,WAA9B;AACA,cAAMrD,IAAI,GAAG0P,OAAO,CAACG,KAAR,EAAb;AACA1M,UAAAA,EAAE,GAAGoU,OAAO,GAAGlX,IAAI,CAACgF,IAAL,CAAUrF,IAAV,CAAH,GAAqBA,IAAjC;AACD,SAPD,MAOO;AACL;AACD;AACF;;AAED,UAAMmP,UAAU,GAAG9O,IAAI,CAACuN,MAAL,CAAYzK,EAAZ,CAAnB;AACA,UAAImO,KAAK,GAAGnO,EAAE,CAACA,EAAE,CAACF,MAAH,GAAY,CAAb,CAAd;;AAEA,UAAI,CAACI,KAAD,IAAUrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,QAAAA,EAAE,EAAEgM;AAAN,OAApB,CAAd,EAAuD;AACrD;AACD;;mDAEkB1K;;;;AAAnB,4DAA0B;AAAA,cAAfhC,KAAe;;AACxB,cAAMzC,KAAI,GAAGmP,UAAU,CAAC/L,MAAX,CAAkBkO,KAAlB,CAAb;;AACAA,UAAAA,KAAK;AACLhT,UAAAA,MAAM,CAACQ,KAAP,CAAa;AAAE+B,YAAAA,IAAI,EAAE,aAAR;AAAuBb,YAAAA,IAAI,EAAJA,KAAvB;AAA6ByC,YAAAA,IAAI,EAAJA;AAA7B,WAAb;AACAU,UAAAA,EAAE,GAAG9C,IAAI,CAACgF,IAAL,CAAUlC,EAAV,CAAL;AACD;;;;;;;AACDA,MAAAA,EAAE,GAAG9C,IAAI,CAACwE,QAAL,CAAc1B,EAAd,CAAL;;AAEA,UAAIkU,MAAJ,EAAY;AACV,YAAMhM,KAAK,GAAGrM,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB6E,EAAnB,CAAd;;AAEA,YAAIkI,KAAJ,EAAW;AACT1K,UAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0B+M,KAA1B;AACD;AACF;AACF,KApGD;AAqGD,GAtH2C;;AAwH5C;;;;AAKAmM,EAAAA,SA7H4C,qBA8H1ClZ,MA9H0C;QA+H1C2M,8EAKI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,wBAAkE2M,OAAlE,CAAQ9H,EAAR;AAAA,UAAQA,EAAR,4BAAa7E,MAAM,CAACG,SAApB;AAAA,2BAAkEwM,OAAlE,CAA+BC,IAA/B;AAAA,UAA+BA,IAA/B,+BAAsC,QAAtC;AAAA,4BAAkED,OAAlE,CAAgD5H,KAAhD;AAAA,UAAgDA,KAAhD,gCAAwD,KAAxD;AACA,UAAM/B,KAAN,GAAgB2J,OAAhB,CAAM3J,KAAN;;AAEA,UAAIA,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,IACJmU,SAAS,CAAChZ,MAAD,EAAS6E,EAAT,CADL,GAEJ,UAAAK,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAFL;AAGD;;AAED,UAAI,CAACL,EAAL,EAAS;AACP;AACD;;AAED,UAAM0H,OAAO,GAAG7L,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa4J,QAAAA,IAAI,EAAJA,IAAb;AAAmB7H,QAAAA,KAAK,EAALA;AAAnB,OAArB,CAAhB;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAWqG,OAAX,EAAoB;AAAA;AAAA,YAAInG,CAAJ;;AAAA,eAAW1F,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBoG,CAAvB,CAAX;AAAA,OAApB,CAAjB;;AAEA,mCAAsBzF,QAAtB,+BAAgC;AAA3B,YAAMyQ,OAAO,gBAAb;AACH,YAAM1P,IAAI,GAAG0P,OAAO,CAACG,KAAR,EAAb;;AAEA,YAAI7P,IAAI,CAACiD,MAAL,GAAc,CAAlB,EAAqB;AACnB,gBAAM,IAAI0K,KAAJ,uCAC2B3N,IAD3B,gDAAN;AAGD;;AAED,YAAMyX,eAAe,GAAGzY,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB+B,IAAI,CAACuN,MAAL,CAAY5N,IAAZ,CAApB,CAAxB;;AACA,8CAA6ByX,eAA7B;AAAA,YAAO7J,MAAP;AAAA,YAAeuB,UAAf;;AACA,YAAMmC,KAAK,GAAGtR,IAAI,CAACA,IAAI,CAACiD,MAAL,GAAc,CAAf,CAAlB;AACA,YAAQA,MAAR,GAAmB2K,MAAM,CAACrP,QAA1B,CAAQ0E,MAAR;;AAEA,YAAIA,MAAM,KAAK,CAAf,EAAkB;AAChB,cAAMyU,MAAM,GAAGrX,IAAI,CAACgF,IAAL,CAAU8J,UAAV,CAAf;AACAxO,UAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAAE6E,YAAAA,EAAE,EAAEnD,IAAN;AAAYwN,YAAAA,EAAE,EAAEkK,MAAhB;AAAwBrU,YAAAA,KAAK,EAALA;AAAxB,WAA7B;AACA1C,UAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAAE6E,YAAAA,EAAE,EAAEgM,UAAN;AAAkB9L,YAAAA,KAAK,EAALA;AAAlB,WAA/B;AACD,SAJD,MAIO,IAAIiO,KAAK,KAAK,CAAd,EAAiB;AACtB3Q,UAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAAE6E,YAAAA,EAAE,EAAEnD,IAAN;AAAYwN,YAAAA,EAAE,EAAE2B,UAAhB;AAA4B9L,YAAAA,KAAK,EAALA;AAA5B,WAA7B;AACD,SAFM,MAEA,IAAIiO,KAAK,KAAKrO,MAAM,GAAG,CAAvB,EAA0B;AAC/B,cAAMyU,OAAM,GAAGrX,IAAI,CAACgF,IAAL,CAAU8J,UAAV,CAAf;;AACAxO,UAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAAE6E,YAAAA,EAAE,EAAEnD,IAAN;AAAYwN,YAAAA,EAAE,EAAEkK,OAAhB;AAAwBrU,YAAAA,KAAK,EAALA;AAAxB,WAA7B;AACD,SAHM,MAGA;AACL,cAAMuU,SAAS,GAAGvX,IAAI,CAACgF,IAAL,CAAUrF,IAAV,CAAlB;;AACA,cAAM0X,QAAM,GAAGrX,IAAI,CAACgF,IAAL,CAAU8J,UAAV,CAAf;;AACAxO,UAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAAE6E,YAAAA,EAAE,EAAEyU,SAAN;AAAiBvU,YAAAA,KAAK,EAALA;AAAjB,WAA9B;AACA1C,UAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAAE6E,YAAAA,EAAE,EAAEnD,IAAN;AAAYwN,YAAAA,EAAE,EAAEkK,QAAhB;AAAwBrU,YAAAA,KAAK,EAALA;AAAxB,WAA7B;AACD;AACF;AACF,KA/CD;AAgDD,GAtL2C;;AAwL5C;;;;AAKAa,EAAAA,UA7L4C,sBA8L1C5F,MA9L0C;QA+L1C2M,8EAMI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,UAAMgD,KAAN,GAAuC2J,OAAvC,CAAM3J,KAAN;AAAA,yBAAuC2J,OAAvC,CAAa9H,EAAb;AAAA,UAAaA,EAAb,6BAAkB7E,MAAM,CAACG,SAAzB;AACA,8BAA4DwM,OAA5D,CAAQmM,OAAR;AAAA,UAAQA,OAAR,kCAAkB,KAAlB;AAAA,4BAA4DnM,OAA5D,CAAyB5H,KAAzB;AAAA,UAAyBA,KAAzB,gCAAiC,KAAjC;AAAA,2BAA4D4H,OAA5D,CAAwCC,IAAxC;AAAA,UAAwCA,IAAxC,+BAA+C,QAA/C;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAIjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB,+BAAiBnE,MAAM,CAAC4O,MAAP,CAActP,MAAd,EAAsB6E,EAAtB,CAAjB;AAAA;AAAA,cAAOyK,MAAP;;AACAtM,UAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,mBAAIoK,MAAM,CAACrP,QAAP,CAAgBsP,QAAhB,CAAyBrK,CAAzB,CAAJ;AAAA,WAAT;AACD,SAHD,MAGO;AACLlC,UAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,mBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,WAAT;AACD;AACF;;AAED,UAAI,CAAC4T,OAAD,IAAYjW,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAhB,EAAmC;AACjCA,QAAAA,EAAE,GAAGnE,MAAM,CAAC6R,WAAP,CAAmBvS,MAAnB,EAA2B6E,EAA3B,CAAL;AACD;;AAED,UAAIhC,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AACrB,YAAIhC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACiI,MAAR;AACD,SAFD,MAEO;AACL,8BAAgBjK,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAAhB;AAAA;AAAA,cAAS0E,GAAT;;AACA,cAAMkI,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuJ,GAAxB,CAAjB;AACAlH,UAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAE6E,YAAAA,EAAE,EAAFA;AAAF,WAA1B;AACAA,UAAAA,EAAE,GAAG4M,QAAQ,CAACF,KAAT,EAAL;;AAEA,cAAI5E,OAAO,CAAC9H,EAAR,IAAc,IAAlB,EAAwB;AACtBxC,YAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0B6E,EAA1B;AACD;AACF;AACF;;AAED,2BAAkBnE,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa+B,QAAAA,KAAK,EAALA,KAAb;AAAoB6H,QAAAA,IAAI,EAAJA;AAApB,OAArB,CAAlB;AAAA;AAAA,UAAO0E,OAAP;;AACA,UAAMjM,IAAI,GAAG3E,MAAM,CAAC6F,QAAP,CAAgBvG,MAAhB,EAAwB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa+B,QAAAA,KAAK,EAALA,KAAb;AAAoB6H,QAAAA,IAAI,EAAJA;AAApB,OAAxB,CAAb;;AAEA,UAAI,CAAC0E,OAAD,IAAY,CAACjM,IAAjB,EAAuB;AACrB;AACD;;AAED,oCAAqBiM,OAArB;AAAA,UAAOnN,IAAP;AAAA,UAAazC,IAAb;;AACA,iCAA6B2D,IAA7B;AAAA,UAAOyJ,QAAP;AAAA,UAAiBC,QAAjB;;AAEA,UAAIrN,IAAI,CAACiD,MAAL,KAAgB,CAAhB,IAAqBoK,QAAQ,CAACpK,MAAT,KAAoB,CAA7C,EAAgD;AAC9C;AACD;;AAED,UAAM1C,OAAO,GAAGF,IAAI,CAACgF,IAAL,CAAUgI,QAAV,CAAhB;AACA,UAAMwK,UAAU,GAAGxX,IAAI,CAACkP,MAAL,CAAYvP,IAAZ,EAAkBqN,QAAlB,CAAnB;AACA,UAAMyK,iBAAiB,GAAGzX,IAAI,CAAC6S,SAAL,CAAelT,IAAf,EAAqBqN,QAArB,CAA1B;AACA,UAAMhJ,MAAM,GAAGE,KAAK,CAACC,IAAN,CAAWxF,MAAM,CAACqF,MAAP,CAAc/F,MAAd,EAAsB;AAAE6E,QAAAA,EAAE,EAAEnD;AAAN,OAAtB,CAAX,EAAgD;AAAA;AAAA,YAAEwD,CAAF;;AAAA,eAASA,CAAT;AAAA,OAAhD,EACZiE,KADY,CACNoQ,UAAU,CAAC5U,MADL,EAEZwE,KAFY,CAEN,CAFM,EAEH,CAAC,CAFE,CAAf;AAKA;;AACA,UAAMsQ,aAAa,GAAG/Y,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AACzC6E,QAAAA,EAAE,EAAEnD,IADqC;AAEzCkL,QAAAA,IAAI,EAAE,SAFmC;AAGzC5J,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIa,MAAM,CAACwJ,QAAP,CAAgBrK,CAAhB,KAAsBwU,kBAAkB,CAAC1Z,MAAD,EAASkF,CAAT,CAA5C;AAAA;AAHiC,OAArB,CAAtB;AAMA,UAAMyU,QAAQ,GAAGF,aAAa,IAAI/Y,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuByZ,aAAa,CAAC,CAAD,CAApC,CAAlC;AACA,UAAIhG,UAAJ;AACA,UAAIc,QAAJ;AAGA;;AACA,UAAItR,IAAI,CAACC,MAAL,CAAYiB,IAAZ,KAAqBlB,IAAI,CAACC,MAAL,CAAY4L,QAAZ,CAAzB,EAAgD;AAC9C,QAA0B3K,IAA1B,CAAQG,IAAR;AAAA,gBAAiBsK,IAAjB,4BAA0BzK,IAA1B;;AACAoQ,QAAAA,QAAQ,GAAGzF,QAAQ,CAACxK,IAAT,CAAcK,MAAzB;AACA8O,QAAAA,UAAU,GAAG7E,IAAb;AACD,OAJD,MAIO,IAAInK,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2BM,OAAO,CAACC,SAAR,CAAkBoK,QAAlB,CAA/B,EAA4D;AACjE,QAA8B3K,IAA9B,CAAQlE,QAAR;AAAA,gBAAqB2O,KAArB,4BAA8BzK,IAA9B;;AACAoQ,QAAAA,QAAQ,GAAGzF,QAAQ,CAAC7O,QAAT,CAAkB0E,MAA7B;AACA8O,QAAAA,UAAU,GAAG7E,KAAb;AACD,OAJM,MAIA;AACL,cAAM,IAAIS,KAAJ,0CAC8B3N,IAD9B,0EACkGuR,IAAI,CAACC,SAAL,CACpG/O,IADoG,CADlG,cAGC8O,IAAI,CAACC,SAAL,CAAepE,QAAf,CAHD,EAAN;AAKD;AAGD;;;AACA,UAAI,CAAC0K,iBAAL,EAAwB;AACtBnX,QAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAAE6E,UAAAA,EAAE,EAAEnD,IAAN;AAAYwN,UAAAA,EAAE,EAAEjN,OAAhB;AAAyB8C,UAAAA,KAAK,EAALA;AAAzB,SAA7B;AACD;AAGD;;;AACA,UAAI4U,QAAJ,EAAc;AACZtX,QAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAAE6E,UAAAA,EAAE,EAAE8U,QAAQ,CAACrI,OAAf;AAAyBvM,UAAAA,KAAK,EAALA;AAAzB,SAA/B;AACD;AAGD;AACA;AACA;AACA;;;AACA,UACGN,OAAO,CAACC,SAAR,CAAkBoK,QAAlB,KAA+BpO,MAAM,CAAC6N,OAAP,CAAevO,MAAf,EAAuB8O,QAAvB,CAAhC,IACC7L,IAAI,CAACC,MAAL,CAAY4L,QAAZ,KACCA,QAAQ,CAACxK,IAAT,KAAkB,EADnB,IAECyK,QAAQ,CAACA,QAAQ,CAACpK,MAAT,GAAkB,CAAnB,CAAR,KAAkC,CAJtC,EAKE;AACAtC,QAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAAE6E,UAAAA,EAAE,EAAEkK,QAAN;AAAgBhK,UAAAA,KAAK,EAALA;AAAhB,SAA/B;AACD,OAPD,MAOO;AACL/E,QAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,UAAAA,IAAI,EAAE,YADK;AAEXb,UAAAA,IAAI,EAAEO,OAFK;AAGXsS,UAAAA,QAAQ,EAARA,QAHW;AAIXd,UAAAA,UAAU,EAAVA;AAJW,SAAb;AAMD;;AAED,UAAIkG,QAAJ,EAAc;AACZA,QAAAA,QAAQ,CAACpI,KAAT;AACD;AACF,KA3HD;AA4HD,GAnU2C;;AAqU5C;;;AAIA8H,EAAAA,SAzU4C,qBA0U1CrZ,MA1U0C,EA2U1C2M,OA3U0C;AAmV1CjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,UACEkP,EADF,GAKIvC,OALJ,CACEuC,EADF;AAAA,yBAKIvC,OALJ,CAEE9H,EAFF;AAAA,UAEEA,EAFF,6BAEO7E,MAAM,CAACG,SAFd;AAAA,2BAKIwM,OALJ,CAGEC,IAHF;AAAA,UAGEA,IAHF,+BAGS,QAHT;AAAA,4BAKID,OALJ,CAIE5H,KAJF;AAAA,UAIEA,KAJF,gCAIU,KAJV;AAMA,UAAM/B,KAAN,GAAgB2J,OAAhB,CAAM3J,KAAN;;AAEA,UAAI,CAAC6B,EAAL,EAAS;AACP;AACD;;AAED,UAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,IACJmU,SAAS,CAAChZ,MAAD,EAAS6E,EAAT,CADL,GAEJ,UAAAK,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAFL;AAGD;;AAED,UAAM0U,KAAK,GAAGlZ,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBkP,EAAvB,CAAd;AACA,UAAM2K,OAAO,GAAGnZ,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa4J,QAAAA,IAAI,EAAJA,IAAb;AAAmB7H,QAAAA,KAAK,EAALA;AAAnB,OAArB,CAAhB;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAW2T,OAAX,EAAoB;AAAA;AAAA,YAAIzT,CAAJ;;AAAA,eAAW1F,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBoG,CAAvB,CAAX;AAAA,OAApB,CAAjB;;AAEA,qCAAsBzF,QAAtB,kCAAgC;AAA3B,YAAMyQ,OAAO,kBAAb;AACH,YAAM1P,IAAI,GAAG0P,OAAO,CAACG,KAAR,EAAb;AACA,YAAMtP,OAAO,GAAG2X,KAAK,CAACtI,OAAtB;;AAEA,YAAI5P,IAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB3E,UAAAA,MAAM,CAACQ,KAAP,CAAa;AAAE+B,YAAAA,IAAI,EAAE,WAAR;AAAqBb,YAAAA,IAAI,EAAJA,IAArB;AAA2BO,YAAAA,OAAO,EAAPA;AAA3B,WAAb;AACD;;AAED,YACE2X,KAAK,CAACtI,OAAN,IACAvP,IAAI,CAAC6S,SAAL,CAAe3S,OAAf,EAAwBP,IAAxB,CADA,IAEAK,IAAI,CAACkS,OAAL,CAAahS,OAAb,EAAsBP,IAAtB,CAHF,EAIE;AACA;AACA;AACA;AACAkY,UAAAA,KAAK,CAACtI,OAAN,GAAgBvP,IAAI,CAACgF,IAAL,CAAU6S,KAAK,CAACtI,OAAhB,CAAhB;AACD;AACF;;AAEDsI,MAAAA,KAAK,CAACrI,KAAN;AACD,KA5CD;AA6CD,GAhY2C;;AAkY5C;;;AAIA/L,EAAAA,WAtY4C,uBAuY1CxF,MAvY0C;QAwY1C2M,8EAMI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,8BAA4D2M,OAA5D,CAAQmM,OAAR;AAAA,UAAQA,OAAR,kCAAkB,KAAlB;AAAA,4BAA4DnM,OAA5D,CAAyB5H,KAAzB;AAAA,UAAyBA,KAAzB,gCAAiC,KAAjC;AAAA,2BAA4D4H,OAA5D,CAAwCC,IAAxC;AAAA,UAAwCA,IAAxC,+BAA+C,QAA/C;AACA,yBAAuCD,OAAvC,CAAM9H,EAAN;AAAA,UAAMA,EAAN,6BAAW7E,MAAM,CAACG,SAAlB;AAAA,UAA6B6C,KAA7B,GAAuC2J,OAAvC,CAA6B3J,KAA7B;;AAEA,UAAI,CAAC6B,EAAL,EAAS;AACP;AACD;;AAED,UAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,IACJmU,SAAS,CAAChZ,MAAD,EAAS6E,EAAT,CADL,GAEJ,UAAAK,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAFL;AAGD;;AAED,UAAI,CAAC4T,OAAD,IAAYjW,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAhB,EAAmC;AACjCA,QAAAA,EAAE,GAAGnE,MAAM,CAAC6R,WAAP,CAAmBvS,MAAnB,EAA2B6E,EAA3B,CAAL;AACD;;AAED,UAAMiV,MAAM,GAAGpZ,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa4J,QAAAA,IAAI,EAAJA,IAAb;AAAmB7H,QAAAA,KAAK,EAALA;AAAnB,OAArB,CAAf;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAW4T,MAAX,EAAmB;AAAA;AAAA,YAAI1T,CAAJ;;AAAA,eAAW1F,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBoG,CAAvB,CAAX;AAAA,OAAnB,CAAjB;;AAEA,qCAAsBzF,QAAtB,kCAAgC;AAA3B,YAAMyQ,OAAO,kBAAb;AACH,YAAM1P,IAAI,GAAG0P,OAAO,CAACG,KAAR,EAAb;;AAEA,YAAI7P,IAAJ,EAAU;AACR,6BAAehB,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB0B,IAApB,CAAf;AAAA;AAAA,cAAOyC,IAAP;;AACAnE,UAAAA,MAAM,CAACQ,KAAP,CAAa;AAAE+B,YAAAA,IAAI,EAAE,aAAR;AAAuBb,YAAAA,IAAI,EAAJA,IAAvB;AAA6ByC,YAAAA,IAAI,EAAJA;AAA7B,WAAb;AACD;AACF;AACF,KA7BD;AA8BD,GA9a2C;;AAgb5C;;;AAIApB,EAAAA,QApb4C,oBAqb1C/C,MArb0C,EAsb1CkM,KAtb0C;QAub1CS,8EAOI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,UAAMgD,KAAN,GAAuC2J,OAAvC,CAAM3J,KAAN;AAAA,yBAAuC2J,OAAvC,CAAa9H,EAAb;AAAA,UAAaA,EAAb,6BAAkB7E,MAAM,CAACG,SAAzB;AACA,8BAKIwM,OALJ,CACEmM,OADF;AAAA,UACEA,OADF,kCACY,KADZ;AAAA,2BAKInM,OALJ,CAEEC,IAFF;AAAA,UAEEA,IAFF,+BAES,QAFT;AAAA,2BAKID,OALJ,CAGExJ,KAHF;AAAA,UAGEA,KAHF,+BAGU,KAHV;AAAA,4BAKIwJ,OALJ,CAIE5H,KAJF;AAAA,UAIEA,KAJF,gCAIU,KAJV;;AAOA,UAAI,CAACF,EAAL,EAAS;AACP;AACD;;AAED,UAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,IACJmU,SAAS,CAAChZ,MAAD,EAAS6E,EAAT,CADL,GAEJ,UAAAK,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAFL;AAGD;;AAED,UAAI,CAAC4T,OAAD,IAAYjW,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAhB,EAAmC;AACjCA,QAAAA,EAAE,GAAGnE,MAAM,CAAC6R,WAAP,CAAmBvS,MAAnB,EAA2B6E,EAA3B,CAAL;AACD;;AAED,UAAI1B,KAAK,IAAIN,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAb,EAAgC;AAC9B,YACEhC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,KACAnE,MAAM,CAACiO,IAAP,CAAY3O,MAAZ,EAAoB6E,EAAE,CAACiI,MAAvB,EAA+B,CAA/B,EAAkCxI,IAAlC,CAAuCK,MAAvC,GAAgD,CAFlD,EAGE;AACA;AACA;AACA;AACD;;AACD,YAAMyN,QAAQ,GAAG1R,MAAM,CAAC0R,QAAP,CAAgBpS,MAAhB,EAAwB6E,EAAxB,EAA4B;AAAEwM,UAAAA,QAAQ,EAAE;AAAZ,SAA5B,CAAjB;;AACA,4BAAqBxO,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAArB;AAAA;AAAA,YAAO0I,KAAP;AAAA,YAAchE,GAAd;;AACA,YAAMwQ,SAAS,GAAGnN,IAAI,KAAK,QAAT,GAAoB,QAApB,GAA+B,SAAjD;AACA,YAAMoN,cAAc,GAAGtZ,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqBuJ,GAArB,EAA0BA,GAAG,CAAC7H,IAA9B,CAAvB;AACAW,QAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAC5B6E,UAAAA,EAAE,EAAE0E,GADwB;AAE5BvG,UAAAA,KAAK,EAALA,KAF4B;AAG5B4J,UAAAA,IAAI,EAAEmN,SAHsB;AAI5BhV,UAAAA,KAAK,EAALA,KAJ4B;AAK5Bf,UAAAA,MAAM,EAAE,CAACgW;AALmB,SAA9B;AAOA,YAAMC,kBAAkB,GAAGvZ,MAAM,CAAC4N,OAAP,CAAetO,MAAf,EAAuBuN,KAAvB,EAA8BA,KAAK,CAAC7L,IAApC,CAA3B;AACAW,QAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAC5B6E,UAAAA,EAAE,EAAE0I,KADwB;AAE5BvK,UAAAA,KAAK,EAALA,KAF4B;AAG5B4J,UAAAA,IAAI,EAAEmN,SAHsB;AAI5BhV,UAAAA,KAAK,EAALA,KAJ4B;AAK5Bf,UAAAA,MAAM,EAAE,CAACiW;AALmB,SAA9B;AAOApV,QAAAA,EAAE,GAAGuN,QAAQ,CAACb,KAAT,EAAL;;AAEA,YAAI5E,OAAO,CAAC9H,EAAR,IAAc,IAAlB,EAAwB;AACtBxC,UAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0B6E,EAA1B;AACD;AACF;;oDAE0BnE,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAC9C6E,QAAAA,EAAE,EAAFA,EAD8C;AAE9C7B,QAAAA,KAAK,EAALA,KAF8C;AAG9C4J,QAAAA,IAAI,EAAJA,IAH8C;AAI9C7H,QAAAA,KAAK,EAALA;AAJ8C,OAArB;;;;AAA3B,+DAKI;AAAA;AAAA,cALQZ,IAKR;AAAA,cALczC,IAKd;;AACF,cAAM+R,UAAU,GAAkB,EAAlC;AACA,cAAMe,aAAa,GAAkB,EAArC,CAFE;;AAKF,cAAI9S,IAAI,CAACiD,MAAL,KAAgB,CAApB,EAAuB;AACrB;AACD;;AAED,cAAIuV,UAAU,GAAG,KAAjB;;AAEA,eAAK,IAAMC,CAAX,IAAgBjO,KAAhB,EAAuB;AACrB,gBAAIiO,CAAC,KAAK,UAAN,IAAoBA,CAAC,KAAK,MAA9B,EAAsC;AACpC;AACD;;AAED,gBAAIjO,KAAK,CAACiO,CAAD,CAAL,KAAahW,IAAI,CAACgW,CAAD,CAArB,EAA0B;AACxBD,cAAAA,UAAU,GAAG,IAAb,CADwB;;AAGxB,kBAAI/V,IAAI,CAACyT,cAAL,CAAoBuC,CAApB,CAAJ,EAA4B1G,UAAU,CAAC0G,CAAD,CAAV,GAAgBhW,IAAI,CAACgW,CAAD,CAApB,CAHJ;;AAKxB,kBAAIjO,KAAK,CAACiO,CAAD,CAAL,IAAY,IAAhB,EAAsB3F,aAAa,CAAC2F,CAAD,CAAb,GAAmBjO,KAAK,CAACiO,CAAD,CAAxB;AACvB;AACF;;AAED,cAAID,UAAJ,EAAgB;AACdla,YAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,cAAAA,IAAI,EAAE,UADK;AAEXb,cAAAA,IAAI,EAAJA,IAFW;AAGX+R,cAAAA,UAAU,EAAVA,UAHW;AAIXe,cAAAA,aAAa,EAAbA;AAJW,aAAb;AAMD;AACF;;;;;;AACF,KAjGD;AAkGD,GAliB2C;;AAoiB5C;;;AAIAzQ,EAAAA,UAxiB4C,sBAyiB1C/D,MAziB0C;QA0iB1C2M,8EAOI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,2BAA2C2M,OAA3C,CAAQC,IAAR;AAAA,UAAQA,IAAR,+BAAe,QAAf;AAAA,4BAA2CD,OAA3C,CAAyB5H,KAAzB;AAAA,UAAyBA,KAAzB,gCAAiC,KAAjC;AACA,UAAM/B,KAAN,GAAmE2J,OAAnE,CAAM3J,KAAN;AAAA,yBAAmE2J,OAAnE,CAAa9H,EAAb;AAAA,UAAaA,EAAb,6BAAkB7E,MAAM,CAACG,SAAzB;AAAA,4BAAmEwM,OAAnE,CAAoCyN,MAApC;AAAA,UAAoCA,MAApC,gCAA6C,CAA7C;AAAA,4BAAmEzN,OAAnE,CAAgD3I,MAAhD;AAAA,UAAgDA,MAAhD,gCAAyD,KAAzD;;AAEA,UAAIhB,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAAT;AACD;;AAED,UAAIrC,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,GAAGwV,WAAW,CAACra,MAAD,EAAS6E,EAAT,CAAhB;AACD;AAGD;;;AACA,UAAI9C,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB,YAAMnD,IAAI,GAAGmD,EAAb;AACA,YAAMkI,KAAK,GAAGrM,MAAM,CAACqM,KAAP,CAAa/M,MAAb,EAAqB0B,IAArB,CAAd;;AACA,8BAAiBhB,MAAM,CAAC4O,MAAP,CAActP,MAAd,EAAsB0B,IAAtB,CAAjB;AAAA;AAAA,YAAO4N,MAAP;;AACAtM,QAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,iBAAIA,CAAC,KAAKoK,MAAV;AAAA,SAAT;;AACA8K,QAAAA,MAAM,GAAGrN,KAAK,CAACrL,IAAN,CAAWiD,MAAX,GAAoBjD,IAAI,CAACiD,MAAzB,GAAkC,CAA3C;AACAE,QAAAA,EAAE,GAAGkI,KAAL;AACA/I,QAAAA,MAAM,GAAG,IAAT;AACD;;AAED,UAAI,CAACa,EAAL,EAAS;AACP;AACD;;AAED,UAAMyV,SAAS,GAAG5Z,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwB6E,EAAxB,EAA4B;AAC5CwM,QAAAA,QAAQ,EAAE;AADkC,OAA5B,CAAlB;;AAGA,2BAAkB3Q,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa4J,QAAAA,IAAI,EAAJA,IAAb;AAAmB7H,QAAAA,KAAK,EAALA;AAAnB,OAArB,CAAlB;AAAA;AAAA,UAAOwV,OAAP;;AAEA,UAAI,CAACA,OAAL,EAAc;AACZ;AACD;;AAED,UAAMC,SAAS,GAAG9Z,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM+H,QAAAA,IAAI,EAAE;AAAZ,OAApB,CAAlB;AACA,UAAM6N,KAAK,GAAG,CAAd;;AAEA,UAAI,CAAC1V,KAAD,IAAUyV,SAAd,EAAyB;AACvB,wCAA6BA,SAA7B;AAAA,YAAOE,QAAP;AAAA,YAAiBC,QAAjB;;AAEA,YAAIlW,OAAO,CAACC,SAAR,CAAkBgW,QAAlB,KAA+B1a,MAAM,CAACK,QAAP,CAAgBqa,QAAhB,CAAnC,EAA8D;AAC5D,cAAI7N,KAAK,GAAGnM,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqB2a,QAArB,CAAZ;;AAEA,cAAI,CAAC9N,KAAL,EAAY;AACV,gBAAMvI,IAAI,GAAG;AAAEA,cAAAA,IAAI,EAAE;AAAR,aAAb;AACA,gBAAMsW,SAAS,GAAG7Y,IAAI,CAACgF,IAAL,CAAU4T,QAAV,CAAlB;AACAtY,YAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+BsE,IAA/B,EAAqC;AAAEO,cAAAA,EAAE,EAAE+V,SAAN;AAAiB7V,cAAAA,KAAK,EAALA;AAAjB,aAArC;AACA8H,YAAAA,KAAK,GAAGnM,MAAM,CAACqM,KAAP,CAAa/M,MAAb,EAAqB4a,SAArB,CAAR;AACD;;AAED/V,UAAAA,EAAE,GAAGgI,KAAL;AACA7I,UAAAA,MAAM,GAAG,IAAT;AACD;;AAED,YAAM6W,aAAa,GAAGhW,EAAE,CAACnD,IAAH,CAAQiD,MAAR,GAAiBgW,QAAQ,CAAChW,MAAhD;AACAyV,QAAAA,MAAM,GAAGS,aAAa,GAAG,CAAzB;AACA7W,QAAAA,MAAM,GAAG,IAAT;AACD;;AAED,UAAM8W,QAAQ,GAAGpa,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwB6E,EAAxB,CAAjB;AACA,UAAMiM,KAAK,GAAGjM,EAAE,CAACnD,IAAH,CAAQiD,MAAR,GAAiByV,MAA/B;;AACA,oCAAwBG,OAAxB;AAAA,UAASQ,WAAT;;AACA,UAAMC,UAAU,GAAGnW,EAAE,CAACnD,IAAH,CAAQyH,KAAR,CAAc,CAAd,EAAiB2H,KAAjB,CAAnB;AACA,UAAIyD,QAAQ,GAAG6F,MAAM,KAAK,CAAX,GAAevV,EAAE,CAAC4J,MAAlB,GAA2B5J,EAAE,CAACnD,IAAH,CAAQoP,KAAR,IAAiB2J,KAA3D;;oDAE2B/Z,MAAM,CAACqF,MAAP,CAAc/F,MAAd,EAAsB;AAC/C6E,QAAAA,EAAE,EAAEmW,UAD2C;AAE/CzX,QAAAA,OAAO,EAAE,IAFsC;AAG/CwB,QAAAA,KAAK,EAALA;AAH+C,OAAtB;;;;AAA3B,+DAII;AAAA;AAAA,cAJQZ,IAIR;AAAA,cAJczC,MAId;;AACF,cAAIyB,KAAK,GAAG,KAAZ;;AAEA,cACEzB,MAAI,CAACiD,MAAL,GAAcoW,WAAW,CAACpW,MAA1B,IACAjD,MAAI,CAACiD,MAAL,KAAgB,CADhB,IAEC,CAACI,KAAD,IAAUrE,MAAM,CAACJ,MAAP,CAAcN,MAAd,EAAsBmE,IAAtB,CAHb,EAIE;AACA;AACD;;AAED,cAAM4I,OAAK,GAAGuN,SAAS,CAAChJ,OAAxB;AACA,cAAMnD,KAAK,GAAGzN,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqB+M,OAArB,EAA4BrL,MAA5B,CAAd;;AAEA,cAAIsC,MAAM,IAAI,CAACsW,SAAX,IAAwB,CAAC5Z,MAAM,CAAC2N,MAAP,CAAcrO,MAAd,EAAsB+M,OAAtB,EAA6BrL,MAA7B,CAA7B,EAAiE;AAC/DyB,YAAAA,KAAK,GAAG,IAAR;AACA,gBAAMsQ,UAAU,GAAG7P,IAAI,CAAC4P,YAAL,CAAkBrP,IAAlB,CAAnB;AACAnE,YAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,cAAAA,IAAI,EAAE,YADK;AAEXb,cAAAA,IAAI,EAAJA,MAFW;AAGX6S,cAAAA,QAAQ,EAARA,QAHW;AAIXd,cAAAA,UAAU,EAAVA;AAJW,aAAb;AAMD;;AAEDc,UAAAA,QAAQ,GAAG7S,MAAI,CAACA,MAAI,CAACiD,MAAL,GAAc,CAAf,CAAJ,IAAyBxB,KAAK,IAAIgL,KAAT,GAAiB,CAAjB,GAAqB,CAA9C,CAAX;AACD;;;;;;;AAED,UAAIxB,OAAO,CAAC9H,EAAR,IAAc,IAAlB,EAAwB;AACtB,YAAMkI,MAAK,GAAG+N,QAAQ,CAACxJ,OAAT,IAAoB5Q,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB,EAAnB,CAAlC;;AACAqC,QAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0B+M,MAA1B;AACD;;AAEDuN,MAAAA,SAAS,CAAC/I,KAAV;AACAuJ,MAAAA,QAAQ,CAACvJ,KAAT;AACD,KA3GD;AA4GD,GA/pB2C;;AAiqB5C;;;AAIAzL,EAAAA,UArqB4C,sBAsqB1C9F,MAtqB0C,EAuqB1CkM,KAvqB0C;QAwqB1CS,8EAMI;;AAEJ,QAAI,CAAC1G,KAAK,CAAC6F,OAAN,CAAcI,KAAd,CAAL,EAA2B;AACzBA,MAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;AACD;;AAED,QAAMwL,GAAG,GAAG,EAAZ;;kDAEkBxL;;;;AAAlB,6DAAyB;AAAA,YAAdvK,GAAc;AACvB+V,QAAAA,GAAG,CAAC/V,GAAD,CAAH,GAAW,IAAX;AACD;;;;;;;AAEDU,IAAAA,UAAU,CAACU,QAAX,CAAoB/C,MAApB,EAA4B0X,GAA5B,EAAiC/K,OAAjC;AACD,GA3rB2C;;AA6rB5C;;;;AAKAsO,EAAAA,WAlsB4C,uBAmsB1Cjb,MAnsB0C;QAosB1C2M,8EAMI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,2BAA0D2M,OAA1D,CAAQC,IAAR;AAAA,UAAQA,IAAR,+BAAe,QAAf;AAAA,4BAA0DD,OAA1D,CAAyBxJ,KAAzB;AAAA,UAAyBA,KAAzB,gCAAiC,KAAjC;AAAA,4BAA0DwJ,OAA1D,CAAwC5H,KAAxC;AAAA,UAAwCA,KAAxC,gCAAgD,KAAhD;AACA,yBAAuC4H,OAAvC,CAAM9H,EAAN;AAAA,UAAMA,EAAN,6BAAW7E,MAAM,CAACG,SAAlB;AAAA,UAA6B6C,KAA7B,GAAuC2J,OAAvC,CAA6B3J,KAA7B;;AAEA,UAAI,CAAC6B,EAAL,EAAS;AACP;AACD;;AAED,UAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,IACJmU,SAAS,CAAChZ,MAAD,EAAS6E,EAAT,CADL,GAEJ,UAAAK,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAFL;AAGD;;AAED,UAAInD,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnBA,QAAAA,EAAE,GAAGnE,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB6E,EAArB,CAAL;AACD;;AAED,UAAMuN,QAAQ,GAAGvP,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,IAAoBnE,MAAM,CAAC0R,QAAP,CAAgBpS,MAAhB,EAAwB6E,EAAxB,CAApB,GAAkD,IAAnE;AACA,UAAM0H,OAAO,GAAG7L,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAM7B,QAAAA,KAAK,EAALA,KAAN;AAAa4J,QAAAA,IAAI,EAAJA,IAAb;AAAmB7H,QAAAA,KAAK,EAALA;AAAnB,OAArB,CAAhB;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CACfqG,OADe,EAEf;AAAA;AAAA,YAAInG,CAAJ;;AAAA,eAAW1F,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBoG,CAAvB,CAAX;AAAA,OAFe;AAIf;AACA;AALe,QAMf7C,OANe,EAAjB;;oDAQsB5C;;;;;cAAXyQ;AACT,cAAM1P,IAAI,GAAG0P,OAAO,CAACG,KAAR,EAAb;;AACA,8BAAe7Q,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB0B,IAApB,CAAf;AAAA;AAAA,cAAOyC,IAAP;;AACA,cAAI+I,KAAK,GAAGxM,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB0B,IAArB,CAAZ;;AAEA,cAAIyB,KAAK,IAAIiP,QAAb,EAAuB;AACrBlF,YAAAA,KAAK,GAAGrK,KAAK,CAACgU,YAAN,CAAmBzE,QAAQ,CAACd,OAA5B,EAAsCpE,KAAtC,CAAR;AACD;;AAED7K,UAAAA,UAAU,CAAC6W,SAAX,CAAqBlZ,MAArB,EAA6B;AAC3B6E,YAAAA,EAAE,EAAEqI,KADuB;AAE3BlK,YAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,qBAAIT,OAAO,CAACmH,UAAR,CAAmBzH,IAAnB,KAA4BA,IAAI,CAAClE,QAAL,CAAcsP,QAAd,CAAuBrK,CAAvB,CAAhC;AAAA,aAFmB;AAG3BH,YAAAA,KAAK,EAALA;AAH2B,WAA7B;;;AATF,+DAAgC;AAAA;AAc/B;;;;;;;AAED,UAAIqN,QAAJ,EAAc;AACZA,QAAAA,QAAQ,CAACb,KAAT;AACD;AACF,KA/CD;AAgDD,GA5vB2C;;AA8vB5C;;;;AAKA2J,EAAAA,SAnwB4C,qBAowB1Clb,MApwB0C,EAqwB1CwM,OArwB0C;QAswB1CG,8EAMI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,2BAA0D2M,OAA1D,CAAQC,IAAR;AAAA,UAAQA,IAAR,+BAAe,QAAf;AAAA,4BAA0DD,OAA1D,CAAyBxJ,KAAzB;AAAA,UAAyBA,KAAzB,gCAAiC,KAAjC;AAAA,4BAA0DwJ,OAA1D,CAAwC5H,KAAxC;AAAA,UAAwCA,KAAxC,gCAAgD,KAAhD;AACA,UAAM/B,KAAN,GAAuC2J,OAAvC,CAAM3J,KAAN;AAAA,yBAAuC2J,OAAvC,CAAa9H,EAAb;AAAA,UAAaA,EAAb,6BAAkB7E,MAAM,CAACG,SAAzB;;AAEA,UAAI,CAAC0E,EAAL,EAAS;AACP;AACD;;AAED,UAAI7B,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAIjB,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnB7B,UAAAA,KAAK,GAAGgW,SAAS,CAAChZ,MAAD,EAAS6E,EAAT,CAAjB;AACD,SAFD,MAEO,IAAI7E,MAAM,CAACK,QAAP,CAAgBmM,OAAhB,CAAJ,EAA8B;AACnCxJ,UAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,mBAAIxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,KAA8BjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,CAAlC;AAAA,WAAT;AACD,SAFM,MAEA;AACLlC,UAAAA,KAAK,GAAG,eAAAkC,CAAC;AAAA,mBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,WAAT;AACD;AACF;;AAED,UAAI/B,KAAK,IAAIN,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAb,EAAgC;AAC9B,4BAAqBhC,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAArB;AAAA;AAAA,YAAO0I,KAAP;AAAA,YAAchE,GAAd;;AACA,YAAM6I,QAAQ,GAAG1R,MAAM,CAAC0R,QAAP,CAAgBpS,MAAhB,EAAwB6E,EAAxB,EAA4B;AAC3CwM,UAAAA,QAAQ,EAAE;AADiC,SAA5B,CAAjB;AAGAhP,QAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAAE6E,UAAAA,EAAE,EAAE0E,GAAN;AAAWvG,UAAAA,KAAK,EAALA,KAAX;AAAkB+B,UAAAA,KAAK,EAALA;AAAlB,SAA9B;AACA1C,QAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAAE6E,UAAAA,EAAE,EAAE0I,KAAN;AAAavK,UAAAA,KAAK,EAALA,KAAb;AAAoB+B,UAAAA,KAAK,EAALA;AAApB,SAA9B;AACAF,QAAAA,EAAE,GAAGuN,QAAQ,CAACb,KAAT,EAAL;;AAEA,YAAI5E,OAAO,CAAC9H,EAAR,IAAc,IAAlB,EAAwB;AACtBxC,UAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0B6E,EAA1B;AACD;AACF;;AAED,UAAMsW,KAAK,GAAGlV,KAAK,CAACC,IAAN,CACZxF,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AACnB6E,QAAAA,EAAE,EAAFA,EADmB;AAEnB7B,QAAAA,KAAK,EAAEhD,MAAM,CAACK,QAAP,CAAgBmM,OAAhB,IACH,UAAAtH,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SADE,GAEH,UAAAA,CAAC;AAAA,iBAAIxE,MAAM,CAACuE,QAAP,CAAgBC,CAAhB,CAAJ;AAAA,SAJc;AAKnB0H,QAAAA,IAAI,EAAE,QALa;AAMnB7H,QAAAA,KAAK,EAALA;AANmB,OAArB,CADY,CAAd;;AAWA,iCAA2BoW,KAA3B,8BAAkC;AAA7B;AAAA,YAASC,QAAT;;AACH,YAAM7D,CAAC,GAAG1U,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,IACNhC,KAAK,CAACgU,YAAN,CAAmBhS,EAAnB,EAAuBnE,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqBob,QAArB,CAAvB,CADM,GAENvW,EAFJ;;AAIA,YAAI,CAAC0S,CAAL,EAAQ;AACN;AACD;;AAED,YAAMhL,OAAO,GAAGtG,KAAK,CAACC,IAAN,CACdxF,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,UAAAA,EAAE,EAAE0S,CAAN;AAASvU,UAAAA,KAAK,EAALA,KAAT;AAAgB4J,UAAAA,IAAI,EAAJA,IAAhB;AAAsB7H,UAAAA,KAAK,EAALA;AAAtB,SAArB,CADc,CAAhB;;AAIA,YAAIwH,OAAO,CAAC5H,MAAR,GAAiB,CAArB,EAAwB;AAAA;AACtB,0CAAgB4H,OAAhB;AAAA,gBAAOkB,KAAP;;AACA,gBAAMiB,IAAI,GAAGnC,OAAO,CAACA,OAAO,CAAC5H,MAAR,GAAiB,CAAlB,CAApB;;AACA,wCAAsB8I,KAAtB;AAAA,gBAASsD,SAAT;;AACA,uCAAqBrC,IAArB;AAAA,gBAASsC,QAAT;;AAEA,gBAAID,SAAS,CAACpM,MAAV,KAAqB,CAArB,IAA0BqM,QAAQ,CAACrM,MAAT,KAAoB,CAAlD,EAAqD;AACnD;AACA;AACD;;AAED,gBAAM4U,UAAU,GAAGxX,IAAI,CAAC2D,MAAL,CAAYqL,SAAZ,EAAuBC,QAAvB,IACfjP,IAAI,CAACuN,MAAL,CAAYyB,SAAZ,CADe,GAEfhP,IAAI,CAACkP,MAAL,CAAYF,SAAZ,EAAuBC,QAAvB,CAFJ;AAIA,gBAAM9D,KAAK,GAAGxM,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB+Q,SAArB,EAAgCC,QAAhC,CAAd;AACA,gBAAMqK,eAAe,GAAG3a,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoBuZ,UAApB,CAAxB;;AACA,kDAAqB8B,eAArB;AAAA,gBAAOC,UAAP;;AACA,gBAAMxK,KAAK,GAAGyI,UAAU,CAAC5U,MAAX,GAAoB,CAAlC;AACA,gBAAM4W,WAAW,GAAGxZ,IAAI,CAACgF,IAAL,CAAUiK,QAAQ,CAAC7H,KAAT,CAAe,CAAf,EAAkB2H,KAAlB,CAAV,CAApB;;AACA,gBAAM0K,OAAO,uCAAQhP,OAAR;AAAiBvM,cAAAA,QAAQ,EAAE;AAA3B,cAAb;;AACAoC,YAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+Bwb,OAA/B,EAAwC;AAAE3W,cAAAA,EAAE,EAAE0W,WAAN;AAAmBxW,cAAAA,KAAK,EAALA;AAAnB,aAAxC;AAEA1C,YAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAC3B6E,cAAAA,EAAE,EAAEqI,KADuB;AAE3BlK,cAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,uBACNT,OAAO,CAACmH,UAAR,CAAmB0P,UAAnB,KAAkCA,UAAU,CAACrb,QAAX,CAAoBsP,QAApB,CAA6BrK,CAA7B,CAD5B;AAAA,eAFmB;AAI3BgK,cAAAA,EAAE,EAAEqM,WAAW,CAACzW,MAAZ,CAAmB,CAAnB,CAJuB;AAK3BC,cAAAA,KAAK,EAALA;AAL2B,aAA7B;AAvBsB;;AAAA,mCAQpB;AAsBH;AACF;AACF,KAxFD;AAyFD;AAv2B2C,CAAvC;;AA02BP,IAAM2U,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC1Z,MAAD,EAAiBmE,IAAjB;AACzB,MAAIM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,CAAJ,EAA6B;AAC3B,QAAMqI,OAAO,GAAGrI,IAAhB;;AACA,QAAIzD,MAAM,CAACJ,MAAP,CAAcN,MAAd,EAAsBmE,IAAtB,CAAJ,EAAiC;AAC/B,aAAO,IAAP;AACD,KAFD,MAEO,IAAIqI,OAAO,CAACvM,QAAR,CAAiB0E,MAAjB,KAA4B,CAAhC,EAAmC;AACxC,aAAO+U,kBAAkB,CAAC1Z,MAAD,EAASwM,OAAO,CAACvM,QAAR,CAAiB,CAAjB,CAAT,CAAzB;AACD,KAFM,MAEA;AACL,aAAO,KAAP;AACD;AACF,GATD,MASO,IAAIS,MAAM,CAACuE,QAAP,CAAgBd,IAAhB,CAAJ,EAA2B;AAChC,WAAO,KAAP;AACD,GAFM,MAEA;AACL,WAAO,IAAP;AACD;AACF,CAfD;AAiBA;;;;;AAIA,IAAMkW,WAAW,GAAG,SAAdA,WAAc,CAACra,MAAD,EAAiBkN,KAAjB;AAClB,MAAIrK,KAAK,CAACS,WAAN,CAAkB4J,KAAlB,CAAJ,EAA8B;AAC5B,WAAOA,KAAK,CAACJ,MAAb;AACD,GAFD,MAEO;AACL,wBAAgBjK,KAAK,CAAC2K,KAAN,CAAYN,KAAZ,CAAhB;AAAA;AAAA,QAAS3D,GAAT;;AACA,QAAMkI,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuJ,GAAxB,CAAjB;AACAlH,IAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAE6E,MAAAA,EAAE,EAAEqI;AAAN,KAA1B;AACA,WAAOuE,QAAQ,CAACF,KAAT,EAAP;AACD;AACF,CATD;;AAWA,IAAMyH,SAAS,GAAG,SAAZA,SAAY,CAAChZ,MAAD,EAAiB0B,IAAjB;AAChB,sBAAehB,MAAM,CAACyD,IAAP,CAAYnE,MAAZ,EAAoB0B,IAApB,CAAf;AAAA;AAAA,MAAOyC,IAAP;;AACA,SAAO,UAAAe,CAAC;AAAA,WAAIA,CAAC,KAAKf,IAAV;AAAA,GAAR;AACD,CAHD;;;;;ACx+BO,IAAMsX,mBAAmB,GAAwB;AACtD;;;AAIAC,EAAAA,QALsD,oBAMpD1b,MANoD;QAOpD2M,8EAEI;AAEJ,wBAA4BA,OAA5B,CAAQK,IAAR;AAAA,QAAQA,IAAR,8BAAe,QAAf;AACA,QAAQ7M,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,QAAI,CAACA,SAAL,EAAgB;AACd;AACD,KAFD,MAEO,IAAI6M,IAAI,KAAK,QAAb,EAAuB;AAC5B3K,MAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0BG,SAAS,CAAC2M,MAApC;AACD,KAFM,MAEA,IAAIE,IAAI,KAAK,OAAb,EAAsB;AAC3B3K,MAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0BG,SAAS,CAAC8M,KAApC;AACD,KAFM,MAEA,IAAID,IAAI,KAAK,OAAb,EAAsB;AAC3B,yBAAgBnK,KAAK,CAAC2K,KAAN,CAAYrN,SAAZ,CAAhB;AAAA;AAAA,UAAOoN,KAAP;;AACAlL,MAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0BuN,KAA1B;AACD,KAHM,MAGA,IAAIP,IAAI,KAAK,KAAb,EAAoB;AACzB,0BAAgBnK,KAAK,CAAC2K,KAAN,CAAYrN,SAAZ,CAAhB;AAAA;AAAA,UAASoJ,GAAT;;AACAlH,MAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0BuJ,GAA1B;AACD;AACF,GA3BqD;;AA6BtD;;;AAIAoS,EAAAA,QAjCsD,oBAiC7C3b,MAjC6C;AAkCpD,QAAQG,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;;AAEA,QAAIA,SAAJ,EAAe;AACbH,MAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,QAAAA,IAAI,EAAE,eADK;AAEXkR,QAAAA,UAAU,EAAEtT,SAFD;AAGXqU,QAAAA,aAAa,EAAE;AAHJ,OAAb;AAKD;AACF,GA3CqD;;AA6CtD;;;AAIAoH,EAAAA,IAjDsD,gBAkDpD5b,MAlDoD;QAmDpD2M,8EAKI;AAEJ,QAAQxM,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;AACA,4BAA8DwM,OAA9D,CAAQjF,QAAR;AAAA,QAAQA,QAAR,kCAAmB,CAAnB;AAAA,wBAA8DiF,OAA9D,CAAsBtJ,IAAtB;AAAA,QAAsBA,IAAtB,8BAA6B,WAA7B;AAAA,2BAA8DsJ,OAA9D,CAA0CpJ,OAA1C;AAAA,QAA0CA,OAA1C,iCAAoD,KAApD;AACA,yBAAsBoJ,OAAtB,CAAMK,IAAN;AAAA,QAAMA,IAAN,+BAAa,IAAb;;AAEA,QAAI,CAAC7M,SAAL,EAAgB;AACd;AACD;;AAED,QAAI6M,IAAI,KAAK,OAAb,EAAsB;AACpBA,MAAAA,IAAI,GAAGnK,KAAK,CAACyT,UAAN,CAAiBnW,SAAjB,IAA8B,OAA9B,GAAwC,QAA/C;AACD;;AAED,QAAI6M,IAAI,KAAK,KAAb,EAAoB;AAClBA,MAAAA,IAAI,GAAGnK,KAAK,CAACyT,UAAN,CAAiBnW,SAAjB,IAA8B,QAA9B,GAAyC,OAAhD;AACD;;AAED,QAAQ2M,MAAR,GAA0B3M,SAA1B,CAAQ2M,MAAR;AAAA,QAAgBG,KAAhB,GAA0B9M,SAA1B,CAAgB8M,KAAhB;AACA,QAAM4O,IAAI,GAAG;AAAEnU,MAAAA,QAAQ,EAARA,QAAF;AAAYrE,MAAAA,IAAI,EAAJA;AAAZ,KAAb;AACA,QAAM6I,KAAK,GAAmB,EAA9B;;AAEA,QAAIc,IAAI,IAAI,IAAR,IAAgBA,IAAI,KAAK,QAA7B,EAAuC;AACrC,UAAMD,KAAK,GAAGxJ,OAAO,GACjB7C,MAAM,CAAC4M,MAAP,CAActN,MAAd,EAAsB8M,MAAtB,EAA8B+O,IAA9B,CADiB,GAEjBnb,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqB8M,MAArB,EAA6B+O,IAA7B,CAFJ;;AAIA,UAAI9O,KAAJ,EAAW;AACTb,QAAAA,KAAK,CAACY,MAAN,GAAeC,KAAf;AACD;AACF;;AAED,QAAIC,IAAI,IAAI,IAAR,IAAgBA,IAAI,KAAK,OAA7B,EAAsC;AACpC,UAAMD,MAAK,GAAGxJ,OAAO,GACjB7C,MAAM,CAAC4M,MAAP,CAActN,MAAd,EAAsBiN,KAAtB,EAA6B4O,IAA7B,CADiB,GAEjBnb,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqBiN,KAArB,EAA4B4O,IAA5B,CAFJ;;AAIA,UAAI9O,MAAJ,EAAW;AACTb,QAAAA,KAAK,CAACe,KAAN,GAAcF,MAAd;AACD;AACF;;AAED1K,IAAAA,UAAU,CAACyZ,YAAX,CAAwB9b,MAAxB,EAAgCkM,KAAhC;AACD,GAnGqD;;AAqGtD;;;AAIA6M,EAAAA,MAzGsD,kBAyG/C/Y,MAzG+C,EAyG/BoN,MAzG+B;AA0GpD,QAAQjN,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;AACAiN,IAAAA,MAAM,GAAG1M,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqBoN,MAArB,CAAT;;AAEA,QAAIjN,SAAJ,EAAe;AACbkC,MAAAA,UAAU,CAACyZ,YAAX,CAAwB9b,MAAxB,EAAgCoN,MAAhC;AACA;AACD;;AAED,QAAI,CAACvK,KAAK,CAACmL,OAAN,CAAcZ,MAAd,CAAL,EAA4B;AAC1B,YAAM,IAAIiC,KAAJ,6IACuI4D,IAAI,CAACC,SAAL,CACzI9F,MADyI,CADvI,EAAN;AAKD;;AAEDpN,IAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,MAAAA,IAAI,EAAE,eADK;AAEXkR,MAAAA,UAAU,EAAEtT,SAFD;AAGXqU,MAAAA,aAAa,EAAEpH;AAHJ,KAAb;AAKD,GA/HqD;;AAiItD;;;AAIA2O,EAAAA,QArIsD,oBAsIpD/b,MAtIoD,EAuIpDkM,KAvIoD;QAwIpDS,8EAEI;AAEJ,QAAQxM,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;AACA,yBAAwBwM,OAAxB,CAAMK,IAAN;AAAA,QAAMA,IAAN,+BAAa,MAAb;;AAEA,QAAI,CAAC7M,SAAL,EAAgB;AACd;AACD;;AAED,QAAI6M,IAAI,KAAK,OAAb,EAAsB;AACpBA,MAAAA,IAAI,GAAGnK,KAAK,CAACyT,UAAN,CAAiBnW,SAAjB,IAA8B,OAA9B,GAAwC,QAA/C;AACD;;AAED,QAAI6M,IAAI,KAAK,KAAb,EAAoB;AAClBA,MAAAA,IAAI,GAAGnK,KAAK,CAACyT,UAAN,CAAiBnW,SAAjB,IAA8B,QAA9B,GAAyC,OAAhD;AACD;;AAED,QAAQ2M,MAAR,GAA0B3M,SAA1B,CAAQ2M,MAAR;AAAA,QAAgBG,KAAhB,GAA0B9M,SAA1B,CAAgB8M,KAAhB;AACA,QAAMF,KAAK,GAAGC,IAAI,KAAK,QAAT,GAAoBF,MAApB,GAA6BG,KAA3C;AAEA5K,IAAAA,UAAU,CAACyZ,YAAX,CAAwB9b,MAAxB,sBACGgN,IAAI,KAAK,QAAT,GAAoB,QAApB,GAA+B,OADlC,sCACiDD,KADjD,GAC2Db,KAD3D;AAGD,GAjKqD;;AAmKtD;;;AAIA4P,EAAAA,YAvKsD,wBAuKzC9b,MAvKyC,EAuKzBkM,KAvKyB;AAwKpD,QAAQ/L,SAAR,GAAsBH,MAAtB,CAAQG,SAAR;AACA,QAAM6b,QAAQ,GAA0B,EAAxC;AACA,QAAMC,QAAQ,GAAmB,EAAjC;;AAEA,QAAI,CAAC9b,SAAL,EAAgB;AACd;AACD;;AAED,SAAK,IAAMga,CAAX,IAAgBjO,KAAhB,EAAuB;AACrB,UACGiO,CAAC,KAAK,QAAN,IACCjO,KAAK,CAACY,MAAN,IAAgB,IADjB,IAEC,CAACsB,KAAK,CAAC1I,MAAN,CAAawG,KAAK,CAACY,MAAnB,EAA2B3M,SAAS,CAAC2M,MAArC,CAFH,IAGCqN,CAAC,KAAK,OAAN,IACCjO,KAAK,CAACe,KAAN,IAAe,IADhB,IAEC,CAACmB,KAAK,CAAC1I,MAAN,CAAawG,KAAK,CAACe,KAAnB,EAA0B9M,SAAS,CAAC8M,KAApC,CALH,IAMCkN,CAAC,KAAK,QAAN,IAAkBA,CAAC,KAAK,OAAxB,IAAmCjO,KAAK,CAACiO,CAAD,CAAL,KAAaha,SAAS,CAACga,CAAD,CAP5D,EAQE;AACA6B,QAAAA,QAAQ,CAAC7B,CAAD,CAAR,GAAcha,SAAS,CAACga,CAAD,CAAvB;AACA8B,QAAAA,QAAQ,CAAC9B,CAAD,CAAR,GAAcjO,KAAK,CAACiO,CAAD,CAAnB;AACD;AACF;;AAED,QAAIlC,MAAM,CAACiE,IAAP,CAAYF,QAAZ,EAAsBrX,MAAtB,GAA+B,CAAnC,EAAsC;AACpC3E,MAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,QAAAA,IAAI,EAAE,eADK;AAEXkR,QAAAA,UAAU,EAAEuI,QAFD;AAGXxH,QAAAA,aAAa,EAAEyH;AAHJ,OAAb;AAKD;AACF;AAtMqD,CAAjD;;;;;;;ACcA,IAAME,cAAc,GAAmB;AAC5C;;;AAD4C,6BAM1Cnc,MAN0C;QAO1C2M,8EAOI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,6BAKI2M,OALJ,CACEpJ,OADF;AAAA,UACEA,OADF,iCACY,KADZ;AAAA,0BAKIoJ,OALJ,CAEEtJ,IAFF;AAAA,UAEEA,IAFF,8BAES,WAFT;AAAA,8BAKIsJ,OALJ,CAGEjF,QAHF;AAAA,UAGEA,QAHF,kCAGa,CAHb;AAAA,2BAKIiF,OALJ,CAIE5H,KAJF;AAAA,UAIEA,KAJF,+BAIU,KAJV;AAMA,wBAAiD4H,OAAjD,CAAM9H,EAAN;AAAA,UAAMA,EAAN,4BAAW7E,MAAM,CAACG,SAAlB;AAAA,6BAAiDwM,OAAjD,CAA6BmM,OAA7B;AAAA,UAA6BA,OAA7B,iCAAuC,KAAvC;;AAEA,UAAI,CAACjU,EAAL,EAAS;AACP;AACD;;AAED,UAAIhC,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,KAAqBhC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,CAAzB,EAAgD;AAC9CA,QAAAA,EAAE,GAAGA,EAAE,CAACiI,MAAR;AACD;;AAED,UAAIsB,KAAK,CAAC8C,OAAN,CAAcrM,EAAd,CAAJ,EAAuB;AACrB,YAAMuX,YAAY,GAAG1b,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,UAAAA,EAAE,EAAFA,EAAF;AAAM+H,UAAAA,IAAI,EAAE;AAAZ,SAApB,CAArB;;AAEA,YAAI,CAAC7H,KAAD,IAAUqX,YAAd,EAA4B;AAC1B,6CAAqBA,YAArB;AAAA,cAASzB,QAAT;;AACA9V,UAAAA,EAAE,GAAG8V,QAAL;AACD,SAHD,MAGO;AACL,cAAMkB,IAAI,GAAG;AAAExY,YAAAA,IAAI,EAAJA,IAAF;AAAQqE,YAAAA,QAAQ,EAARA;AAAR,WAAb;AACA,cAAM0F,MAAM,GAAG7J,OAAO,GAClB7C,MAAM,CAAC4M,MAAP,CAActN,MAAd,EAAsB6E,EAAtB,EAA0BgX,IAA1B,KAAmCnb,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB,EAArB,CADjB,GAElBU,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqB6E,EAArB,EAAyBgX,IAAzB,KAAkCnb,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB,EAAnB,CAFtC;AAGA6E,UAAAA,EAAE,GAAG;AAAEiI,YAAAA,MAAM,EAAEjI,EAAV;AAAcoI,YAAAA,KAAK,EAAEG;AAArB,WAAL;AACA0L,UAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,UAAI/W,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnBxC,QAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAAE6E,UAAAA,EAAE,EAAFA,EAAF;AAAME,UAAAA,KAAK,EAALA;AAAN,SAA/B;AACA;AACD;;AAED,UAAIlC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,CAAJ,EAA2B;AACzB;AACD;;AAED,UAAI,CAACiU,OAAL,EAAc;AACZ,2BAAgBjW,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAAhB;AAAA;AAAA,YAAS0E,IAAT;;AACA,YAAM8S,QAAQ,GAAG3b,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB,EAAnB,CAAjB;;AAEA,YAAI,CAACoO,KAAK,CAAC1I,MAAN,CAAa6D,IAAb,EAAkB8S,QAAlB,CAAL,EAAkC;AAChCxX,UAAAA,EAAE,GAAGnE,MAAM,CAAC6R,WAAP,CAAmBvS,MAAnB,EAA2B6E,EAA3B,EAA+B;AAAEE,YAAAA,KAAK,EAALA;AAAF,WAA/B,CAAL;AACD;AACF;;AAED,0BAAmBlC,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAAnB;AAAA;AAAA,UAAK0I,KAAL;AAAA,UAAYhE,GAAZ;;AACA,UAAM+S,UAAU,GAAG5b,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AACtCgD,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAD8B;AAEtCL,QAAAA,EAAE,EAAE0I,KAFkC;AAGtCxI,QAAAA,KAAK,EAALA;AAHsC,OAArB,CAAnB;AAKA,UAAMyN,QAAQ,GAAG9R,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AACpCgD,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAD4B;AAEpCL,QAAAA,EAAE,EAAE0E,GAFgC;AAGpCxE,QAAAA,KAAK,EAALA;AAHoC,OAArB,CAAjB;AAKA,UAAMwX,cAAc,GAClBD,UAAU,IAAI9J,QAAd,IAA0B,CAACzQ,IAAI,CAAC2D,MAAL,CAAY4W,UAAU,CAAC,CAAD,CAAtB,EAA2B9J,QAAQ,CAAC,CAAD,CAAnC,CAD7B;AAEA,UAAMgK,YAAY,GAAGza,IAAI,CAAC2D,MAAL,CAAY6H,KAAK,CAAC7L,IAAlB,EAAwB6H,GAAG,CAAC7H,IAA5B,CAArB;AACA,UAAM+a,SAAS,GAAG1X,KAAK,GACnB,IADmB,GAEnBrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,QAAAA,EAAE,EAAE0I,KAAN;AAAaX,QAAAA,IAAI,EAAE;AAAnB,OAApB,CAFJ;AAGA,UAAM8P,OAAO,GAAG3X,KAAK,GACjB,IADiB,GAEjBrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,QAAAA,EAAE,EAAE0E,GAAN;AAAWqD,QAAAA,IAAI,EAAE;AAAjB,OAApB,CAFJ;;AAKA,UAAI6P,SAAJ,EAAe;AACb,YAAMnP,MAAM,GAAG5M,MAAM,CAAC4M,MAAP,CAActN,MAAd,EAAsBuN,KAAtB,CAAf;;AAEA,YACED,MAAM,IACNgP,UADA,IAEAva,IAAI,CAAC6J,UAAL,CAAgB0Q,UAAU,CAAC,CAAD,CAA1B,EAA+BhP,MAAM,CAAC5L,IAAtC,CAHF,EAIE;AACA6L,UAAAA,KAAK,GAAGD,MAAR;AACD;AACF;;AAED,UAAIoP,OAAJ,EAAa;AACX,YAAM7P,KAAK,GAAGnM,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqBuJ,GAArB,CAAd;;AAEA,YAAIsD,KAAK,IAAI2F,QAAT,IAAqBzQ,IAAI,CAAC6J,UAAL,CAAgB4G,QAAQ,CAAC,CAAD,CAAxB,EAA6B3F,KAAK,CAACnL,IAAnC,CAAzB,EAAmE;AACjE6H,UAAAA,GAAG,GAAGsD,KAAN;AACD;AACF;AAGD;;;AACA,UAAMN,OAAO,GAAgB,EAA7B;AACA,UAAIyE,QAAJ;;iDAEoBtQ,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AAAE6E,QAAAA,EAAE,EAAFA,EAAF;AAAME,QAAAA,KAAK,EAALA;AAAN,OAArB;;;;AAApB,4DAAyD;AAAA,cAA9CP,KAA8C;;AACvD,sCAAqBA,KAArB;AAAA,cAAOL,MAAP;AAAA,cAAazC,MAAb;;AAEA,cAAIsP,QAAQ,IAAIjP,IAAI,CAACgO,OAAL,CAAarO,MAAb,EAAmBsP,QAAnB,MAAiC,CAAjD,EAAoD;AAClD;AACD;;AAED,cACG,CAACjM,KAAD,IAAUrE,MAAM,CAACJ,MAAP,CAAcN,MAAd,EAAsBmE,MAAtB,CAAX,IACC,CAACpC,IAAI,CAAC4T,QAAL,CAAcjU,MAAd,EAAoB6L,KAAK,CAAC7L,IAA1B,CAAD,IAAoC,CAACK,IAAI,CAAC4T,QAAL,CAAcjU,MAAd,EAAoB6H,GAAG,CAAC7H,IAAxB,CAFxC,EAGE;AACA6K,YAAAA,OAAO,CAACzK,IAAR,CAAa0C,KAAb;AACAwM,YAAAA,QAAQ,GAAGtP,MAAX;AACD;AACF;;;;;;;AAED,UAAMf,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAWqG,OAAX,EAAoB;AAAA;AAAA,YAAInG,CAAJ;;AAAA,eAAW1F,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBoG,CAAvB,CAAX;AAAA,OAApB,CAAjB;AACA,UAAMuW,QAAQ,GAAGjc,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuN,KAAxB,CAAjB;AACA,UAAMqP,MAAM,GAAGlc,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuJ,GAAxB,CAAf;;AAEA,UAAI,CAACiT,YAAD,IAAiB,CAACC,SAAtB,EAAiC;AAC/B,YAAM1P,MAAK,GAAG4P,QAAQ,CAACrL,OAAvB;;AACA,2BAAe5Q,MAAM,CAACiO,IAAP,CAAY3O,MAAZ,EAAoB+M,MAApB,CAAf;AAAA;AAAA,YAAO5I,IAAP;;AACA,YAAQzC,IAAR,GAAiBqL,MAAjB,CAAQrL,IAAR;AACA,qBAAmB6L,KAAnB;AAAA,YAAQkB,MAAR,UAAQA,MAAR;AACA,YAAMnK,IAAI,GAAGH,IAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgBsF,MAAhB,CAAb;AACA,YAAInK,IAAI,CAACK,MAAL,GAAc,CAAlB,EACE3E,MAAM,CAACQ,KAAP,CAAa;AAAE+B,UAAAA,IAAI,EAAE,aAAR;AAAuBb,UAAAA,IAAI,EAAJA,IAAvB;AAA6B+M,UAAAA,MAAM,EAANA,MAA7B;AAAqCnK,UAAAA,IAAI,EAAJA;AAArC,SAAb;AACH;;AAED,mCAAsB3D,QAAtB,+BAAgC;AAA3B,YAAMyQ,OAAO,gBAAb;;AACH,YAAM1P,KAAI,GAAG0P,OAAO,CAACG,KAAR,EAAb;;AACAlP,QAAAA,UAAU,CAACmD,WAAX,CAAuBxF,MAAvB,EAA+B;AAAE6E,UAAAA,EAAE,EAAEnD,KAAN;AAAYqD,UAAAA,KAAK,EAALA;AAAZ,SAA/B;AACD;;AAED,UAAI,CAAC2X,OAAL,EAAc;AACZ,YAAM3P,OAAK,GAAG6P,MAAM,CAACtL,OAArB;;AACA,4BAAe5Q,MAAM,CAACiO,IAAP,CAAY3O,MAAZ,EAAoB+M,OAApB,CAAf;AAAA;AAAA,YAAO5I,KAAP;;AACA,YAAQzC,MAAR,GAAiBqL,OAAjB,CAAQrL,IAAR;;AACA,YAAM+M,OAAM,GAAG+N,YAAY,GAAGjP,KAAK,CAACkB,MAAT,GAAkB,CAA7C;;AACA,YAAMnK,KAAI,GAAGH,KAAI,CAACG,IAAL,CAAU6E,KAAV,CAAgBsF,OAAhB,EAAwBlF,GAAG,CAACkF,MAA5B,CAAb;;AACA,YAAInK,KAAI,CAACK,MAAL,GAAc,CAAlB,EACE3E,MAAM,CAACQ,KAAP,CAAa;AAAE+B,UAAAA,IAAI,EAAE,aAAR;AAAuBb,UAAAA,IAAI,EAAJA,MAAvB;AAA6B+M,UAAAA,MAAM,EAANA,OAA7B;AAAqCnK,UAAAA,IAAI,EAAJA;AAArC,SAAb;AACH;;AAED,UACE,CAACkY,YAAD,IACAD,cADA,IAEAK,MAAM,CAACtL,OAFP,IAGAqL,QAAQ,CAACrL,OAJX,EAKE;AACAjP,QAAAA,UAAU,CAACuD,UAAX,CAAsB5F,MAAtB,EAA8B;AAC5B6E,UAAAA,EAAE,EAAE+X,MAAM,CAACtL,OADiB;AAE5BwH,UAAAA,OAAO,EAAE,IAFmB;AAG5B/T,UAAAA,KAAK,EAALA;AAH4B,SAA9B;AAKD;;AAED,UAAMgI,KAAK,GAAGxJ,OAAO,GACjBoZ,QAAQ,CAACpL,KAAT,MAAoBqL,MAAM,CAACrL,KAAP,EADH,GAEjBqL,MAAM,CAACrL,KAAP,MAAkBoL,QAAQ,CAACpL,KAAT,EAFtB;;AAIA,UAAI5E,OAAO,CAAC9H,EAAR,IAAc,IAAd,IAAsBkI,KAA1B,EAAiC;AAC/B1K,QAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0B+M,KAA1B;AACD;AACF,KAnKD;AAoKD,GApL2C;;AAsL5C;;;AAIA9I,EAAAA,cA1L4C,0BA2L1CjE,MA3L0C,EA4L1C6D,QA5L0C;QA6L1C8I,8EAII;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,8BAA2C2M,OAA3C,CAAQmM,OAAR;AAAA,UAAQA,OAAR,kCAAkB,KAAlB;AAAA,4BAA2CnM,OAA3C,CAAyB5H,KAAzB;AAAA,UAAyBA,KAAzB,gCAAiC,KAAjC;AACA,yBAAgC4H,OAAhC,CAAM9H,EAAN;AAAA,UAAMA,EAAN,6BAAW7E,MAAM,CAACG,SAAlB;;AAEA,UAAI,CAAC0D,QAAQ,CAACc,MAAd,EAAsB;AACpB;AACD;;AAED,UAAI,CAACE,EAAL,EAAS;AACP;AACD,OAFD,MAEO,IAAIhC,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AAC5B,YAAI,CAACiU,OAAL,EAAc;AACZjU,UAAAA,EAAE,GAAGnE,MAAM,CAAC6R,WAAP,CAAmBvS,MAAnB,EAA2B6E,EAA3B,CAAL;AACD;;AAED,YAAIhC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACiI,MAAR;AACD,SAFD,MAEO;AACL,8BAAgBjK,KAAK,CAAC2K,KAAN,CAAY3I,EAAZ,CAAhB;AAAA;AAAA,cAAS0E,GAAT;;AAEA,cAAI,CAACxE,KAAD,IAAUrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,YAAAA,EAAE,EAAE0E;AAAN,WAApB,CAAd,EAAgD;AAC9C;AACD;;AAED,cAAMkI,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuJ,GAAxB,CAAjB;AACAlH,UAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAE6E,YAAAA,EAAE,EAAFA;AAAF,WAA1B;AACAA,UAAAA,EAAE,GAAG4M,QAAQ,CAACF,KAAT,EAAL;AACD;AACF,OAlBM,MAkBA,IAAIxP,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AAC1BA,QAAAA,EAAE,GAAGnE,MAAM,CAAC6M,KAAP,CAAavN,MAAb,EAAqB6E,EAArB,CAAL;AACD;;AAED,UAAI,CAACE,KAAD,IAAUrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,QAAAA,EAAE,EAAFA;AAAF,OAApB,CAAd,EAA2C;AACzC;AACD;AAGD;;;AACA,UAAMgY,kBAAkB,GAAGnc,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AAC9C6E,QAAAA,EAAE,EAAFA,EAD8C;AAE9C7B,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAAJ;AAAA,SAFsC;AAG9C0H,QAAAA,IAAI,EAAE,SAHwC;AAI9C7H,QAAAA,KAAK,EAALA;AAJ8C,OAArB,CAA3B;;AAOA,UAAI8X,kBAAJ,EAAwB;AACtB,iDAAuBA,kBAAvB;AAAA,YAASC,WAAT;;AAEA,YAAIpc,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqB6E,EAArB,EAAyBiY,WAAzB,CAAJ,EAA0C;AACxC,cAAMjQ,KAAK,GAAGnM,MAAM,CAACmM,KAAP,CAAa7M,MAAb,EAAqB8c,WAArB,CAAd;AACAjY,UAAAA,EAAE,GAAGgI,KAAL;AACD,SAHD,MAGO,IAAInM,MAAM,CAAC4N,OAAP,CAAetO,MAAf,EAAuB6E,EAAvB,EAA2BiY,WAA3B,CAAJ,EAA4C;AACjD,cAAMxP,MAAM,GAAG5M,MAAM,CAAC4M,MAAP,CAActN,MAAd,EAAsB8c,WAAtB,CAAf;AACAjY,UAAAA,EAAE,GAAGyI,MAAL;AACD;AACF;;AAED,UAAMyP,UAAU,GAAGrc,MAAM,CAACgM,KAAP,CAAa1M,MAAb,EAAqB;AACtCgD,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAD8B;AAEtCL,QAAAA,EAAE,EAAFA,EAFsC;AAGtCE,QAAAA,KAAK,EAALA;AAHsC,OAArB,CAAnB;;AAKA,uCAAsBgY,UAAtB;AAAA,UAAS/N,SAAT;;AACA,UAAMgO,YAAY,GAAGtc,MAAM,CAAC4N,OAAP,CAAetO,MAAf,EAAuB6E,EAAvB,EAA2BmK,SAA3B,CAArB;AACA,UAAMiO,UAAU,GAAGvc,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqB6E,EAArB,EAAyBmK,SAAzB,CAAnB;AACA,UAAMkO,YAAY,GAAGF,YAAY,IAAIC,UAArC;AACA,UAAME,UAAU,GAAG,CAACH,YAAD,IAAkBA,YAAY,IAAIC,UAArD;AACA,UAAMG,QAAQ,GAAG,CAACH,UAAlB;;AACA,wBAAsBrZ,IAAI,CAAC6J,KAAL,CAAW;AAAExN,QAAAA,QAAQ,EAAE4D;AAAZ,OAAX,EAAmC,EAAnC,CAAtB;AAAA;AAAA,UAASkN,SAAT;;AACA,uBAAqBnN,IAAI,CAAC8K,IAAL,CAAU;AAAEzO,QAAAA,QAAQ,EAAE4D;AAAZ,OAAV,EAAkC,EAAlC,CAArB;AAAA;AAAA,UAASmN,QAAT;;AAEA,UAAMzE,OAAO,GAAgB,EAA7B;;AACA,UAAM8Q,OAAO,GAAG,SAAVA,OAAU;;YAAEnY;YAAGkB;;AACnB,YAAMkX,MAAM,GAAGlX,CAAC,CAACzB,MAAF,KAAa,CAA5B;;AACA,YAAI2Y,MAAJ,EAAY;AACV,iBAAO,KAAP;AACD;;AAED,YAAIJ,YAAJ,EAAkB;AAChB,iBAAO,IAAP;AACD;;AAED,YACEC,UAAU,IACVpb,IAAI,CAAC6J,UAAL,CAAgBxF,CAAhB,EAAmB2K,SAAnB,CADA,IAEAtM,OAAO,CAACC,SAAR,CAAkBQ,CAAlB,CAFA,IAGA,CAAClF,MAAM,CAACM,MAAP,CAAc4E,CAAd,CAHD,IAIA,CAAClF,MAAM,CAACK,QAAP,CAAgB6E,CAAhB,CALH,EAME;AACA,iBAAO,KAAP;AACD;;AAED,YACEkY,QAAQ,IACRrb,IAAI,CAAC6J,UAAL,CAAgBxF,CAAhB,EAAmB4K,QAAnB,CADA,IAEAvM,OAAO,CAACC,SAAR,CAAkBQ,CAAlB,CAFA,IAGA,CAAClF,MAAM,CAACM,MAAP,CAAc4E,CAAd,CAHD,IAIA,CAAClF,MAAM,CAACK,QAAP,CAAgB6E,CAAhB,CALH,EAME;AACA,iBAAO,KAAP;AACD;;AAED,eAAO,IAAP;AACD,OA/BD;;kDAiCoBtB,IAAI,CAACuC,KAAL,CAClB;AAAElG,QAAAA,QAAQ,EAAE4D;AAAZ,OADkB,EAElB;AAAE+L,QAAAA,IAAI,EAAEyN;AAAR,OAFkB;;;;AAApB,+DAGG;AAAA,cAHQ7Y,KAGR;;AACD,cAAI6Y,OAAO,CAAC7Y,KAAD,CAAX,EAAoB;AAClB+H,YAAAA,OAAO,CAACzK,IAAR,CAAa0C,KAAb;AACD;AACF;;;;;;;AAED,UAAM+Y,MAAM,GAAG,EAAf;AACA,UAAMC,OAAO,GAAG,EAAhB;AACA,UAAMC,IAAI,GAAG,EAAb;AACA,UAAIC,QAAQ,GAAG,IAAf;AACA,UAAIhQ,SAAS,GAAG,KAAhB;;AAEA,mCAAqBnB,OAArB,gCAA8B;AAAzB;AAAA,YAAOpI,IAAP;;AACH,YAAIM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2B,CAACnE,MAAM,CAACK,QAAP,CAAgB8D,IAAhB,CAAhC,EAAuD;AACrDuZ,UAAAA,QAAQ,GAAG,KAAX;AACAhQ,UAAAA,SAAS,GAAG,IAAZ;AACA8P,UAAAA,OAAO,CAAC1b,IAAR,CAAaqC,IAAb;AACD,SAJD,MAIO,IAAIuZ,QAAJ,EAAc;AACnBH,UAAAA,MAAM,CAACzb,IAAP,CAAYqC,IAAZ;AACD,SAFM,MAEA;AACLsZ,UAAAA,IAAI,CAAC3b,IAAL,CAAUqC,IAAV;AACD;AACF;;AAED,0BAAsBzD,MAAM,CAACyF,KAAP,CAAanG,MAAb,EAAqB;AACzC6E,QAAAA,EAAE,EAAFA,EADyC;AAEzC7B,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAAtB;AAAA,SAFiC;AAGzC0H,QAAAA,IAAI,EAAE,SAHmC;AAIzC7H,QAAAA,KAAK,EAALA;AAJyC,OAArB,CAAtB;AAAA;AAAA,UAAO4Y,WAAP;;AAOA,wCAAuBA,WAAvB;AAAA,UAASb,UAAT;;AACA,UAAMc,aAAa,GAAGld,MAAM,CAAC4N,OAAP,CAAetO,MAAf,EAAuB6E,EAAvB,EAA2BiY,UAA3B,CAAtB;AACA,UAAMe,WAAW,GAAGnd,MAAM,CAACyN,KAAP,CAAanO,MAAb,EAAqB6E,EAArB,EAAyBiY,UAAzB,CAApB;AAEA,UAAMgB,SAAS,GAAGpd,MAAM,CAAC0Q,OAAP,CAChBpR,MADgB,EAEhBid,UAAU,GAAGlb,IAAI,CAACgF,IAAL,CAAUiI,SAAV,CAAH,GAA0BA,SAFpB,CAAlB;AAKA,UAAM4N,MAAM,GAAGlc,MAAM,CAAC0Q,OAAP,CACbpR,MADa,EAEb6d,WAAW,GAAG9b,IAAI,CAACgF,IAAL,CAAU+V,UAAV,CAAH,GAA2BA,UAFzB,CAAf;AAKA,UAAMiB,YAAY,GAAGrd,MAAM,CAAC0Q,OAAP,CAAepR,MAAf,EAAuBgP,SAAvB,CAArB;AAEA3M,MAAAA,UAAU,CAAC0B,UAAX,CAAsB/D,MAAtB,EAA8B;AAC5B6E,QAAAA,EAAE,EAAFA,EAD4B;AAE5B7B,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBACNwI,SAAS,GACLhN,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CADK,GAELjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAHhB;AAAA,SAFoB;AAM5B0H,QAAAA,IAAI,EAAEc,SAAS,GAAG,QAAH,GAAc,SAND;AAO5B3I,QAAAA,KAAK,EAALA;AAP4B,OAA9B;AAUA,UAAM4X,QAAQ,GAAGjc,MAAM,CAAC0Q,OAAP,CACfpR,MADe,EAEf,CAAC4d,aAAD,IAAmBA,aAAa,IAAIC,WAApC,GACI9b,IAAI,CAACgF,IAAL,CAAU+V,UAAV,CADJ,GAEIA,UAJW,CAAjB;AAOAza,MAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+Bud,MAA/B,EAAuC;AACrC1Y,QAAAA,EAAE,EAAE8X,QAAQ,CAACrL,OADwB;AAErCtO,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAAtB;AAAA,SAF6B;AAGrC0H,QAAAA,IAAI,EAAE,SAH+B;AAIrC7H,QAAAA,KAAK,EAALA;AAJqC,OAAvC;;AAOA,UAAImY,YAAY,IAAIM,OAAO,CAAC7Y,MAA5B,EAAoC;AAClCtC,QAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAE6E,UAAAA,EAAE,EAAEkZ,YAAY,CAACxM,KAAb,EAAN;AAA6BxM,UAAAA,KAAK,EAALA;AAA7B,SAA1B;AACD;;AAED1C,MAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+Bwd,OAA/B,EAAwC;AACtC3Y,QAAAA,EAAE,EAAEiZ,SAAS,CAACxM,OADwB;AAEtCtO,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIxE,MAAM,CAACkN,OAAP,CAAe5N,MAAf,EAAuBkF,CAAvB,CAAJ;AAAA,SAF8B;AAGtC0H,QAAAA,IAAI,EAAE,QAHgC;AAItC7H,QAAAA,KAAK,EAALA;AAJsC,OAAxC;AAOA1C,MAAAA,UAAU,CAAC+B,WAAX,CAAuBpE,MAAvB,EAA+Byd,IAA/B,EAAqC;AACnC5Y,QAAAA,EAAE,EAAE+X,MAAM,CAACtL,OADwB;AAEnCtO,QAAAA,KAAK,EAAE,eAAAkC,CAAC;AAAA,iBAAIjC,IAAI,CAACC,MAAL,CAAYgC,CAAZ,KAAkBxE,MAAM,CAACL,QAAP,CAAgBL,MAAhB,EAAwBkF,CAAxB,CAAtB;AAAA,SAF2B;AAGnC0H,QAAAA,IAAI,EAAE,SAH6B;AAInC7H,QAAAA,KAAK,EAALA;AAJmC,OAArC;;AAOA,UAAI,CAAC4H,OAAO,CAAC9H,EAAb,EAAiB;AACf,YAAInD,IAAJ;;AAEA,YAAI+b,IAAI,CAAC9Y,MAAL,GAAc,CAAlB,EAAqB;AACnBjD,UAAAA,IAAI,GAAGK,IAAI,CAACwE,QAAL,CAAcqW,MAAM,CAACtL,OAArB,CAAP;AACD,SAFD,MAEO,IAAIkM,OAAO,CAAC7Y,MAAR,GAAiB,CAArB,EAAwB;AAC7BjD,UAAAA,IAAI,GAAGK,IAAI,CAACwE,QAAL,CAAcuX,SAAS,CAACxM,OAAxB,CAAP;AACD,SAFM,MAEA;AACL5P,UAAAA,IAAI,GAAGK,IAAI,CAACwE,QAAL,CAAcoW,QAAQ,CAACrL,OAAvB,CAAP;AACD;;AAED,YAAM/H,KAAG,GAAG7I,MAAM,CAAC6I,GAAP,CAAWvJ,MAAX,EAAmB0B,IAAnB,CAAZ;;AACAW,QAAAA,UAAU,CAAC0W,MAAX,CAAkB/Y,MAAlB,EAA0BuJ,KAA1B;AACD;;AAEDoT,MAAAA,QAAQ,CAACpL,KAAT;AACAuM,MAAAA,SAAS,CAACvM,KAAV;AACAqL,MAAAA,MAAM,CAACrL,KAAP;AACD,KAvND;AAwND,GA3Z2C;;AA6Z5C;;;AAIAlN,EAAAA,UAja4C,sBAka1CrE,MAla0C,EAma1CsE,IAna0C;QAoa1CqI,8EAGI;AAEJjM,IAAAA,MAAM,CAAC8P,kBAAP,CAA0BxQ,MAA1B,EAAkC;AAChC,4BAA0B2M,OAA1B,CAAQ5H,KAAR;AAAA,UAAQA,KAAR,gCAAgB,KAAhB;AACA,yBAAgC4H,OAAhC,CAAM9H,EAAN;AAAA,UAAMA,EAAN,6BAAW7E,MAAM,CAACG,SAAlB;;AAEA,UAAI,CAAC0E,EAAL,EAAS;AACP;AACD;;AAED,UAAI9C,IAAI,CAACqN,MAAL,CAAYvK,EAAZ,CAAJ,EAAqB;AACnBA,QAAAA,EAAE,GAAGnE,MAAM,CAACwM,KAAP,CAAalN,MAAb,EAAqB6E,EAArB,CAAL;AACD;;AAED,UAAIhC,KAAK,CAACmL,OAAN,CAAcnJ,EAAd,CAAJ,EAAuB;AACrB,YAAIhC,KAAK,CAACS,WAAN,CAAkBuB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACiI,MAAR;AACD,SAFD,MAEO;AACL,cAAMvD,GAAG,GAAG1G,KAAK,CAAC0G,GAAN,CAAU1E,EAAV,CAAZ;;AACA,cAAI,CAACE,KAAD,IAAUrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,YAAAA,EAAE,EAAE0E;AAAN,WAApB,CAAd,EAAgD;AAC9C;AACD;;AACD,cAAMgE,KAAK,GAAG1K,KAAK,CAAC0K,KAAN,CAAY1I,EAAZ,CAAd;AACA,cAAM4M,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAP,CAAgBzR,MAAhB,EAAwBuN,KAAxB,CAAjB;AACAlL,UAAAA,UAAU,UAAV,CAAkBrC,MAAlB,EAA0B;AAAE6E,YAAAA,EAAE,EAAFA,EAAF;AAAME,YAAAA,KAAK,EAALA;AAAN,WAA1B;AACAF,UAAAA,EAAE,GAAG4M,QAAQ,CAACF,KAAT,EAAL;AACAlP,UAAAA,UAAU,CAACyZ,YAAX,CAAwB9b,MAAxB,EAAgC;AAAE8M,YAAAA,MAAM,EAAEjI,EAAV;AAAcoI,YAAAA,KAAK,EAAEpI;AAArB,WAAhC;AACD;AACF;;AAED,UAAI,CAACE,KAAD,IAAUrE,MAAM,QAAN,CAAYV,MAAZ,EAAoB;AAAE6E,QAAAA,EAAE,EAAFA;AAAF,OAApB,CAAd,EAA2C;AACzC;AACD;;AAED,gBAAyBA,EAAzB;AAAA,UAAQnD,IAAR,OAAQA,IAAR;AAAA,UAAc+M,MAAd,OAAcA,MAAd;AACA,UAAInK,IAAI,CAACK,MAAL,GAAc,CAAlB,EACE3E,MAAM,CAACQ,KAAP,CAAa;AAAE+B,QAAAA,IAAI,EAAE,aAAR;AAAuBb,QAAAA,IAAI,EAAJA,IAAvB;AAA6B+M,QAAAA,MAAM,EAANA,MAA7B;AAAqCnK,QAAAA,IAAI,EAAJA;AAArC,OAAb;AACH,KAnCD;AAoCD;AA7c2C,CAAvC;;;;;ICvCMjC,UAAU,+DAIlBoW,iBAJkB,GAKlBI,cALkB,GAMlB4C,mBANkB,GAOlBU,cAPkB;;;;;;;;;;;;;;;;;;"}