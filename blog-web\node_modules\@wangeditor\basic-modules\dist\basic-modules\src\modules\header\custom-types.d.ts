/**
 * @description 自定义 element
 * <AUTHOR>
 */
import { Text } from 'slate';
export declare type Header1Element = {
    type: 'header1';
    children: Text[];
};
export declare type Header2Element = {
    type: 'header2';
    children: Text[];
};
export declare type Header3Element = {
    type: 'header3';
    children: Text[];
};
export declare type Header4Element = {
    type: 'header4';
    children: Text[];
};
export declare type Header5Element = {
    type: 'header5';
    children: Text[];
};
