/* eslint-disable */
import type { Locale } from '@uppy/core'

type CoreLocale = Locale<
    | 'addBulkFilesFailed'
| 'youCanOnlyUploadX'
| 'youHaveToAtLeastSelectX'
| 'exceedsSize'
| 'missingRequiredMetaField'
| 'missingRequiredMetaFieldOnFile'
| 'inferiorSize'
| 'youCanOnlyUploadFileTypes'
| 'noMoreFilesAllowed'
| 'noDuplicates'
| 'companionError'
| 'authAborted'
| 'companionUnauthorizeHint'
| 'failedToUpload'
| 'noInternetConnection'
| 'connectedToInternet'
| 'noFilesFound'
| 'selectX'
| 'allFilesFromFolderNamed'
| 'openFolderNamed'
| 'cancel'
| 'logOut'
| 'filter'
| 'resetFilter'
| 'loading'
| 'authenticateWithTitle'
| 'authenticateWith'
| 'signInWithGoogle'
| 'searchImages'
| 'enterTextToSearch'
| 'search'
| 'emptyFolderAdded'
| 'folderAlreadyAdded'
| 'folderAdded'
>

export default CoreLocale