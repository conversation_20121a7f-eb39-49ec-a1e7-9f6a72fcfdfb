<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.mapper.ArticleMapper">

    <!-- 根据用户名查询文章列表 -->
    <select id="selectByUsername" resultType="com.blog.entity.Article">
        SELECT a.*
        FROM article a
        JOIN user u ON a.author_id = u.id
        WHERE u.username = #{username}
        AND a.del_flag = 0
        ORDER BY a.is_top DESC, a.create_time DESC
    </select>

</mapper> 