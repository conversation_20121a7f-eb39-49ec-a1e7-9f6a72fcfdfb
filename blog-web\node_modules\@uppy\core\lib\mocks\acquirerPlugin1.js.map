{"version": 3, "sources": ["acquirerPlugin1.js"], "names": ["UIPlugin", "TestSelector1", "constructor", "uppy", "opts", "type", "id", "name", "mocks", "run", "jest", "fn", "update", "uninstall", "results", "log", "class", "method", "Promise", "resolve", "state"], "mappings": ";;AAAA;;AAAqC;MAC9BA,Q;;AAEQ,MAAMC,aAAN,SAA4BD,QAA5B,CAAqC;AAClDE,EAAAA,WAAW,CAAEC,IAAF,EAAQC,IAAR,EAAc;AACvB,UAAMD,IAAN,EAAYC,IAAZ;AACA,SAAKC,IAAL,GAAY,UAAZ;AACA,SAAKC,EAAL,GAAU,eAAV;AACA,SAAKC,IAAL,GAAY,KAAKL,WAAL,CAAiBK,IAA7B;AAEA,SAAKC,KAAL,GAAa;AACXC,MAAAA,GAAG,EAAEC,cAAKC,EAAL,EADM;AAEXC,MAAAA,MAAM,EAAEF,cAAKC,EAAL,EAFG;AAGXE,MAAAA,SAAS,EAAEH,cAAKC,EAAL;AAHA,KAAb;AAKD;;AAEDF,EAAAA,GAAG,CAAEK,OAAF,EAAW;AACZ,SAAKX,IAAL,CAAUY,GAAV,CAAc;AACZC,MAAAA,KAAK,EAAE,KAAKd,WAAL,CAAiBK,IADZ;AAEZU,MAAAA,MAAM,EAAE,KAFI;AAGZH,MAAAA;AAHY,KAAd;AAKA,SAAKN,KAAL,CAAWC,GAAX,CAAeK,OAAf;AACA,WAAOI,OAAO,CAACC,OAAR,CAAgB,SAAhB,CAAP;AACD;;AAEDP,EAAAA,MAAM,CAAEQ,KAAF,EAAS;AACb,SAAKZ,KAAL,CAAWI,MAAX,CAAkBQ,KAAlB;AACD;;AAEDP,EAAAA,SAAS,GAAI;AACX,SAAKL,KAAL,CAAWK,SAAX;AACD;;AA9BiD;;iBAA/BZ,a", "sourcesContent": ["import { jest } from '@jest/globals' // eslint-disable-line import/no-extraneous-dependencies\nimport UIPlugin from '../UIPlugin.js'\n\nexport default class TestSelector1 extends UIPlugin {\n  constructor (uppy, opts) {\n    super(uppy, opts)\n    this.type = 'acquirer'\n    this.id = 'TestSelector1'\n    this.name = this.constructor.name\n\n    this.mocks = {\n      run: jest.fn(),\n      update: jest.fn(),\n      uninstall: jest.fn(),\n    }\n  }\n\n  run (results) {\n    this.uppy.log({\n      class: this.constructor.name,\n      method: 'run',\n      results,\n    })\n    this.mocks.run(results)\n    return Promise.resolve('success')\n  }\n\n  update (state) {\n    this.mocks.update(state)\n  }\n\n  uninstall () {\n    this.mocks.uninstall()\n  }\n}\n"]}