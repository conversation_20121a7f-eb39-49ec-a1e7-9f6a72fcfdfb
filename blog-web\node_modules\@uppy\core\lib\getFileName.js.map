{"version": 3, "sources": ["getFileName.js"], "names": ["getFileName", "fileType", "fileDescriptor", "name", "split"], "mappings": ";;AAAe,SAASA,WAAT,CAAsBC,QAAtB,EAAgCC,cAAhC,EAAgD;AAC7D,MAAIA,cAAc,CAACC,IAAnB,EAAyB;AACvB,WAAOD,cAAc,CAACC,IAAtB;AACD;;AAED,MAAIF,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,MAA2B,OAA/B,EAAwC;AACtC,WAAQ,GAAEH,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,IAAGH,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAA3D;AACD;;AAED,SAAO,QAAP;AACD;;iBAVuBJ,W", "sourcesContent": ["export default function getFileName (fileType, fileDescriptor) {\n  if (fileDescriptor.name) {\n    return fileDescriptor.name\n  }\n\n  if (fileType.split('/')[0] === 'image') {\n    return `${fileType.split('/')[0]}.${fileType.split('/')[1]}`\n  }\n\n  return 'noname'\n}\n"]}