<template>
  <div class="article-edit">
    <h1>编辑文章</h1>
    <div class="form-container">
      <el-form v-loading="loading" :model="articleForm" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="articleForm.title" placeholder="请输入文章标题"></el-input>
        </el-form-item>
        <el-form-item label="封面">
          <el-upload
            class="cover-uploader"
            action="/api/upload"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload">
            <img v-if="articleForm.coverImage" :src="articleForm.coverImage" class="cover-image" />
            <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="articleForm.categoryId" placeholder="请选择分类">
            <el-option 
              v-for="item in categories" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="articleForm.tagIds"
            multiple
            filterable
            placeholder="请选择或输入新标签"
            style="width: 100%">
            <el-option
              v-for="item in allTags"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="摘要">
          <el-input 
            v-model="articleForm.summary" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入文章摘要">
          </el-input>
        </el-form-item>
        <!-- 文章内容编辑器 - 使用Markdown格式编写文章正文，支持富文本编辑和实时预览 -->
        <el-form-item label="内容" prop="content">
          <div class="editor-container">
            <!-- 编辑器使用说明 -->
            <div class="editor-guide">
              <div class="guide-title">
                <i class="el-icon-question"></i>
                Markdown编辑器使用指南
              </div>
              <div class="guide-content">
                <div class="guide-row">
                  <span class="guide-label">工具栏功能：</span>
                  <span class="guide-text">标题、加粗、斜体、删除线、引用、列表、表格、图片、链接、代码块等</span>
                </div>
                <div class="guide-row">
                  <span class="guide-label">编辑模式：</span>
                  <span class="guide-text">左侧编辑区域输入Markdown语法，右侧实时预览渲染效果</span>
                </div>
                <div class="guide-row">
                  <span class="guide-label">快捷语法：</span>
                  <span class="guide-text"># 标题、**粗体**、*斜体*、`代码`、> 引用、- 列表</span>
                </div>
                <div class="guide-row">
                  <span class="guide-label">图片插入：</span>
                  <span class="guide-text">点击工具栏图片按钮或使用 ![描述](图片链接) 语法</span>
                </div>
              </div>
            </div>
            <!-- Markdown编辑器组件 -->
            <MarkdownEditor v-model="articleForm.content" height="500px" />
            <!-- 编辑提示 -->
            <div class="editor-tips">
              <div class="tip-item">
                <i class="el-icon-info"></i>
                支持拖拽上传图片，自动生成Markdown图片语法
              </div>
              <div class="tip-item">
                <i class="el-icon-warning"></i>
                建议文章内容不少于100字，确保内容完整性
              </div>
              <div class="tip-item">
                <i class="el-icon-success"></i>
                编辑器支持全屏模式，点击工具栏全屏按钮获得更好的编辑体验
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="articleForm.status">
            <el-radio :label="1">发布</el-radio>
            <el-radio :label="0">草稿</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="置顶">
          <el-switch v-model="articleForm.isTop" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="允许评论">
          <el-switch v-model="articleForm.allowComment" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存修改</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import { getArticleDetail, updateArticle } from '@/api/article'
import { getCategories } from '@/api/category'
import { getTags } from '@/api/tag'
import { useUserStore } from '@/store/user'
import { buildResourceUrl } from '@/config/settings'

export default {
  name: 'ArticleEdit',
  components: {
    MarkdownEditor,
    Plus
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const submitting = ref(false)
    const formRef = ref(null)
    const allTags = ref([])
    
    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入文章标题', trigger: 'blur' },
        { min: 2, max: 100, message: '标题长度在2到100个字符之间', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入文章内容', trigger: 'blur' }
      ]
    }
    
    const articleForm = reactive({
      id: '',
      title: '',
      categoryId: null,
      tagIds: [],
      summary: '',
      content: '',
      coverImage: '',
      status: 1,
      isTop: 0,
      allowComment: 1
    })
    
    const categories = ref([])
    
    // 使用Pinia的用户store
    const userStore = useUserStore()
    const uploadHeaders = computed(() => {
      return {
        Authorization: `Bearer ${userStore.token}`
      }
    })
    
    // 获取文章详情
    const fetchArticle = async (id) => {
      loading.value = true
      try {
        const res = await getArticleDetail(id)
        if (res.code === 200) {
          const article = res.data
          articleForm.id = article.id
          articleForm.title = article.title
          articleForm.categoryId = article.categoryId
          articleForm.tagIds = article.tags ? article.tags.map(tag => tag.id) : []
          articleForm.summary = article.summary
          articleForm.content = article.content
          
          // 处理封面图片URL
          let coverImage = article.coverImage;
          
          // 使用统一的资源URL构建函数
          if (coverImage) {
            articleForm.coverImage = buildResourceUrl(coverImage);
          }
          
          articleForm.status = article.status
          articleForm.isTop = article.isTop
          articleForm.allowComment = article.allowComment
        } else {
          ElMessage.error(res.message || '获取文章详情失败')
        }
      } catch (error) {
        console.error('获取文章详情失败', error)
        ElMessage.error('获取文章详情失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取分类列表
    const fetchCategories = async () => {
      try {
        const res = await getCategories()
        if (res.code === 200) {
          categories.value = res.data
        } else {
          ElMessage.error(res.message || '获取分类列表失败')
        }
      } catch (error) {
        console.error('获取分类列表失败', error)
        ElMessage.error('获取分类列表失败')
      }
    }
    
    // 获取标签列表
    const fetchTags = async () => {
      try {
        const res = await getTags()
        if (res.code === 200) {
          allTags.value = res.data
        } else {
          ElMessage.error(res.message || '获取标签列表失败')
        }
      } catch (error) {
        console.error('获取标签列表失败', error)
        ElMessage.error('获取标签列表失败')
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          submitting.value = true
          try {
            const res = await updateArticle(articleForm.id, articleForm)
            if (res.code === 200) {
              ElMessage.success('文章更新成功')
              router.push('/admin/article')
            } else {
              ElMessage.error(res.message || '文章更新失败')
            }
          } catch (error) {
            console.error('文章更新失败', error)
            ElMessage.error('文章更新失败，请稍后重试')
          } finally {
            submitting.value = false
          }
        } else {
          ElMessage.warning('请完善表单信息')
          return false
        }
      })
    }
    
    // 处理封面上传成功
    const handleCoverSuccess = (res) => {
      if (res.code === 200) {
        // 使用统一的资源URL构建函数
        let imageUrl = res.data;
        
        // 调试信息
        console.log('上传成功响应:', res);
        console.log('原始图片URL:', imageUrl);
        
        // 使用统一的资源URL构建函数
        articleForm.coverImage = buildResourceUrl(imageUrl);
        console.log('构建的完整URL:', articleForm.coverImage);
        
        // 创建一个图片元素测试URL是否可访问
        const testImg = new Image();
        testImg.onload = () => console.log('图片URL有效，可以正常加载');
        testImg.onerror = () => console.error('图片URL无效，无法加载');
        testImg.src = articleForm.coverImage;
        
        ElMessage.success('封面上传成功');
      } else {
        ElMessage.error(res.message || '封面上传失败');
      }
    }
    
    // 上传前检查
    const beforeCoverUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('上传封面只能是图片格式!')
      }
      if (!isLt2M) {
        ElMessage.error('上传封面图片大小不能超过 2MB!')
      }
      
      return isImage && isLt2M
    }
    
    const goBack = () => {
      router.back()
    }
    
    onMounted(() => {
      const id = route.params.id
      fetchArticle(id)
      fetchCategories()
      fetchTags()
    })
    
    return {
      loading,
      submitting,
      formRef,
      articleForm,
      rules,
      categories,
      allTags,
      uploadHeaders,
      submitForm,
      goBack,
      handleCoverSuccess,
      beforeCoverUpload
    }
  }
}
</script>

<style scoped>
.article-edit {
  padding: 20px;
}
.form-container {
  max-width: 900px;
  margin-top: 20px;
}
.editor-container {
  width: 100%;
  margin-bottom: 20px;
}
.cover-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
.cover-uploader:hover {
  border-color: #409EFF;
}
.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 编辑器指南样式 */
.editor-guide {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guide-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.guide-title i {
  font-size: 18px;
  color: #fff;
}

.guide-content {
  font-size: 13px;
  line-height: 1.6;
}

.guide-row {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.guide-row:last-child {
  margin-bottom: 0;
}

.guide-label {
  font-weight: bold;
  color: #e8f4fd;
  min-width: 80px;
  flex-shrink: 0;
}

.guide-text {
  color: #ffffff;
  opacity: 0.95;
}

/* 编辑器提示样式 */
.editor-tips {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item i {
  font-size: 14px;
  flex-shrink: 0;
}

.tip-item .el-icon-info {
  color: #409eff;
}

.tip-item .el-icon-warning {
  color: #e6a23c;
}

.tip-item .el-icon-success {
  color: #67c23a;
}
</style> 