{"name": "slate", "description": "A completely customizable framework for building rich text editors.", "version": "0.72.8", "license": "MIT", "repository": "git://github.com/ianstormtaylor/slate.git", "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "umd": "dist/slate.js", "umdMin": "dist/slate.min.js", "sideEffects": false, "files": ["dist/"], "dependencies": {"immer": "^9.0.6", "is-plain-object": "^5.0.0", "tiny-warning": "^1.0.3"}, "devDependencies": {"@babel/runtime": "^7.7.4", "lodash": "^4.17.21", "slate-hyperscript": "^0.67.0", "source-map-loader": "^0.2.4"}, "keywords": ["canvas", "contenteditable", "custom", "document", "edit", "editor", "html", "immutable", "markdown", "medium", "paper", "react", "rich", "richtext", "richtext", "slate", "text", "wysiwyg", "wysiwym"]}