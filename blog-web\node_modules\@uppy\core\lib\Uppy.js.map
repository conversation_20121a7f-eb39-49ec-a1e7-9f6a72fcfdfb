{"version": 3, "sources": ["Uppy.js"], "names": ["Translator", "ee", "throttle", "DefaultStore", "getFileType", "getFileNameAndExtension", "generateFileID", "supportsUploadProgress", "getFileName", "packageJson", "locale", "Symbol", "for", "Uppy", "constructor", "opts", "Object", "create", "Set", "updateOnlineStatus", "bind", "defaultLocale", "defaultOptions", "id", "autoProceed", "allowMultipleUploads", "allowMultipleUploadBatches", "debug", "restrictions", "defaultRestrictionOptions", "meta", "onBeforeFileAdded", "currentFile", "onBeforeUpload", "files", "store", "logger", "just<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infoTimeout", "log", "debugLogger", "VERSION", "i18nInit", "calculateProgress", "leading", "trailing", "setState", "plugins", "currentUploads", "allowNewUpload", "capabilities", "uploadProgress", "individualCancellation", "resumableUploads", "totalProgress", "info", "recoveredState", "Restricter", "i18n", "subscribe", "prevState", "nextState", "patch", "emit", "updateAll", "window", "event", "args", "on", "callback", "once", "off", "state", "iteratePlugins", "plugin", "update", "getState", "setFileState", "fileID", "Error", "translator", "translate", "i18nArray", "translateArray", "setOptions", "newOpts", "setMeta", "resetProgress", "defaultProgress", "percentage", "bytesUploaded", "uploadComplete", "uploadStarted", "updatedFiles", "keys", "for<PERSON>ach", "updatedFile", "progress", "addPreProcessor", "fn", "add", "removePreProcessor", "delete", "addPostProcessor", "removePostProcessor", "addUploader", "removeUploader", "data", "updatedMeta", "setFileMeta", "newMeta", "getFile", "getFiles", "values", "getObjectOfFilesPerState", "filesObject", "error", "inProgressFiles", "filter", "newFiles", "file", "startedFiles", "preprocess", "postprocess", "uploadStartedFiles", "pausedFiles", "isPaused", "completeFiles", "erroredFiles", "inProgressNotPausedFiles", "processingFiles", "isUploadStarted", "length", "isAllComplete", "isAllErrored", "isAllPaused", "isUploadInProgress", "isSomeGhost", "some", "isGhost", "validateRestrictions", "validate", "result", "err", "reason", "message", "checkIfFileAlreadyExists", "addFile", "newFile", "name", "type", "addFiles", "fileDescriptors", "errors", "i", "push", "isRestriction", "subError", "smart_count", "details", "AggregateError", "removeFiles", "fileIDs", "updatedUploads", "removedFiles", "fileIsNotRemoved", "uploadFileID", "undefined", "uploadID", "newFileIDs", "stateUpdate", "calculateTotalProgress", "removedFileIDs", "join", "removeFile", "pauseResume", "wasPaused", "pauseAll", "inProgressUpdatedFiles", "resumeAll", "retryAll", "filesToRetry", "Promise", "resolve", "successful", "failed", "forceAllowNewUpload", "cancelAll", "retryUpload", "reset", "logout", "provider", "canHavePercentage", "Number", "isFinite", "bytesTotal", "Math", "round", "inProgress", "sizedFiles", "unsizedFiles", "progressMax", "currentProgress", "reduce", "acc", "totalSize", "averageSize", "uploadedSize", "online", "navigator", "onLine", "wasOffline", "getID", "use", "Plugin", "msg", "TypeError", "pluginId", "existsPluginAlready", "getPlugin", "install", "foundPlugin", "find", "method", "flat", "removePlugin", "instance", "uninstall", "list", "index", "findIndex", "item", "splice", "updatedState", "close", "removeEventListener", "hideInfo", "slice", "duration", "isComplexMessage", "setTimeout", "warn", "restore", "reject", "addResultData", "currentUpload", "upload", "uploader", "onBeforeUploadResult", "then", "validateMinNumberOfFiles", "catch", "RestrictionError", "currentlyUploadingFiles", "flatMap", "curr", "waitingFileIDs", "indexOf", "trim", "missingFields", "getMissingRequiredMetaFields", "missingRequired<PERSON>etaFields", "success", "fileDescriptor", "fileType", "fileName", "fileExtension", "extension", "isRemote", "Boolean", "size", "source", "remote", "preview", "onBeforeFileAddedResult", "filesArray", "map", "scheduledAutoProceed", "stack", "<PERSON><PERSON><PERSON><PERSON>", "response", "errorMsg", "newError", "Date", "now", "uploadResp", "mode", "uploadURL", "addEventListener", "step", "restoreStep", "steps", "updatedUpload", "version"], "mappings": ";;AAKA;;AAQA;;AACA;;;;;;;;;;AAdA;;AACA;MAEOA,U;;MACAC,E;;MAEAC,Q;;MACAC,Y;;MACAC,W;;MACAC,uB;;MACAC,c;;MACAC,sB;;MACAC,W;;MAQAC,W;;;;MACAC,M;AAEP;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA+oCGC,MAAM,CAACC,GAAP,CAAW,uBAAX,C;eAwKAD,MAAM,CAACC,GAAP,CAAW,yBAAX,C;;AAtzCH,MAAMC,IAAN,CAAW;AAGT;;AAeA;AACF;AACA;AACA;AACA;AACEC,EAAAA,WAAW,CAAEC,KAAF,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAnBRC,MAAM,CAACC,MAAP,CAAc,IAAd;AAmBQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAbRhB,EAAE;AAaM;AAAA;AAAA;AAAA,aAXF,IAAIiB,GAAJ;AAWE;AAAA;AAAA;AAAA,aATN,IAAIA,GAAJ;AASM;AAAA;AAAA;AAAA,aAPD,IAAIA,GAAJ;AAOC;AAAA;AAAA;AAAA,aAkjCG,KAAKC,kBAAL,CAAwBC,IAAxB,CAA6B,IAA7B;AAljCH;AACjB,SAAKC,aAAL,GAAqBX,MAArB;AAEA,UAAMY,cAAc,GAAG;AACrBC,MAAAA,EAAE,EAAE,MADiB;AAErBC,MAAAA,WAAW,EAAE,KAFQ;;AAGrB;AACN;AACA;AACMC,MAAAA,oBAAoB,EAAE,IAND;AAOrBC,MAAAA,0BAA0B,EAAE,IAPP;AAQrBC,MAAAA,KAAK,EAAE,KARc;AASrBC,MAAAA,YAAY,EAAEC,0BATO;AAUrBC,MAAAA,IAAI,EAAE,EAVe;AAWrBC,MAAAA,iBAAiB,EAAGC,WAAD,IAAiBA,WAXf;AAYrBC,MAAAA,cAAc,EAAGC,KAAD,IAAWA,KAZN;AAarBC,MAAAA,KAAK,EAAEhC,YAAY,EAbE;AAcrBiC,MAAAA,MAAM,EAAEC,yBAda;AAerBC,MAAAA,WAAW,EAAE;AAfQ,KAAvB,CAHiB,CAqBjB;AACA;;AACA,SAAKvB,IAAL,GAAY,EACV,GAAGO,cADO;AAEV,SAAGP,KAFO;AAGVa,MAAAA,YAAY,EAAE,EACZ,GAAGN,cAAc,CAACM,YADN;AAEZ,YAAIb,KAAI,IAAIA,KAAI,CAACa,YAAjB;AAFY;AAHJ,KAAZ,CAvBiB,CAgCjB;AACA;;AACA,QAAIb,KAAI,IAAIA,KAAI,CAACqB,MAAb,IAAuBrB,KAAI,CAACY,KAAhC,EAAuC;AACrC,WAAKY,GAAL,CAAS,2KAAT,EAAsL,SAAtL;AACD,KAFD,MAEO,IAAIxB,KAAI,IAAIA,KAAI,CAACY,KAAjB,EAAwB;AAC7B,WAAKZ,IAAL,CAAUqB,MAAV,GAAmBI,oBAAnB;AACD;;AAED,SAAKD,GAAL,CAAU,eAAc,KAAKzB,WAAL,CAAiB2B,OAAQ,EAAjD;AAEA,SAAKC,QAAL,GA1CiB,CA4CjB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAAKC,iBAAL,GAAyBzC,QAAQ,CAAC,KAAKyC,iBAAL,CAAuBvB,IAAvB,CAA4B,IAA5B,CAAD,EAAoC,GAApC,EAAyC;AAAEwB,MAAAA,OAAO,EAAE,IAAX;AAAiBC,MAAAA,QAAQ,EAAE;AAA3B,KAAzC,CAAjC;AAEA,SAAKV,KAAL,GAAa,KAAKpB,IAAL,CAAUoB,KAAvB;AACA,SAAKW,QAAL,CAAc;AACZC,MAAAA,OAAO,EAAE,EADG;AAEZb,MAAAA,KAAK,EAAE,EAFK;AAGZc,MAAAA,cAAc,EAAE,EAHJ;AAIZC,MAAAA,cAAc,EAAE,IAJJ;AAKZC,MAAAA,YAAY,EAAE;AACZC,QAAAA,cAAc,EAAE5C,sBAAsB,EAD1B;AAEZ6C,QAAAA,sBAAsB,EAAE,IAFZ;AAGZC,QAAAA,gBAAgB,EAAE;AAHN,OALF;AAUZC,MAAAA,aAAa,EAAE,CAVH;AAWZxB,MAAAA,IAAI,EAAE,EAAE,GAAG,KAAKf,IAAL,CAAUe;AAAf,OAXM;AAYZyB,MAAAA,IAAI,EAAE,EAZM;AAaZC,MAAAA,cAAc,EAAE;AAbJ,KAAd;AAgBA,kEAAmB,IAAIC,sBAAJ,CAAe,MAAM,KAAK1C,IAA1B,EAAgC,KAAK2C,IAArC,CAAnB;AAEA,8EAAyB,KAAKvB,KAAL,CAAWwB,SAAX,CAAqB,CAACC,SAAD,EAAYC,SAAZ,EAAuBC,KAAvB,KAAiC;AAC7E,WAAKC,IAAL,CAAU,cAAV,EAA0BH,SAA1B,EAAqCC,SAArC,EAAgDC,KAAhD;AACA,WAAKE,SAAL,CAAeH,SAAf;AACD,KAHwB,CAAzB,CAxEiB,CA6EjB;;AACA,QAAI,KAAK9C,IAAL,CAAUY,KAAV,IAAmB,OAAOsC,MAAP,KAAkB,WAAzC,EAAsD;AACpDA,MAAAA,MAAM,CAAC,KAAKlD,IAAL,CAAUQ,EAAX,CAAN,GAAuB,IAAvB;AACD;;AAED;AACD;;AAEDwC,EAAAA,IAAI,CAAEG,KAAF,EAAkB;AAAA,sCAANC,IAAM;AAANA,MAAAA,IAAM;AAAA;;AACpB,0DAAcJ,IAAd,CAAmBG,KAAnB,EAA0B,GAAGC,IAA7B;AACD;;AAEDC,EAAAA,EAAE,CAAEF,KAAF,EAASG,QAAT,EAAmB;AACnB,0DAAcD,EAAd,CAAiBF,KAAjB,EAAwBG,QAAxB;;AACA,WAAO,IAAP;AACD;;AAEDC,EAAAA,IAAI,CAAEJ,KAAF,EAASG,QAAT,EAAmB;AACrB,0DAAcC,IAAd,CAAmBJ,KAAnB,EAA0BG,QAA1B;;AACA,WAAO,IAAP;AACD;;AAEDE,EAAAA,GAAG,CAAEL,KAAF,EAASG,QAAT,EAAmB;AACpB,0DAAcE,GAAd,CAAkBL,KAAlB,EAAyBG,QAAzB;;AACA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEL,EAAAA,SAAS,CAAEQ,KAAF,EAAS;AAChB,SAAKC,cAAL,CAAoBC,MAAM,IAAI;AAC5BA,MAAAA,MAAM,CAACC,MAAP,CAAcH,KAAd;AACD,KAFD;AAGD;AAED;AACF;AACA;AACA;AACA;;;AACE1B,EAAAA,QAAQ,CAAEgB,KAAF,EAAS;AACf,SAAK3B,KAAL,CAAWW,QAAX,CAAoBgB,KAApB;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEc,EAAAA,QAAQ,GAAI;AACV,WAAO,KAAKzC,KAAL,CAAWyC,QAAX,EAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACW,MAALJ,KAAK,GAAI;AACX;AACA,WAAO,KAAKI,QAAL,EAAP;AACD;AAED;AACF;AACA;;;AACEC,EAAAA,YAAY,CAAEC,MAAF,EAAUN,KAAV,EAAiB;AAC3B,QAAI,CAAC,KAAKI,QAAL,GAAgB1C,KAAhB,CAAsB4C,MAAtB,CAAL,EAAoC;AAClC,YAAM,IAAIC,KAAJ,CAAW,uBAAsBD,MAAO,qCAAxC,CAAN;AACD;;AAED,SAAKhC,QAAL,CAAc;AACZZ,MAAAA,KAAK,EAAE,EAAE,GAAG,KAAK0C,QAAL,GAAgB1C,KAArB;AAA4B,SAAC4C,MAAD,GAAU,EAAE,GAAG,KAAKF,QAAL,GAAgB1C,KAAhB,CAAsB4C,MAAtB,CAAL;AAAoC,aAAGN;AAAvC;AAAtC;AADK,KAAd;AAGD;;AAED9B,EAAAA,QAAQ,GAAI;AACV,UAAMsC,UAAU,GAAG,IAAIhF,UAAJ,CAAe,CAAC,KAAKqB,aAAN,EAAqB,KAAKN,IAAL,CAAUL,MAA/B,CAAf,CAAnB;AACA,SAAKgD,IAAL,GAAYsB,UAAU,CAACC,SAAX,CAAqB7D,IAArB,CAA0B4D,UAA1B,CAAZ;AACA,SAAKE,SAAL,GAAiBF,UAAU,CAACG,cAAX,CAA0B/D,IAA1B,CAA+B4D,UAA/B,CAAjB;AACA,SAAKtE,MAAL,GAAcsE,UAAU,CAACtE,MAAzB;AACD;;AAED0E,EAAAA,UAAU,CAAEC,OAAF,EAAW;AACnB,SAAKtE,IAAL,GAAY,EACV,GAAG,KAAKA,IADE;AAEV,SAAGsE,OAFO;AAGVzD,MAAAA,YAAY,EAAE,EACZ,GAAG,KAAKb,IAAL,CAAUa,YADD;AAEZ,YAAIyD,OAAO,IAAIA,OAAO,CAACzD,YAAvB;AAFY;AAHJ,KAAZ;;AASA,QAAIyD,OAAO,CAACvD,IAAZ,EAAkB;AAChB,WAAKwD,OAAL,CAAaD,OAAO,CAACvD,IAArB;AACD;;AAED,SAAKY,QAAL;;AAEA,QAAI2C,OAAO,CAAC3E,MAAZ,EAAoB;AAClB,WAAK+D,cAAL,CAAqBC,MAAD,IAAY;AAC9BA,QAAAA,MAAM,CAACU,UAAP;AACD,OAFD;AAGD,KApBkB,CAsBnB;;;AACA,SAAKtC,QAAL,GAvBmB,CAuBH;AACjB;;AAEDyC,EAAAA,aAAa,GAAI;AACf,UAAMC,eAAe,GAAG;AACtBC,MAAAA,UAAU,EAAE,CADU;AAEtBC,MAAAA,aAAa,EAAE,CAFO;AAGtBC,MAAAA,cAAc,EAAE,KAHM;AAItBC,MAAAA,aAAa,EAAE;AAJO,KAAxB;AAMA,UAAM1D,KAAK,GAAG,EAAE,GAAG,KAAK0C,QAAL,GAAgB1C;AAArB,KAAd;AACA,UAAM2D,YAAY,GAAG,EAArB;AACA7E,IAAAA,MAAM,CAAC8E,IAAP,CAAY5D,KAAZ,EAAmB6D,OAAnB,CAA2BjB,MAAM,IAAI;AACnC,YAAMkB,WAAW,GAAG,EAAE,GAAG9D,KAAK,CAAC4C,MAAD;AAAV,OAApB;AACAkB,MAAAA,WAAW,CAACC,QAAZ,GAAuB,EAAE,GAAGD,WAAW,CAACC,QAAjB;AAA2B,WAAGT;AAA9B,OAAvB;AACAK,MAAAA,YAAY,CAACf,MAAD,CAAZ,GAAuBkB,WAAvB;AACD,KAJD;AAMA,SAAKlD,QAAL,CAAc;AACZZ,MAAAA,KAAK,EAAE2D,YADK;AAEZvC,MAAAA,aAAa,EAAE;AAFH,KAAd;AAKA,SAAKS,IAAL,CAAU,gBAAV;AACD;;AAEDmC,EAAAA,eAAe,CAAEC,EAAF,EAAM;AACnB,sEAAoBC,GAApB,CAAwBD,EAAxB;AACD;;AAEDE,EAAAA,kBAAkB,CAAEF,EAAF,EAAM;AACtB,WAAO,kEAAoBG,MAApB,CAA2BH,EAA3B,CAAP;AACD;;AAEDI,EAAAA,gBAAgB,CAAEJ,EAAF,EAAM;AACpB,wEAAqBC,GAArB,CAAyBD,EAAzB;AACD;;AAEDK,EAAAA,mBAAmB,CAAEL,EAAF,EAAM;AACvB,WAAO,oEAAqBG,MAArB,CAA4BH,EAA5B,CAAP;AACD;;AAEDM,EAAAA,WAAW,CAAEN,EAAF,EAAM;AACf,8DAAgBC,GAAhB,CAAoBD,EAApB;AACD;;AAEDO,EAAAA,cAAc,CAAEP,EAAF,EAAM;AAClB,WAAO,0DAAgBG,MAAhB,CAAuBH,EAAvB,CAAP;AACD;;AAEDb,EAAAA,OAAO,CAAEqB,IAAF,EAAQ;AACb,UAAMC,WAAW,GAAG,EAAE,GAAG,KAAKhC,QAAL,GAAgB9C,IAArB;AAA2B,SAAG6E;AAA9B,KAApB;AACA,UAAMd,YAAY,GAAG,EAAE,GAAG,KAAKjB,QAAL,GAAgB1C;AAArB,KAArB;AAEAlB,IAAAA,MAAM,CAAC8E,IAAP,CAAYD,YAAZ,EAA0BE,OAA1B,CAAmCjB,MAAD,IAAY;AAC5Ce,MAAAA,YAAY,CAACf,MAAD,CAAZ,GAAuB,EAAE,GAAGe,YAAY,CAACf,MAAD,CAAjB;AAA2BhD,QAAAA,IAAI,EAAE,EAAE,GAAG+D,YAAY,CAACf,MAAD,CAAZ,CAAqBhD,IAA1B;AAAgC,aAAG6E;AAAnC;AAAjC,OAAvB;AACD,KAFD;AAIA,SAAKpE,GAAL,CAAS,kBAAT;AACA,SAAKA,GAAL,CAASoE,IAAT;AAEA,SAAK7D,QAAL,CAAc;AACZhB,MAAAA,IAAI,EAAE8E,WADM;AAEZ1E,MAAAA,KAAK,EAAE2D;AAFK,KAAd;AAID;;AAEDgB,EAAAA,WAAW,CAAE/B,MAAF,EAAU6B,IAAV,EAAgB;AACzB,UAAMd,YAAY,GAAG,EAAE,GAAG,KAAKjB,QAAL,GAAgB1C;AAArB,KAArB;;AACA,QAAI,CAAC2D,YAAY,CAACf,MAAD,CAAjB,EAA2B;AACzB,WAAKvC,GAAL,CAAS,+DAAT,EAA0EuC,MAA1E;AACA;AACD;;AACD,UAAMgC,OAAO,GAAG,EAAE,GAAGjB,YAAY,CAACf,MAAD,CAAZ,CAAqBhD,IAA1B;AAAgC,SAAG6E;AAAnC,KAAhB;AACAd,IAAAA,YAAY,CAACf,MAAD,CAAZ,GAAuB,EAAE,GAAGe,YAAY,CAACf,MAAD,CAAjB;AAA2BhD,MAAAA,IAAI,EAAEgF;AAAjC,KAAvB;AACA,SAAKhE,QAAL,CAAc;AAAEZ,MAAAA,KAAK,EAAE2D;AAAT,KAAd;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEkB,EAAAA,OAAO,CAAEjC,MAAF,EAAU;AACf,WAAO,KAAKF,QAAL,GAAgB1C,KAAhB,CAAsB4C,MAAtB,CAAP;AACD;AAED;AACF;AACA;;;AACEkC,EAAAA,QAAQ,GAAI;AACV,UAAM;AAAE9E,MAAAA;AAAF,QAAY,KAAK0C,QAAL,EAAlB;AACA,WAAO5D,MAAM,CAACiG,MAAP,CAAc/E,KAAd,CAAP;AACD;;AAEDgF,EAAAA,wBAAwB,GAAI;AAC1B,UAAM;AAAEhF,MAAAA,KAAK,EAAEiF,WAAT;AAAsB7D,MAAAA,aAAtB;AAAqC8D,MAAAA;AAArC,QAA+C,KAAKxC,QAAL,EAArD;AACA,UAAM1C,KAAK,GAAGlB,MAAM,CAACiG,MAAP,CAAcE,WAAd,CAAd;AACA,UAAME,eAAe,GAAGnF,KAAK,CAACoF,MAAN,CAAa;AAAA,UAAC;AAAErB,QAAAA;AAAF,OAAD;AAAA,aAAkB,CAACA,QAAQ,CAACN,cAAV,IAA4BM,QAAQ,CAACL,aAAvD;AAAA,KAAb,CAAxB;AACA,UAAM2B,QAAQ,GAAIrF,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAU,CAACA,IAAI,CAACvB,QAAL,CAAcL,aAAtC,CAAlB;AACA,UAAM6B,YAAY,GAAGvF,KAAK,CAACoF,MAAN,CACnBE,IAAI,IAAIA,IAAI,CAACvB,QAAL,CAAcL,aAAd,IAA+B4B,IAAI,CAACvB,QAAL,CAAcyB,UAA7C,IAA2DF,IAAI,CAACvB,QAAL,CAAc0B,WAD9D,CAArB;AAGA,UAAMC,kBAAkB,GAAG1F,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAUA,IAAI,CAACvB,QAAL,CAAcL,aAArC,CAA3B;AACA,UAAMiC,WAAW,GAAG3F,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAUA,IAAI,CAACM,QAA5B,CAApB;AACA,UAAMC,aAAa,GAAG7F,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAUA,IAAI,CAACvB,QAAL,CAAcN,cAArC,CAAtB;AACA,UAAMqC,YAAY,GAAG9F,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAUA,IAAI,CAACJ,KAA5B,CAArB;AACA,UAAMa,wBAAwB,GAAGZ,eAAe,CAACC,MAAhB,CAAwBE,IAAD,IAAU,CAACA,IAAI,CAACM,QAAvC,CAAjC;AACA,UAAMI,eAAe,GAAGhG,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAUA,IAAI,CAACvB,QAAL,CAAcyB,UAAd,IAA4BF,IAAI,CAACvB,QAAL,CAAc0B,WAAjE,CAAxB;AAEA,WAAO;AACLJ,MAAAA,QADK;AAELE,MAAAA,YAFK;AAGLG,MAAAA,kBAHK;AAILC,MAAAA,WAJK;AAKLE,MAAAA,aALK;AAMLC,MAAAA,YANK;AAOLX,MAAAA,eAPK;AAQLY,MAAAA,wBARK;AASLC,MAAAA,eATK;AAWLC,MAAAA,eAAe,EAAEP,kBAAkB,CAACQ,MAAnB,GAA4B,CAXxC;AAYLC,MAAAA,aAAa,EAAE/E,aAAa,KAAK,GAAlB,IACVyE,aAAa,CAACK,MAAd,KAAyBlG,KAAK,CAACkG,MADrB,IAEVF,eAAe,CAACE,MAAhB,KAA2B,CAd3B;AAeLE,MAAAA,YAAY,EAAE,CAAC,CAAClB,KAAF,IAAWY,YAAY,CAACI,MAAb,KAAwBlG,KAAK,CAACkG,MAflD;AAgBLG,MAAAA,WAAW,EAAElB,eAAe,CAACe,MAAhB,KAA2B,CAA3B,IAAgCP,WAAW,CAACO,MAAZ,KAAuBf,eAAe,CAACe,MAhB/E;AAiBLI,MAAAA,kBAAkB,EAAEnB,eAAe,CAACe,MAAhB,GAAyB,CAjBxC;AAkBLK,MAAAA,WAAW,EAAEvG,KAAK,CAACwG,IAAN,CAAWlB,IAAI,IAAIA,IAAI,CAACmB,OAAxB;AAlBR,KAAP;AAoBD;AAED;AACF;AACA;AACA;AACA;;AACE;AACF;AACA;AACA;AACA;;;AAaEC,EAAAA,oBAAoB,CAAEpB,IAAF,EAAQtF,KAAR,EAAiC;AAAA,QAAzBA,KAAyB;AAAzBA,MAAAA,KAAyB,GAAjB,KAAK8E,QAAL,EAAiB;AAAA;;AACnD;AACA;AACA,QAAI;AACF,kEAAiB6B,QAAjB,CAA0BrB,IAA1B,EAAgCtF,KAAhC;;AACA,aAAO;AAAE4G,QAAAA,MAAM,EAAE;AAAV,OAAP;AACD,KAHD,CAGE,OAAOC,GAAP,EAAY;AACZ,aAAO;AAAED,QAAAA,MAAM,EAAE,KAAV;AAAiBE,QAAAA,MAAM,EAAED,GAAG,CAACE;AAA7B,OAAP;AACD;AACF;;AAkCDC,EAAAA,wBAAwB,CAAEpE,MAAF,EAAU;AAChC,UAAM;AAAE5C,MAAAA;AAAF,QAAY,KAAK0C,QAAL,EAAlB;;AAEA,QAAI1C,KAAK,CAAC4C,MAAD,CAAL,IAAiB,CAAC5C,KAAK,CAAC4C,MAAD,CAAL,CAAc6D,OAApC,EAA6C;AAC3C,aAAO,IAAP;AACD;;AACD,WAAO,KAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AAoFE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEQ,EAAAA,OAAO,CAAE3B,IAAF,EAAQ;AACb,wFAA6BA,IAA7B;;AAEA,UAAM;AAAEtF,MAAAA;AAAF,QAAY,KAAK0C,QAAL,EAAlB;;AACA,QAAIwE,OAAO,+BAAG,IAAH,kEAAuClH,KAAvC,EAA8CsF,IAA9C,CAAX,CAJa,CAMb;AACA;;;AACA,QAAItF,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CAAL,IAAqBW,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CAAL,CAAkBoH,OAA3C,EAAoD;AAClDS,MAAAA,OAAO,GAAG,EACR,GAAGlH,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CADA;AAERoF,QAAAA,IAAI,EAAEa,IAAI,CAACb,IAFH;AAGRgC,QAAAA,OAAO,EAAE;AAHD,OAAV;AAKA,WAAKpG,GAAL,CAAU,iDAAgD6G,OAAO,CAACC,IAAK,KAAID,OAAO,CAAC7H,EAAG,EAAtF;AACD;;AAED,SAAKuB,QAAL,CAAc;AACZZ,MAAAA,KAAK,EAAE,EACL,GAAGA,KADE;AAEL,SAACkH,OAAO,CAAC7H,EAAT,GAAc6H;AAFT;AADK,KAAd;AAOA,SAAKrF,IAAL,CAAU,YAAV,EAAwBqF,OAAxB;AACA,SAAKrF,IAAL,CAAU,aAAV,EAAyB,CAACqF,OAAD,CAAzB;AACA,SAAK7G,GAAL,CAAU,eAAc6G,OAAO,CAACC,IAAK,KAAID,OAAO,CAAC7H,EAAG,gBAAe6H,OAAO,CAACE,IAAK,EAAhF;;AAEA;;AAEA,WAAOF,OAAO,CAAC7H,EAAf;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEgI,EAAAA,QAAQ,CAAEC,eAAF,EAAmB;AACzB,0FADyB,CAGzB;;;AACA,UAAMtH,KAAK,GAAG,EAAE,GAAG,KAAK0C,QAAL,GAAgB1C;AAArB,KAAd;AACA,UAAMqF,QAAQ,GAAG,EAAjB;AACA,UAAMkC,MAAM,GAAG,EAAf;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,eAAe,CAACpB,MAApC,EAA4CsB,CAAC,EAA7C,EAAiD;AAC/C,UAAI;AACF,YAAIN,OAAO,+BAAG,IAAH,kEAAuClH,KAAvC,EAA8CsH,eAAe,CAACE,CAAD,CAA7D,CAAX,CADE,CAEF;AACA;;;AACA,YAAIxH,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CAAL,IAAqBW,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CAAL,CAAkBoH,OAA3C,EAAoD;AAClDS,UAAAA,OAAO,GAAG,EACR,GAAGlH,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CADA;AAERoF,YAAAA,IAAI,EAAE6C,eAAe,CAACE,CAAD,CAAf,CAAmB/C,IAFjB;AAGRgC,YAAAA,OAAO,EAAE;AAHD,WAAV;AAKA,eAAKpG,GAAL,CAAU,kCAAiC6G,OAAO,CAACC,IAAK,KAAID,OAAO,CAAC7H,EAAG,EAAvE;AACD;;AACDW,QAAAA,KAAK,CAACkH,OAAO,CAAC7H,EAAT,CAAL,GAAoB6H,OAApB;AACA7B,QAAAA,QAAQ,CAACoC,IAAT,CAAcP,OAAd;AACD,OAdD,CAcE,OAAOL,GAAP,EAAY;AACZ,YAAI,CAACA,GAAG,CAACa,aAAT,EAAwB;AACtBH,UAAAA,MAAM,CAACE,IAAP,CAAYZ,GAAZ;AACD;AACF;AACF;;AAED,SAAKjG,QAAL,CAAc;AAAEZ,MAAAA;AAAF,KAAd;AAEAqF,IAAAA,QAAQ,CAACxB,OAAT,CAAkBqD,OAAD,IAAa;AAC5B,WAAKrF,IAAL,CAAU,YAAV,EAAwBqF,OAAxB;AACD,KAFD;AAIA,SAAKrF,IAAL,CAAU,aAAV,EAAyBwD,QAAzB;;AAEA,QAAIA,QAAQ,CAACa,MAAT,GAAkB,CAAtB,EAAyB;AACvB,WAAK7F,GAAL,CAAU,kBAAiBgF,QAAQ,CAACa,MAAO,QAA3C;AACD,KAFD,MAEO;AACLpH,MAAAA,MAAM,CAAC8E,IAAP,CAAYyB,QAAZ,EAAsBxB,OAAtB,CAA8BjB,MAAM,IAAI;AACtC,aAAKvC,GAAL,CAAU,eAAcgF,QAAQ,CAACzC,MAAD,CAAR,CAAiBuE,IAAK,UAAS9B,QAAQ,CAACzC,MAAD,CAAR,CAAiBvD,EAAG,YAAWgG,QAAQ,CAACzC,MAAD,CAAR,CAAiBwE,IAAK,EAA5G;AACD,OAFD;AAGD;;AAED,QAAI/B,QAAQ,CAACa,MAAT,GAAkB,CAAtB,EAAyB;AACvB;AACD;;AAED,QAAIqB,MAAM,CAACrB,MAAP,GAAgB,CAApB,EAAuB;AACrB,UAAIa,OAAO,GAAG,gDAAd;AACAQ,MAAAA,MAAM,CAAC1D,OAAP,CAAgB8D,QAAD,IAAc;AAC3BZ,QAAAA,OAAO,IAAK,QAAOY,QAAQ,CAACZ,OAAQ,EAApC;AACD,OAFD;AAIA,WAAK1F,IAAL,CAAU;AACR0F,QAAAA,OAAO,EAAE,KAAKvF,IAAL,CAAU,oBAAV,EAAgC;AAAEoG,UAAAA,WAAW,EAAEL,MAAM,CAACrB;AAAtB,SAAhC,CADD;AAER2B,QAAAA,OAAO,EAAEd;AAFD,OAAV,EAGG,OAHH,EAGY,KAAKlI,IAAL,CAAUuB,WAHtB;;AAKA,UAAI,OAAO0H,cAAP,KAA0B,UAA9B,EAA0C;AACxC,cAAM,IAAIA,cAAJ,CAAmBP,MAAnB,EAA2BR,OAA3B,CAAN;AACD,OAFD,MAEO;AACL,cAAMF,GAAG,GAAG,IAAIhE,KAAJ,CAAUkE,OAAV,CAAZ;AACAF,QAAAA,GAAG,CAACU,MAAJ,GAAaA,MAAb;AACA,cAAMV,GAAN;AACD;AACF;AACF;;AAEDkB,EAAAA,WAAW,CAAEC,OAAF,EAAWlB,MAAX,EAAmB;AAC5B,UAAM;AAAE9G,MAAAA,KAAF;AAASc,MAAAA;AAAT,QAA4B,KAAK4B,QAAL,EAAlC;AACA,UAAMiB,YAAY,GAAG,EAAE,GAAG3D;AAAL,KAArB;AACA,UAAMiI,cAAc,GAAG,EAAE,GAAGnH;AAAL,KAAvB;AAEA,UAAMoH,YAAY,GAAGpJ,MAAM,CAACC,MAAP,CAAc,IAAd,CAArB;AACAiJ,IAAAA,OAAO,CAACnE,OAAR,CAAiBjB,MAAD,IAAY;AAC1B,UAAI5C,KAAK,CAAC4C,MAAD,CAAT,EAAmB;AACjBsF,QAAAA,YAAY,CAACtF,MAAD,CAAZ,GAAuB5C,KAAK,CAAC4C,MAAD,CAA5B;AACA,eAAOe,YAAY,CAACf,MAAD,CAAnB;AACD;AACF,KALD,EAN4B,CAa5B;;AACA,aAASuF,gBAAT,CAA2BC,YAA3B,EAAyC;AACvC,aAAOF,YAAY,CAACE,YAAD,CAAZ,KAA+BC,SAAtC;AACD;;AAEDvJ,IAAAA,MAAM,CAAC8E,IAAP,CAAYqE,cAAZ,EAA4BpE,OAA5B,CAAqCyE,QAAD,IAAc;AAChD,YAAMC,UAAU,GAAGzH,cAAc,CAACwH,QAAD,CAAd,CAAyBN,OAAzB,CAAiC5C,MAAjC,CAAwC+C,gBAAxC,CAAnB,CADgD,CAGhD;;AACA,UAAII,UAAU,CAACrC,MAAX,KAAsB,CAA1B,EAA6B;AAC3B,eAAO+B,cAAc,CAACK,QAAD,CAArB;AACA;AACD;;AAED,YAAM;AAAEtH,QAAAA;AAAF,UAAmB,KAAK0B,QAAL,EAAzB;;AACA,UAAI6F,UAAU,CAACrC,MAAX,KAAsBpF,cAAc,CAACwH,QAAD,CAAd,CAAyBN,OAAzB,CAAiC9B,MAAvD,IACG,CAAClF,YAAY,CAACE,sBADrB,EAC6C;AAC3C,cAAM,IAAI2B,KAAJ,CAAU,oCAAV,CAAN;AACD;;AAEDoF,MAAAA,cAAc,CAACK,QAAD,CAAd,GAA2B,EACzB,GAAGxH,cAAc,CAACwH,QAAD,CADQ;AAEzBN,QAAAA,OAAO,EAAEO;AAFgB,OAA3B;AAID,KAnBD;AAqBA,UAAMC,WAAW,GAAG;AAClB1H,MAAAA,cAAc,EAAEmH,cADE;AAElBjI,MAAAA,KAAK,EAAE2D;AAFW,KAApB,CAvC4B,CA4C5B;AACA;;AACA,QAAI7E,MAAM,CAAC8E,IAAP,CAAYD,YAAZ,EAA0BuC,MAA1B,KAAqC,CAAzC,EAA4C;AAC1CsC,MAAAA,WAAW,CAACzH,cAAZ,GAA6B,IAA7B;AACAyH,MAAAA,WAAW,CAACtD,KAAZ,GAAoB,IAApB;AACAsD,MAAAA,WAAW,CAAClH,cAAZ,GAA6B,IAA7B;AACD;;AAED,SAAKV,QAAL,CAAc4H,WAAd;AACA,SAAKC,sBAAL;AAEA,UAAMC,cAAc,GAAG5J,MAAM,CAAC8E,IAAP,CAAYsE,YAAZ,CAAvB;AACAQ,IAAAA,cAAc,CAAC7E,OAAf,CAAwBjB,MAAD,IAAY;AACjC,WAAKf,IAAL,CAAU,cAAV,EAA0BqG,YAAY,CAACtF,MAAD,CAAtC,EAAgDkE,MAAhD;AACD,KAFD;;AAIA,QAAI4B,cAAc,CAACxC,MAAf,GAAwB,CAA5B,EAA+B;AAC7B,WAAK7F,GAAL,CAAU,WAAUqI,cAAc,CAACxC,MAAO,QAA1C;AACD,KAFD,MAEO;AACL,WAAK7F,GAAL,CAAU,kBAAiBqI,cAAc,CAACC,IAAf,CAAoB,IAApB,CAA0B,EAArD;AACD;AACF;;AAEDC,EAAAA,UAAU,CAAEhG,MAAF,EAAUkE,MAAV,EAAyB;AAAA,QAAfA,MAAe;AAAfA,MAAAA,MAAe,GAAN,IAAM;AAAA;;AACjC,SAAKiB,WAAL,CAAiB,CAACnF,MAAD,CAAjB,EAA2BkE,MAA3B;AACD;;AAED+B,EAAAA,WAAW,CAAEjG,MAAF,EAAU;AACnB,QAAI,CAAC,KAAKF,QAAL,GAAgB1B,YAAhB,CAA6BG,gBAA9B,IACI,KAAK0D,OAAL,CAAajC,MAAb,EAAqBa,cAD7B,EAC6C;AAC3C,aAAO4E,SAAP;AACD;;AAED,UAAMS,SAAS,GAAG,KAAKjE,OAAL,CAAajC,MAAb,EAAqBgD,QAArB,IAAiC,KAAnD;AACA,UAAMA,QAAQ,GAAG,CAACkD,SAAlB;AAEA,SAAKnG,YAAL,CAAkBC,MAAlB,EAA0B;AACxBgD,MAAAA;AADwB,KAA1B;AAIA,SAAK/D,IAAL,CAAU,cAAV,EAA0Be,MAA1B,EAAkCgD,QAAlC;AAEA,WAAOA,QAAP;AACD;;AAEDmD,EAAAA,QAAQ,GAAI;AACV,UAAMpF,YAAY,GAAG,EAAE,GAAG,KAAKjB,QAAL,GAAgB1C;AAArB,KAArB;AACA,UAAMgJ,sBAAsB,GAAGlK,MAAM,CAAC8E,IAAP,CAAYD,YAAZ,EAA0ByB,MAA1B,CAAkCE,IAAD,IAAU;AACxE,aAAO,CAAC3B,YAAY,CAAC2B,IAAD,CAAZ,CAAmBvB,QAAnB,CAA4BN,cAA7B,IACGE,YAAY,CAAC2B,IAAD,CAAZ,CAAmBvB,QAAnB,CAA4BL,aADtC;AAED,KAH8B,CAA/B;AAKAsF,IAAAA,sBAAsB,CAACnF,OAAvB,CAAgCyB,IAAD,IAAU;AACvC,YAAMxB,WAAW,GAAG,EAAE,GAAGH,YAAY,CAAC2B,IAAD,CAAjB;AAAyBM,QAAAA,QAAQ,EAAE;AAAnC,OAApB;AACAjC,MAAAA,YAAY,CAAC2B,IAAD,CAAZ,GAAqBxB,WAArB;AACD,KAHD;AAKA,SAAKlD,QAAL,CAAc;AAAEZ,MAAAA,KAAK,EAAE2D;AAAT,KAAd;AACA,SAAK9B,IAAL,CAAU,WAAV;AACD;;AAEDoH,EAAAA,SAAS,GAAI;AACX,UAAMtF,YAAY,GAAG,EAAE,GAAG,KAAKjB,QAAL,GAAgB1C;AAArB,KAArB;AACA,UAAMgJ,sBAAsB,GAAGlK,MAAM,CAAC8E,IAAP,CAAYD,YAAZ,EAA0ByB,MAA1B,CAAkCE,IAAD,IAAU;AACxE,aAAO,CAAC3B,YAAY,CAAC2B,IAAD,CAAZ,CAAmBvB,QAAnB,CAA4BN,cAA7B,IACGE,YAAY,CAAC2B,IAAD,CAAZ,CAAmBvB,QAAnB,CAA4BL,aADtC;AAED,KAH8B,CAA/B;AAKAsF,IAAAA,sBAAsB,CAACnF,OAAvB,CAAgCyB,IAAD,IAAU;AACvC,YAAMxB,WAAW,GAAG,EAClB,GAAGH,YAAY,CAAC2B,IAAD,CADG;AAElBM,QAAAA,QAAQ,EAAE,KAFQ;AAGlBV,QAAAA,KAAK,EAAE;AAHW,OAApB;AAKAvB,MAAAA,YAAY,CAAC2B,IAAD,CAAZ,GAAqBxB,WAArB;AACD,KAPD;AAQA,SAAKlD,QAAL,CAAc;AAAEZ,MAAAA,KAAK,EAAE2D;AAAT,KAAd;AAEA,SAAK9B,IAAL,CAAU,YAAV;AACD;;AAEDqH,EAAAA,QAAQ,GAAI;AACV,UAAMvF,YAAY,GAAG,EAAE,GAAG,KAAKjB,QAAL,GAAgB1C;AAArB,KAArB;AACA,UAAMmJ,YAAY,GAAGrK,MAAM,CAAC8E,IAAP,CAAYD,YAAZ,EAA0ByB,MAA1B,CAAiCE,IAAI,IAAI;AAC5D,aAAO3B,YAAY,CAAC2B,IAAD,CAAZ,CAAmBJ,KAA1B;AACD,KAFoB,CAArB;AAIAiE,IAAAA,YAAY,CAACtF,OAAb,CAAsByB,IAAD,IAAU;AAC7B,YAAMxB,WAAW,GAAG,EAClB,GAAGH,YAAY,CAAC2B,IAAD,CADG;AAElBM,QAAAA,QAAQ,EAAE,KAFQ;AAGlBV,QAAAA,KAAK,EAAE;AAHW,OAApB;AAKAvB,MAAAA,YAAY,CAAC2B,IAAD,CAAZ,GAAqBxB,WAArB;AACD,KAPD;AAQA,SAAKlD,QAAL,CAAc;AACZZ,MAAAA,KAAK,EAAE2D,YADK;AAEZuB,MAAAA,KAAK,EAAE;AAFK,KAAd;AAKA,SAAKrD,IAAL,CAAU,WAAV,EAAuBsH,YAAvB;;AAEA,QAAIA,YAAY,CAACjD,MAAb,KAAwB,CAA5B,EAA+B;AAC7B,aAAOkD,OAAO,CAACC,OAAR,CAAgB;AACrBC,QAAAA,UAAU,EAAE,EADS;AAErBC,QAAAA,MAAM,EAAE;AAFa,OAAhB,CAAP;AAID;;AAED,UAAMjB,QAAQ,+BAAG,IAAH,gCAAsBa,YAAtB,EAAoC;AAChDK,MAAAA,mBAAmB,EAAE,IAD2B,CACrB;;AADqB,KAApC,CAAd;;AAGA,uCAAO,IAAP,0BAAuBlB,QAAvB;AACD;;AAEDmB,EAAAA,SAAS,QAA4B;AAAA,QAA1B;AAAE3C,MAAAA,MAAM,GAAG;AAAX,KAA0B,sBAAJ,EAAI;AACnC,SAAKjF,IAAL,CAAU,YAAV,EAAwB;AAAEiF,MAAAA;AAAF,KAAxB,EADmC,CAGnC;;AACA,QAAIA,MAAM,KAAK,MAAf,EAAuB;AACrB,YAAM;AAAE9G,QAAAA;AAAF,UAAY,KAAK0C,QAAL,EAAlB;AAEA,YAAMsF,OAAO,GAAGlJ,MAAM,CAAC8E,IAAP,CAAY5D,KAAZ,CAAhB;;AACA,UAAIgI,OAAO,CAAC9B,MAAZ,EAAoB;AAClB,aAAK6B,WAAL,CAAiBC,OAAjB,EAA0B,YAA1B;AACD;;AAED,WAAKpH,QAAL,CAAc;AACZQ,QAAAA,aAAa,EAAE,CADH;AAEZ8D,QAAAA,KAAK,EAAE,IAFK;AAGZ5D,QAAAA,cAAc,EAAE;AAHJ,OAAd;AAKD;AACF;;AAEDoI,EAAAA,WAAW,CAAE9G,MAAF,EAAU;AACnB,SAAKD,YAAL,CAAkBC,MAAlB,EAA0B;AACxBsC,MAAAA,KAAK,EAAE,IADiB;AAExBU,MAAAA,QAAQ,EAAE;AAFc,KAA1B;AAKA,SAAK/D,IAAL,CAAU,cAAV,EAA0Be,MAA1B;;AAEA,UAAM0F,QAAQ,+BAAG,IAAH,gCAAsB,CAAC1F,MAAD,CAAtB,EAAgC;AAC5C4G,MAAAA,mBAAmB,EAAE,IADuB,CACjB;;AADiB,KAAhC,CAAd;;AAGA,uCAAO,IAAP,0BAAuBlB,QAAvB;AACD,GA/yBQ,CAizBT;;;AACAqB,EAAAA,KAAK,GAAW;AACd,SAAKF,SAAL,CAAe,YAAf;AACD;;AAEDG,EAAAA,MAAM,GAAI;AACR,SAAKrH,cAAL,CAAoBC,MAAM,IAAI;AAC5B,UAAIA,MAAM,CAACqH,QAAP,IAAmBrH,MAAM,CAACqH,QAAP,CAAgBD,MAAvC,EAA+C;AAC7CpH,QAAAA,MAAM,CAACqH,QAAP,CAAgBD,MAAhB;AACD;AACF,KAJD;AAKD;;AAEDnJ,EAAAA,iBAAiB,CAAE6E,IAAF,EAAQb,IAAR,EAAc;AAC7B,QAAIa,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD,KAJ4B,CAM7B;;;AACA,UAAMyK,iBAAiB,GAAGC,MAAM,CAACC,QAAP,CAAgBvF,IAAI,CAACwF,UAArB,KAAoCxF,IAAI,CAACwF,UAAL,GAAkB,CAAhF;AACA,SAAKtH,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzB0E,MAAAA,QAAQ,EAAE,EACR,GAAG,KAAKc,OAAL,CAAaS,IAAI,CAACjG,EAAlB,EAAsB0E,QADjB;AAERP,QAAAA,aAAa,EAAEiB,IAAI,CAACjB,aAFZ;AAGRyG,QAAAA,UAAU,EAAExF,IAAI,CAACwF,UAHT;AAIR1G,QAAAA,UAAU,EAAEuG,iBAAiB,GACzBI,IAAI,CAACC,KAAL,CAAY1F,IAAI,CAACjB,aAAL,GAAqBiB,IAAI,CAACwF,UAA3B,GAAyC,GAApD,CADyB,GAEzB;AANI;AADe,KAA3B;AAWA,SAAKxB,sBAAL;AACD;;AAEDA,EAAAA,sBAAsB,GAAI;AACxB;AACA;AACA,UAAMzI,KAAK,GAAG,KAAK8E,QAAL,EAAd;AAEA,UAAMsF,UAAU,GAAGpK,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAU;AACxC,aAAOA,IAAI,CAACvB,QAAL,CAAcL,aAAd,IACF4B,IAAI,CAACvB,QAAL,CAAcyB,UADZ,IAEFF,IAAI,CAACvB,QAAL,CAAc0B,WAFnB;AAGD,KAJkB,CAAnB;;AAMA,QAAI2E,UAAU,CAAClE,MAAX,KAAsB,CAA1B,EAA6B;AAC3B,WAAKrE,IAAL,CAAU,UAAV,EAAsB,CAAtB;AACA,WAAKjB,QAAL,CAAc;AAAEQ,QAAAA,aAAa,EAAE;AAAjB,OAAd;AACA;AACD;;AAED,UAAMiJ,UAAU,GAAGD,UAAU,CAAChF,MAAX,CAAmBE,IAAD,IAAUA,IAAI,CAACvB,QAAL,CAAckG,UAAd,IAA4B,IAAxD,CAAnB;AACA,UAAMK,YAAY,GAAGF,UAAU,CAAChF,MAAX,CAAmBE,IAAD,IAAUA,IAAI,CAACvB,QAAL,CAAckG,UAAd,IAA4B,IAAxD,CAArB;;AAEA,QAAII,UAAU,CAACnE,MAAX,KAAsB,CAA1B,EAA6B;AAC3B,YAAMqE,WAAW,GAAGH,UAAU,CAAClE,MAAX,GAAoB,GAAxC;AACA,YAAMsE,eAAe,GAAGF,YAAY,CAACG,MAAb,CAAoB,CAACC,GAAD,EAAMpF,IAAN,KAAe;AACzD,eAAOoF,GAAG,GAAGpF,IAAI,CAACvB,QAAL,CAAcR,UAA3B;AACD,OAFuB,EAErB,CAFqB,CAAxB;AAGA,YAAMnC,aAAa,GAAG8I,IAAI,CAACC,KAAL,CAAYK,eAAe,GAAGD,WAAnB,GAAkC,GAA7C,CAAtB;AACA,WAAK3J,QAAL,CAAc;AAAEQ,QAAAA;AAAF,OAAd;AACA;AACD;;AAED,QAAIuJ,SAAS,GAAGN,UAAU,CAACI,MAAX,CAAkB,CAACC,GAAD,EAAMpF,IAAN,KAAe;AAC/C,aAAOoF,GAAG,GAAGpF,IAAI,CAACvB,QAAL,CAAckG,UAA3B;AACD,KAFe,EAEb,CAFa,CAAhB;AAGA,UAAMW,WAAW,GAAGD,SAAS,GAAGN,UAAU,CAACnE,MAA3C;AACAyE,IAAAA,SAAS,IAAIC,WAAW,GAAGN,YAAY,CAACpE,MAAxC;AAEA,QAAI2E,YAAY,GAAG,CAAnB;AACAR,IAAAA,UAAU,CAACxG,OAAX,CAAoByB,IAAD,IAAU;AAC3BuF,MAAAA,YAAY,IAAIvF,IAAI,CAACvB,QAAL,CAAcP,aAA9B;AACD,KAFD;AAGA8G,IAAAA,YAAY,CAACzG,OAAb,CAAsByB,IAAD,IAAU;AAC7BuF,MAAAA,YAAY,IAAKD,WAAW,IAAItF,IAAI,CAACvB,QAAL,CAAcR,UAAd,IAA4B,CAAhC,CAAZ,GAAkD,GAAlE;AACD,KAFD;AAIA,QAAInC,aAAa,GAAGuJ,SAAS,KAAK,CAAd,GAChB,CADgB,GAEhBT,IAAI,CAACC,KAAL,CAAYU,YAAY,GAAGF,SAAhB,GAA6B,GAAxC,CAFJ,CA5CwB,CAgDxB;AACA;;AACA,QAAIvJ,aAAa,GAAG,GAApB,EAAyB;AACvBA,MAAAA,aAAa,GAAG,GAAhB;AACD;;AAED,SAAKR,QAAL,CAAc;AAAEQ,MAAAA;AAAF,KAAd;AACA,SAAKS,IAAL,CAAU,UAAV,EAAsBT,aAAtB;AACD;AAED;AACF;AACA;AACA;;;AAsKEnC,EAAAA,kBAAkB,GAAI;AACpB,UAAM6L,MAAM,GAAG,OAAO/I,MAAM,CAACgJ,SAAP,CAAiBC,MAAxB,KAAmC,WAAnC,GACXjJ,MAAM,CAACgJ,SAAP,CAAiBC,MADN,GAEX,IAFJ;;AAGA,QAAI,CAACF,MAAL,EAAa;AACX,WAAKjJ,IAAL,CAAU,YAAV;AACA,WAAKR,IAAL,CAAU,KAAKG,IAAL,CAAU,sBAAV,CAAV,EAA6C,OAA7C,EAAsD,CAAtD;AACA,WAAKyJ,UAAL,GAAkB,IAAlB;AACD,KAJD,MAIO;AACL,WAAKpJ,IAAL,CAAU,WAAV;;AACA,UAAI,KAAKoJ,UAAT,EAAqB;AACnB,aAAKpJ,IAAL,CAAU,aAAV;AACA,aAAKR,IAAL,CAAU,KAAKG,IAAL,CAAU,qBAAV,CAAV,EAA4C,SAA5C,EAAuD,IAAvD;AACA,aAAKyJ,UAAL,GAAkB,KAAlB;AACD;AACF;AACF;;AAIDC,EAAAA,KAAK,GAAI;AACP,WAAO,KAAKrM,IAAL,CAAUQ,EAAjB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACE;;;AACA8L,EAAAA,GAAG,CAAEC,MAAF,EAAUvM,IAAV,EAAgB;AACjB,QAAI,OAAOuM,MAAP,KAAkB,UAAtB,EAAkC;AAChC,YAAMC,GAAG,GAAI,oCAAmCD,MAAM,KAAK,IAAX,GAAkB,MAAlB,GAA2B,OAAOA,MAAO,GAA7E,GACR,oEADJ;AAEA,YAAM,IAAIE,SAAJ,CAAcD,GAAd,CAAN;AACD,KALgB,CAOjB;;;AACA,UAAM7I,MAAM,GAAG,IAAI4I,MAAJ,CAAW,IAAX,EAAiBvM,IAAjB,CAAf;AACA,UAAM0M,QAAQ,GAAG/I,MAAM,CAACnD,EAAxB;;AAEA,QAAI,CAACkM,QAAL,EAAe;AACb,YAAM,IAAI1I,KAAJ,CAAU,6BAAV,CAAN;AACD;;AAED,QAAI,CAACL,MAAM,CAAC4E,IAAZ,EAAkB;AAChB,YAAM,IAAIvE,KAAJ,CAAU,8BAAV,CAAN;AACD;;AAED,UAAM2I,mBAAmB,GAAG,KAAKC,SAAL,CAAeF,QAAf,CAA5B;;AACA,QAAIC,mBAAJ,EAAyB;AACvB,YAAMH,GAAG,GAAI,iCAAgCG,mBAAmB,CAACnM,EAAG,KAAxD,GACP,kBAAiBkM,QAAS,MADnB,GAER,mFAFJ;AAGA,YAAM,IAAI1I,KAAJ,CAAUwI,GAAV,CAAN;AACD;;AAED,QAAID,MAAM,CAAC7K,OAAX,EAAoB;AAClB,WAAKF,GAAL,CAAU,SAAQkL,QAAS,KAAIH,MAAM,CAAC7K,OAAQ,EAA9C;AACD;;AAED,QAAIiC,MAAM,CAAC4E,IAAP,gCAAe,IAAf,qBAAJ,EAAkC;AAChC,4DAAc5E,MAAM,CAAC4E,IAArB,EAA2BK,IAA3B,CAAgCjF,MAAhC;AACD,KAFD,MAEO;AACL,4DAAcA,MAAM,CAAC4E,IAArB,IAA6B,CAAC5E,MAAD,CAA7B;AACD;;AACDA,IAAAA,MAAM,CAACkJ,OAAP;AAEA,WAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AACED,EAAAA,SAAS,CAAEpM,EAAF,EAAM;AACb,SAAK,MAAMwB,OAAX,IAAsB/B,MAAM,CAACiG,MAAP,6BAAc,IAAd,sBAAtB,EAAoD;AAClD,YAAM4G,WAAW,GAAG9K,OAAO,CAAC+K,IAAR,CAAapJ,MAAM,IAAIA,MAAM,CAACnD,EAAP,KAAcA,EAArC,CAApB;AACA,UAAIsM,WAAW,IAAI,IAAnB,EAAyB,OAAOA,WAAP;AAC1B;;AACD,WAAOtD,SAAP;AACD;;AAED,gBAAuCjB,IAAvC,EAA6C;AAC3C,WAAO,sDAAcA,IAAd,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;AACE7E,EAAAA,cAAc,CAAEsJ,MAAF,EAAU;AACtB/M,IAAAA,MAAM,CAACiG,MAAP,6BAAc,IAAd,uBAA6B+G,IAA7B,CAAkC,CAAlC,EAAqCjI,OAArC,CAA6CgI,MAA7C;AACD;AAED;AACF;AACA;AACA;AACA;;;AACEE,EAAAA,YAAY,CAAEC,QAAF,EAAY;AACtB,SAAK3L,GAAL,CAAU,mBAAkB2L,QAAQ,CAAC3M,EAAG,EAAxC;AACA,SAAKwC,IAAL,CAAU,eAAV,EAA2BmK,QAA3B;;AAEA,QAAIA,QAAQ,CAACC,SAAb,EAAwB;AACtBD,MAAAA,QAAQ,CAACC,SAAT;AACD;;AAED,UAAMC,IAAI,GAAG,sDAAcF,QAAQ,CAAC5E,IAAvB,CAAb,CARsB,CAStB;AACA;AACA;;;AACA,UAAM+E,KAAK,GAAGD,IAAI,CAACE,SAAL,CAAeC,IAAI,IAAIA,IAAI,CAAChN,EAAL,KAAY2M,QAAQ,CAAC3M,EAA5C,CAAd;;AACA,QAAI8M,KAAK,KAAK,CAAC,CAAf,EAAkB;AAChBD,MAAAA,IAAI,CAACI,MAAL,CAAYH,KAAZ,EAAmB,CAAnB;AACD;;AAED,UAAM7J,KAAK,GAAG,KAAKI,QAAL,EAAd;AACA,UAAM6J,YAAY,GAAG;AACnB1L,MAAAA,OAAO,EAAE,EACP,GAAGyB,KAAK,CAACzB,OADF;AAEP,SAACmL,QAAQ,CAAC3M,EAAV,GAAegJ;AAFR;AADU,KAArB;AAMA,SAAKzH,QAAL,CAAc2L,YAAd;AACD;AAED;AACF;AACA;;;AACEC,EAAAA,KAAK,SAAmB;AAAA,QAAjB;AAAE1F,MAAAA;AAAF,KAAiB,uBAAJ,EAAI;AACtB,SAAKzG,GAAL,CAAU,yBAAwB,KAAKxB,IAAL,CAAUQ,EAAG,+CAA/C;AAEA,SAAKoK,SAAL,CAAe;AAAE3C,MAAAA;AAAF,KAAf;;AAEA;;AAEA,SAAKvE,cAAL,CAAqBC,MAAD,IAAY;AAC9B,WAAKuJ,YAAL,CAAkBvJ,MAAlB;AACD,KAFD;;AAIA,QAAI,OAAOT,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAAC0K,mBAA5C,EAAiE;AAC/D1K,MAAAA,MAAM,CAAC0K,mBAAP,CAA2B,QAA3B,8BAAqC,IAArC;AACA1K,MAAAA,MAAM,CAAC0K,mBAAP,CAA2B,SAA3B,8BAAsC,IAAtC;AACD;AACF;;AAEDC,EAAAA,QAAQ,GAAI;AACV,UAAM;AAAErL,MAAAA;AAAF,QAAW,KAAKqB,QAAL,EAAjB;AAEA,SAAK9B,QAAL,CAAc;AAAES,MAAAA,IAAI,EAAEA,IAAI,CAACsL,KAAL,CAAW,CAAX;AAAR,KAAd;AAEA,SAAK9K,IAAL,CAAU,aAAV;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACER,EAAAA,IAAI,CAAE0F,OAAF,EAAWK,IAAX,EAA0BwF,QAA1B,EAA2C;AAAA,QAAhCxF,IAAgC;AAAhCA,MAAAA,IAAgC,GAAzB,MAAyB;AAAA;;AAAA,QAAjBwF,QAAiB;AAAjBA,MAAAA,QAAiB,GAAN,IAAM;AAAA;;AAC7C,UAAMC,gBAAgB,GAAG,OAAO9F,OAAP,KAAmB,QAA5C;AAEA,SAAKnG,QAAL,CAAc;AACZS,MAAAA,IAAI,EAAE,CACJ,GAAG,KAAKqB,QAAL,GAAgBrB,IADf,EAEJ;AACE+F,QAAAA,IADF;AAEEL,QAAAA,OAAO,EAAE8F,gBAAgB,GAAG9F,OAAO,CAACA,OAAX,GAAqBA,OAFhD;AAGEc,QAAAA,OAAO,EAAEgF,gBAAgB,GAAG9F,OAAO,CAACc,OAAX,GAAqB;AAHhD,OAFI;AADM,KAAd;AAWAiF,IAAAA,UAAU,CAAC,MAAM,KAAKJ,QAAL,EAAP,EAAwBE,QAAxB,CAAV;AAEA,SAAK/K,IAAL,CAAU,cAAV;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACExB,EAAAA,GAAG,CAAE0G,OAAF,EAAWK,IAAX,EAAiB;AAClB,UAAM;AAAElH,MAAAA;AAAF,QAAa,KAAKrB,IAAxB;;AACA,YAAQuI,IAAR;AACE,WAAK,OAAL;AAAclH,QAAAA,MAAM,CAACgF,KAAP,CAAa6B,OAAb;AAAuB;;AACrC,WAAK,SAAL;AAAgB7G,QAAAA,MAAM,CAAC6M,IAAP,CAAYhG,OAAZ;AAAsB;;AACtC;AAAS7G,QAAAA,MAAM,CAACT,KAAP,CAAasH,OAAb;AAAuB;AAHlC;AAKD;AAED;AACF;AACA;;;AACEiG,EAAAA,OAAO,CAAE1E,QAAF,EAAY;AACjB,SAAKjI,GAAL,CAAU,uCAAsCiI,QAAS,GAAzD;;AAEA,QAAI,CAAC,KAAK5F,QAAL,GAAgB5B,cAAhB,CAA+BwH,QAA/B,CAAL,EAA+C;AAC7C,sEAAmBA,QAAnB;;AACA,aAAOc,OAAO,CAAC6D,MAAR,CAAe,IAAIpK,KAAJ,CAAU,oBAAV,CAAf,CAAP;AACD;;AAED,uCAAO,IAAP,0BAAuByF,QAAvB;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;AAiCE,mBAAkD;AAAE,uCAAO,IAAP,gCAA0B,YAA1B;AAAoC;;AAQxF;AACF;AACA;AACA;AACA;AACA;AACE4E,EAAAA,aAAa,CAAE5E,QAAF,EAAY7D,IAAZ,EAAkB;AAC7B,QAAI,6BAAC,IAAD,0BAAiB6D,QAAjB,CAAJ,EAAgC;AAC9B,WAAKjI,GAAL,CAAU,2DAA0DiI,QAAS,EAA7E;AACA;AACD;;AACD,UAAM;AAAExH,MAAAA;AAAF,QAAqB,KAAK4B,QAAL,EAA3B;AACA,UAAMyK,aAAa,GAAG,EAAE,GAAGrM,cAAc,CAACwH,QAAD,CAAnB;AAA+B1B,MAAAA,MAAM,EAAE,EAAE,GAAG9F,cAAc,CAACwH,QAAD,CAAd,CAAyB1B,MAA9B;AAAsC,WAAGnC;AAAzC;AAAvC,KAAtB;AACA,SAAK7D,QAAL,CAAc;AACZE,MAAAA,cAAc,EAAE,EAAE,GAAGA,cAAL;AAAqB,SAACwH,QAAD,GAAY6E;AAAjC;AADJ,KAAd;AAGD;AAED;AACF;AACA;AACA;AACA;;;AAsGE;AACF;AACA;AACA;AACA;AACEC,EAAAA,MAAM,GAAI;AAAA;;AACR,QAAI,2BAAC,sDAAcC,QAAf,aAAC,sBAAwBnH,MAAzB,CAAJ,EAAqC;AACnC,WAAK7F,GAAL,CAAS,mCAAT,EAA8C,SAA9C;AACD;;AAED,QAAI;AAAEL,MAAAA;AAAF,QAAY,KAAK0C,QAAL,EAAhB;AAEA,UAAM4K,oBAAoB,GAAG,KAAKzO,IAAL,CAAUkB,cAAV,CAAyBC,KAAzB,CAA7B;;AAEA,QAAIsN,oBAAoB,KAAK,KAA7B,EAAoC;AAClC,aAAOlE,OAAO,CAAC6D,MAAR,CAAe,IAAIpK,KAAJ,CAAU,+DAAV,CAAf,CAAP;AACD;;AAED,QAAIyK,oBAAoB,IAAI,OAAOA,oBAAP,KAAgC,QAA5D,EAAsE;AACpEtN,MAAAA,KAAK,GAAGsN,oBAAR,CADoE,CAEpE;AACA;;AACA,WAAK1M,QAAL,CAAc;AACZZ,QAAAA;AADY,OAAd;AAGD;;AAED,WAAOoJ,OAAO,CAACC,OAAR,GACJkE,IADI,CACC,MAAM,4DAAiBC,wBAAjB,CAA0CxN,KAA1C,CADP,EAEJyN,KAFI,CAEG5G,GAAD,IAAS;AACd,wEAAoBA,GAApB;;AACA,YAAMA,GAAN;AACD,KALI,EAMJ0G,IANI,CAMC,MAAM;AACV,UAAI,6BAAC,IAAD,sDAA+BvN,KAA/B,CAAJ,EAA2C;AACzC,cAAM,IAAI0N,4BAAJ,CAAqB,KAAKlM,IAAL,CAAU,0BAAV,CAArB,CAAN;AACD;AACF,KAVI,EAWJiM,KAXI,CAWG5G,GAAD,IAAS;AACd;AACA;AACA;AACA,YAAMA,GAAN;AACD,KAhBI,EAiBJ0G,IAjBI,CAiBC,MAAM;AACV,YAAM;AAAEzM,QAAAA;AAAF,UAAqB,KAAK4B,QAAL,EAA3B,CADU,CAEV;;AACA,YAAMiL,uBAAuB,GAAG7O,MAAM,CAACiG,MAAP,CAAcjE,cAAd,EAA8B8M,OAA9B,CAAsCC,IAAI,IAAIA,IAAI,CAAC7F,OAAnD,CAAhC;AAEA,YAAM8F,cAAc,GAAG,EAAvB;AACAhP,MAAAA,MAAM,CAAC8E,IAAP,CAAY5D,KAAZ,EAAmB6D,OAAnB,CAA4BjB,MAAD,IAAY;AACrC,cAAM0C,IAAI,GAAG,KAAKT,OAAL,CAAajC,MAAb,CAAb,CADqC,CAErC;;AACA,YAAK,CAAC0C,IAAI,CAACvB,QAAL,CAAcL,aAAhB,IAAmCiK,uBAAuB,CAACI,OAAxB,CAAgCnL,MAAhC,MAA4C,CAAC,CAApF,EAAwF;AACtFkL,UAAAA,cAAc,CAACrG,IAAf,CAAoBnC,IAAI,CAACjG,EAAzB;AACD;AACF,OAND;;AAQA,YAAMiJ,QAAQ,+BAAG,IAAH,gCAAsBwF,cAAtB,CAAd;;AACA,yCAAO,IAAP,0BAAuBxF,QAAvB;AACD,KAjCI,EAkCJmF,KAlCI,CAkCG5G,GAAD,IAAS;AACd,WAAKhF,IAAL,CAAU,OAAV,EAAmBgF,GAAnB;AACA,WAAKxG,GAAL,CAASwG,GAAT,EAAc,OAAd;AACA,YAAMA,GAAN;AACD,KAtCI,CAAP;AAuCD;;AA5/CQ;;yBA+VO3B,K,EAAOI,I,EAAM;AAC3B,QAAM;AAAEyB,IAAAA,OAAF;AAAWc,IAAAA,OAAO,GAAG;AAArB,MAA4B3C,KAAlC;;AAEA,MAAIA,KAAK,CAACwC,aAAV,EAAyB;AACvB,SAAK7F,IAAL,CAAU,oBAAV,EAAgCyD,IAAhC,EAAsCJ,KAAtC;AACD,GAFD,MAEO;AACL,SAAKrD,IAAL,CAAU,OAAV,EAAmBqD,KAAnB;AACD;;AACD,OAAK7D,IAAL,CAAU;AAAE0F,IAAAA,OAAF;AAAWc,IAAAA;AAAX,GAAV,EAAgC,OAAhC,EAAyC,KAAKhJ,IAAL,CAAUuB,WAAnD;AACA,OAAKC,GAAL,CAAU,GAAE0G,OAAQ,IAAGc,OAAQ,EAAtB,CAAwBmG,IAAxB,EAAT,EAAyC,OAAzC;AACD;;yCAa+B1I,I,EAAM;AACpC,QAAM;AAAE2I,IAAAA,aAAF;AAAiB/I,IAAAA;AAAjB,MAA2B,4DAAiBgJ,4BAAjB,CAA8C5I,IAA9C,CAAjC;;AAEA,MAAI2I,aAAa,CAAC/H,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,SAAKvD,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AAAE8O,MAAAA,yBAAyB,EAAEF;AAA7B,KAA3B;AACA,SAAK5N,GAAL,CAAS6E,KAAK,CAAC6B,OAAf;AACA,SAAKlF,IAAL,CAAU,oBAAV,EAAgCyD,IAAhC,EAAsCJ,KAAtC;AACA,WAAO,KAAP;AACD;;AACD,SAAO,IAAP;AACD;;mCAEyBlF,K,EAAO;AAC/B,MAAIoO,OAAO,GAAG,IAAd;;AACA,OAAK,MAAM9I,IAAX,IAAmBxG,MAAM,CAACiG,MAAP,CAAc/E,KAAd,CAAnB,EAAyC;AACvC,QAAI,6BAAC,IAAD,kEAAqCsF,IAArC,CAAJ,EAAgD;AAC9C8I,MAAAA,OAAO,GAAG,KAAV;AACD;AACF;;AACD,SAAOA,OAAP;AACD;;kCAEwB9I,I,EAAM;AAC7B,QAAM;AAAEvE,IAAAA;AAAF,MAAqB,KAAK2B,QAAL,EAA3B;;AAEA,MAAI3B,cAAc,KAAK,KAAvB,EAA8B;AAC5B,UAAMmE,KAAK,GAAG,IAAIwI,4BAAJ,CAAqB,KAAKlM,IAAL,CAAU,oBAAV,CAArB,CAAd;;AACA,sEAAoB0D,KAApB,EAA2BI,IAA3B;;AACA,UAAMJ,KAAN;AACD;AACF;;yCAmB+BlF,K,EAAOqO,c,EAAgB;AACrD,QAAMC,QAAQ,GAAGpQ,WAAW,CAACmQ,cAAD,CAA5B;AACA,QAAME,QAAQ,GAAGjQ,WAAW,CAACgQ,QAAD,EAAWD,cAAX,CAA5B;AACA,QAAMG,aAAa,GAAGrQ,uBAAuB,CAACoQ,QAAD,CAAvB,CAAkCE,SAAxD;AACA,QAAMC,QAAQ,GAAGC,OAAO,CAACN,cAAc,CAACK,QAAhB,CAAxB;AACA,QAAM9L,MAAM,GAAGxE,cAAc,CAAC,EAC5B,GAAGiQ,cADyB;AAE5BjH,IAAAA,IAAI,EAAEkH;AAFsB,GAAD,CAA7B;;AAKA,MAAI,KAAKtH,wBAAL,CAA8BpE,MAA9B,CAAJ,EAA2C;AACzC,UAAMsC,KAAK,GAAG,IAAIwI,4BAAJ,CAAqB,KAAKlM,IAAL,CAAU,cAAV,EAA0B;AAAE+M,MAAAA;AAAF,KAA1B,CAArB,CAAd;;AACA,sEAAoBrJ,KAApB,EAA2BmJ,cAA3B;;AACA,UAAMnJ,KAAN;AACD;;AAED,QAAMtF,IAAI,GAAGyO,cAAc,CAACzO,IAAf,IAAuB,EAApC;AACAA,EAAAA,IAAI,CAACuH,IAAL,GAAYoH,QAAZ;AACA3O,EAAAA,IAAI,CAACwH,IAAL,GAAYkH,QAAZ,CAlBqD,CAoBrD;;AACA,QAAMM,IAAI,GAAG7E,MAAM,CAACC,QAAP,CAAgBqE,cAAc,CAAC5J,IAAf,CAAoBmK,IAApC,IAA4CP,cAAc,CAAC5J,IAAf,CAAoBmK,IAAhE,GAAuE,IAApF;AAEA,MAAI1H,OAAO,GAAG;AACZ2H,IAAAA,MAAM,EAAER,cAAc,CAACQ,MAAf,IAAyB,EADrB;AAEZxP,IAAAA,EAAE,EAAEuD,MAFQ;AAGZuE,IAAAA,IAAI,EAAEoH,QAHM;AAIZE,IAAAA,SAAS,EAAED,aAAa,IAAI,EAJhB;AAKZ5O,IAAAA,IAAI,EAAE,EACJ,GAAG,KAAK8C,QAAL,GAAgB9C,IADf;AAEJ,SAAGA;AAFC,KALM;AASZwH,IAAAA,IAAI,EAAEkH,QATM;AAUZ7J,IAAAA,IAAI,EAAE4J,cAAc,CAAC5J,IAVT;AAWZV,IAAAA,QAAQ,EAAE;AACRR,MAAAA,UAAU,EAAE,CADJ;AAERC,MAAAA,aAAa,EAAE,CAFP;AAGRyG,MAAAA,UAAU,EAAE2E,IAHJ;AAIRnL,MAAAA,cAAc,EAAE,KAJR;AAKRC,MAAAA,aAAa,EAAE;AALP,KAXE;AAkBZkL,IAAAA,IAlBY;AAmBZF,IAAAA,QAnBY;AAoBZI,IAAAA,MAAM,EAAET,cAAc,CAACS,MAAf,IAAyB,EApBrB;AAqBZC,IAAAA,OAAO,EAAEV,cAAc,CAACU;AArBZ,GAAd;AAwBA,QAAMC,uBAAuB,GAAG,KAAKnQ,IAAL,CAAUgB,iBAAV,CAA4BqH,OAA5B,EAAqClH,KAArC,CAAhC;;AAEA,MAAIgP,uBAAuB,KAAK,KAAhC,EAAuC;AACrC;AACA,UAAM9J,KAAK,GAAG,IAAIwI,4BAAJ,CAAqB,+DAArB,CAAd;AACA,SAAK7L,IAAL,CAAU,oBAAV,EAAgCwM,cAAhC,EAAgDnJ,KAAhD;AACA,UAAMA,KAAN;AACD,GALD,MAKO,IAAI,OAAO8J,uBAAP,KAAmC,QAAnC,IAA+CA,uBAAuB,KAAK,IAA/E,EAAqF;AAC1F9H,IAAAA,OAAO,GAAG8H,uBAAV;AACD;;AAED,MAAI;AACF,UAAMC,UAAU,GAAGnQ,MAAM,CAAC8E,IAAP,CAAY5D,KAAZ,EAAmBkP,GAAnB,CAAuB1H,CAAC,IAAIxH,KAAK,CAACwH,CAAD,CAAjC,CAAnB;;AACA,gEAAiBb,QAAjB,CAA0BO,OAA1B,EAAmC+H,UAAnC;AACD,GAHD,CAGE,OAAOpI,GAAP,EAAY;AACZ,sEAAoBA,GAApB,EAAyBK,OAAzB;;AACA,UAAML,GAAN;AACD;;AAED,SAAOK,OAAP;AACD;;gCAGsB;AACrB,MAAI,KAAKrI,IAAL,CAAUS,WAAV,IAAyB,CAAC,KAAK6P,oBAAnC,EAAyD;AACvD,SAAKA,oBAAL,GAA4BrC,UAAU,CAAC,MAAM;AAC3C,WAAKqC,oBAAL,GAA4B,IAA5B;AACA,WAAK/B,MAAL,GAAcK,KAAd,CAAqB5G,GAAD,IAAS;AAC3B,YAAI,CAACA,GAAG,CAACa,aAAT,EAAwB;AACtB,eAAKrH,GAAL,CAASwG,GAAG,CAACuI,KAAJ,IAAavI,GAAG,CAACE,OAAjB,IAA4BF,GAArC;AACD;AACF,OAJD;AAKD,KAPqC,EAOnC,CAPmC,CAAtC;AAQD;AACF;;0BA0ZgB;AACf;AACJ;AACA;AACA;AACA;AACI,QAAMwI,YAAY,GAAG,CAACnK,KAAD,EAAQI,IAAR,EAAcgK,QAAd,KAA2B;AAC9C,QAAIC,QAAQ,GAAGrK,KAAK,CAAC6B,OAAN,IAAiB,eAAhC;;AACA,QAAI7B,KAAK,CAAC2C,OAAV,EAAmB;AACjB0H,MAAAA,QAAQ,IAAK,IAAGrK,KAAK,CAAC2C,OAAQ,EAA9B;AACD;;AAED,SAAKjH,QAAL,CAAc;AAAEsE,MAAAA,KAAK,EAAEqK;AAAT,KAAd;;AAEA,QAAIjK,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAACjG,EAAL,IAAW,KAAKqD,QAAL,GAAgB1C,KAA/C,EAAsD;AACpD,WAAK2C,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzB6F,QAAAA,KAAK,EAAEqK,QADkB;AAEzBD,QAAAA;AAFyB,OAA3B;AAID;AACF,GAdD;;AAgBA,OAAKpN,EAAL,CAAQ,OAAR,EAAiBmN,YAAjB;AAEA,OAAKnN,EAAL,CAAQ,cAAR,EAAwB,CAACoD,IAAD,EAAOJ,KAAP,EAAcoK,QAAd,KAA2B;AACjDD,IAAAA,YAAY,CAACnK,KAAD,EAAQI,IAAR,EAAcgK,QAAd,CAAZ;;AAEA,QAAI,OAAOpK,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAAC6B,OAAvC,EAAgD;AAC9C,YAAMyI,QAAQ,GAAG,IAAI3M,KAAJ,CAAUqC,KAAK,CAAC6B,OAAhB,CAAjB;AACAyI,MAAAA,QAAQ,CAAC3H,OAAT,GAAmB3C,KAAK,CAAC6B,OAAzB;;AACA,UAAI7B,KAAK,CAAC2C,OAAV,EAAmB;AACjB2H,QAAAA,QAAQ,CAAC3H,OAAT,IAAqB,IAAG3C,KAAK,CAAC2C,OAAQ,EAAtC;AACD;;AACD2H,MAAAA,QAAQ,CAACzI,OAAT,GAAmB,KAAKvF,IAAL,CAAU,gBAAV,EAA4B;AAAE8D,QAAAA,IAAI,EAAEA,IAAF,oBAAEA,IAAI,CAAE6B;AAAd,OAA5B,CAAnB;;AACA,wEAAoBqI,QAApB;AACD,KARD,MAQO;AACL,wEAAoBtK,KAApB;AACD;AACF,GAdD;AAgBA,OAAKhD,EAAL,CAAQ,QAAR,EAAkB,MAAM;AACtB,SAAKtB,QAAL,CAAc;AAAEsE,MAAAA,KAAK,EAAE;AAAT,KAAd;AACD,GAFD;AAIA,OAAKhD,EAAL,CAAQ,gBAAR,EAA2BoD,IAAD,IAAU;AAClC,QAAIA,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD;;AACD,SAAKsD,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzB0E,MAAAA,QAAQ,EAAE;AACRL,QAAAA,aAAa,EAAE+L,IAAI,CAACC,GAAL,EADP;AAERjM,QAAAA,cAAc,EAAE,KAFR;AAGRF,QAAAA,UAAU,EAAE,CAHJ;AAIRC,QAAAA,aAAa,EAAE,CAJP;AAKRyG,QAAAA,UAAU,EAAE3E,IAAI,CAACsJ;AALT;AADe,KAA3B;AASD,GAdD;AAgBA,OAAK1M,EAAL,CAAQ,iBAAR,EAA2B,KAAKzB,iBAAhC;AAEA,OAAKyB,EAAL,CAAQ,gBAAR,EAA0B,CAACoD,IAAD,EAAOqK,UAAP,KAAsB;AAC9C,QAAIrK,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD;;AAED,UAAMmL,eAAe,GAAG,KAAK3F,OAAL,CAAaS,IAAI,CAACjG,EAAlB,EAAsB0E,QAA9C;AACA,SAAKpB,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzB0E,MAAAA,QAAQ,EAAE,EACR,GAAGyG,eADK;AAER/E,QAAAA,WAAW,EAAE,oEAAqBmJ,IAArB,GAA4B,CAA5B,GAAgC;AAC3CgB,UAAAA,IAAI,EAAE;AADqC,SAAhC,GAET,IAJI;AAKRnM,QAAAA,cAAc,EAAE,IALR;AAMRF,QAAAA,UAAU,EAAE,GANJ;AAORC,QAAAA,aAAa,EAAEgH,eAAe,CAACP;AAPvB,OADe;AAUzBqF,MAAAA,QAAQ,EAAEK,UAVe;AAWzBE,MAAAA,SAAS,EAAEF,UAAU,CAACE,SAXG;AAYzBjK,MAAAA,QAAQ,EAAE;AAZe,KAA3B,EAP8C,CAsB9C;AACA;;AACA,QAAIN,IAAI,CAACsJ,IAAL,IAAa,IAAjB,EAAuB;AACrB,WAAKjM,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzBuP,QAAAA,IAAI,EAAEe,UAAU,CAACnM,aAAX,IAA4BgH,eAAe,CAACP;AADzB,OAA3B;AAGD;;AAED,SAAKxB,sBAAL;AACD,GA/BD;AAiCA,OAAKvG,EAAL,CAAQ,qBAAR,EAA+B,CAACoD,IAAD,EAAOvB,QAAP,KAAoB;AACjD,QAAIuB,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD;;AACD,SAAKsD,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzB0E,MAAAA,QAAQ,EAAE,EAAE,GAAG,KAAKc,OAAL,CAAaS,IAAI,CAACjG,EAAlB,EAAsB0E,QAA3B;AAAqCyB,QAAAA,UAAU,EAAEzB;AAAjD;AADe,KAA3B;AAGD,GARD;AAUA,OAAK7B,EAAL,CAAQ,qBAAR,EAAgCoD,IAAD,IAAU;AACvC,QAAIA,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD;;AACD,UAAMW,KAAK,GAAG,EAAE,GAAG,KAAK0C,QAAL,GAAgB1C;AAArB,KAAd;AACAA,IAAAA,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAL,GAAiB,EAAE,GAAGW,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAV;AAAqB0E,MAAAA,QAAQ,EAAE,EAAE,GAAG/D,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAL,CAAe0E;AAApB;AAA/B,KAAjB;AACA,WAAO/D,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAL,CAAe0E,QAAf,CAAwByB,UAA/B;AAEA,SAAK5E,QAAL,CAAc;AAAEZ,MAAAA;AAAF,KAAd;AACD,GAVD;AAYA,OAAKkC,EAAL,CAAQ,sBAAR,EAAgC,CAACoD,IAAD,EAAOvB,QAAP,KAAoB;AAClD,QAAIuB,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD;;AACD,SAAKsD,YAAL,CAAkB2C,IAAI,CAACjG,EAAvB,EAA2B;AACzB0E,MAAAA,QAAQ,EAAE,EAAE,GAAG,KAAKrB,QAAL,GAAgB1C,KAAhB,CAAsBsF,IAAI,CAACjG,EAA3B,EAA+B0E,QAApC;AAA8C0B,QAAAA,WAAW,EAAE1B;AAA3D;AADe,KAA3B;AAGD,GARD;AAUA,OAAK7B,EAAL,CAAQ,sBAAR,EAAiCoD,IAAD,IAAU;AACxC,QAAIA,IAAI,IAAI,IAAR,IAAgB,CAAC,KAAKT,OAAL,CAAaS,IAAI,CAACjG,EAAlB,CAArB,EAA4C;AAC1C,WAAKgB,GAAL,CAAU,0DAAyDiF,IAA1D,oBAA0DA,IAAI,CAAEjG,EAAG,EAA5E;AACA;AACD;;AACD,UAAMW,KAAK,GAAG,EACZ,GAAG,KAAK0C,QAAL,GAAgB1C;AADP,KAAd;AAGAA,IAAAA,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAL,GAAiB,EACf,GAAGW,KAAK,CAACsF,IAAI,CAACjG,EAAN,CADO;AAEf0E,MAAAA,QAAQ,EAAE,EACR,GAAG/D,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAL,CAAe0E;AADV;AAFK,KAAjB;AAMA,WAAO/D,KAAK,CAACsF,IAAI,CAACjG,EAAN,CAAL,CAAe0E,QAAf,CAAwB0B,WAA/B;AAEA,SAAK7E,QAAL,CAAc;AAAEZ,MAAAA;AAAF,KAAd;AACD,GAjBD;AAmBA,OAAKkC,EAAL,CAAQ,UAAR,EAAoB,MAAM;AACxB;AACA,SAAKuG,sBAAL;AACD,GAHD;AAKA,OAAKvG,EAAL,CAAQ,8BAAR,EAAyCoD,IAAD,IAAU;AAChD,QAAIA,IAAJ,EAAU;AACR,wGAAoCA,IAApC;AACD;AACF,GAJD,EAvJe,CA6Jf;;AACA,MAAI,OAAOvD,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAAC+N,gBAA5C,EAA8D;AAC5D/N,IAAAA,MAAM,CAAC+N,gBAAP,CAAwB,QAAxB,8BAAkC,IAAlC;AACA/N,IAAAA,MAAM,CAAC+N,gBAAP,CAAwB,SAAxB,8BAAmC,IAAnC;AACAhD,IAAAA,UAAU,6BAAC,IAAD,6CAA2B,IAA3B,CAAV;AACD;AACF;;wBAiOc9E,O,EAASnJ,I,EAAW;AAAA,MAAXA,IAAW;AAAXA,IAAAA,IAAW,GAAJ,EAAI;AAAA;;AACjC;AACA,QAAM;AAAE2K,IAAAA,mBAAmB,GAAG;AAAxB,MAAkC3K,IAAxC;AAEA,QAAM;AAAEkC,IAAAA,cAAF;AAAkBD,IAAAA;AAAlB,MAAqC,KAAK4B,QAAL,EAA3C;;AACA,MAAI,CAAC3B,cAAD,IAAmB,CAACyI,mBAAxB,EAA6C;AAC3C,UAAM,IAAI3G,KAAJ,CAAU,gDAAV,CAAN;AACD;;AAED,QAAMyF,QAAQ,GAAG,wBAAjB;AAEA,OAAKzG,IAAL,CAAU,QAAV,EAAoB;AAClBxC,IAAAA,EAAE,EAAEiJ,QADc;AAElBN,IAAAA;AAFkB,GAApB;AAKA,OAAKpH,QAAL,CAAc;AACZG,IAAAA,cAAc,EAAE,KAAKlC,IAAL,CAAUW,0BAAV,KAAyC,KAAzC,IAAkD,KAAKX,IAAL,CAAUU,oBAAV,KAAmC,KADzF;AAGZuB,IAAAA,cAAc,EAAE,EACd,GAAGA,cADW;AAEd,OAACwH,QAAD,GAAY;AACVN,QAAAA,OADU;AAEV+H,QAAAA,IAAI,EAAE,CAFI;AAGVnJ,QAAAA,MAAM,EAAE;AAHE;AAFE;AAHJ,GAAd;AAaA,SAAO0B,QAAP;AACD;;qBAIWA,Q,EAAU;AACpB,QAAM;AAAExH,IAAAA;AAAF,MAAqB,KAAK4B,QAAL,EAA3B;AAEA,SAAO5B,cAAc,CAACwH,QAAD,CAArB;AACD;;wBAyBcA,Q,EAAU;AACvB,QAAMxH,cAAc,GAAG,EAAE,GAAG,KAAK4B,QAAL,GAAgB5B;AAArB,GAAvB;AACA,SAAOA,cAAc,CAACwH,QAAD,CAArB;AAEA,OAAK1H,QAAL,CAAc;AACZE,IAAAA;AADY,GAAd;AAGD;;2BAOiBwH,Q,EAAU;AAC1B,MAAI;AAAExH,IAAAA;AAAF,MAAqB,KAAK4B,QAAL,EAAzB;AACA,MAAIyK,aAAa,GAAGrM,cAAc,CAACwH,QAAD,CAAlC;AACA,QAAM0H,WAAW,GAAG7C,aAAa,CAAC4C,IAAd,IAAsB,CAA1C;AAEA,QAAME,KAAK,GAAG,CACZ,+BAAG,IAAH,iCADY,EAEZ,+BAAG,IAAH,yBAFY,EAGZ,+BAAG,IAAH,mCAHY,CAAd;;AAKA,MAAI;AACF,SAAK,IAAIF,IAAI,GAAGC,WAAhB,EAA6BD,IAAI,GAAGE,KAAK,CAAC/J,MAA1C,EAAkD6J,IAAI,EAAtD,EAA0D;AACxD,UAAI,CAAC5C,aAAL,EAAoB;AAClB;AACD;;AACD,YAAMlJ,EAAE,GAAGgM,KAAK,CAACF,IAAD,CAAhB;AAEA,YAAMG,aAAa,GAAG,EACpB,GAAG/C,aADiB;AAEpB4C,QAAAA;AAFoB,OAAtB;AAKA,WAAKnP,QAAL,CAAc;AACZE,QAAAA,cAAc,EAAE,EACd,GAAGA,cADW;AAEd,WAACwH,QAAD,GAAY4H;AAFE;AADJ,OAAd,EAXwD,CAkBxD;AACA;;AACA,YAAMjM,EAAE,CAACiM,aAAa,CAAClI,OAAf,EAAwBM,QAAxB,CAAR,CApBwD,CAsBxD;;AACAxH,MAAAA,cAAc,GAAG,KAAK4B,QAAL,GAAgB5B,cAAjC;AACAqM,MAAAA,aAAa,GAAGrM,cAAc,CAACwH,QAAD,CAA9B;AACD;AACF,GA3BD,CA2BE,OAAOzB,GAAP,EAAY;AACZ,oEAAmByB,QAAnB;;AACA,UAAMzB,GAAN;AACD,GAxCyB,CA0C1B;;;AACA,MAAIsG,aAAJ,EAAmB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,IAAAA,aAAa,CAACnF,OAAd,CAAsBnE,OAAtB,CAA+BjB,MAAD,IAAY;AACxC,YAAM0C,IAAI,GAAG,KAAKT,OAAL,CAAajC,MAAb,CAAb;;AACA,UAAI0C,IAAI,IAAIA,IAAI,CAACvB,QAAL,CAAc0B,WAA1B,EAAuC;AACrC,aAAK5D,IAAL,CAAU,sBAAV,EAAkCyD,IAAlC;AACD;AACF,KALD;AAOA,UAAMtF,KAAK,GAAGmN,aAAa,CAACnF,OAAd,CAAsBkH,GAAtB,CAA2BtM,MAAD,IAAY,KAAKiC,OAAL,CAAajC,MAAb,CAAtC,CAAd;AACA,UAAM0G,UAAU,GAAGtJ,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAU,CAACA,IAAI,CAACJ,KAA7B,CAAnB;AACA,UAAMqE,MAAM,GAAGvJ,KAAK,CAACoF,MAAN,CAAcE,IAAD,IAAUA,IAAI,CAACJ,KAA5B,CAAf;AACA,UAAM,KAAKgI,aAAL,CAAmB5E,QAAnB,EAA6B;AAAEgB,MAAAA,UAAF;AAAcC,MAAAA,MAAd;AAAsBjB,MAAAA;AAAtB,KAA7B,CAAN,CArBiB,CAuBjB;;AACAxH,IAAAA,cAAc,GAAG,KAAK4B,QAAL,GAAgB5B,cAAjC;AACAqM,IAAAA,aAAa,GAAGrM,cAAc,CAACwH,QAAD,CAA9B;AACD,GArEyB,CAsE1B;AACA;AACA;AACA;;;AACA,MAAI1B,MAAJ;;AACA,MAAIuG,aAAJ,EAAmB;AACjBvG,IAAAA,MAAM,GAAGuG,aAAa,CAACvG,MAAvB;AACA,SAAK/E,IAAL,CAAU,UAAV,EAAsB+E,MAAtB;;AAEA,oEAAmB0B,QAAnB;AACD;;AACD,MAAI1B,MAAM,IAAI,IAAd,EAAoB;AAClB,SAAKvG,GAAL,CAAU,2DAA0DiI,QAAS,EAA7E;AACD;;AACD,SAAO1B,MAAP;AACD;;AAx7CGjI,I,CACG4B,O,GAAUhC,WAAW,CAAC4R,O;iBA8/ChBxR,I", "sourcesContent": ["/* eslint-disable max-classes-per-file */\n/* global AggregateError */\n\nimport Translator from '@uppy/utils/lib/Translator'\nimport ee from 'namespace-emitter'\nimport { nanoid } from 'nanoid/non-secure'\nimport throttle from 'lodash.throttle'\nimport DefaultStore from '@uppy/store-default'\nimport getFileType from '@uppy/utils/lib/getFileType'\nimport getFileNameAndExtension from '@uppy/utils/lib/getFileNameAndExtension'\nimport generateFileID from '@uppy/utils/lib/generateFileID'\nimport supportsUploadProgress from './supportsUploadProgress.js'\nimport getFileName from './getFileName.js'\nimport { justErrorsLogger, debugLogger } from './loggers.js'\nimport {\n  Restricter,\n  defaultOptions as defaultRestrictionOptions,\n  RestrictionError,\n} from './Restricter.js'\n\nimport packageJson from '../package.json'\nimport locale from './locale.js'\n\n/**\n * Uppy Core module.\n * Manages plugins, state updates, acts as an event bus,\n * adds/removes files and metadata.\n */\nclass Uppy {\n  static VERSION = packageJson.version\n\n  /** @type {Record<string, BasePlugin[]>} */\n  #plugins = Object.create(null)\n\n  #restricter\n\n  #storeUnsubscribe\n\n  #emitter = ee()\n\n  #preProcessors = new Set()\n\n  #uploaders = new Set()\n\n  #postProcessors = new Set()\n\n  /**\n   * Instantiate Uppy\n   *\n   * @param {object} opts — Uppy options\n   */\n  constructor (opts) {\n    this.defaultLocale = locale\n\n    const defaultOptions = {\n      id: 'uppy',\n      autoProceed: false,\n      /**\n       * @deprecated The method should not be used\n       */\n      allowMultipleUploads: true,\n      allowMultipleUploadBatches: true,\n      debug: false,\n      restrictions: defaultRestrictionOptions,\n      meta: {},\n      onBeforeFileAdded: (currentFile) => currentFile,\n      onBeforeUpload: (files) => files,\n      store: DefaultStore(),\n      logger: justErrorsLogger,\n      infoTimeout: 5000,\n    }\n\n    // Merge default options with the ones set by user,\n    // making sure to merge restrictions too\n    this.opts = {\n      ...defaultOptions,\n      ...opts,\n      restrictions: {\n        ...defaultOptions.restrictions,\n        ...(opts && opts.restrictions),\n      },\n    }\n\n    // Support debug: true for backwards-compatability, unless logger is set in opts\n    // opts instead of this.opts to avoid comparing objects — we set logger: justErrorsLogger in defaultOptions\n    if (opts && opts.logger && opts.debug) {\n      this.log('You are using a custom `logger`, but also set `debug: true`, which uses built-in logger to output logs to console. Ignoring `debug: true` and using your custom `logger`.', 'warning')\n    } else if (opts && opts.debug) {\n      this.opts.logger = debugLogger\n    }\n\n    this.log(`Using Core v${this.constructor.VERSION}`)\n\n    this.i18nInit()\n\n    // ___Why throttle at 500ms?\n    //    - We must throttle at >250ms for superfocus in Dashboard to work well\n    //    (because animation takes 0.25s, and we want to wait for all animations to be over before refocusing).\n    //    [Practical Check]: if thottle is at 100ms, then if you are uploading a file,\n    //    and click 'ADD MORE FILES', - focus won't activate in Firefox.\n    //    - We must throttle at around >500ms to avoid performance lags.\n    //    [Practical Check] Firefox, try to upload a big file for a prolonged period of time. Laptop will start to heat up.\n    this.calculateProgress = throttle(this.calculateProgress.bind(this), 500, { leading: true, trailing: true })\n\n    this.store = this.opts.store\n    this.setState({\n      plugins: {},\n      files: {},\n      currentUploads: {},\n      allowNewUpload: true,\n      capabilities: {\n        uploadProgress: supportsUploadProgress(),\n        individualCancellation: true,\n        resumableUploads: false,\n      },\n      totalProgress: 0,\n      meta: { ...this.opts.meta },\n      info: [],\n      recoveredState: null,\n    })\n\n    this.#restricter = new Restricter(() => this.opts, this.i18n)\n\n    this.#storeUnsubscribe = this.store.subscribe((prevState, nextState, patch) => {\n      this.emit('state-update', prevState, nextState, patch)\n      this.updateAll(nextState)\n    })\n\n    // Exposing uppy object on window for debugging and testing\n    if (this.opts.debug && typeof window !== 'undefined') {\n      window[this.opts.id] = this\n    }\n\n    this.#addListeners()\n  }\n\n  emit (event, ...args) {\n    this.#emitter.emit(event, ...args)\n  }\n\n  on (event, callback) {\n    this.#emitter.on(event, callback)\n    return this\n  }\n\n  once (event, callback) {\n    this.#emitter.once(event, callback)\n    return this\n  }\n\n  off (event, callback) {\n    this.#emitter.off(event, callback)\n    return this\n  }\n\n  /**\n   * Iterate on all plugins and run `update` on them.\n   * Called each time state changes.\n   *\n   */\n  updateAll (state) {\n    this.iteratePlugins(plugin => {\n      plugin.update(state)\n    })\n  }\n\n  /**\n   * Updates state with a patch\n   *\n   * @param {object} patch {foo: 'bar'}\n   */\n  setState (patch) {\n    this.store.setState(patch)\n  }\n\n  /**\n   * Returns current state.\n   *\n   * @returns {object}\n   */\n  getState () {\n    return this.store.getState()\n  }\n\n  /**\n   * Back compat for when uppy.state is used instead of uppy.getState().\n   *\n   * @deprecated\n   */\n  get state () {\n    // Here, state is a non-enumerable property.\n    return this.getState()\n  }\n\n  /**\n   * Shorthand to set state for a specific file.\n   */\n  setFileState (fileID, state) {\n    if (!this.getState().files[fileID]) {\n      throw new Error(`Can’t set state for ${fileID} (the file could have been removed)`)\n    }\n\n    this.setState({\n      files: { ...this.getState().files, [fileID]: { ...this.getState().files[fileID], ...state } },\n    })\n  }\n\n  i18nInit () {\n    const translator = new Translator([this.defaultLocale, this.opts.locale])\n    this.i18n = translator.translate.bind(translator)\n    this.i18nArray = translator.translateArray.bind(translator)\n    this.locale = translator.locale\n  }\n\n  setOptions (newOpts) {\n    this.opts = {\n      ...this.opts,\n      ...newOpts,\n      restrictions: {\n        ...this.opts.restrictions,\n        ...(newOpts && newOpts.restrictions),\n      },\n    }\n\n    if (newOpts.meta) {\n      this.setMeta(newOpts.meta)\n    }\n\n    this.i18nInit()\n\n    if (newOpts.locale) {\n      this.iteratePlugins((plugin) => {\n        plugin.setOptions()\n      })\n    }\n\n    // Note: this is not the preact `setState`, it's an internal function that has the same name.\n    this.setState() // so that UI re-renders with new options\n  }\n\n  resetProgress () {\n    const defaultProgress = {\n      percentage: 0,\n      bytesUploaded: 0,\n      uploadComplete: false,\n      uploadStarted: null,\n    }\n    const files = { ...this.getState().files }\n    const updatedFiles = {}\n    Object.keys(files).forEach(fileID => {\n      const updatedFile = { ...files[fileID] }\n      updatedFile.progress = { ...updatedFile.progress, ...defaultProgress }\n      updatedFiles[fileID] = updatedFile\n    })\n\n    this.setState({\n      files: updatedFiles,\n      totalProgress: 0,\n    })\n\n    this.emit('reset-progress')\n  }\n\n  addPreProcessor (fn) {\n    this.#preProcessors.add(fn)\n  }\n\n  removePreProcessor (fn) {\n    return this.#preProcessors.delete(fn)\n  }\n\n  addPostProcessor (fn) {\n    this.#postProcessors.add(fn)\n  }\n\n  removePostProcessor (fn) {\n    return this.#postProcessors.delete(fn)\n  }\n\n  addUploader (fn) {\n    this.#uploaders.add(fn)\n  }\n\n  removeUploader (fn) {\n    return this.#uploaders.delete(fn)\n  }\n\n  setMeta (data) {\n    const updatedMeta = { ...this.getState().meta, ...data }\n    const updatedFiles = { ...this.getState().files }\n\n    Object.keys(updatedFiles).forEach((fileID) => {\n      updatedFiles[fileID] = { ...updatedFiles[fileID], meta: { ...updatedFiles[fileID].meta, ...data } }\n    })\n\n    this.log('Adding metadata:')\n    this.log(data)\n\n    this.setState({\n      meta: updatedMeta,\n      files: updatedFiles,\n    })\n  }\n\n  setFileMeta (fileID, data) {\n    const updatedFiles = { ...this.getState().files }\n    if (!updatedFiles[fileID]) {\n      this.log('Was trying to set metadata for a file that has been removed: ', fileID)\n      return\n    }\n    const newMeta = { ...updatedFiles[fileID].meta, ...data }\n    updatedFiles[fileID] = { ...updatedFiles[fileID], meta: newMeta }\n    this.setState({ files: updatedFiles })\n  }\n\n  /**\n   * Get a file object.\n   *\n   * @param {string} fileID The ID of the file object to return.\n   */\n  getFile (fileID) {\n    return this.getState().files[fileID]\n  }\n\n  /**\n   * Get all files in an array.\n   */\n  getFiles () {\n    const { files } = this.getState()\n    return Object.values(files)\n  }\n\n  getObjectOfFilesPerState () {\n    const { files: filesObject, totalProgress, error } = this.getState()\n    const files = Object.values(filesObject)\n    const inProgressFiles = files.filter(({ progress }) => !progress.uploadComplete && progress.uploadStarted)\n    const newFiles =  files.filter((file) => !file.progress.uploadStarted)\n    const startedFiles = files.filter(\n      file => file.progress.uploadStarted || file.progress.preprocess || file.progress.postprocess,\n    )\n    const uploadStartedFiles = files.filter((file) => file.progress.uploadStarted)\n    const pausedFiles = files.filter((file) => file.isPaused)\n    const completeFiles = files.filter((file) => file.progress.uploadComplete)\n    const erroredFiles = files.filter((file) => file.error)\n    const inProgressNotPausedFiles = inProgressFiles.filter((file) => !file.isPaused)\n    const processingFiles = files.filter((file) => file.progress.preprocess || file.progress.postprocess)\n\n    return {\n      newFiles,\n      startedFiles,\n      uploadStartedFiles,\n      pausedFiles,\n      completeFiles,\n      erroredFiles,\n      inProgressFiles,\n      inProgressNotPausedFiles,\n      processingFiles,\n\n      isUploadStarted: uploadStartedFiles.length > 0,\n      isAllComplete: totalProgress === 100\n        && completeFiles.length === files.length\n        && processingFiles.length === 0,\n      isAllErrored: !!error && erroredFiles.length === files.length,\n      isAllPaused: inProgressFiles.length !== 0 && pausedFiles.length === inProgressFiles.length,\n      isUploadInProgress: inProgressFiles.length > 0,\n      isSomeGhost: files.some(file => file.isGhost),\n    }\n  }\n\n  /*\n  * @constructs\n  * @param { Error } error\n  * @param { undefined } file\n  */\n  /*\n  * @constructs\n  * @param { RestrictionError } error\n  * @param { UppyFile | undefined } file\n  */\n  #informAndEmit (error, file) {\n    const { message, details = '' } = error\n\n    if (error.isRestriction) {\n      this.emit('restriction-failed', file, error)\n    } else {\n      this.emit('error', error)\n    }\n    this.info({ message, details }, 'error', this.opts.infoTimeout)\n    this.log(`${message} ${details}`.trim(), 'error')\n  }\n\n  validateRestrictions (file, files = this.getFiles()) {\n    // TODO: directly return the Restriction error in next major version.\n    // we create RestrictionError's just to discard immediately, which doesn't make sense.\n    try {\n      this.#restricter.validate(file, files)\n      return { result: true }\n    } catch (err) {\n      return { result: false, reason: err.message }\n    }\n  }\n\n  #checkRequiredMetaFieldsOnFile (file) {\n    const { missingFields, error } = this.#restricter.getMissingRequiredMetaFields(file)\n\n    if (missingFields.length > 0) {\n      this.setFileState(file.id, { missingRequiredMetaFields: missingFields })\n      this.log(error.message)\n      this.emit('restriction-failed', file, error)\n      return false\n    }\n    return true\n  }\n\n  #checkRequiredMetaFields (files) {\n    let success = true\n    for (const file of Object.values(files)) {\n      if (!this.#checkRequiredMetaFieldsOnFile(file)) {\n        success = false\n      }\n    }\n    return success\n  }\n\n  #assertNewUploadAllowed (file) {\n    const { allowNewUpload } = this.getState()\n\n    if (allowNewUpload === false) {\n      const error = new RestrictionError(this.i18n('noMoreFilesAllowed'))\n      this.#informAndEmit(error, file)\n      throw error\n    }\n  }\n\n  checkIfFileAlreadyExists (fileID) {\n    const { files } = this.getState()\n\n    if (files[fileID] && !files[fileID].isGhost) {\n      return true\n    }\n    return false\n  }\n\n  /**\n   * Create a file state object based on user-provided `addFile()` options.\n   *\n   * Note this is extremely side-effectful and should only be done when a file state object\n   * will be added to state immediately afterward!\n   *\n   * The `files` value is passed in because it may be updated by the caller without updating the store.\n   */\n  #checkAndCreateFileStateObject (files, fileDescriptor) {\n    const fileType = getFileType(fileDescriptor)\n    const fileName = getFileName(fileType, fileDescriptor)\n    const fileExtension = getFileNameAndExtension(fileName).extension\n    const isRemote = Boolean(fileDescriptor.isRemote)\n    const fileID = generateFileID({\n      ...fileDescriptor,\n      type: fileType,\n    })\n\n    if (this.checkIfFileAlreadyExists(fileID)) {\n      const error = new RestrictionError(this.i18n('noDuplicates', { fileName }))\n      this.#informAndEmit(error, fileDescriptor)\n      throw error\n    }\n\n    const meta = fileDescriptor.meta || {}\n    meta.name = fileName\n    meta.type = fileType\n\n    // `null` means the size is unknown.\n    const size = Number.isFinite(fileDescriptor.data.size) ? fileDescriptor.data.size : null\n\n    let newFile = {\n      source: fileDescriptor.source || '',\n      id: fileID,\n      name: fileName,\n      extension: fileExtension || '',\n      meta: {\n        ...this.getState().meta,\n        ...meta,\n      },\n      type: fileType,\n      data: fileDescriptor.data,\n      progress: {\n        percentage: 0,\n        bytesUploaded: 0,\n        bytesTotal: size,\n        uploadComplete: false,\n        uploadStarted: null,\n      },\n      size,\n      isRemote,\n      remote: fileDescriptor.remote || '',\n      preview: fileDescriptor.preview,\n    }\n\n    const onBeforeFileAddedResult = this.opts.onBeforeFileAdded(newFile, files)\n\n    if (onBeforeFileAddedResult === false) {\n      // Don’t show UI info for this error, as it should be done by the developer\n      const error = new RestrictionError('Cannot add the file because onBeforeFileAdded returned false.')\n      this.emit('restriction-failed', fileDescriptor, error)\n      throw error\n    } else if (typeof onBeforeFileAddedResult === 'object' && onBeforeFileAddedResult !== null) {\n      newFile = onBeforeFileAddedResult\n    }\n\n    try {\n      const filesArray = Object.keys(files).map(i => files[i])\n      this.#restricter.validate(newFile, filesArray)\n    } catch (err) {\n      this.#informAndEmit(err, newFile)\n      throw err\n    }\n\n    return newFile\n  }\n\n  // Schedule an upload if `autoProceed` is enabled.\n  #startIfAutoProceed () {\n    if (this.opts.autoProceed && !this.scheduledAutoProceed) {\n      this.scheduledAutoProceed = setTimeout(() => {\n        this.scheduledAutoProceed = null\n        this.upload().catch((err) => {\n          if (!err.isRestriction) {\n            this.log(err.stack || err.message || err)\n          }\n        })\n      }, 4)\n    }\n  }\n\n  /**\n   * Add a new file to `state.files`. This will run `onBeforeFileAdded`,\n   * try to guess file type in a clever way, check file against restrictions,\n   * and start an upload if `autoProceed === true`.\n   *\n   * @param {object} file object to add\n   * @returns {string} id for the added file\n   */\n  addFile (file) {\n    this.#assertNewUploadAllowed(file)\n\n    const { files } = this.getState()\n    let newFile = this.#checkAndCreateFileStateObject(files, file)\n\n    // Users are asked to re-select recovered files without data,\n    // and to keep the progress, meta and everthing else, we only replace said data\n    if (files[newFile.id] && files[newFile.id].isGhost) {\n      newFile = {\n        ...files[newFile.id],\n        data: file.data,\n        isGhost: false,\n      }\n      this.log(`Replaced the blob in the restored ghost file: ${newFile.name}, ${newFile.id}`)\n    }\n\n    this.setState({\n      files: {\n        ...files,\n        [newFile.id]: newFile,\n      },\n    })\n\n    this.emit('file-added', newFile)\n    this.emit('files-added', [newFile])\n    this.log(`Added file: ${newFile.name}, ${newFile.id}, mime type: ${newFile.type}`)\n\n    this.#startIfAutoProceed()\n\n    return newFile.id\n  }\n\n  /**\n   * Add multiple files to `state.files`. See the `addFile()` documentation.\n   *\n   * If an error occurs while adding a file, it is logged and the user is notified.\n   * This is good for UI plugins, but not for programmatic use.\n   * Programmatic users should usually still use `addFile()` on individual files.\n   */\n  addFiles (fileDescriptors) {\n    this.#assertNewUploadAllowed()\n\n    // create a copy of the files object only once\n    const files = { ...this.getState().files }\n    const newFiles = []\n    const errors = []\n    for (let i = 0; i < fileDescriptors.length; i++) {\n      try {\n        let newFile = this.#checkAndCreateFileStateObject(files, fileDescriptors[i])\n        // Users are asked to re-select recovered files without data,\n        // and to keep the progress, meta and everthing else, we only replace said data\n        if (files[newFile.id] && files[newFile.id].isGhost) {\n          newFile = {\n            ...files[newFile.id],\n            data: fileDescriptors[i].data,\n            isGhost: false,\n          }\n          this.log(`Replaced blob in a ghost file: ${newFile.name}, ${newFile.id}`)\n        }\n        files[newFile.id] = newFile\n        newFiles.push(newFile)\n      } catch (err) {\n        if (!err.isRestriction) {\n          errors.push(err)\n        }\n      }\n    }\n\n    this.setState({ files })\n\n    newFiles.forEach((newFile) => {\n      this.emit('file-added', newFile)\n    })\n\n    this.emit('files-added', newFiles)\n\n    if (newFiles.length > 5) {\n      this.log(`Added batch of ${newFiles.length} files`)\n    } else {\n      Object.keys(newFiles).forEach(fileID => {\n        this.log(`Added file: ${newFiles[fileID].name}\\n id: ${newFiles[fileID].id}\\n type: ${newFiles[fileID].type}`)\n      })\n    }\n\n    if (newFiles.length > 0) {\n      this.#startIfAutoProceed()\n    }\n\n    if (errors.length > 0) {\n      let message = 'Multiple errors occurred while adding files:\\n'\n      errors.forEach((subError) => {\n        message += `\\n * ${subError.message}`\n      })\n\n      this.info({\n        message: this.i18n('addBulkFilesFailed', { smart_count: errors.length }),\n        details: message,\n      }, 'error', this.opts.infoTimeout)\n\n      if (typeof AggregateError === 'function') {\n        throw new AggregateError(errors, message)\n      } else {\n        const err = new Error(message)\n        err.errors = errors\n        throw err\n      }\n    }\n  }\n\n  removeFiles (fileIDs, reason) {\n    const { files, currentUploads } = this.getState()\n    const updatedFiles = { ...files }\n    const updatedUploads = { ...currentUploads }\n\n    const removedFiles = Object.create(null)\n    fileIDs.forEach((fileID) => {\n      if (files[fileID]) {\n        removedFiles[fileID] = files[fileID]\n        delete updatedFiles[fileID]\n      }\n    })\n\n    // Remove files from the `fileIDs` list in each upload.\n    function fileIsNotRemoved (uploadFileID) {\n      return removedFiles[uploadFileID] === undefined\n    }\n\n    Object.keys(updatedUploads).forEach((uploadID) => {\n      const newFileIDs = currentUploads[uploadID].fileIDs.filter(fileIsNotRemoved)\n\n      // Remove the upload if no files are associated with it anymore.\n      if (newFileIDs.length === 0) {\n        delete updatedUploads[uploadID]\n        return\n      }\n\n      const { capabilities } = this.getState()\n      if (newFileIDs.length !== currentUploads[uploadID].fileIDs.length\n          && !capabilities.individualCancellation) {\n        throw new Error('individualCancellation is disabled')\n      }\n\n      updatedUploads[uploadID] = {\n        ...currentUploads[uploadID],\n        fileIDs: newFileIDs,\n      }\n    })\n\n    const stateUpdate = {\n      currentUploads: updatedUploads,\n      files: updatedFiles,\n    }\n\n    // If all files were removed - allow new uploads,\n    // and clear recoveredState\n    if (Object.keys(updatedFiles).length === 0) {\n      stateUpdate.allowNewUpload = true\n      stateUpdate.error = null\n      stateUpdate.recoveredState = null\n    }\n\n    this.setState(stateUpdate)\n    this.calculateTotalProgress()\n\n    const removedFileIDs = Object.keys(removedFiles)\n    removedFileIDs.forEach((fileID) => {\n      this.emit('file-removed', removedFiles[fileID], reason)\n    })\n\n    if (removedFileIDs.length > 5) {\n      this.log(`Removed ${removedFileIDs.length} files`)\n    } else {\n      this.log(`Removed files: ${removedFileIDs.join(', ')}`)\n    }\n  }\n\n  removeFile (fileID, reason = null) {\n    this.removeFiles([fileID], reason)\n  }\n\n  pauseResume (fileID) {\n    if (!this.getState().capabilities.resumableUploads\n         || this.getFile(fileID).uploadComplete) {\n      return undefined\n    }\n\n    const wasPaused = this.getFile(fileID).isPaused || false\n    const isPaused = !wasPaused\n\n    this.setFileState(fileID, {\n      isPaused,\n    })\n\n    this.emit('upload-pause', fileID, isPaused)\n\n    return isPaused\n  }\n\n  pauseAll () {\n    const updatedFiles = { ...this.getState().files }\n    const inProgressUpdatedFiles = Object.keys(updatedFiles).filter((file) => {\n      return !updatedFiles[file].progress.uploadComplete\n             && updatedFiles[file].progress.uploadStarted\n    })\n\n    inProgressUpdatedFiles.forEach((file) => {\n      const updatedFile = { ...updatedFiles[file], isPaused: true }\n      updatedFiles[file] = updatedFile\n    })\n\n    this.setState({ files: updatedFiles })\n    this.emit('pause-all')\n  }\n\n  resumeAll () {\n    const updatedFiles = { ...this.getState().files }\n    const inProgressUpdatedFiles = Object.keys(updatedFiles).filter((file) => {\n      return !updatedFiles[file].progress.uploadComplete\n             && updatedFiles[file].progress.uploadStarted\n    })\n\n    inProgressUpdatedFiles.forEach((file) => {\n      const updatedFile = {\n        ...updatedFiles[file],\n        isPaused: false,\n        error: null,\n      }\n      updatedFiles[file] = updatedFile\n    })\n    this.setState({ files: updatedFiles })\n\n    this.emit('resume-all')\n  }\n\n  retryAll () {\n    const updatedFiles = { ...this.getState().files }\n    const filesToRetry = Object.keys(updatedFiles).filter(file => {\n      return updatedFiles[file].error\n    })\n\n    filesToRetry.forEach((file) => {\n      const updatedFile = {\n        ...updatedFiles[file],\n        isPaused: false,\n        error: null,\n      }\n      updatedFiles[file] = updatedFile\n    })\n    this.setState({\n      files: updatedFiles,\n      error: null,\n    })\n\n    this.emit('retry-all', filesToRetry)\n\n    if (filesToRetry.length === 0) {\n      return Promise.resolve({\n        successful: [],\n        failed: [],\n      })\n    }\n\n    const uploadID = this.#createUpload(filesToRetry, {\n      forceAllowNewUpload: true, // create new upload even if allowNewUpload: false\n    })\n    return this.#runUpload(uploadID)\n  }\n\n  cancelAll ({ reason = 'user' } = {}) {\n    this.emit('cancel-all', { reason })\n\n    // Only remove existing uploads if user is canceling\n    if (reason === 'user') {\n      const { files } = this.getState()\n\n      const fileIDs = Object.keys(files)\n      if (fileIDs.length) {\n        this.removeFiles(fileIDs, 'cancel-all')\n      }\n\n      this.setState({\n        totalProgress: 0,\n        error: null,\n        recoveredState: null,\n      })\n    }\n  }\n\n  retryUpload (fileID) {\n    this.setFileState(fileID, {\n      error: null,\n      isPaused: false,\n    })\n\n    this.emit('upload-retry', fileID)\n\n    const uploadID = this.#createUpload([fileID], {\n      forceAllowNewUpload: true, // create new upload even if allowNewUpload: false\n    })\n    return this.#runUpload(uploadID)\n  }\n\n  // todo remove in next major. what is the point of the reset method when we have cancelAll or vice versa?\n  reset (...args) {\n    this.cancelAll(...args)\n  }\n\n  logout () {\n    this.iteratePlugins(plugin => {\n      if (plugin.provider && plugin.provider.logout) {\n        plugin.provider.logout()\n      }\n    })\n  }\n\n  calculateProgress (file, data) {\n    if (file == null || !this.getFile(file.id)) {\n      this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n      return\n    }\n\n    // bytesTotal may be null or zero; in that case we can't divide by it\n    const canHavePercentage = Number.isFinite(data.bytesTotal) && data.bytesTotal > 0\n    this.setFileState(file.id, {\n      progress: {\n        ...this.getFile(file.id).progress,\n        bytesUploaded: data.bytesUploaded,\n        bytesTotal: data.bytesTotal,\n        percentage: canHavePercentage\n          ? Math.round((data.bytesUploaded / data.bytesTotal) * 100)\n          : 0,\n      },\n    })\n\n    this.calculateTotalProgress()\n  }\n\n  calculateTotalProgress () {\n    // calculate total progress, using the number of files currently uploading,\n    // multiplied by 100 and the summ of individual progress of each file\n    const files = this.getFiles()\n\n    const inProgress = files.filter((file) => {\n      return file.progress.uploadStarted\n        || file.progress.preprocess\n        || file.progress.postprocess\n    })\n\n    if (inProgress.length === 0) {\n      this.emit('progress', 0)\n      this.setState({ totalProgress: 0 })\n      return\n    }\n\n    const sizedFiles = inProgress.filter((file) => file.progress.bytesTotal != null)\n    const unsizedFiles = inProgress.filter((file) => file.progress.bytesTotal == null)\n\n    if (sizedFiles.length === 0) {\n      const progressMax = inProgress.length * 100\n      const currentProgress = unsizedFiles.reduce((acc, file) => {\n        return acc + file.progress.percentage\n      }, 0)\n      const totalProgress = Math.round((currentProgress / progressMax) * 100)\n      this.setState({ totalProgress })\n      return\n    }\n\n    let totalSize = sizedFiles.reduce((acc, file) => {\n      return acc + file.progress.bytesTotal\n    }, 0)\n    const averageSize = totalSize / sizedFiles.length\n    totalSize += averageSize * unsizedFiles.length\n\n    let uploadedSize = 0\n    sizedFiles.forEach((file) => {\n      uploadedSize += file.progress.bytesUploaded\n    })\n    unsizedFiles.forEach((file) => {\n      uploadedSize += (averageSize * (file.progress.percentage || 0)) / 100\n    })\n\n    let totalProgress = totalSize === 0\n      ? 0\n      : Math.round((uploadedSize / totalSize) * 100)\n\n    // hot fix, because:\n    // uploadedSize ended up larger than totalSize, resulting in 1325% total\n    if (totalProgress > 100) {\n      totalProgress = 100\n    }\n\n    this.setState({ totalProgress })\n    this.emit('progress', totalProgress)\n  }\n\n  /**\n   * Registers listeners for all global actions, like:\n   * `error`, `file-removed`, `upload-progress`\n   */\n  #addListeners () {\n    /**\n     * @param {Error} error\n     * @param {object} [file]\n     * @param {object} [response]\n     */\n    const errorHandler = (error, file, response) => {\n      let errorMsg = error.message || 'Unknown error'\n      if (error.details) {\n        errorMsg += ` ${error.details}`\n      }\n\n      this.setState({ error: errorMsg })\n\n      if (file != null && file.id in this.getState().files) {\n        this.setFileState(file.id, {\n          error: errorMsg,\n          response,\n        })\n      }\n    }\n\n    this.on('error', errorHandler)\n\n    this.on('upload-error', (file, error, response) => {\n      errorHandler(error, file, response)\n\n      if (typeof error === 'object' && error.message) {\n        const newError = new Error(error.message)\n        newError.details = error.message\n        if (error.details) {\n          newError.details += ` ${error.details}`\n        }\n        newError.message = this.i18n('failedToUpload', { file: file?.name })\n        this.#informAndEmit(newError)\n      } else {\n        this.#informAndEmit(error)\n      }\n    })\n\n    this.on('upload', () => {\n      this.setState({ error: null })\n    })\n\n    this.on('upload-started', (file) => {\n      if (file == null || !this.getFile(file.id)) {\n        this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n        return\n      }\n      this.setFileState(file.id, {\n        progress: {\n          uploadStarted: Date.now(),\n          uploadComplete: false,\n          percentage: 0,\n          bytesUploaded: 0,\n          bytesTotal: file.size,\n        },\n      })\n    })\n\n    this.on('upload-progress', this.calculateProgress)\n\n    this.on('upload-success', (file, uploadResp) => {\n      if (file == null || !this.getFile(file.id)) {\n        this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n        return\n      }\n\n      const currentProgress = this.getFile(file.id).progress\n      this.setFileState(file.id, {\n        progress: {\n          ...currentProgress,\n          postprocess: this.#postProcessors.size > 0 ? {\n            mode: 'indeterminate',\n          } : null,\n          uploadComplete: true,\n          percentage: 100,\n          bytesUploaded: currentProgress.bytesTotal,\n        },\n        response: uploadResp,\n        uploadURL: uploadResp.uploadURL,\n        isPaused: false,\n      })\n\n      // Remote providers sometimes don't tell us the file size,\n      // but we can know how many bytes we uploaded once the upload is complete.\n      if (file.size == null) {\n        this.setFileState(file.id, {\n          size: uploadResp.bytesUploaded || currentProgress.bytesTotal,\n        })\n      }\n\n      this.calculateTotalProgress()\n    })\n\n    this.on('preprocess-progress', (file, progress) => {\n      if (file == null || !this.getFile(file.id)) {\n        this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n        return\n      }\n      this.setFileState(file.id, {\n        progress: { ...this.getFile(file.id).progress, preprocess: progress },\n      })\n    })\n\n    this.on('preprocess-complete', (file) => {\n      if (file == null || !this.getFile(file.id)) {\n        this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n        return\n      }\n      const files = { ...this.getState().files }\n      files[file.id] = { ...files[file.id], progress: { ...files[file.id].progress } }\n      delete files[file.id].progress.preprocess\n\n      this.setState({ files })\n    })\n\n    this.on('postprocess-progress', (file, progress) => {\n      if (file == null || !this.getFile(file.id)) {\n        this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n        return\n      }\n      this.setFileState(file.id, {\n        progress: { ...this.getState().files[file.id].progress, postprocess: progress },\n      })\n    })\n\n    this.on('postprocess-complete', (file) => {\n      if (file == null || !this.getFile(file.id)) {\n        this.log(`Not setting progress for a file that has been removed: ${file?.id}`)\n        return\n      }\n      const files = {\n        ...this.getState().files,\n      }\n      files[file.id] = {\n        ...files[file.id],\n        progress: {\n          ...files[file.id].progress,\n        },\n      }\n      delete files[file.id].progress.postprocess\n\n      this.setState({ files })\n    })\n\n    this.on('restored', () => {\n      // Files may have changed--ensure progress is still accurate.\n      this.calculateTotalProgress()\n    })\n\n    this.on('dashboard:file-edit-complete', (file) => {\n      if (file) {\n        this.#checkRequiredMetaFieldsOnFile(file)\n      }\n    })\n\n    // show informer if offline\n    if (typeof window !== 'undefined' && window.addEventListener) {\n      window.addEventListener('online', this.#updateOnlineStatus)\n      window.addEventListener('offline', this.#updateOnlineStatus)\n      setTimeout(this.#updateOnlineStatus, 3000)\n    }\n  }\n\n  updateOnlineStatus () {\n    const online = typeof window.navigator.onLine !== 'undefined'\n      ? window.navigator.onLine\n      : true\n    if (!online) {\n      this.emit('is-offline')\n      this.info(this.i18n('noInternetConnection'), 'error', 0)\n      this.wasOffline = true\n    } else {\n      this.emit('is-online')\n      if (this.wasOffline) {\n        this.emit('back-online')\n        this.info(this.i18n('connectedToInternet'), 'success', 3000)\n        this.wasOffline = false\n      }\n    }\n  }\n\n  #updateOnlineStatus = this.updateOnlineStatus.bind(this)\n\n  getID () {\n    return this.opts.id\n  }\n\n  /**\n   * Registers a plugin with Core.\n   *\n   * @param {object} Plugin object\n   * @param {object} [opts] object with options to be passed to Plugin\n   * @returns {object} self for chaining\n   */\n  // eslint-disable-next-line no-shadow\n  use (Plugin, opts) {\n    if (typeof Plugin !== 'function') {\n      const msg = `Expected a plugin class, but got ${Plugin === null ? 'null' : typeof Plugin}.`\n        + ' Please verify that the plugin was imported and spelled correctly.'\n      throw new TypeError(msg)\n    }\n\n    // Instantiate\n    const plugin = new Plugin(this, opts)\n    const pluginId = plugin.id\n\n    if (!pluginId) {\n      throw new Error('Your plugin must have an id')\n    }\n\n    if (!plugin.type) {\n      throw new Error('Your plugin must have a type')\n    }\n\n    const existsPluginAlready = this.getPlugin(pluginId)\n    if (existsPluginAlready) {\n      const msg = `Already found a plugin named '${existsPluginAlready.id}'. `\n        + `Tried to use: '${pluginId}'.\\n`\n        + 'Uppy plugins must have unique `id` options. See https://uppy.io/docs/plugins/#id.'\n      throw new Error(msg)\n    }\n\n    if (Plugin.VERSION) {\n      this.log(`Using ${pluginId} v${Plugin.VERSION}`)\n    }\n\n    if (plugin.type in this.#plugins) {\n      this.#plugins[plugin.type].push(plugin)\n    } else {\n      this.#plugins[plugin.type] = [plugin]\n    }\n    plugin.install()\n\n    return this\n  }\n\n  /**\n   * Find one Plugin by name.\n   *\n   * @param {string} id plugin id\n   * @returns {BasePlugin|undefined}\n   */\n  getPlugin (id) {\n    for (const plugins of Object.values(this.#plugins)) {\n      const foundPlugin = plugins.find(plugin => plugin.id === id)\n      if (foundPlugin != null) return foundPlugin\n    }\n    return undefined\n  }\n\n  [Symbol.for('uppy test: getPlugins')] (type) {\n    return this.#plugins[type]\n  }\n\n  /**\n   * Iterate through all `use`d plugins.\n   *\n   * @param {Function} method that will be run on each plugin\n   */\n  iteratePlugins (method) {\n    Object.values(this.#plugins).flat(1).forEach(method)\n  }\n\n  /**\n   * Uninstall and remove a plugin.\n   *\n   * @param {object} instance The plugin instance to remove.\n   */\n  removePlugin (instance) {\n    this.log(`Removing plugin ${instance.id}`)\n    this.emit('plugin-remove', instance)\n\n    if (instance.uninstall) {\n      instance.uninstall()\n    }\n\n    const list = this.#plugins[instance.type]\n    // list.indexOf failed here, because Vue3 converted the plugin instance\n    // to a Proxy object, which failed the strict comparison test:\n    // obj !== objProxy\n    const index = list.findIndex(item => item.id === instance.id)\n    if (index !== -1) {\n      list.splice(index, 1)\n    }\n\n    const state = this.getState()\n    const updatedState = {\n      plugins: {\n        ...state.plugins,\n        [instance.id]: undefined,\n      },\n    }\n    this.setState(updatedState)\n  }\n\n  /**\n   * Uninstall all plugins and close down this Uppy instance.\n   */\n  close ({ reason } = {}) {\n    this.log(`Closing Uppy instance ${this.opts.id}: removing all files and uninstalling plugins`)\n\n    this.cancelAll({ reason })\n\n    this.#storeUnsubscribe()\n\n    this.iteratePlugins((plugin) => {\n      this.removePlugin(plugin)\n    })\n\n    if (typeof window !== 'undefined' && window.removeEventListener) {\n      window.removeEventListener('online', this.#updateOnlineStatus)\n      window.removeEventListener('offline', this.#updateOnlineStatus)\n    }\n  }\n\n  hideInfo () {\n    const { info } = this.getState()\n\n    this.setState({ info: info.slice(1) })\n\n    this.emit('info-hidden')\n  }\n\n  /**\n   * Set info message in `state.info`, so that UI plugins like `Informer`\n   * can display the message.\n   *\n   * @param {string | object} message Message to be displayed by the informer\n   * @param {string} [type]\n   * @param {number} [duration]\n   */\n  info (message, type = 'info', duration = 3000) {\n    const isComplexMessage = typeof message === 'object'\n\n    this.setState({\n      info: [\n        ...this.getState().info,\n        {\n          type,\n          message: isComplexMessage ? message.message : message,\n          details: isComplexMessage ? message.details : null,\n        },\n      ],\n    })\n\n    setTimeout(() => this.hideInfo(), duration)\n\n    this.emit('info-visible')\n  }\n\n  /**\n   * Passes messages to a function, provided in `opts.logger`.\n   * If `opts.logger: Uppy.debugLogger` or `opts.debug: true`, logs to the browser console.\n   *\n   * @param {string|object} message to log\n   * @param {string} [type] optional `error` or `warning`\n   */\n  log (message, type) {\n    const { logger } = this.opts\n    switch (type) {\n      case 'error': logger.error(message); break\n      case 'warning': logger.warn(message); break\n      default: logger.debug(message); break\n    }\n  }\n\n  /**\n   * Restore an upload by its ID.\n   */\n  restore (uploadID) {\n    this.log(`Core: attempting to restore upload \"${uploadID}\"`)\n\n    if (!this.getState().currentUploads[uploadID]) {\n      this.#removeUpload(uploadID)\n      return Promise.reject(new Error('Nonexistent upload'))\n    }\n\n    return this.#runUpload(uploadID)\n  }\n\n  /**\n   * Create an upload for a bunch of files.\n   *\n   * @param {Array<string>} fileIDs File IDs to include in this upload.\n   * @returns {string} ID of this upload.\n   */\n  #createUpload (fileIDs, opts = {}) {\n    // uppy.retryAll sets this to true — when retrying we want to ignore `allowNewUpload: false`\n    const { forceAllowNewUpload = false } = opts\n\n    const { allowNewUpload, currentUploads } = this.getState()\n    if (!allowNewUpload && !forceAllowNewUpload) {\n      throw new Error('Cannot create a new upload: already uploading.')\n    }\n\n    const uploadID = nanoid()\n\n    this.emit('upload', {\n      id: uploadID,\n      fileIDs,\n    })\n\n    this.setState({\n      allowNewUpload: this.opts.allowMultipleUploadBatches !== false && this.opts.allowMultipleUploads !== false,\n\n      currentUploads: {\n        ...currentUploads,\n        [uploadID]: {\n          fileIDs,\n          step: 0,\n          result: {},\n        },\n      },\n    })\n\n    return uploadID\n  }\n\n  [Symbol.for('uppy test: createUpload')] (...args) { return this.#createUpload(...args) }\n\n  #getUpload (uploadID) {\n    const { currentUploads } = this.getState()\n\n    return currentUploads[uploadID]\n  }\n\n  /**\n   * Add data to an upload's result object.\n   *\n   * @param {string} uploadID The ID of the upload.\n   * @param {object} data Data properties to add to the result object.\n   */\n  addResultData (uploadID, data) {\n    if (!this.#getUpload(uploadID)) {\n      this.log(`Not setting result for an upload that has been removed: ${uploadID}`)\n      return\n    }\n    const { currentUploads } = this.getState()\n    const currentUpload = { ...currentUploads[uploadID], result: { ...currentUploads[uploadID].result, ...data } }\n    this.setState({\n      currentUploads: { ...currentUploads, [uploadID]: currentUpload },\n    })\n  }\n\n  /**\n   * Remove an upload, eg. if it has been canceled or completed.\n   *\n   * @param {string} uploadID The ID of the upload.\n   */\n  #removeUpload (uploadID) {\n    const currentUploads = { ...this.getState().currentUploads }\n    delete currentUploads[uploadID]\n\n    this.setState({\n      currentUploads,\n    })\n  }\n\n  /**\n   * Run an upload. This picks up where it left off in case the upload is being restored.\n   *\n   * @private\n   */\n  async #runUpload (uploadID) {\n    let { currentUploads } = this.getState()\n    let currentUpload = currentUploads[uploadID]\n    const restoreStep = currentUpload.step || 0\n\n    const steps = [\n      ...this.#preProcessors,\n      ...this.#uploaders,\n      ...this.#postProcessors,\n    ]\n    try {\n      for (let step = restoreStep; step < steps.length; step++) {\n        if (!currentUpload) {\n          break\n        }\n        const fn = steps[step]\n\n        const updatedUpload = {\n          ...currentUpload,\n          step,\n        }\n\n        this.setState({\n          currentUploads: {\n            ...currentUploads,\n            [uploadID]: updatedUpload,\n          },\n        })\n\n        // TODO give this the `updatedUpload` object as its only parameter maybe?\n        // Otherwise when more metadata may be added to the upload this would keep getting more parameters\n        await fn(updatedUpload.fileIDs, uploadID)\n\n        // Update currentUpload value in case it was modified asynchronously.\n        currentUploads = this.getState().currentUploads\n        currentUpload = currentUploads[uploadID]\n      }\n    } catch (err) {\n      this.#removeUpload(uploadID)\n      throw err\n    }\n\n    // Set result data.\n    if (currentUpload) {\n      // Mark postprocessing step as complete if necessary; this addresses a case where we might get\n      // stuck in the postprocessing UI while the upload is fully complete.\n      // If the postprocessing steps do not do any work, they may not emit postprocessing events at\n      // all, and never mark the postprocessing as complete. This is fine on its own but we\n      // introduced code in the @uppy/core upload-success handler to prepare postprocessing progress\n      // state if any postprocessors are registered. That is to avoid a \"flash of completed state\"\n      // before the postprocessing plugins can emit events.\n      //\n      // So, just in case an upload with postprocessing plugins *has* completed *without* emitting\n      // postprocessing completion, we do it instead.\n      currentUpload.fileIDs.forEach((fileID) => {\n        const file = this.getFile(fileID)\n        if (file && file.progress.postprocess) {\n          this.emit('postprocess-complete', file)\n        }\n      })\n\n      const files = currentUpload.fileIDs.map((fileID) => this.getFile(fileID))\n      const successful = files.filter((file) => !file.error)\n      const failed = files.filter((file) => file.error)\n      await this.addResultData(uploadID, { successful, failed, uploadID })\n\n      // Update currentUpload value in case it was modified asynchronously.\n      currentUploads = this.getState().currentUploads\n      currentUpload = currentUploads[uploadID]\n    }\n    // Emit completion events.\n    // This is in a separate function so that the `currentUploads` variable\n    // always refers to the latest state. In the handler right above it refers\n    // to an outdated object without the `.result` property.\n    let result\n    if (currentUpload) {\n      result = currentUpload.result\n      this.emit('complete', result)\n\n      this.#removeUpload(uploadID)\n    }\n    if (result == null) {\n      this.log(`Not setting result for an upload that has been removed: ${uploadID}`)\n    }\n    return result\n  }\n\n  /**\n   * Start an upload for all the files that are not currently being uploaded.\n   *\n   * @returns {Promise}\n   */\n  upload () {\n    if (!this.#plugins.uploader?.length) {\n      this.log('No uploader type plugins are used', 'warning')\n    }\n\n    let { files } = this.getState()\n\n    const onBeforeUploadResult = this.opts.onBeforeUpload(files)\n\n    if (onBeforeUploadResult === false) {\n      return Promise.reject(new Error('Not starting the upload because onBeforeUpload returned false'))\n    }\n\n    if (onBeforeUploadResult && typeof onBeforeUploadResult === 'object') {\n      files = onBeforeUploadResult\n      // Updating files in state, because uploader plugins receive file IDs,\n      // and then fetch the actual file object from state\n      this.setState({\n        files,\n      })\n    }\n\n    return Promise.resolve()\n      .then(() => this.#restricter.validateMinNumberOfFiles(files))\n      .catch((err) => {\n        this.#informAndEmit(err)\n        throw err\n      })\n      .then(() => {\n        if (!this.#checkRequiredMetaFields(files)) {\n          throw new RestrictionError(this.i18n('missingRequiredMetaField'))\n        }\n      })\n      .catch((err) => {\n        // Doing this in a separate catch because we already emited and logged\n        // all the errors in `checkRequiredMetaFields` so we only throw a generic\n        // missing fields error here.\n        throw err\n      })\n      .then(() => {\n        const { currentUploads } = this.getState()\n        // get a list of files that are currently assigned to uploads\n        const currentlyUploadingFiles = Object.values(currentUploads).flatMap(curr => curr.fileIDs)\n\n        const waitingFileIDs = []\n        Object.keys(files).forEach((fileID) => {\n          const file = this.getFile(fileID)\n          // if the file hasn't started uploading and hasn't already been assigned to an upload..\n          if ((!file.progress.uploadStarted) && (currentlyUploadingFiles.indexOf(fileID) === -1)) {\n            waitingFileIDs.push(file.id)\n          }\n        })\n\n        const uploadID = this.#createUpload(waitingFileIDs)\n        return this.#runUpload(uploadID)\n      })\n      .catch((err) => {\n        this.emit('error', err)\n        this.log(err, 'error')\n        throw err\n      })\n  }\n}\n\nexport default Uppy\n"]}