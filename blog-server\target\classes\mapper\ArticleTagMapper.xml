<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.mapper.ArticleTagMapper">

    <!-- 批量插入文章标签关联 -->
    <insert id="insertBatch">
        INSERT INTO article_tag (article_id, tag_id)
        VALUES
        <foreach collection="tagIds" item="tagId" separator=",">
            (#{articleId}, #{tagId})
        </foreach>
    </insert>
    
    <!-- 根据文章ID删除所有关联 -->
    <delete id="deleteByArticleId">
        DELETE FROM article_tag WHERE article_id = #{articleId}
    </delete>
    
    <!-- 根据标签ID删除所有关联 -->
    <delete id="deleteByTagId">
        DELETE FROM article_tag WHERE tag_id = #{tagId}
    </delete>

</mapper> 