{"version": 3, "file": "attachto.js", "sourceRoot": "", "sources": ["../../src/helpers/attachto.ts"], "names": [], "mappings": "AAiBA,SAAS,GAAG,CAAC,KAA0B,EAAE,QAA6B;IACpE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;IACzC,2DAA2D;IAC3D,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;IAC9D,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;IAChD,kEAAkE;IAClE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACzC,CAAC;AAED,SAAS,IAAI,CAAC,CAAM,EAAE,KAA0B;IAC9C,gEAAgE;IAChE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;AAChD,CAAC;AAED,SAAS,OAAO,CAAC,KAA0B;IACzC,qBAAqB;IACrB,IAAI,KAAK,CAAC,GAAG,KAAK,SAAS,EAAE;QAC1B,KAAK,CAAC,GAAG,CAAC,UAA0B,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC9D;IACD,iDAAiD;IACjD,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACzC,CAAC;AAED,SAAS,MAAM,CAAC,CAAM,EAAE,KAA0B;IAChD,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;IACvB,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;IACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACnD,gDAAgD;IAChD,gDAAgD;IAChD,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC;IACxB,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;IACvB,UAAU,CAAC,WAAW,GAAG,WAAW,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,QAAQ,CAAC,MAAe,EAAE,KAAY;IACpD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS;QAAE,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;IAC9C,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS;QAAE,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;IACxD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IACxB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IAC7B,IAAI,CAAC,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAC9E,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,OAAO,KAAK,CAAC;AACf,CAAC"}