# 个人动态博客系统 - 环境配置指南

本文档提供了个人动态博客系统的环境配置指南，包括开发环境配置、系统配置和常见问题解决方案。

## 1. 开发环境要求

### 1.1 后端环境

- **JDK**: 1.8
- **构建工具**: Maven 3.6+
- **IDE**: IntelliJ IDEA 2023.3.8 (推荐)
- **数据库**: MySQL 8.0+
- **缓存**: Redis (可选)

### 1.2 前端环境

- **Node.js**: 16.x+
- **包管理器**: npm 8.x+
- **IDE**: Visual Studio Code (推荐)

## 2. 数据库配置

### 2.1 MySQL配置

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: root
    password: 12345
    type: com.alibaba.druid.pool.DruidDataSource
```

### 2.2 Redis配置 (可选)

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 10000
```

## 3. 文件上传与资源访问配置

### 3.1 基础配置

在`application.yml`中配置文件上传相关参数：

```yaml
# 文件上传配置
file:
  upload:
    path: uploads  # 文件上传的物理存储目录，相对于项目根目录
  access:
    path: /upload/  # 文件访问的URL路径前缀
```

### 3.2 资源处理器配置

系统使用`WebMvcConfig`配置静态资源访问：

```java
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${file.upload.path:upload}")
    private String uploadPath;

    @Value("${file.access.path:/upload/}")
    private String accessPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取项目根目录
        String projectPath = System.getProperty("user.dir");
        
        // 配置上传文件的访问路径
        String uploadRealPath = projectPath + File.separator + uploadPath;
        
        // 添加资源处理器 - 不包含上下文路径的访问方式
        registry.addResourceHandler(accessPath + "**")
                .addResourceLocations("file:" + uploadRealPath + File.separator);
        
        // 添加资源处理器 - 包含上下文路径的访问方式
        registry.addResourceHandler("/api" + accessPath + "**")
                .addResourceLocations("file:" + uploadRealPath + File.separator);
    }
}
```

### 3.3 安全配置

在`SecurityConfig`中配置静态资源的访问权限：

```java
@Override
protected void configure(HttpSecurity httpSecurity) throws Exception {
    httpSecurity
        // ...其他配置...
        .authorizeRequests()
        // 允许对于网站静态资源的无授权访问
        .antMatchers(HttpMethod.GET,
                "/",
                "/*.html",
                "/favicon.ico",
                "/**/*.html",
                "/**/*.css",
                "/**/*.js",
                "/swagger-resources/**",
                "/v2/api-docs/**",
                "/upload/**"  // 允许静态资源路径匿名访问
        ).permitAll()
        // 文件上传接口允许匿名访问
        .antMatchers(HttpMethod.GET, "/upload/**").permitAll()
        .antMatchers(HttpMethod.POST, "/upload/**").permitAll();
}
```

### 3.4 前端资源URL构建

前端使用统一的资源URL构建函数：

```javascript
/**
 * 构建资源URL
 * @param {string} path 资源路径
 * @returns {string} 完整的资源URL
 */
export function buildResourceUrl(path) {
  if (!path) return '';
  
  // 如果是完整URL（以http或https开头），直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  // 如果是相对路径（以/开头）
  if (path.startsWith('/')) {
    // 判断路径是否已包含API前缀
    if (path.startsWith(apiPrefix)) {
      return `${apiUrl}${path}`;
    } else {
      return `${apiUrl}${apiPrefix}${path}`;
    }
  }
  
  // 其他情况，假设是相对路径但没有/开头
  return `${apiUrl}${apiPrefix}/${path}`;
}
```

## 4. JWT认证配置

```yaml
# JWT配置
jwt:
  # JWT密钥
  secret: blogSystem123456
  # JWT过期时间（毫秒）
  expiration: 604800000  # 7天
  # JWT头部
  tokenHeader: Authorization
  # JWT负载中拿到开头
  tokenHead: Bearer
```

## 5. 跨域配置

```java
@Override
public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600);
}
```

## 6. 常见问题与解决方案

### 6.1 图片上传后不显示

**问题描述**：上传图片后，前端页面无法显示图片。

**可能原因**：
1. 资源URL构建错误，前端拼接的URL格式不正确
2. 静态资源映射配置错误
3. 安全配置阻止了静态资源的访问

**解决方案**：
1. 使用统一的`buildResourceUrl`函数构建资源URL
2. 确保`WebMvcConfig`中正确配置了资源处理器
3. 在`SecurityConfig`中允许对静态资源路径的匿名访问
4. 检查文件上传目录是否存在且有正确的读写权限

### 6.2 跨域问题

**问题描述**：前端请求后端API时出现跨域错误。

**解决方案**：
1. 确保正确配置了`addCorsMappings`方法
2. 检查前端请求是否包含正确的`Origin`头
3. 如果使用了代理，确保代理配置正确

### 6.3 认证问题

**问题描述**：用户登录后无法访问需要认证的API。

**解决方案**：
1. 检查JWT token是否正确生成和验证
2. 确保前端请求中包含正确格式的`Authorization`头
3. 检查用户角色和权限配置

## 7. 部署配置

### 7.1 开发环境

```yaml
server:
  port: 8080
  servlet:
    context-path: /api
```

### 7.2 生产环境

```yaml
server:
  port: 8080
  servlet:
    context-path: /api

# 生产环境特定配置
spring:
  datasource:
    # 配置生产环境数据库连接
  redis:
    # 配置生产环境Redis连接
```

## 8. 日志配置

```yaml
# 日志配置
logging:
  level:
    com.blog: info
    org.springframework: warn
```

## 9. 环境切换

使用Spring Boot的profile机制切换环境：

```bash
# 开发环境
java -jar blog-server.jar --spring.profiles.active=dev

# 生产环境
java -jar blog-server.jar --spring.profiles.active=prod
```

## 10. 配置文件加密

对敏感信息（如数据库密码）进行加密：

1. 使用Jasypt等工具加密敏感配置
2. 在启动时提供解密密钥 