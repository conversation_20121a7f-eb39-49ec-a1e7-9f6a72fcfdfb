{"name": "@wangeditor/editor-for-vue", "version": "5.1.12", "description": "wangEditor component for vue@next", "author": "liuqh0609 <<EMAIL>>", "homepage": "http://www.wangeditor.com/", "main": "./dist/index.js", "module": "./dist/index.esm.js", "license": "MIT", "types": "dist/src/index.d.ts", "files": ["dist"], "keywords": ["wang<PERSON><PERSON><PERSON>", "富文本编辑器", "富文本", "vue3"], "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/wangeditor-team/wangEditor-for-vue3.git"}, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "serve": "vite preview", "release": "release-it", "test": "jest"}, "peerDependencies": {"@wangeditor/editor": ">=5.1.0", "vue": "^3.0.5"}, "dependencies": {}, "devDependencies": {"@babel/preset-env": "^7.15.8", "@testing-library/jest-dom": "^5.14.1", "@types/jest": "^27.0.2", "@types/lodash": "^4.14.172", "@types/node": "^16.7.1", "@typescript-eslint/parser": "^4.29.3", "@vitejs/plugin-vue": "^1.3.0", "@vue/compiler-sfc": "^3.0.5", "@vue/test-utils": "^2.0.0-rc.16", "@wangeditor/editor": "^5.1.1", "babel-jest": "^26.6.3", "jest": "25.5.4", "lodash": "^4.17.21", "prettier": "^2.5.1", "release-it": "^14.11.5", "ts-jest": "^25.3.1", "typescript": "^4.3.2", "vite": "^2.4.4", "vite-plugin-dts": "^0.7.0", "vue": "^3.2.7", "vue-jest": "^5.0.0-alpha.10", "vue-tsc": "^0.2.2"}}