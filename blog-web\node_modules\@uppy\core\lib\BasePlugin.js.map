{"version": 3, "sources": ["BasePlugin.js"], "names": ["Translator", "BasePlugin", "constructor", "uppy", "opts", "getPluginState", "plugins", "getState", "id", "setPluginState", "update", "setState", "setOptions", "newOpts", "i18nInit", "translator", "defaultLocale", "locale", "i18n", "translate", "bind", "i18nArray", "translateArray", "addTarget", "Error", "install", "uninstall", "render", "afterUpdate"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAEOA,U;;AAEQ,MAAMC,UAAN,CAAiB;AAC9BC,EAAAA,WAAW,CAAEC,IAAF,EAAQC,IAAR,EAAmB;AAAA,QAAXA,IAAW;AAAXA,MAAAA,IAAW,GAAJ,EAAI;AAAA;;AAC5B,SAAKD,IAAL,GAAYA,IAAZ;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACD;;AAEDC,EAAAA,cAAc,GAAI;AAChB,UAAM;AAAEC,MAAAA;AAAF,QAAc,KAAKH,IAAL,CAAUI,QAAV,EAApB;AACA,WAAOD,OAAO,CAAC,KAAKE,EAAN,CAAP,IAAoB,EAA3B;AACD;;AAEDC,EAAAA,cAAc,CAAEC,MAAF,EAAU;AACtB,UAAM;AAAEJ,MAAAA;AAAF,QAAc,KAAKH,IAAL,CAAUI,QAAV,EAApB;AAEA,SAAKJ,IAAL,CAAUQ,QAAV,CAAmB;AACjBL,MAAAA,OAAO,EAAE,EACP,GAAGA,OADI;AAEP,SAAC,KAAKE,EAAN,GAAW,EACT,GAAGF,OAAO,CAAC,KAAKE,EAAN,CADD;AAET,aAAGE;AAFM;AAFJ;AADQ,KAAnB;AASD;;AAEDE,EAAAA,UAAU,CAAEC,OAAF,EAAW;AACnB,SAAKT,IAAL,GAAY,EAAE,GAAG,KAAKA,IAAV;AAAgB,SAAGS;AAAnB,KAAZ;AACA,SAAKJ,cAAL,GAFmB,CAEG;;AACtB,SAAKK,QAAL;AACD;;AAEDA,EAAAA,QAAQ,GAAI;AACV,UAAMC,UAAU,GAAG,IAAIf,UAAJ,CAAe,CAAC,KAAKgB,aAAN,EAAqB,KAAKb,IAAL,CAAUc,MAA/B,EAAuC,KAAKb,IAAL,CAAUa,MAAjD,CAAf,CAAnB;AACA,SAAKC,IAAL,GAAYH,UAAU,CAACI,SAAX,CAAqBC,IAArB,CAA0BL,UAA1B,CAAZ;AACA,SAAKM,SAAL,GAAiBN,UAAU,CAACO,cAAX,CAA0BF,IAA1B,CAA+BL,UAA/B,CAAjB;AACA,SAAKN,cAAL,GAJU,CAIY;AACvB;AAED;AACF;AACA;AACA;AACA;AACA;AAEE;;;AACAc,EAAAA,SAAS,GAAI;AACX,UAAM,IAAIC,KAAJ,CAAU,4EAAV,CAAN;AACD,GAhD6B,CAkD9B;;;AACAC,EAAAA,OAAO,GAAI,CAAE,CAnDiB,CAqD9B;;;AACAC,EAAAA,SAAS,GAAI,CAAE;AAEf;AACF;AACA;AACA;AACA;AACA;;;AACEC,EAAAA,MAAM,GAAI;AACR,UAAM,IAAIH,KAAJ,CAAU,8DAAV,CAAN;AACD,GAhE6B,CAkE9B;AACA;AACA;;;AACAd,EAAAA,MAAM,GAAI,CAAE,CArEkB,CAuE9B;AACA;;;AACAkB,EAAAA,WAAW,GAAI,CAAE;;AAzEa;;iBAAX3B,U", "sourcesContent": ["/**\n * Core plugin logic that all plugins share.\n *\n * BasePlugin does not contain DOM rendering so it can be used for plugins\n * without a user interface.\n *\n * See `Plugin` for the extended version with Preact rendering for interfaces.\n */\n\nimport Translator from '@uppy/utils/lib/Translator'\n\nexport default class BasePlugin {\n  constructor (uppy, opts = {}) {\n    this.uppy = uppy\n    this.opts = opts\n  }\n\n  getPluginState () {\n    const { plugins } = this.uppy.getState()\n    return plugins[this.id] || {}\n  }\n\n  setPluginState (update) {\n    const { plugins } = this.uppy.getState()\n\n    this.uppy.setState({\n      plugins: {\n        ...plugins,\n        [this.id]: {\n          ...plugins[this.id],\n          ...update,\n        },\n      },\n    })\n  }\n\n  setOptions (newOpts) {\n    this.opts = { ...this.opts, ...newOpts }\n    this.setPluginState() // so that UI re-renders with new options\n    this.i18nInit()\n  }\n\n  i18nInit () {\n    const translator = new Translator([this.defaultLocale, this.uppy.locale, this.opts.locale])\n    this.i18n = translator.translate.bind(translator)\n    this.i18nArray = translator.translateArray.bind(translator)\n    this.setPluginState() // so that UI re-renders and we see the updated locale\n  }\n\n  /**\n   * Extendable methods\n   * ==================\n   * These methods are here to serve as an overview of the extendable methods as well as\n   * making them not conditional in use, such as `if (this.afterUpdate)`.\n   */\n\n  // eslint-disable-next-line class-methods-use-this\n  addTarget () {\n    throw new Error('Extend the addTarget method to add your plugin to another plugin\\'s target')\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  install () {}\n\n  // eslint-disable-next-line class-methods-use-this\n  uninstall () {}\n\n  /**\n   * Called when plugin is mounted, whether in DOM or into another plugin.\n   * Needed because sometimes plugins are mounted separately/after `install`,\n   * so this.el and this.parent might not be available in `install`.\n   * This is the case with @uppy/react plugins, for example.\n   */\n  render () {\n    throw new Error('Extend the render method to add your plugin to a DOM element')\n  }\n\n  // TODO: remove in the next major version. It's not feasible to\n  // try to use plugins with other frameworks.\n  // eslint-disable-next-line class-methods-use-this\n  update () {}\n\n  // Called after every state update, after everything's mounted. Debounced.\n  // eslint-disable-next-line class-methods-use-this\n  afterUpdate () {}\n}\n"]}