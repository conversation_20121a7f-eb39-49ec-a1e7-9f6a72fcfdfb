#!/bin/bash

# 备份管理脚本
# 功能：查看、恢复、清理备份文件

BACKUP_DIR="/opt/blog-system/backups"
DB_NAME="blog_system"
DB_USER="root"
DB_PASS="12345"
UPLOADS_DIR="/opt/blog-system/uploads"

# 显示使用帮助
show_help() {
    echo "备份管理脚本使用说明:"
    echo ""
    echo "查看备份:"
    echo "  $0 list                    # 列出所有备份"
    echo "  $0 size                    # 显示备份大小统计"
    echo "  $0 status                  # 显示备份状态"
    echo ""
    echo "恢复备份:"
    echo "  $0 restore db <文件名>     # 恢复数据库"
    echo "  $0 restore files <文件名>  # 恢复文件"
    echo ""
    echo "清理备份:"
    echo "  $0 clean old               # 清理7天前的备份"
    echo "  $0 clean all               # 清理所有备份"
    echo ""
    echo "示例:"
    echo "  $0 list"
    echo "  $0 restore db db_blog_system_20250727_120000.sql.gz"
}

# 列出所有备份
list_backups() {
    echo "========== 备份文件列表 =========="
    echo ""
    
    if [ -d "$BACKUP_DIR/daily" ]; then
        echo "每日备份:"
        ls -lah $BACKUP_DIR/daily/ 2>/dev/null | grep -v "^total" | head -10
        echo ""
    fi
    
    if [ -d "$BACKUP_DIR/weekly" ]; then
        echo "周备份:"
        ls -lah $BACKUP_DIR/weekly/ 2>/dev/null | grep -v "^total"
        echo ""
    fi
    
    if [ -d "$BACKUP_DIR/monthly" ]; then
        echo "月备份:"
        ls -lah $BACKUP_DIR/monthly/ 2>/dev/null | grep -v "^total"
        echo ""
    fi
}

# 显示备份大小统计
show_size() {
    echo "========== 备份空间统计 =========="
    echo ""
    
    if [ -d "$BACKUP_DIR" ]; then
        echo "总备份大小: $(du -sh $BACKUP_DIR 2>/dev/null | cut -f1)"
        echo ""
        
        for dir in daily weekly monthly; do
            if [ -d "$BACKUP_DIR/$dir" ]; then
                size=$(du -sh $BACKUP_DIR/$dir 2>/dev/null | cut -f1)
                count=$(ls $BACKUP_DIR/$dir/*.gz 2>/dev/null | wc -l)
                echo "${dir}备份: $size ($count 个文件)"
            fi
        done
        echo ""
        
        echo "磁盘使用情况:"
        df -h $BACKUP_DIR
    else
        echo "备份目录不存在: $BACKUP_DIR"
    fi
}

# 显示备份状态
show_status() {
    echo "========== 备份状态 =========="
    echo ""
    
    # 最新备份时间
    latest_backup=$(find $BACKUP_DIR -name "*.gz" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    if [ -n "$latest_backup" ]; then
        echo "最新备份: $(basename $latest_backup)"
        echo "备份时间: $(stat -c %y "$latest_backup" 2>/dev/null | cut -d'.' -f1)"
    else
        echo "未找到备份文件"
    fi
    echo ""
    
    # 检查定时任务
    echo "定时备份任务:"
    crontab -l 2>/dev/null | grep backup || echo "未配置定时备份"
    echo ""
    
    show_size
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    local full_path=""
    
    # 查找备份文件
    for dir in daily weekly monthly; do
        if [ -f "$BACKUP_DIR/$dir/$backup_file" ]; then
            full_path="$BACKUP_DIR/$dir/$backup_file"
            break
        fi
    done
    
    if [ -z "$full_path" ]; then
        echo "错误: 找不到备份文件 $backup_file"
        return 1
    fi
    
    echo "准备恢复数据库: $backup_file"
    echo "警告: 这将覆盖当前数据库！"
    read -p "确认继续? (y/N): " confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "取消恢复操作"
        return 1
    fi
    
    echo "正在恢复数据库..."
    
    # 解压并恢复
    if [[ $backup_file == *.gz ]]; then
        zcat "$full_path" | mysql -u$DB_USER -p$DB_PASS $DB_NAME
    else
        mysql -u$DB_USER -p$DB_PASS $DB_NAME < "$full_path"
    fi
    
    if [ $? -eq 0 ]; then
        echo "数据库恢复成功"
    else
        echo "数据库恢复失败"
        return 1
    fi
}

# 恢复文件
restore_files() {
    local backup_file="$1"
    local full_path=""
    
    # 查找备份文件
    for dir in daily weekly monthly; do
        if [ -f "$BACKUP_DIR/$dir/$backup_file" ]; then
            full_path="$BACKUP_DIR/$dir/$backup_file"
            break
        fi
    done
    
    if [ -z "$full_path" ]; then
        echo "错误: 找不到备份文件 $backup_file"
        return 1
    fi
    
    echo "准备恢复文件: $backup_file"
    echo "警告: 这将覆盖当前上传文件！"
    read -p "确认继续? (y/N): " confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "取消恢复操作"
        return 1
    fi
    
    echo "正在恢复文件..."
    
    # 备份当前文件
    if [ -d "$UPLOADS_DIR" ]; then
        mv "$UPLOADS_DIR" "${UPLOADS_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 解压恢复
    tar -xzf "$full_path" -C $(dirname $UPLOADS_DIR)
    
    if [ $? -eq 0 ]; then
        echo "文件恢复成功"
        chown -R nginx:nginx $UPLOADS_DIR
        chmod -R 755 $UPLOADS_DIR
    else
        echo "文件恢复失败"
        return 1
    fi
}

# 清理备份
clean_backups() {
    local type="$1"
    
    case $type in
        "old")
            echo "清理7天前的备份..."
            find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
            echo "清理完成"
            ;;
        "all")
            echo "警告: 这将删除所有备份文件！"
            read -p "确认继续? (y/N): " confirm
            if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                rm -rf $BACKUP_DIR/*
                echo "所有备份已删除"
            else
                echo "取消清理操作"
            fi
            ;;
        *)
            echo "错误: 无效的清理类型"
            show_help
            ;;
    esac
}

# 主函数
case "$1" in
    "list")
        list_backups
        ;;
    "size")
        show_size
        ;;
    "status")
        show_status
        ;;
    "restore")
        case "$2" in
            "db")
                restore_database "$3"
                ;;
            "files")
                restore_files "$3"
                ;;
            *)
                echo "错误: 请指定恢复类型 (db/files)"
                show_help
                ;;
        esac
        ;;
    "clean")
        clean_backups "$2"
        ;;
    *)
        show_help
        ;;
esac
