# 博客系统快速开始

## 🚀 5分钟快速启动

### 环境要求
- Java 1.8+、MySQL 8.0+、Node.js 16+

### 启动步骤

#### 1. 启动后端服务
```bash
cd blog-server
mvn spring-boot:run
```
服务启动在：http://localhost:8080

#### 2. 启动前端服务
```bash
cd blog-web
npm run dev
```
服务启动在：http://localhost:3000

#### 3. 验证系统
- 访问：http://localhost:3000
- 注册账户并登录
- 发布一篇测试文章

## 🔧 快速验证

### 检查服务状态
```bash
# 后端服务
curl http://localhost:8080/api/health

# 前端服务
curl http://localhost:3000
```

### 测试核心功能
1. **用户功能**：注册 → 登录 → 个人中心
2. **文章功能**：发布 → 编辑 → 查看
3. **互动功能**：点赞 → 评论 → 关注 → 通知

## 🐛 常见问题

### 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8080
```

### 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库 `blog_system` 是否存在
- 验证用户名密码：root/12345

### 前端页面空白
- 检查Node.js版本是否16+
- 重新安装依赖：`rm -rf node_modules && npm install`

## 📚 详细指南

### 按阶段测试
- [第一阶段测试指南](testing/第一阶段测试指南.md) - 用户认证
- [第二阶段测试指南](testing/第二阶段测试指南.md) - 文章管理
- [第三阶段测试指南](testing/第三阶段测试指南.md) - 分类标签
- [第四阶段测试指南](testing/第四阶段测试指南.md) - 评论系统
- [第五阶段测试指南](testing/第五阶段测试指南.md) - 用户互动

### 项目信息
- [项目说明文档](项目说明文档.md) - 项目介绍
- [项目进度](项目进度.md) - 开发进度
- [环境配置指南](环境配置指南.md) - 详细配置

### 开发文档
- [项目开发文档](development/项目开发文档.md) - 技术架构
- [各阶段总结](development/) - 开发总结

---

**快速开始完成时间**：5分钟  
**详细测试时间**：按需选择对应阶段
