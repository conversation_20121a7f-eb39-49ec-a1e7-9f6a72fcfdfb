{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../packages/slate/src/transforms/node.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,IAAI,EAOL,MAAM,IAAI,CAAA;AACX,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAEhD,MAAM,WAAW,cAAc;IAC7B,WAAW,EAAE,CAAC,CAAC,SAAS,IAAI,EAC1B,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE,EACpB,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAA;QAC3B,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,MAAM,CAAC,EAAE,OAAO,CAAA;QAChB,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,SAAS,EAAE,CAAC,CAAC,SAAS,IAAI,EACxB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,UAAU,EAAE,CAAC,CAAC,SAAS,IAAI,EACzB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAA;QAC3B,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,SAAS,EAAE,CAAC,CAAC,SAAS,IAAI,EACxB,MAAM,EAAE,MAAM,EACd,OAAO,EAAE;QACP,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,EAAE,EAAE,IAAI,CAAA;QACR,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,WAAW,EAAE,CAAC,CAAC,SAAS,IAAI,EAC1B,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAA;QAC3B,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,QAAQ,EAAE,CAAC,CAAC,SAAS,IAAI,EACvB,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EACjB,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,KAAK,CAAC,EAAE,OAAO,CAAA;QACf,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,UAAU,EAAE,CAAC,CAAC,SAAS,IAAI,EACzB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAA;QAC3B,MAAM,CAAC,EAAE,OAAO,CAAA;QAChB,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,UAAU,EAAE,CAAC,CAAC,SAAS,IAAI,EACzB,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EACxB,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,KAAK,CAAC,EAAE,OAAO,CAAA;QACf,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,WAAW,EAAE,CAAC,CAAC,SAAS,IAAI,EAC1B,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,KAAK,CAAC,EAAE,OAAO,CAAA;QACf,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,SAAS,EAAE,CAAC,CAAC,SAAS,IAAI,EACxB,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,OAAO,EAChB,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,KAAK,CAAC,EAAE,OAAO,CAAA;QACf,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;CACV;AAED,eAAO,MAAM,cAAc,EAAE,cAw2B5B,CAAA"}