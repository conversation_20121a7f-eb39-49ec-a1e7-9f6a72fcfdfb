<template>
  <div class="modern-layout">
    <!-- 现代化导航栏 -->
    <header class="modern-header glass-effect">
      <div class="container">
        <div class="logo fade-in">
          <router-link to="/" data-cy="system-title" class="logo-link">
            <img v-if="siteLogo" :src="siteLogo" :alt="siteTitle" class="logo-img">
            <span v-else class="logo-text">{{ siteTitle }}</span>
          </router-link>
        </div>

        <!-- 移动端菜单按钮 -->
        <button class="mobile-menu-btn" @click="toggleMobileMenu" :class="{ active: mobileMenuOpen }">
          <span></span>
          <span></span>
          <span></span>
        </button>

        <nav class="modern-nav" :class="{ 'mobile-open': mobileMenuOpen }">
          <div class="nav-links">
            <router-link to="/home" class="nav-link">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </router-link>
            <router-link to="/archive" class="nav-link">
              <el-icon><FolderOpened /></el-icon>
              <span>归档</span>
            </router-link>
            <router-link to="/search" class="nav-link">
              <el-icon><Search /></el-icon>
              <span>搜索</span>
            </router-link>
          </div>

          <div class="nav-actions">
            <template v-if="!isLoggedIn">
              <router-link to="/login" class="nav-btn login-btn">登录</router-link>
              <router-link to="/register" class="nav-btn register-btn">注册</router-link>
            </template>
            <template v-else>
              <router-link v-if="isAdmin" to="/admin/dashboard" class="nav-btn admin-btn">
                <el-icon><Setting /></el-icon>
                <span>管理后台</span>
              </router-link>

              <!-- 现代化通知图标 -->
              <div class="notification-wrapper" @click="goToNotifications">
                <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99" class="notification-badge">
                  <div class="notification-icon">
                    <el-icon :size="20"><Bell /></el-icon>
                  </div>
                </el-badge>
              </div>

              <!-- 现代化用户下拉菜单 -->
              <el-dropdown class="user-dropdown-wrapper" trigger="click">
                <div class="user-avatar">
                  <span class="avatar-text">{{ username.charAt(0).toUpperCase() }}</span>
                  <span class="username">{{ username }}</span>
                  <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="modern-dropdown">
                    <el-dropdown-item class="dropdown-item">
                      <router-link to="/user/profile" class="dropdown-link">
                        <el-icon><User /></el-icon>
                        <span>个人资料</span>
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item class="dropdown-item">
                      <router-link to="/user/notifications" class="dropdown-link">
                        <el-icon><Bell /></el-icon>
                        <span>消息中心</span>
                        <el-badge v-if="unreadCount > 0" :value="unreadCount" :max="99" class="inline-badge" />
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item class="dropdown-item">
                      <router-link to="/user/collection" class="dropdown-link">
                        <el-icon><Star /></el-icon>
                        <span>我的收藏</span>
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item class="dropdown-item">
                      <router-link to="/user/comment" class="dropdown-link">
                        <el-icon><ChatDotRound /></el-icon>
                        <span>我的评论</span>
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item divided class="dropdown-item logout-item" @click="logout" data-cy="logout-button">
                      <div class="dropdown-link logout-link">
                        <el-icon><SwitchButton /></el-icon>
                        <span>退出登录</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </div>
        </nav>
      </div>
    </header>

    <!-- 现代化主内容区域 -->
    <main class="modern-main">
      <div class="main-container">
        <router-view v-slot="{ Component }">
          <transition name="page-fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </main>

    <!-- 现代化页脚 -->
    <footer class="modern-footer">
      <div class="footer-content">
        <div class="footer-section">
          <h3 class="footer-title">{{ siteTitle }}</h3>
          <p class="footer-description">现代化的博客系统，分享知识与思考</p>
        </div>
        <div class="footer-section">
          <h4 class="footer-subtitle">快速链接</h4>
          <div class="footer-links">
            <router-link to="/home">首页</router-link>
            <router-link to="/archive">归档</router-link>
            <router-link to="/search">搜索</router-link>
          </div>
        </div>
        <div class="footer-section">
          <h4 class="footer-subtitle">联系我们</h4>
          <div class="footer-links">
            <a href="#" class="footer-link">关于我们</a>
            <a href="#" class="footer-link">联系方式</a>
            <a href="#" class="footer-link">意见反馈</a>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <div class="container">
          <p class="copyright">
            &copy; 2025 {{ siteTitle }} 版权所有
            <span v-if="icpNumber" class="icp">| {{ icpNumber }}</span>
          </p>
          <div class="footer-social">
            <span class="made-with">Made with ❤️</span>
          </div>
        </div>
      </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <transition name="fade">
      <div v-show="showBackToTop" class="back-to-top" @click="scrollToTop">
        <el-icon :size="20"><Top /></el-icon>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getToken, removeToken, getUserInfo } from '@/utils/auth'
import { useUserStore } from '@/store/user'
import { getUnreadCount } from '@/api/notification'
import {
  Bell, House, FolderOpened, Search, Setting, User, Star,
  ChatDotRound, SwitchButton, ArrowDown, Top
} from '@element-plus/icons-vue'
import { siteConfig, fetchSiteConfig } from '@/utils/siteConfig'

export default {
  name: 'ModernLayout',
  components: {
    Bell, House, FolderOpened, Search, Setting, User, Star,
    ChatDotRound, SwitchButton, ArrowDown, Top
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const username = ref('用户')
    const unreadCount = ref(0)
    const mobileMenuOpen = ref(false)
    const showBackToTop = ref(false)
    let notificationTimer = null

    // 使用全局网站配置
    const siteTitle = computed(() => siteConfig.value.title)
    const siteLogo = computed(() => siteConfig.value.logo)
    const icpNumber = computed(() => siteConfig.value.icp)

    const isLoggedIn = computed(() => {
      return !!getToken()
    })

    const isAdmin = computed(() => {
      return userStore.isAdmin
    })

    // 初始化获取用户名
    if (isLoggedIn.value) {
      const userInfo = getUserInfo()
      if (userInfo) {
        username.value = userInfo.nickname || userInfo.username || '用户'
      }
    }

    // 获取未读通知数量
    const fetchUnreadCount = async () => {
      if (!isLoggedIn.value) return

      try {
        const res = await getUnreadCount()
        if (res.code === 200) {
          unreadCount.value = res.data.unreadCount
        }
      } catch (error) {
        console.error('获取未读通知数量失败', error)
      }
    }

    // 跳转到消息中心
    const goToNotifications = () => {
      router.push('/user/notifications')
    }

    // 定期更新未读通知数量
    const startNotificationPolling = () => {
      if (!isLoggedIn.value) return

      fetchUnreadCount() // 立即获取一次
      notificationTimer = setInterval(fetchUnreadCount, 30000) // 每30秒更新一次
    }

    const stopNotificationPolling = () => {
      if (notificationTimer) {
        clearInterval(notificationTimer)
        notificationTimer = null
      }
    }

    // 切换移动端菜单
    const toggleMobileMenu = () => {
      mobileMenuOpen.value = !mobileMenuOpen.value
    }

    // 滚动到顶部
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }

    // 监听滚动事件
    const handleScroll = () => {
      showBackToTop.value = window.scrollY > 300
    }

    const logout = () => {
      stopNotificationPolling()
      userStore.logout()
      mobileMenuOpen.value = false
    }

    onMounted(() => {
      fetchSiteConfig() // 获取完整的网站配置信息
      if (isLoggedIn.value) {
        startNotificationPolling()
      }

      // 添加滚动监听
      window.addEventListener('scroll', handleScroll)

      // 点击外部关闭移动端菜单
      document.addEventListener('click', (e) => {
        if (!e.target.closest('.modern-nav') && !e.target.closest('.mobile-menu-btn')) {
          mobileMenuOpen.value = false
        }
      })
    })

    onUnmounted(() => {
      stopNotificationPolling()
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      isLoggedIn,
      isAdmin,
      username,
      unreadCount,
      siteTitle,
      siteLogo,
      icpNumber,
      mobileMenuOpen,
      showBackToTop,
      toggleMobileMenu,
      scrollToTop,
      goToNotifications,
      logout
    }
  }
}
</script>

<style scoped>
/* 现代化布局样式 */
.modern-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

/* 现代化导航栏 */
.modern-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition-normal);
}

.container {
  width: 1200px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.modern-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

/* Logo样式 */
.logo {
  z-index: 1001;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: var(--transition-normal);
}

.logo-link:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-img {
  height: 45px;
  max-width: 200px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 1001;
}

.mobile-menu-btn span {
  width: 100%;
  height: 3px;
  background: var(--primary-color);
  border-radius: 2px;
  transition: var(--transition-normal);
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(8px, 8px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 现代化导航 */
.modern-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-links {
  display: flex;
  gap: var(--spacing-lg);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition-normal);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.nav-link.router-link-active {
  color: var(--primary-color);
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.login-btn {
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.login-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.register-btn {
  background: var(--gradient-primary);
  color: white;
  border: 2px solid transparent;
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.admin-btn {
  background: var(--gradient-secondary);
  color: white;
  border: 2px solid transparent;
}

.admin-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 通知样式 */
.notification-wrapper {
  position: relative;
  cursor: pointer;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  color: var(--primary-color);
}

.notification-icon:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.notification-badge :deep(.el-badge__content) {
  background: var(--gradient-secondary);
  border: 2px solid white;
}

/* 用户下拉菜单 */
.user-dropdown-wrapper {
  cursor: pointer;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  background: rgba(102, 126, 234, 0.1);
  transition: var(--transition-normal);
}

.user-avatar:hover {
  background: var(--gradient-primary);
  color: white;
  transform: translateY(-2px);
}

.avatar-text {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.username {
  font-weight: 500;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-arrow {
  transition: var(--transition-normal);
}

.user-dropdown-wrapper.is-active .dropdown-arrow {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
.modern-dropdown {
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-sm);
  min-width: 200px;
}

.dropdown-item {
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
  transition: var(--transition-normal);
}

.dropdown-item:hover {
  background: rgba(102, 126, 234, 0.1);
}

.dropdown-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  color: var(--text-primary);
  width: 100%;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.logout-item:hover {
  background: rgba(245, 108, 108, 0.1);
}

.logout-link {
  color: #f56c6c;
}

.inline-badge {
  margin-left: auto;
}

/* 主内容区域 */
.modern-main {
  flex: 1;
  padding: var(--spacing-xl) 0;
  position: relative;
}

.main-container {
  width: 1200px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* 页面过渡动画 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: all 0.3s ease-out;
}

.page-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
/* 现代化页脚 */
.modern-footer {
  background: var(--gradient-dark);
  color: white;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-2xl) var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(45deg, #fff, #a8edea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-subtitle {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: #e2e8f0;
}

.footer-description {
  color: #cbd5e0;
  line-height: 1.6;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-links a {
  color: #cbd5e0;
  text-decoration: none;
  transition: var(--transition-normal);
  padding: var(--spacing-xs) 0;
}

.footer-links a:hover {
  color: white;
  transform: translateX(5px);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);
}

.footer-bottom .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  color: #cbd5e0;
  font-size: 0.875rem;
}

.icp {
  color: #a0aec0;
}

.footer-social {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.made-with {
  color: #cbd5e0;
  font-size: 0.875rem;
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  z-index: 999;
  color: white;
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .modern-nav {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--bg-overlay);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .modern-nav.mobile-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links {
    flex-direction: column;
    width: 100%;
    gap: var(--spacing-sm);
  }

  .nav-link {
    justify-content: center;
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
  }

  .nav-actions {
    flex-direction: column;
    width: 100%;
    gap: var(--spacing-sm);
  }

  .nav-btn {
    justify-content: center;
    width: 100%;
  }

  .user-avatar {
    justify-content: center;
    width: 100%;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .footer-bottom .container {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .back-to-top {
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .modern-header .container {
    height: 60px;
    padding: 0 var(--spacing-md);
  }

  .logo-text {
    font-size: 1.25rem;
  }

  .main-container {
    padding: 0 var(--spacing-md);
  }

  .footer-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }
}
</style>