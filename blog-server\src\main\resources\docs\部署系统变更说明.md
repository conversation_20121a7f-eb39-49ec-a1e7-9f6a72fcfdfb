# 部署系统变更说明 - 从阿里云Linux3到CentOS 7.6

## 📋 变更概述

本文档说明了从阿里云Linux3迁移到CentOS 7.6的主要变更和注意事项。

## 🔄 主要变更对比

### 1. 操作系统差异

| 项目 | 阿里云Linux3 | CentOS 7.6 |
|------|-------------|------------|
| 内核版本 | 5.10+ | 3.10+ |
| 包管理器 | yum (dnf兼容) | yum |
| 系统服务 | systemd | systemd |
| SELinux | 默认启用 | 默认启用 |
| 防火墙 | firewalld | firewalld |

### 2. 软件安装差异

#### JDK安装
```bash
# 阿里云Linux3 & CentOS 7.6 (相同)
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel
```

#### 数据库安装
```bash
# 阿里云Linux3 - 使用MariaDB
yum install -y mariadb-server mariadb

# CentOS 7.6 - 使用MySQL 5.7
wget https://dev.mysql.com/get/mysql57-community-release-el7-11.noarch.rpm
rpm -ivh mysql57-community-release-el7-11.noarch.rpm
yum install -y mysql-server
```

#### Node.js安装
```bash
# 阿里云Linux3 & CentOS 7.6 (相同)
curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
yum install -y nodejs
```

#### Maven安装
```bash
# 阿里云Linux3 - 可能包含在默认仓库
yum install -y maven

# CentOS 7.6 - 需要手动安装
cd /opt
wget https://archive.apache.org/dist/maven/maven-3/3.8.6/binaries/apache-maven-3.8.6-bin.tar.gz
tar -xzf apache-maven-3.8.6-bin.tar.gz
mv apache-maven-3.8.6 maven
echo 'export PATH=/opt/maven/bin:$PATH' >> /etc/profile
source /etc/profile
```

#### Nginx安装
```bash
# 阿里云Linux3 & CentOS 7.6 (相同)
yum install -y nginx
```

### 3. 数据库配置差异

#### 服务名称
```bash
# 阿里云Linux3 - MariaDB
systemctl start mariadb
systemctl enable mariadb

# CentOS 7.6 - MySQL
systemctl start mysqld
systemctl enable mysqld
```

#### 初始密码
```bash
# 阿里云Linux3 - MariaDB (无初始密码)
mysql_secure_installation

# CentOS 7.6 - MySQL (有临时密码)
grep 'temporary password' /var/log/mysqld.log
mysql_secure_installation
```

#### 数据库驱动
```properties
# 阿里云Linux3 - MariaDB驱动
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver

# CentOS 7.6 - MySQL驱动
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
```

### 4. SELinux配置

#### 基本设置（相同）
```bash
# 设置布尔值
setsebool -P httpd_can_network_connect 1
setsebool -P httpd_can_network_relay 1

# 设置文件上下文
chcon -R -t httpd_exec_t /opt/blog-system/uploads
```

#### 上下文管理
```bash
# CentOS 7.6 需要额外的上下文管理
semanage fcontext -a -t httpd_exec_t "/opt/blog-system/uploads(/.*)?"
restorecon -R /opt/blog-system/uploads
```

### 5. 防火墙配置（相同）

```bash
# 两个系统都使用firewalld
systemctl start firewalld
systemctl enable firewalld
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload
```

## 🔧 配置文件变更

### 1. 应用配置文件

#### 数据库连接
```properties
# 阿里云Linux3 - MariaDB
spring.datasource.url=*********************************************************************************
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.password=12345

# CentOS 7.6 - MySQL
spring.datasource.url=**************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.password=Blog@123456
```

### 2. 备份脚本

```bash
# 阿里云Linux3 - MariaDB备份
mysqldump -u root -p12345 blog_system > backup.sql

# CentOS 7.6 - MySQL备份
mysqldump -u root -pBlog@123456 blog_system > backup.sql
```

### 3. 监控脚本

```bash
# 阿里云Linux3 - 检查MariaDB
if ! systemctl is-active --quiet mariadb; then
    systemctl start mariadb
fi

# CentOS 7.6 - 检查MySQL
if ! systemctl is-active --quiet mysqld; then
    systemctl start mysqld
fi
```

## ⚠️ 注意事项

### 1. 密码策略
- **阿里云Linux3**: MariaDB密码策略相对宽松
- **CentOS 7.6**: MySQL 5.7有严格的密码策略，需要包含大小写字母、数字和特殊字符

### 2. 字符集支持
- **阿里云Linux3**: MariaDB默认支持utf8mb4
- **CentOS 7.6**: MySQL 5.7需要明确指定utf8mb4字符集

### 3. 连接参数
- **CentOS 7.6**: MySQL需要添加`allowPublicKeyRetrieval=true`参数

### 4. SELinux严格程度
- **CentOS 7.6**: SELinux策略可能更严格，需要更多的上下文设置

## 🚀 迁移步骤

### 1. 数据备份
```bash
# 在阿里云Linux3上备份数据
mysqldump -u root -p12345 blog_system > blog_system_backup.sql
```

### 2. 系统部署
按照[CentOS7.6完整部署指南.md](CentOS7.6完整部署指南.md)进行新系统部署

### 3. 数据恢复
```bash
# 在CentOS 7.6上恢复数据
mysql -u root -pBlog@123456 blog_system < blog_system_backup.sql
```

### 4. 配置验证
- 检查所有服务状态
- 验证网站功能
- 测试文件上传
- 确认邮件功能

## 📞 技术支持

如果在迁移过程中遇到问题，请参考：
1. **[CentOS7.6完整部署指南.md](CentOS7.6完整部署指南.md)** - 完整部署流程
2. **[部署检查清单.md](部署检查清单.md)** - 部署验证清单
3. **[问题记录与解决方案.md](issues/问题记录与解决方案.md)** - 常见问题解决

---

**迁移完成标志**：
- ✅ 所有服务正常启动
- ✅ 网站可以正常访问
- ✅ 数据库连接正常
- ✅ 文件上传功能正常
- ✅ 邮件功能配置成功
