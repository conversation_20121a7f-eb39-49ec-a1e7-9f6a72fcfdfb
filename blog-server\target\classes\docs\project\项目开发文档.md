# 个人动态博客系统开发文档

## 1. 项目概述

个人动态博客系统是一个基于Spring Boot和Vue3开发的全栈应用，旨在提供一个简洁、高效、安全的博客平台，让用户可以发布、管理个人博客内容，并与其他用户进行互动。

### 1.1 项目目标

- 构建一个功能完善的个人博客系统
- 实现用户注册、登录等基础功能
- 支持博客文章的创建、编辑、发布和管理
- 提供评论互动功能
- 实现文章分类和标签管理
- 提供个人信息管理功能
- 支持响应式设计，适配不同设备

## 2. 技术栈

### 2.1 后端技术

- **核心框架**: Spring Boot 2.7.x
- **安全框架**: Spring Security + JWT
- **ORM框架**: MyBatis Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis (可选)
- **API文档**: Swagger/Knife4j
- **其他工具**: Lombok, Hutool等

### 2.2 前端技术

- **核心框架**: Vue 3
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **Markdown编辑器**: mavon-editor或vditor

## 3. 系统架构

### 3.1 整体架构

- 采用前后端分离架构
- 后端提供RESTful API
- 前端通过HTTP请求与后端交互
- 使用JWT进行身份认证和授权

### 3.2 目录结构

#### 后端目录结构

```
blog-server/
├── src/main/java/com/blog/
│   ├── common/         # 通用工具类和常量
│   ├── config/         # 配置类
│   ├── controller/     # 控制器
│   ├── dto/            # 数据传输对象
│   ├── entity/         # 实体类
│   ├── mapper/         # MyBatis映射接口
│   ├── security/       # 安全相关类
│   ├── service/        # 服务接口和实现
│   └── vo/             # 视图对象
├── src/main/resources/
│   ├── mapper/         # MyBatis XML映射文件
│   ├── application.yml # 应用配置文件
│   └── application-dev.yml # 开发环境配置
└── src/test/           # 测试代码
```

#### 前端目录结构

```
blog-web/
├── src/
│   ├── api/            # API请求
│   ├── assets/         # 静态资源
│   ├── components/     # 公共组件
│   ├── router/         # 路由配置
│   ├── store/          # 状态管理
│   ├── utils/          # 工具函数
│   ├── views/          # 页面组件
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── public/             # 公共静态资源
├── index.html          # HTML模板
└── vite.config.js      # Vite配置
```

## 4. 数据库设计

### 4.1 用户表(user)

| 字段名      | 类型         | 说明                   |
|------------|--------------|------------------------|
| id         | bigint       | 主键ID                 |
| username   | varchar(50)  | 用户名                 |
| password   | varchar(100) | 密码(加密存储)         |
| nickname   | varchar(50)  | 昵称                   |
| email      | varchar(100) | 邮箱                   |
| avatar     | varchar(255) | 头像地址               |
| role       | varchar(20)  | 角色(admin/user)       |
| status     | tinyint      | 状态(0禁用,1正常)      |
| del_flag   | tinyint      | 删除标志(0存在,1删除)  |
| create_time| datetime     | 创建时间               |
| update_time| datetime     | 更新时间               |

### 4.2 文章表(article)

| 字段名        | 类型         | 说明                     |
|--------------|--------------|--------------------------|
| id           | bigint       | 主键ID                   |
| title        | varchar(100) | 文章标题                 |
| summary      | varchar(255) | 文章摘要                 |
| content      | longtext     | 文章内容                 |
| cover_image  | varchar(255) | 封面图片地址             |
| category_id  | bigint       | 分类ID                   |
| user_id      | bigint       | 作者ID                   |
| view_count   | int          | 浏览量                   |
| like_count   | int          | 点赞量                   |
| comment_count| int          | 评论量                   |
| status       | tinyint      | 状态(0草稿,1已发布)      |
| is_top       | tinyint      | 是否置顶(0否,1是)        |
| is_comment   | tinyint      | 是否允许评论(0否,1是)    |
| del_flag     | tinyint      | 删除标志(0存在,1删除)    |
| create_time  | datetime     | 创建时间                 |
| update_time  | datetime     | 更新时间                 |

### 4.3 分类表(category)

| 字段名      | 类型         | 说明                   |
|------------|--------------|------------------------|
| id         | bigint       | 主键ID                 |
| name       | varchar(50)  | 分类名称               |
| description| varchar(255) | 分类描述               |
| parent_id  | bigint       | 父分类ID               |
| status     | tinyint      | 状态(0禁用,1正常)      |
| del_flag   | tinyint      | 删除标志(0存在,1删除)  |
| create_time| datetime     | 创建时间               |
| update_time| datetime     | 更新时间               |

### 4.4 标签表(tag)

| 字段名      | 类型         | 说明                   |
|------------|--------------|------------------------|
| id         | bigint       | 主键ID                 |
| name       | varchar(50)  | 标签名称               |
| del_flag   | tinyint      | 删除标志(0存在,1删除)  |
| create_time| datetime     | 创建时间               |
| update_time| datetime     | 更新时间               |

### 4.5 文章标签关联表(article_tag)

| 字段名      | 类型         | 说明                   |
|------------|--------------|------------------------|
| id         | bigint       | 主键ID                 |
| article_id | bigint       | 文章ID                 |
| tag_id     | bigint       | 标签ID                 |
| create_time| datetime     | 创建时间               |

### 4.6 评论表(comment)

| 字段名      | 类型         | 说明                   |
|------------|--------------|------------------------|
| id         | bigint       | 主键ID                 |
| article_id | bigint       | 文章ID                 |
| user_id    | bigint       | 评论用户ID             |
| content    | varchar(500) | 评论内容               |
| parent_id  | bigint       | 父评论ID               |
| to_user_id | bigint       | 回复的用户ID           |
| status     | tinyint      | 状态(0待审核,1正常)    |
| del_flag   | tinyint      | 删除标志(0存在,1删除)  |
| create_time| datetime     | 创建时间               |
| update_time| datetime     | 更新时间               |

## 5. 功能模块

### 5.1 用户模块

- 用户注册
- 用户登录
- 用户信息修改
  - 个人资料编辑（昵称、邮箱）
  - 个人信息查看
- 密码修改
  - 旧密码验证
  - 新密码确认
- 头像上传
  - 支持JPG/PNG/GIF格式
  - 大小限制2MB以内
  - 实时预览

### 5.2 文章模块

- 文章创建
- 文章编辑
- 文章发布
- 文章列表查询
- 文章详情查看
- 文章删除
- 文章置顶

### 5.3 分类模块

- 分类创建
- 分类编辑
- 分类列表查询
- 分类删除

### 5.4 标签模块

- 标签创建
- 标签编辑
- 标签列表查询
- 标签删除

### 5.5 评论模块

- 评论发布
- 评论回复
- 评论列表查询
- 评论删除

### 5.6 统计模块

- 文章浏览量统计
- 文章点赞统计
- 评论数量统计

## 6. 开发计划

### 6.1 第一阶段：基础框架搭建和用户认证模块

- 项目初始化
- 数据库设计
- 用户实体类创建
- 用户认证和授权实现
- 用户注册和登录功能
- 前端基础框架搭建
- 前端登录和注册页面实现

### 6.2 第二阶段：文章管理模块

- 文章实体类创建
- 文章CRUD功能实现
- 文章列表和详情页面
- 文章编辑器集成
- 文章发布和预览功能

### 6.3 第三阶段：分类和标签模块

- 分类和标签实体类创建
- 分类和标签管理功能
- 文章关联分类和标签
- 按分类和标签筛选文章

### 6.4 第四阶段：评论模块

- 评论实体类创建
- 评论发布和回复功能
- 评论列表展示
- 评论管理功能

### 6.5 第五阶段：系统优化和部署

- 性能优化
- 代码重构
- 单元测试
- 系统部署

## 7. 接口设计

### 7.1 用户模块接口

- `POST /api/auth/register`: 用户注册
- `POST /api/auth/login`: 用户登录
- `GET /api/user/info`: 获取用户信息
- `PUT /api/user/info`: 更新用户信息
- `POST /api/user/avatar`: 上传用户头像
- `PUT /api/user/password`: 修改密码
  - 请求参数：currentPassword, newPassword, confirmPassword
  - 响应：更新成功后需重新登录

### 7.2 文章模块接口

- `POST /api/articles`: 创建文章
- `PUT /api/articles/{id}`: 更新文章
- `GET /api/articles`: 获取文章列表
- `GET /api/articles/{id}`: 获取文章详情
- `DELETE /api/articles/{id}`: 删除文章
- `PUT /api/articles/{id}/top`: 文章置顶/取消置顶
- `PUT /api/articles/{id}/status`: 更新文章状态

### 7.3 分类模块接口

- `POST /api/categories`: 创建分类
- `PUT /api/categories/{id}`: 更新分类
- `GET /api/categories`: 获取分类列表
- `DELETE /api/categories/{id}`: 删除分类

### 7.4 标签模块接口

- `POST /api/tags`: 创建标签
- `PUT /api/tags/{id}`: 更新标签
- `GET /api/tags`: 获取标签列表
- `DELETE /api/tags/{id}`: 删除标签

### 7.5 评论模块接口

- `POST /api/comments`: 发布评论
- `GET /api/comments/article/{articleId}`: 获取文章评论列表
- `DELETE /api/comments/{id}`: 删除评论

## 8. 测试计划

### 8.1 单元测试

- 服务层单元测试
- 控制器单元测试
- 工具类单元测试

### 8.2 集成测试

- API接口测试
- 数据库交互测试
- 安全认证测试

### 8.3 前端测试

- 组件单元测试
- 页面功能测试
- 用户交互测试

## 9. 第一阶段测试指南

### 9.1 后端测试

1. **准备工作**
   - 确保MySQL数据库已启动，用户名为root，密码为12345
   - 创建名为`blog_system`的数据库：
     ```sql
     CREATE DATABASE IF NOT EXISTS blog_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
     ```
   - 导入数据库初始化SQL脚本（如果有）

2. **启动后端服务**
   - 进入blog-server目录
   - 执行命令：`mvn spring-boot:run`
   - 确认服务启动在8080端口

3. **API测试**
   - 使用Postman或其他API测试工具
   - 测试用户注册接口：`POST http://localhost:8080/api/auth/register`
     ```json
     {
       "username": "testuser",
       "password": "password123",
       "nickname": "测试用户",
       "email": "<EMAIL>"
     }
     ```
   - 测试用户登录接口：`POST http://localhost:8080/api/auth/login`
     ```json
     {
       "username": "testuser",
       "password": "password123"
     }
     ```
   - 使用返回的token测试获取用户信息：`GET http://localhost:8080/api/user/info`
     (在请求头中添加`Authorization: Bearer {token}`)

### 9.2 前端测试

1. **准备工作**
   - 确保Node.js环境已安装
   - 确保后端服务已启动

2. **启动前端服务**
   - 进入blog-web目录
   - 执行命令：`npm install`（首次运行需要安装依赖）
   - 执行命令：`npm run dev`
   - 确认服务启动在3000端口

3. **功能测试**
   - 打开浏览器访问：`http://localhost:3000`
   - 测试注册功能：点击注册按钮，填写注册信息
   - 测试登录功能：使用注册的账号进行登录
   - 验证登录后是否能正确显示用户信息

### 9.3 常见问题排查

1. **后端启动失败**
   - 检查数据库连接配置是否正确
   - 检查端口8080是否被占用
   - 查看日志确认具体错误信息

2. **前端启动失败**
   - 检查Node.js版本是否兼容
   - 检查依赖是否安装完整
   - 检查端口3000是否被占用

3. **登录认证失败**
   - 检查用户名和密码是否正确
   - 检查token是否正确传递
   - 检查后端JWT配置是否正确

4. **跨域问题**
   - 确认后端CORS配置是否正确
   - 检查前端API请求地址是否正确
   
5. **个人资料更新失败**
   - 检查请求参数格式是否正确
   - 验证表单数据是否符合验证规则
   - 确认用户认证状态是否有效

6. **头像上传失败**
   - 检查文件格式是否为JPG/PNG/GIF
   - 确认文件大小是否超过2MB限制
   - 验证上传路径权限是否正确

## 10. 部署指南

### 10.1 后端部署

1. 打包Spring Boot应用
   ```bash
   mvn clean package -DskipTests
   ```

2. 运行JAR包
   ```bash
   java -jar target/blog-server.jar --spring.profiles.active=prod
   ```

### 10.2 前端部署

1. 构建生产环境代码
   ```bash
   npm run build
   ```

2. 部署dist目录到Web服务器
   - 可以使用Nginx、Apache等Web服务器
   - 配置反向代理，将API请求转发到后端服务

### 10.3 数据库部署

1. 备份开发环境数据库
2. 在生产环境创建数据库并导入备份
3. 配置生产环境数据库连接信息