{"version": 3, "sources": ["index.js"], "names": ["Uppy", "UIPlugin", "BasePlugin", "debugLogger"], "mappings": ";;;;;;;;;;;;;AAGA;;yBAHwB,W;;;;;;;;;;AAKxB;;AACA;MACOA,I;;;;MACAC,Q;;MACAC,U;;AAGP;AACA;AACAF,IAAI,CAACA,IAAL,GAAYA,IAAZ;AACAA,IAAI,CAACC,QAAL,GAAgBA,QAAhB;AACAD,IAAI,CAACE,UAAL,GAAkBA,UAAlB;AACAF,IAAI,CAACG,WAAL,GAAmBA,oBAAnB", "sourcesContent": ["export { default } from './Uppy.js'\nexport { default as UIPlugin } from './UIPlugin.js'\nexport { default as BasePlugin } from './BasePlugin.js'\nexport { debugLogger } from './loggers.js'\n\n// TODO: remove all the following in the next major\n/* eslint-disable import/first */\nimport Uppy from './Uppy.js'\nimport UIPlugin from './UIPlugin.js'\nimport BasePlugin from './BasePlugin.js'\nimport { debugLogger } from './loggers.js'\n\n// Backward compatibility: we want those to keep being accessible as static\n// properties of `Uppy` to avoid a breaking change.\nUppy.Uppy = Uppy\nUppy.UIPlugin = UIPlugin\nUppy.BasePlugin = BasePlugin\nUppy.debugLogger = debugLogger\n\nexport { Uppy }\n"]}