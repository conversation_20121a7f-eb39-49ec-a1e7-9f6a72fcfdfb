{"version": 3, "file": "jsx.js", "sourceRoot": "", "sources": ["../src/jsx.ts"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,OAAO,EAAO,KAAK,EAAoB,MAAM,SAAS,CAAC;AACvD,OAAO,EAAE,CAAC,EAAkB,MAAM,KAAK,CAAC;AAkBxC,MAAM,UAAU,QAAQ,CACtB,IAA0B,EAC1B,GAAG,QAA4B;IAE/B,MAAM,YAAY,GAAG,gBAAgB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAEpD,IACE,YAAY,CAAC,MAAM,KAAK,CAAC;QACzB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG;QACpB,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EACpB;QACA,qEAAqE;QACrE,OAAO,KAAK,CACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EACpB,SAAS,CACV,CAAC;KACH;SAAM;QACL,OAAO,KAAK,CAAC,SAAS,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;KACzE;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,QAA4B,EAC5B,SAAkB;IAElB,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;QAC5B,0FAA0F;QAC1F,IACE,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,IAAI;YACd,KAAK,KAAK,KAAK;YACf,KAAK,KAAK,EAAE,EACZ;YACA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;aACpC;iBAAM,IACL,OAAO,KAAK,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK,SAAS,EAC1B;gBACA,SAAS,CAAC,IAAI,CACZ,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,CACjE,CAAC;aACH;iBAAM;gBACL,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACvB;SACF;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,GAAG,CACjB,GAA+B,EAC/B,IAAsB,EACtB,GAAG,QAA4B;IAE/B,MAAM,YAAY,GAAG,gBAAgB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACpD,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;QAC7B,8BAA8B;QAC9B,OAAO,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KAChC;SAAM;QACL,IACE,YAAY,CAAC,MAAM,KAAK,CAAC;YACzB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG;YACpB,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EACpB;YACA,qEAAqE;YACrE,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAC3C;aAAM;YACL,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;SACnC;KACF;AACH,CAAC"}