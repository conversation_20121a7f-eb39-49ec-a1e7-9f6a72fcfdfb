{"version": 3, "sources": ["invalidPluginWithoutType.js"], "names": ["UIPlugin", "InvalidPluginWithoutType", "constructor", "uppy", "opts", "id", "name", "run", "results", "log", "class", "method", "Promise", "resolve"], "mappings": ";;MAAOA,Q;;AAEQ,MAAMC,wBAAN,SAAuCD,QAAvC,CAAgD;AAC7DE,EAAAA,WAAW,CAAEC,IAAF,EAAQC,IAAR,EAAc;AACvB,UAAMD,IAAN,EAAYC,IAAZ;AACA,SAAKC,EAAL,GAAU,0BAAV;AACA,SAAKC,IAAL,GAAY,KAAKJ,WAAL,CAAiBI,IAA7B;AACD;;AAEDC,EAAAA,GAAG,CAAEC,OAAF,EAAW;AACZ,SAAKL,IAAL,CAAUM,GAAV,CAAc;AACZC,MAAAA,KAAK,EAAE,KAAKR,WAAL,CAAiBI,IADZ;AAEZK,MAAAA,MAAM,EAAE,KAFI;AAGZH,MAAAA;AAHY,KAAd;AAMA,WAAOI,OAAO,CAACC,OAAR,CAAgB,SAAhB,CAAP;AACD;;AAf4D;;iBAA1CZ,wB", "sourcesContent": ["import UIPlugin from '../UIPlugin.js'\n\nexport default class InvalidPluginWithoutType extends UIPlugin {\n  constructor (uppy, opts) {\n    super(uppy, opts)\n    this.id = 'InvalidPluginWithoutType'\n    this.name = this.constructor.name\n  }\n\n  run (results) {\n    this.uppy.log({\n      class: this.constructor.name,\n      method: 'run',\n      results,\n    })\n\n    return Promise.resolve('success')\n  }\n}\n"]}