<template>
  <div class="rich-text-viewer" v-html="safeContent"></div>
</template>

<script>
import { computed } from 'vue'
import '@wangeditor/editor/dist/css/style.css'
import { buildResourceUrl } from '@/config/settings'

export default {
  name: 'RichTextViewer',
  props: {
    content: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    // 处理安全的内容渲染
    const safeContent = computed(() => {
      if (!props.content) return ''
      
      // 对内容进行基本的安全处理和样式优化
      let processedContent = props.content
      
      // 处理图片URL和样式
      processedContent = processedContent.replace(
        /<img([^>]*?)src=["']([^"']*?)["']([^>]*)>/g, 
        (match, before, src, after) => {
          // 如果图片URL不是完整URL，则使用buildResourceUrl构建
          let fullSrc = src
          if (!src.startsWith('http') && !src.startsWith('data:')) {
            fullSrc = buildResourceUrl(src)
          }
          console.log('处理图片URL:', src, '->', fullSrc)
          return `<img${before}src="${fullSrc}"${after} style="max-width: 100%; height: auto; border-radius: 8px; margin: 16px 0; display: block;">`
        }
      )
      
      // 为代码块添加样式
      processedContent = processedContent.replace(
        /<pre([^>]*)>/g,
        '<pre$1 style="background: #f6f8fa; padding: 16px; border-radius: 8px; overflow-x: auto; margin: 16px 0;">'
      )
      
      // 为代码添加样式
      processedContent = processedContent.replace(
        /<code([^>]*)>/g,
        '<code$1 style="background: #f6f8fa; padding: 2px 6px; border-radius: 4px; font-family: Consolas, Monaco, monospace;">'
      )
      
      // 为表格添加样式
      processedContent = processedContent.replace(
        /<table([^>]*)>/g,
        '<table$1 style="width: 100%; border-collapse: collapse; margin: 16px 0; border: 1px solid #ddd;">'
      )
      
      processedContent = processedContent.replace(
        /<td([^>]*)>/g,
        '<td$1 style="padding: 12px; border: 1px solid #ddd;">'
      )
      
      processedContent = processedContent.replace(
        /<th([^>]*)>/g,
        '<th$1 style="padding: 12px; border: 1px solid #ddd; background: #f6f8fa; font-weight: bold;">'
      )
      
      // 为引用块添加样式
      processedContent = processedContent.replace(
        /<blockquote([^>]*)>/g,
        '<blockquote$1 style="border-left: 4px solid #409eff; padding-left: 16px; margin: 16px 0; color: #666; background: #f9f9f9; padding: 16px; border-radius: 4px;">'
      )
      
      return processedContent
    })
    
    return {
      safeContent
    }
  }
}
</script>

<style scoped>
.rich-text-viewer {
  line-height: 1.8;
  color: #333;
  font-size: 16px;
}

/* 全局样式，用于渲染的HTML内容 */
.rich-text-viewer :deep(h1) {
  font-size: 2em;
  font-weight: bold;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #eee;
}

.rich-text-viewer :deep(h2) {
  font-size: 1.75em;
  font-weight: bold;
  margin: 20px 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.rich-text-viewer :deep(h3) {
  font-size: 1.5em;
  font-weight: bold;
  margin: 16px 0 8px 0;
}

.rich-text-viewer :deep(h4) {
  font-size: 1.25em;
  font-weight: bold;
  margin: 12px 0 6px 0;
}

.rich-text-viewer :deep(h5) {
  font-size: 1.1em;
  font-weight: bold;
  margin: 10px 0 4px 0;
}

.rich-text-viewer :deep(h6) {
  font-size: 1em;
  font-weight: bold;
  margin: 8px 0 4px 0;
}

.rich-text-viewer :deep(p) {
  margin: 12px 0;
  line-height: 1.8;
}

.rich-text-viewer :deep(ul), 
.rich-text-viewer :deep(ol) {
  margin: 12px 0;
  padding-left: 24px;
}

.rich-text-viewer :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.rich-text-viewer :deep(a) {
  color: #409eff;
  text-decoration: none;
}

.rich-text-viewer :deep(a:hover) {
  text-decoration: underline;
}

.rich-text-viewer :deep(strong) {
  font-weight: bold;
}

.rich-text-viewer :deep(em) {
  font-style: italic;
}

.rich-text-viewer :deep(del) {
  text-decoration: line-through;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rich-text-viewer {
    font-size: 14px;
  }
  
  .rich-text-viewer :deep(h1) {
    font-size: 1.8em;
  }
  
  .rich-text-viewer :deep(h2) {
    font-size: 1.5em;
  }
  
  .rich-text-viewer :deep(h3) {
    font-size: 1.3em;
  }
  
  .rich-text-viewer :deep(ul), 
  .rich-text-viewer :deep(ol) {
    padding-left: 20px;
  }
}
</style>