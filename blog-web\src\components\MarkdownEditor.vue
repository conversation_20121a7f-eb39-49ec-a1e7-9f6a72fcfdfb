<template>
  <!-- Markdown编辑器组件 - 提供专业的文章内容编辑功能 -->
  <div class="markdown-editor">
    <!-- 编辑器功能状态栏 -->
    <div class="editor-status-bar">
      <div class="status-left">
        <span class="status-item">
          <i class="el-icon-edit"></i>
          {{ readonly ? '预览模式' : '编辑模式' }}
        </span>
        <span class="status-item" v-if="wordCount > 0">
          <i class="el-icon-document"></i>
          字数统计: {{ wordCount }}
        </span>
      </div>
      <div class="status-right">
        <span class="status-item">
          <i class="el-icon-info"></i>
          支持Markdown语法
        </span>
      </div>
    </div>
    <!-- 编辑器DOM挂载点 -->
    <div ref="editorElement" class="editor-wrapper"></div>
    <!-- 编辑器底部工具提示 -->
    <div class="editor-footer">
      <div class="shortcut-tips">
        <span class="tip-group">
          <strong>快捷键:</strong>
          <span class="shortcut">Ctrl+B 加粗</span>
          <span class="shortcut">Ctrl+I 斜体</span>
          <span class="shortcut">Ctrl+K 插入链接</span>
          <span class="shortcut">Ctrl+Shift+I 插入图片</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import '@toast-ui/editor/dist/toastui-editor.css'
import Editor from '@toast-ui/editor'

export default {
  name: 'MarkdownEditor',
  props: {
    // 编辑器内容
    modelValue: {
      type: String,
      default: ''
    },
    // 编辑器高度
    height: {
      type: String,
      default: '400px'
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    // 编辑器DOM元素引用
    const editorElement = ref(null)
    // 编辑器实例
    let editor = null
    // 字数统计
    const wordCount = ref(0)

    // 初始化编辑器
    const initEditor = () => {
      // 创建编辑器实例
      editor = new Editor({
        el: editorElement.value,
        height: props.height,
        initialEditType: 'markdown',
        previewStyle: 'vertical',
        initialValue: props.modelValue,
        toolbarItems: [
          ['heading', 'bold', 'italic', 'strike'],
          ['hr', 'quote'],
          ['ul', 'ol', 'task', 'indent', 'outdent'],
          ['table', 'image', 'link'],
          ['code', 'codeblock'],
          ['scrollSync']
        ]
      })

      // 监听编辑器内容变化
      editor.on('change', () => {
        const content = editor.getMarkdown()
        // 计算字数（去除Markdown语法字符）
        const plainText = content.replace(/[#*`>[\]()_~]/g, '').replace(/\s+/g, ' ').trim()
        wordCount.value = plainText.length
        emit('update:modelValue', content)
        emit('change', content)
      })
    }

    // 组件挂载后初始化编辑器
    onMounted(() => {
      initEditor()
    })

    // 组件卸载前销毁编辑器
    onBeforeUnmount(() => {
      if (editor) {
        editor.destroy()
        editor = null
      }
    })

    // 监听modelValue变化，更新编辑器内容
    watch(() => props.modelValue, (newVal) => {
      if (editor && newVal !== editor.getMarkdown()) {
        editor.setMarkdown(newVal)
      }
    })

    // 监听readonly变化，更新编辑器状态
    watch(() => props.readonly, (newVal) => {
      if (editor) {
        if (newVal) {
          editor.disable()
        } else {
          editor.enable()
        }
      }
    }, { immediate: true })

    return {
      editorElement,
      wordCount
    }
  }
}
</script>

<style scoped>
.markdown-editor {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 编辑器状态栏样式 */
.editor-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  font-size: 12px;
  color: #495057;
}

.status-left, .status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.status-item i {
  font-size: 14px;
  color: #6c757d;
}

/* 编辑器包装容器 */
.editor-wrapper {
  min-height: 400px;
  background-color: #fff;
}

/* 编辑器底部工具提示 */
.editor-footer {
  padding: 10px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  font-size: 11px;
  color: #6c757d;
}

.shortcut-tips {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.tip-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tip-group strong {
  color: #495057;
  margin-right: 4px;
}

.shortcut {
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: #495057;
  border: 1px solid #ced4da;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-status-bar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .status-left, .status-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .shortcut-tips {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .tip-group {
    flex-wrap: wrap;
    gap: 4px;
  }
}

/* 编辑器主题定制 */
.markdown-editor :deep(.toastui-editor-defaultUI) {
  border: none;
}

.markdown-editor :deep(.toastui-editor-toolbar) {
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 12px;
}

.markdown-editor :deep(.toastui-editor-toolbar-icons) {
  gap: 4px;
}

.markdown-editor :deep(.toastui-editor-toolbar-icons button) {
  border-radius: 4px;
  transition: all 0.2s ease;
}

.markdown-editor :deep(.toastui-editor-toolbar-icons button:hover) {
  background-color: #f8f9fa;
  transform: translateY(-1px);
}
</style> 