/**
 * @description i18n en
 * <AUTHOR>
 */
declare const _default: {
    common: {
        ok: string;
        delete: string;
        enter: string;
    };
    blockQuote: {
        title: string;
    };
    codeBlock: {
        title: string;
    };
    color: {
        color: string;
        bgColor: string;
        default: string;
        clear: string;
    };
    divider: {
        title: string;
    };
    emotion: {
        title: string;
    };
    fontSize: {
        title: string;
        default: string;
    };
    fontFamily: {
        title: string;
        default: string;
    };
    fullScreen: {
        title: string;
    };
    header: {
        title: string;
        text: string;
    };
    image: {
        netImage: string;
        delete: string;
        edit: string;
        viewLink: string;
        src: string;
        desc: string;
        link: string;
    };
    indent: {
        decrease: string;
        increase: string;
    };
    justify: {
        left: string;
        right: string;
        center: string;
        justify: string;
    };
    lineHeight: {
        title: string;
        default: string;
    };
    link: {
        insert: string;
        text: string;
        url: string;
        unLink: string;
        edit: string;
        view: string;
    };
    textStyle: {
        bold: string;
        clear: string;
        code: string;
        italic: string;
        sub: string;
        sup: string;
        through: string;
        underline: string;
    };
    undo: {
        undo: string;
        redo: string;
    };
    todo: {
        todo: string;
    };
};
export default _default;
