# 服务器配置更新说明

## 📋 新服务器信息
- **服务器IP**: **************
- **SSH端口**: 22
- **用户名**: root
- **密码**: qwer123..
- **系统版本**: CentOS Linux 7.6 (Core)

## 🔄 已更新的文件

### 1. 部署文档
- `blog-server/src/main/resources/docs/CentOS7.6完整部署指南.md`
- `blog-server/target/classes/docs/CentOS7.6完整部署指南.md`

### 2. 前端环境配置
- `blog-web/.env.production` - 生产环境API地址
- `blog-web/.env.development` - 开发环境API地址（格式修正）
- `blog-web/src/config/settings.js` - 环境变量名修正
- `blog-web/vite.config.js` - 环境变量名修正

### 3. 部署脚本
- `deploy.bat` - Windows部署脚本
- `deploy.sh` - Linux/Mac部署脚本

## 🚀 部署方式

### 快速部署
```bash
# Windows
deploy.bat

# Linux/Mac
./deploy.sh
```

### 手动部署
```bash
# 1. 后端打包
cd blog-server
mvn clean package -DskipTests

# 2. 前端打包
cd ../blog-web
npm run build

# 3. 上传文件
cd ..
scp -P 22 blog-server/target/blog-server-0.0.1-SNAPSHOT.jar root@**************:/opt/blog-system/backend/
scp -P 22 blog-server/src/main/resources/application.yml root@**************:/opt/blog-system/backend/
scp -r -P 22 blog-web/dist/* root@**************:/opt/blog-system/frontend/dist/

# 4. 重启服务
ssh root@************** -p 22 "cd /opt/blog-system/backend && ./restart.sh"
```

## 🌐 访问地址
- **网站地址**: http://**************
- **管理员账户**: admin / admin123

## 📝 注意事项
1. 数据库表和数据需要手动导入
2. 确保服务器已按照部署指南配置好环境
3. 首次部署需要在服务器上创建目录结构
4. 生产环境使用的是HTTP，如需HTTPS请配置SSL证书

## 🔧 环境变量说明
- **开发环境**: `VITE_API_BASE_URL=http://localhost:8080`
- **生产环境**: `VITE_API_BASE_URL=http://**************`
