<template>
  <div class="modern-search-page">
    <div class="search-header">
      <h1 class="page-title">搜索发现</h1>
      <p class="page-subtitle">探索你感兴趣的内容</p>
    </div>

    <div class="search-form">
      <div class="search-input-wrapper">
        <el-input
          v-model="keyword"
          placeholder="请输入关键词搜索文章..."
          clearable
          size="large"
          class="search-input"
          @keyup.enter="handleSearch">
          <template #prefix>
            <el-icon class="search-icon"><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearch" class="search-btn">
              <el-icon><Search /></el-icon>
              <span>搜索</span>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <div class="search-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>搜索中...</p>
      </div>

      <div v-else-if="!keyword" class="empty-container">
        <div class="empty-illustration">
          <el-icon :size="80"><Search /></el-icon>
        </div>
        <h3>开始你的搜索之旅</h3>
        <p>在上方输入关键词，发现精彩内容</p>
      </div>

      <div v-else-if="articles.length === 0" class="empty-container">
        <div class="empty-illustration">
          <el-icon :size="80"><Document /></el-icon>
        </div>
        <h3>未找到相关内容</h3>
        <p>没有找到与 <span class="keyword">"{{ keyword }}"</span> 相关的文章</p>
        <p class="search-tip">试试其他关键词或检查拼写</p>
      </div>

      <div v-else class="search-results">
        <div class="result-header">
          <div class="result-info">
            找到 <span class="result-count">{{ pagination.total }}</span> 篇与
            <span class="keyword">"{{ keyword }}"</span> 相关的文章
          </div>
        </div>

        <div class="articles-grid">
          <article
            v-for="(article, index) in articles"
            :key="article.id"
            class="article-card modern-card hover-lift"
            :style="{ animationDelay: `${index * 0.1}s` }">

            <!-- 封面图片 -->
            <div v-if="article.coverImage" class="article-cover">
              <router-link :to="`/article/${article.id}`">
                <img :src="buildCoverImageUrl(article.coverImage)" :alt="article.title" />
              </router-link>
            </div>

            <div class="article-header">
              <h3 class="article-title">
                <router-link :to="`/article/${article.id}`">
                  {{ article.title }}
                </router-link>
              </h3>
              <div class="article-meta">
                <div class="meta-item">
                  <el-icon><User /></el-icon>
                  <span>{{ article.author }}</span>
                </div>
                <div class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(article.createTime) }}</span>
                </div>
                <div v-if="article.categoryName" class="meta-item">
                  <el-icon><Folder /></el-icon>
                  <span>{{ article.categoryName }}</span>
                </div>
              </div>
            </div>

            <div class="article-content">
              <p class="article-summary">{{ article.summary || '暂无摘要' }}</p>
            </div>

            <div class="article-footer">
              <router-link :to="`/article/${article.id}`" class="read-more-btn">
                <span>阅读全文</span>
                <el-icon><ArrowRight /></el-icon>
              </router-link>
            </div>
          </article>
        </div>

        <div v-if="pagination.total > 0" class="pagination-container">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-size="pagination.pageSize"
            layout="prev, pager, next, total"
            :total="pagination.total"
            class="modern-pagination">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { buildResourceUrl } from '@/config/settings'
import {
  Search, Document, User, Calendar, Folder, ArrowRight
} from '@element-plus/icons-vue'

export default {
  name: 'ModernSearchPage',
  components: {
    Search, Document, User, Calendar, Folder, ArrowRight
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const loading = ref(false)
    const keyword = ref('')

    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    const articles = ref([])

    const search = async () => {
      if (!keyword.value) {
        articles.value = []
        pagination.total = 0
        return
      }

      loading.value = true
      try {
        // 调用搜索API
        const response = await fetch(`/api/articles?current=${pagination.currentPage}&size=${pagination.pageSize}&keyword=${encodeURIComponent(keyword.value)}`)
        const result = await response.json()

        if (result.code === 200) {
          articles.value = result.data.records || []
          pagination.total = result.data.total || 0
        } else {
          console.error('搜索失败', result.message)
          articles.value = []
          pagination.total = 0
        }
      } catch (error) {
        console.error('搜索失败', error)
        articles.value = []
        pagination.total = 0
      } finally {
        loading.value = false
      }
    }

    const handleSearch = () => {
      pagination.currentPage = 1
      router.push({
        path: '/search',
        query: { q: keyword.value }
      })
    }

    const handleCurrentChange = (page) => {
      pagination.currentPage = page
      search()
    }

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      if (days < 365) return `${Math.floor(days / 30)}个月前`
      return `${Math.floor(days / 365)}年前`
    }

    // 构建封面图片URL
    const buildCoverImageUrl = (coverImage) => {
      if (!coverImage) return ''
      return buildResourceUrl(coverImage)
    }

    // 监听路由参数变化
    watch(() => route.query.q, (newKeyword) => {
      if (newKeyword !== undefined) {
        keyword.value = newKeyword
        search()
      }
    }, { immediate: true })

    return {
      loading,
      keyword,
      pagination,
      articles,
      handleSearch,
      handleCurrentChange,
      formatDate,
      buildCoverImageUrl
    }
  }
}
</script>

<style scoped>
/* 现代化搜索页面样式 */
.modern-search-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--spacing-xl) 0;
}

/* 搜索头部 */
.search-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  padding: 0 var(--spacing-lg);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* 搜索表单 */
.search-form {
  max-width: 800px;
  margin: 0 auto var(--spacing-2xl);
  padding: 0 var(--spacing-lg);
}

.search-input-wrapper {
  position: relative;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 2px solid transparent;
  transition: var(--transition-normal);
  background: var(--bg-primary);
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.search-icon {
  color: var(--text-secondary);
}

.search-btn {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-lg);
  font-weight: 600;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 搜索内容区域 */
.search-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-illustration {
  margin-bottom: var(--spacing-lg);
  color: var(--text-light);
}

.empty-container h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.empty-container p {
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.search-tip {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* 搜索结果 */
.search-results {
  animation: fadeInUp 0.6s ease-out;
}

.result-header {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.result-info {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.result-count {
  font-weight: 600;
  color: var(--primary-color);
}

.keyword {
  color: var(--primary-color);
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

/* 文章网格 */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

/* 文章卡片 */
.article-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--border-light);
  animation: slideInUp 0.6s ease-out both;
}

.article-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

/* 文章封面 */
.article-cover {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.article-cover img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition-normal);
}

.article-cover:hover img {
  transform: scale(1.05);
}

.article-header {
  margin-bottom: var(--spacing-lg);
}

.article-title {
  margin-bottom: var(--spacing-md);
}

.article-title a {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
  line-height: 1.4;
  display: block;
  transition: var(--transition-normal);
}

.article-title a:hover {
  color: var(--primary-color);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.meta-item .el-icon {
  font-size: 1rem;
  color: var(--primary-color);
}

.article-content {
  margin-bottom: var(--spacing-lg);
}

.article-summary {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.read-more-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition-normal);
}

.read-more-btn:hover {
  color: var(--primary-dark);
  transform: translateX(3px);
}

.read-more-btn .el-icon {
  transition: var(--transition-normal);
}

.read-more-btn:hover .el-icon {
  transform: translateX(3px);
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
}

.modern-pagination :deep(.el-pagination) {
  gap: var(--spacing-sm);
}

.modern-pagination :deep(.el-pager li) {
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.modern-pagination :deep(.el-pager li:hover) {
  transform: translateY(-2px);
}

.modern-pagination :deep(.el-pager li.is-active) {
  background: var(--gradient-primary);
  color: white;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .search-form {
    padding: 0 var(--spacing-md);
  }

  .search-content {
    padding: 0 var(--spacing-md);
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .article-card {
    padding: var(--spacing-lg);
  }

  .article-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }

  .search-header {
    margin-bottom: var(--spacing-xl);
  }

  .article-card {
    padding: var(--spacing-md);
  }

  .result-header {
    padding: var(--spacing-md);
  }
}
</style>