{"name": "@uppy/core", "description": "Core module for the extensible JavaScript file upload widget with support for drag&drop, resumable uploads, previews, restrictions, file processing/encoding, remote providers like Instagram, Dropbox, Google Drive, S3 and more :dog:", "version": "2.3.4", "license": "MIT", "main": "lib/index.js", "style": "dist/style.min.css", "types": "types/index.d.ts", "type": "module", "keywords": ["file uploader", "uppy", "uppy-plugin"], "homepage": "https://uppy.io", "bugs": {"url": "https://github.com/transloadit/uppy/issues"}, "repository": {"type": "git", "url": "git+https://github.com/transloadit/uppy.git"}, "dependencies": {"@transloadit/prettier-bytes": "0.0.7", "@uppy/store-default": "^2.1.1", "@uppy/utils": "^4.1.3", "lodash.throttle": "^4.1.1", "mime-match": "^1.0.2", "namespace-emitter": "^2.0.1", "nanoid": "^3.1.25", "preact": "^10.5.13"}, "devDependencies": {"@jest/globals": "^27.4.2"}}