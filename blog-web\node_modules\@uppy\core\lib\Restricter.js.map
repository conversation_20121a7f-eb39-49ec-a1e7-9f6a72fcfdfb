{"version": 3, "sources": ["Restricter.js"], "names": ["prettier<PERSON><PERSON>s", "match", "defaultOptions", "maxFileSize", "minFileSize", "maxTotalFileSize", "maxNumberOfFiles", "minNumberOfFiles", "allowedFileTypes", "required<PERSON>eta<PERSON>ields", "RestrictionError", "Error", "isRestriction", "AggregateError", "globalThis", "constructor", "errors", "message", "Restricter", "getOpts", "i18n", "opts", "restrictions", "Array", "isArray", "TypeError", "validate", "file", "files", "nonGhostFiles", "filter", "f", "isGhost", "length", "smart_count", "isCorrectFileType", "some", "type", "includes", "replace", "extension", "toLowerCase", "slice", "allowedFileTypesString", "join", "types", "size", "totalFilesSize", "reduce", "total", "name", "validateMinNumberOfFiles", "Object", "keys", "getMissingRequiredMetaFields", "error", "fileName", "own", "prototype", "hasOwnProperty", "missingFields", "field", "call", "meta", "push"], "mappings": ";;;;;;;AAAA;;AACA;MACOA,a;;MACAC,K;;AAEP,MAAMC,cAAc,GAAG;AACrBC,EAAAA,WAAW,EAAE,IADQ;AAErBC,EAAAA,WAAW,EAAE,IAFQ;AAGrBC,EAAAA,gBAAgB,EAAE,IAHG;AAIrBC,EAAAA,gBAAgB,EAAE,IAJG;AAKrBC,EAAAA,gBAAgB,EAAE,IALG;AAMrBC,EAAAA,gBAAgB,EAAE,IANG;AAOrBC,EAAAA,kBAAkB,EAAE;AAPC,CAAvB;;;AAUA,MAAMC,gBAAN,SAA+BC,KAA/B,CAAqC;AAAA;AAAA;AAAA,SACnCC,aADmC,GACnB,IADmB;AAAA;;AAAA;;;;AAIrC,IAAI,OAAOC,cAAP,KAA0B,WAA9B,EAA2C;AACzC;AACA;AACAC,EAAAA,UAAU,CAACD,cAAX,GAA4B,MAAMA,cAAN,SAA6BF,KAA7B,CAAmC;AAC7DI,IAAAA,WAAW,CAAEC,MAAF,EAAUC,OAAV,EAAmB;AAC5B,YAAMA,OAAN;AACA,WAAKD,MAAL,GAAcA,MAAd;AACD;;AAJ4D,GAA/D;AAMD;;AAED,MAAME,UAAN,CAAiB;AACfH,EAAAA,WAAW,CAAEI,OAAF,EAAWC,IAAX,EAAiB;AAC1B,SAAKA,IAAL,GAAYA,IAAZ;;AACA,SAAKD,OAAL,GAAe,MAAM;AACnB,YAAME,IAAI,GAAGF,OAAO,EAApB;;AAEA,UAAIE,IAAI,CAACC,YAAL,CAAkBd,gBAAlB,IAAsC,IAAtC,IACG,CAACe,KAAK,CAACC,OAAN,CAAcH,IAAI,CAACC,YAAL,CAAkBd,gBAAhC,CADR,EAC2D;AACzD,cAAM,IAAIiB,SAAJ,CAAc,kDAAd,CAAN;AACD;;AACD,aAAOJ,IAAP;AACD,KARD;AASD;;AAEDK,EAAAA,QAAQ,CAAEC,IAAF,EAAQC,KAAR,EAAe;AACrB,UAAM;AAAEzB,MAAAA,WAAF;AAAeC,MAAAA,WAAf;AAA4BC,MAAAA,gBAA5B;AAA8CC,MAAAA,gBAA9C;AAAgEE,MAAAA;AAAhE,QAAqF,KAAKW,OAAL,GAAeG,YAA1G;;AAEA,QAAIhB,gBAAJ,EAAsB;AACpB,YAAMuB,aAAa,GAAGD,KAAK,CAACE,MAAN,CAAaC,CAAC,IAAI,CAACA,CAAC,CAACC,OAArB,CAAtB;;AACA,UAAIH,aAAa,CAACI,MAAd,GAAuB,CAAvB,GAA2B3B,gBAA/B,EAAiD;AAC/C,cAAM,IAAII,gBAAJ,CAAsB,GAAE,KAAKU,IAAL,CAAU,mBAAV,EAA+B;AAAEc,UAAAA,WAAW,EAAE5B;AAAf,SAA/B,CAAkE,EAA1F,CAAN;AACD;AACF;;AAED,QAAIE,gBAAJ,EAAsB;AACpB,YAAM2B,iBAAiB,GAAG3B,gBAAgB,CAAC4B,IAAjB,CAAuBC,IAAD,IAAU;AACxD;AACA,YAAIA,IAAI,CAACC,QAAL,CAAc,GAAd,CAAJ,EAAwB;AACtB,cAAI,CAACX,IAAI,CAACU,IAAV,EAAgB,OAAO,KAAP;AAChB,iBAAOpC,KAAK,CAAC0B,IAAI,CAACU,IAAL,CAAUE,OAAV,CAAkB,OAAlB,EAA2B,EAA3B,CAAD,EAAiCF,IAAjC,CAAZ;AACD,SALuD,CAOxD;;;AACA,YAAIA,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmBV,IAAI,CAACa,SAA5B,EAAuC;AACrC,iBAAOb,IAAI,CAACa,SAAL,CAAeC,WAAf,OAAiCJ,IAAI,CAACK,KAAL,CAAW,CAAX,EAAcD,WAAd,EAAxC;AACD;;AACD,eAAO,KAAP;AACD,OAZyB,CAA1B;;AAcA,UAAI,CAACN,iBAAL,EAAwB;AACtB,cAAMQ,sBAAsB,GAAGnC,gBAAgB,CAACoC,IAAjB,CAAsB,IAAtB,CAA/B;AACA,cAAM,IAAIlC,gBAAJ,CAAqB,KAAKU,IAAL,CAAU,2BAAV,EAAuC;AAAEyB,UAAAA,KAAK,EAAEF;AAAT,SAAvC,CAArB,CAAN;AACD;AACF,KA7BoB,CA+BrB;;;AACA,QAAItC,gBAAgB,IAAIsB,IAAI,CAACmB,IAAL,IAAa,IAArC,EAA2C;AACzC,YAAMC,cAAc,GAAGnB,KAAK,CAACoB,MAAN,CAAa,CAACC,KAAD,EAAQlB,CAAR,KAAekB,KAAK,GAAGlB,CAAC,CAACe,IAAtC,EAA6CnB,IAAI,CAACmB,IAAlD,CAAvB;;AAEA,UAAIC,cAAc,GAAG1C,gBAArB,EAAuC;AACrC,cAAM,IAAIK,gBAAJ,CAAqB,KAAKU,IAAL,CAAU,aAAV,EAAyB;AAClD0B,UAAAA,IAAI,EAAE9C,aAAa,CAACK,gBAAD,CAD+B;AAElDsB,UAAAA,IAAI,EAAEA,IAAI,CAACuB;AAFuC,SAAzB,CAArB,CAAN;AAID;AACF,KAzCoB,CA2CrB;;;AACA,QAAI/C,WAAW,IAAIwB,IAAI,CAACmB,IAAL,IAAa,IAA5B,IAAoCnB,IAAI,CAACmB,IAAL,GAAY3C,WAApD,EAAiE;AAC/D,YAAM,IAAIO,gBAAJ,CAAqB,KAAKU,IAAL,CAAU,aAAV,EAAyB;AAClD0B,QAAAA,IAAI,EAAE9C,aAAa,CAACG,WAAD,CAD+B;AAElDwB,QAAAA,IAAI,EAAEA,IAAI,CAACuB;AAFuC,OAAzB,CAArB,CAAN;AAID,KAjDoB,CAmDrB;;;AACA,QAAI9C,WAAW,IAAIuB,IAAI,CAACmB,IAAL,IAAa,IAA5B,IAAoCnB,IAAI,CAACmB,IAAL,GAAY1C,WAApD,EAAiE;AAC/D,YAAM,IAAIM,gBAAJ,CAAqB,KAAKU,IAAL,CAAU,cAAV,EAA0B;AACnD0B,QAAAA,IAAI,EAAE9C,aAAa,CAACI,WAAD;AADgC,OAA1B,CAArB,CAAN;AAGD;AACF;;AAED+C,EAAAA,wBAAwB,CAAEvB,KAAF,EAAS;AAC/B,UAAM;AAAErB,MAAAA;AAAF,QAAuB,KAAKY,OAAL,GAAeG,YAA5C;;AACA,QAAI8B,MAAM,CAACC,IAAP,CAAYzB,KAAZ,EAAmBK,MAAnB,GAA4B1B,gBAAhC,EAAkD;AAChD,YAAM,IAAIG,gBAAJ,CAAqB,KAAKU,IAAL,CAAU,yBAAV,EAAqC;AAAEc,QAAAA,WAAW,EAAE3B;AAAf,OAArC,CAArB,CAAN;AACD;AACF;;AAED+C,EAAAA,4BAA4B,CAAE3B,IAAF,EAAQ;AAClC,UAAM4B,KAAK,GAAG,IAAI7C,gBAAJ,CAAqB,KAAKU,IAAL,CAAU,gCAAV,EAA4C;AAAEoC,MAAAA,QAAQ,EAAE7B,IAAI,CAACuB;AAAjB,KAA5C,CAArB,CAAd;AACA,UAAM;AAAEzC,MAAAA;AAAF,QAAyB,KAAKU,OAAL,GAAeG,YAA9C,CAFkC,CAGlC;;AACA,UAAMmC,GAAG,GAAGL,MAAM,CAACM,SAAP,CAAiBC,cAA7B;AACA,UAAMC,aAAa,GAAG,EAAtB;;AAEA,SAAK,MAAMC,KAAX,IAAoBpD,kBAApB,EAAwC;AACtC,UAAI,CAACgD,GAAG,CAACK,IAAJ,CAASnC,IAAI,CAACoC,IAAd,EAAoBF,KAApB,CAAD,IAA+BlC,IAAI,CAACoC,IAAL,CAAUF,KAAV,MAAqB,EAAxD,EAA4D;AAC1DD,QAAAA,aAAa,CAACI,IAAd,CAAmBH,KAAnB;AACD;AACF;;AAED,WAAO;AAAED,MAAAA,aAAF;AAAiBL,MAAAA;AAAjB,KAAP;AACD;;AA9Fc", "sourcesContent": ["/* eslint-disable max-classes-per-file, class-methods-use-this */\n/* global AggregateError */\nimport prettierBytes from '@transloadit/prettier-bytes'\nimport match from 'mime-match'\n\nconst defaultOptions = {\n  maxFileSize: null,\n  minFileSize: null,\n  maxTotalFileSize: null,\n  maxNumberOfFiles: null,\n  minNumberOfFiles: null,\n  allowedFileTypes: null,\n  requiredMetaFields: [],\n}\n\nclass RestrictionError extends Error {\n  isRestriction = true\n}\n\nif (typeof AggregateError === 'undefined') {\n  // eslint-disable-next-line no-global-assign\n  // TODO: remove this \"polyfill\" in the next major.\n  globalThis.AggregateError = class AggregateError extends Error {\n    constructor (errors, message) {\n      super(message)\n      this.errors = errors\n    }\n  }\n}\n\nclass Restricter {\n  constructor (getOpts, i18n) {\n    this.i18n = i18n\n    this.getOpts = () => {\n      const opts = getOpts()\n\n      if (opts.restrictions.allowedFileTypes != null\n          && !Array.isArray(opts.restrictions.allowedFileTypes)) {\n        throw new TypeError('`restrictions.allowedFileTypes` must be an array')\n      }\n      return opts\n    }\n  }\n\n  validate (file, files) {\n    const { maxFileSize, minFileSize, maxTotalFileSize, maxNumberOfFiles, allowedFileTypes } = this.getOpts().restrictions\n\n    if (maxNumberOfFiles) {\n      const nonGhostFiles = files.filter(f => !f.isGhost)\n      if (nonGhostFiles.length + 1 > maxNumberOfFiles) {\n        throw new RestrictionError(`${this.i18n('youCanOnlyUploadX', { smart_count: maxNumberOfFiles })}`)\n      }\n    }\n\n    if (allowedFileTypes) {\n      const isCorrectFileType = allowedFileTypes.some((type) => {\n        // check if this is a mime-type\n        if (type.includes('/')) {\n          if (!file.type) return false\n          return match(file.type.replace(/;.*?$/, ''), type)\n        }\n\n        // otherwise this is likely an extension\n        if (type[0] === '.' && file.extension) {\n          return file.extension.toLowerCase() === type.slice(1).toLowerCase()\n        }\n        return false\n      })\n\n      if (!isCorrectFileType) {\n        const allowedFileTypesString = allowedFileTypes.join(', ')\n        throw new RestrictionError(this.i18n('youCanOnlyUploadFileTypes', { types: allowedFileTypesString }))\n      }\n    }\n\n    // We can't check maxTotalFileSize if the size is unknown.\n    if (maxTotalFileSize && file.size != null) {\n      const totalFilesSize = files.reduce((total, f) => (total + f.size), file.size)\n\n      if (totalFilesSize > maxTotalFileSize) {\n        throw new RestrictionError(this.i18n('exceedsSize', {\n          size: prettierBytes(maxTotalFileSize),\n          file: file.name,\n        }))\n      }\n    }\n\n    // We can't check maxFileSize if the size is unknown.\n    if (maxFileSize && file.size != null && file.size > maxFileSize) {\n      throw new RestrictionError(this.i18n('exceedsSize', {\n        size: prettierBytes(maxFileSize),\n        file: file.name,\n      }))\n    }\n\n    // We can't check minFileSize if the size is unknown.\n    if (minFileSize && file.size != null && file.size < minFileSize) {\n      throw new RestrictionError(this.i18n('inferiorSize', {\n        size: prettierBytes(minFileSize),\n      }))\n    }\n  }\n\n  validateMinNumberOfFiles (files) {\n    const { minNumberOfFiles } = this.getOpts().restrictions\n    if (Object.keys(files).length < minNumberOfFiles) {\n      throw new RestrictionError(this.i18n('youHaveToAtLeastSelectX', { smart_count: minNumberOfFiles }))\n    }\n  }\n\n  getMissingRequiredMetaFields (file) {\n    const error = new RestrictionError(this.i18n('missingRequiredMetaFieldOnFile', { fileName: file.name }))\n    const { requiredMetaFields } = this.getOpts().restrictions\n    // TODO: migrate to Object.hasOwn in the next major.\n    const own = Object.prototype.hasOwnProperty\n    const missingFields = []\n\n    for (const field of requiredMetaFields) {\n      if (!own.call(file.meta, field) || file.meta[field] === '') {\n        missingFields.push(field)\n      }\n    }\n\n    return { missingFields, error }\n  }\n}\n\nexport { Restricter, defaultOptions, RestrictionError }\n"]}