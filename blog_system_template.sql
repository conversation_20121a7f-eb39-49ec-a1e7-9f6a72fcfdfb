-- =============================================
-- 博客系统完整数据库脚本
-- 数据库：blog_system
-- 版本：MySQL 8.0
-- 字符集：utf8mb4
-- 生成时间：2025-01-26
-- =============================================

-- 删除数据库（如果存在）
DROP DATABASE IF EXISTS blog_system;

-- 创建数据库
CREATE DATABASE blog_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE blog_system;

-- =============================================
-- 表结构创建
-- =============================================

-- 用户表
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  `bio` text COMMENT '个人简介',
  `role` varchar(20) DEFAULT 'USER' COMMENT '角色',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 分类表
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `order_num` int DEFAULT '0' COMMENT '排序号',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 标签表
CREATE TABLE `tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- =============================================
-- 在这里添加DBeaver导出的其他表结构
-- =============================================

-- 文章表、评论表、关联表等...
-- （请将DBeaver导出的CREATE TABLE语句粘贴到这里）

-- =============================================
-- 初始数据插入
-- =============================================

-- 插入管理员用户
INSERT INTO `user` (`username`, `password`, `email`, `nickname`, `role`, `status`) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeWpqHNNpwjWlWzXGG.y/ItgtZ5nxPRjC', '<EMAIL>', '管理员', 'ADMIN', 1);

-- 插入默认分类
INSERT INTO `category` (`name`, `description`, `order_num`) VALUES
('技术分享', '技术相关文章', 1),
('生活随笔', '生活感悟和随笔', 2),
('学习笔记', '学习过程中的笔记', 3);

-- 插入默认标签
INSERT INTO `tag` (`name`, `color`) VALUES
('Java', '#f56a00'),
('Spring Boot', '#87d068'),
('Vue.js', '#2db7f5'),
('MySQL', '#108ee9');

-- =============================================
-- 在这里添加DBeaver导出的数据
-- =============================================

-- （请将DBeaver导出的INSERT语句粘贴到这里）

-- =============================================
-- 脚本执行完成
-- =============================================
