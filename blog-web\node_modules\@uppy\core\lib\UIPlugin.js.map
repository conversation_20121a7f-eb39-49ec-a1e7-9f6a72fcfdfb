{"version": 3, "sources": ["UIPlugin.js"], "names": ["findDOMElement", "getTextDirection", "BasePlugin", "debounce", "fn", "calling", "latestArgs", "args", "Promise", "resolve", "then", "UIPlugin", "mount", "target", "plugin", "callerPluginName", "id", "targetElement", "isTargetDOMEl", "uppyRootElement", "document", "createElement", "classList", "add", "state", "uppy", "getPlugin", "render", "afterUpdate", "log", "opts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "innerHTML", "getState", "el", "append<PERSON><PERSON><PERSON>", "dir", "direction", "onMount", "targetPlugin", "Target", "iteratePlugins", "p", "parent", "addTarget", "message", "Error", "update", "unmount", "remove", "onUnmount"], "mappings": ";;AAAA;;;;;;;;MACOA,c;;MACAC,gB;;MAEAC,U;AAEP;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,QAAT,CAAmBC,EAAnB,EAAuB;AACrB,MAAIC,OAAO,GAAG,IAAd;AACA,MAAIC,UAAU,GAAG,IAAjB;AACA,SAAO,YAAa;AAAA,sCAATC,IAAS;AAATA,MAAAA,IAAS;AAAA;;AAClBD,IAAAA,UAAU,GAAGC,IAAb;;AACA,QAAI,CAACF,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAGG,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AACrCL,QAAAA,OAAO,GAAG,IAAV,CADqC,CAErC;AACA;AACA;AACA;;AACA,eAAOD,EAAE,CAAC,GAAGE,UAAJ,CAAT;AACD,OAPS,CAAV;AAQD;;AACD,WAAOD,OAAP;AACD,GAbD;AAcD;AAED;AACA;AACA;AACA;AACA;AACA;;;;;AACA,MAAMM,QAAN,SAAuBT,UAAvB,CAAkC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAGhC;AACF;AACA;AACA;AACA;AACEU,EAAAA,KAAK,CAAEC,MAAF,EAAUC,MAAV,EAAkB;AACrB,UAAMC,gBAAgB,GAAGD,MAAM,CAACE,EAAhC;AAEA,UAAMC,aAAa,GAAGjB,cAAc,CAACa,MAAD,CAApC;;AAEA,QAAII,aAAJ,EAAmB;AACjB,WAAKC,aAAL,GAAqB,IAArB,CADiB,CAEjB;AACA;AACA;;AACA,YAAMC,eAAe,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAxB;AACAF,MAAAA,eAAe,CAACG,SAAhB,CAA0BC,GAA1B,CAA8B,WAA9B,EANiB,CAQjB;;AACA,gEAAiBpB,QAAQ,CAAEqB,KAAD,IAAW;AACnC;AACA;AACA;AACA,YAAI,CAAC,KAAKC,IAAL,CAAUC,SAAV,CAAoB,KAAKV,EAAzB,CAAL,EAAmC;AACnC,4BAAO,KAAKW,MAAL,CAAYH,KAAZ,CAAP,EAA2BL,eAA3B;AACA,aAAKS,WAAL;AACD,OAPwB,CAAzB;AASA,WAAKH,IAAL,CAAUI,GAAV,CAAe,cAAad,gBAAiB,sBAAqBF,MAAO,GAAzE;;AAEA,UAAI,KAAKiB,IAAL,CAAUC,oBAAd,EAAoC;AAClC;AACA;AACA;AACAd,QAAAA,aAAa,CAACe,SAAd,GAA0B,EAA1B;AACD;;AAED,0BAAO,KAAKL,MAAL,CAAY,KAAKF,IAAL,CAAUQ,QAAV,EAAZ,CAAP,EAA0Cd,eAA1C;AACA,WAAKe,EAAL,GAAUf,eAAV;AACAF,MAAAA,aAAa,CAACkB,WAAd,CAA0BhB,eAA1B,EA7BiB,CA+BjB;;AACAA,MAAAA,eAAe,CAACiB,GAAhB,GAAsB,KAAKN,IAAL,CAAUO,SAAV,IAAuBpC,gBAAgB,CAACkB,eAAD,CAAvC,IAA4D,KAAlF;AAEA,WAAKmB,OAAL;AAEA,aAAO,KAAKJ,EAAZ;AACD;;AAED,QAAIK,YAAJ;;AACA,QAAI,OAAO1B,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,YAAYF,QAApD,EAA8D;AAC5D;AACA4B,MAAAA,YAAY,GAAG1B,MAAf;AACD,KAHD,MAGO,IAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AACvC;AACA,YAAM2B,MAAM,GAAG3B,MAAf,CAFuC,CAGvC;;AACA,WAAKY,IAAL,CAAUgB,cAAV,CAAyBC,CAAC,IAAI;AAC5B,YAAIA,CAAC,YAAYF,MAAjB,EAAyB;AACvBD,UAAAA,YAAY,GAAGG,CAAf;AACD;AACF,OAJD;AAKD;;AAED,QAAIH,YAAJ,EAAkB;AAChB,WAAKd,IAAL,CAAUI,GAAV,CAAe,cAAad,gBAAiB,OAAMwB,YAAY,CAACvB,EAAG,EAAnE;AACA,WAAK2B,MAAL,GAAcJ,YAAd;AACA,WAAKL,EAAL,GAAUK,YAAY,CAACK,SAAb,CAAuB9B,MAAvB,CAAV;AAEA,WAAKwB,OAAL;AACA,aAAO,KAAKJ,EAAZ;AACD;;AAED,SAAKT,IAAL,CAAUI,GAAV,CAAe,kBAAiBd,gBAAiB,EAAjD;AAEA,QAAI8B,OAAO,GAAI,kCAAiC9B,gBAAiB,GAAjE;;AACA,QAAI,OAAOF,MAAP,KAAkB,UAAtB,EAAkC;AAChCgC,MAAAA,OAAO,IAAI,8CACP,kFADO,GAEP,yGAFO,GAGP,+GAHJ;AAID,KALD,MAKO;AACLA,MAAAA,OAAO,IAAI,uFACP,gHADO,GAEP,2DAFO,GAGP,+GAHJ;AAID;;AACD,UAAM,IAAIC,KAAJ,CAAUD,OAAV,CAAN;AACD;;AAEDE,EAAAA,MAAM,CAAEvB,KAAF,EAAS;AACb,QAAI,KAAKU,EAAL,IAAW,IAAf,EAAqB;AAAA;;AACnB,yLAAiBV,KAAjB;AACD;AACF;;AAEDwB,EAAAA,OAAO,GAAI;AACT,QAAI,KAAK9B,aAAT,EAAwB;AAAA;;AACtB,uBAAKgB,EAAL,8BAASe,MAAT;AACD;;AACD,SAAKC,SAAL;AACD,GAxG+B,CA0GhC;;;AACAZ,EAAAA,OAAO,GAAI,CAAE,CA3GmB,CA6GhC;;;AACAY,EAAAA,SAAS,GAAI,CAAE;;AA9GiB;;iBAiHnBvC,Q", "sourcesContent": ["import { render } from 'preact'\nimport findDOMElement from '@uppy/utils/lib/findDOMElement'\nimport getTextDirection from '@uppy/utils/lib/getTextDirection'\n\nimport BasePlugin from './BasePlugin.js'\n\n/**\n * Defer a frequent call to the microtask queue.\n *\n * @param {() => T} fn\n * @returns {Promise<T>}\n */\nfunction debounce (fn) {\n  let calling = null\n  let latestArgs = null\n  return (...args) => {\n    latestArgs = args\n    if (!calling) {\n      calling = Promise.resolve().then(() => {\n        calling = null\n        // At this point `args` may be different from the most\n        // recent state, if multiple calls happened since this task\n        // was queued. So we use the `latestArgs`, which definitely\n        // is the most recent call.\n        return fn(...latestArgs)\n      })\n    }\n    return calling\n  }\n}\n\n/**\n * UIPlugin is the extended version of BasePlugin to incorporate rendering with Preact.\n * Use this for plugins that need a user interface.\n *\n * For plugins without an user interface, see BasePlugin.\n */\nclass UIPlugin extends BasePlugin {\n  #updateUI\n\n  /**\n   * Check if supplied `target` is a DOM element or an `object`.\n   * If it’s an object — target is a plugin, and we search `plugins`\n   * for a plugin with same name and return its target.\n   */\n  mount (target, plugin) {\n    const callerPluginName = plugin.id\n\n    const targetElement = findDOMElement(target)\n\n    if (targetElement) {\n      this.isTargetDOMEl = true\n      // When target is <body> with a single <div> element,\n      // Preact thinks it’s the Uppy root element in there when doing a diff,\n      // and destroys it. So we are creating a fragment (could be empty div)\n      const uppyRootElement = document.createElement('div')\n      uppyRootElement.classList.add('uppy-Root')\n\n      // API for plugins that require a synchronous rerender.\n      this.#updateUI = debounce((state) => {\n        // plugin could be removed, but this.rerender is debounced below,\n        // so it could still be called even after uppy.removePlugin or uppy.close\n        // hence the check\n        if (!this.uppy.getPlugin(this.id)) return\n        render(this.render(state), uppyRootElement)\n        this.afterUpdate()\n      })\n\n      this.uppy.log(`Installing ${callerPluginName} to a DOM element '${target}'`)\n\n      if (this.opts.replaceTargetContent) {\n        // Doing render(h(null), targetElement), which should have been\n        // a better way, since because the component might need to do additional cleanup when it is removed,\n        // stopped working — Preact just adds null into target, not replacing\n        targetElement.innerHTML = ''\n      }\n\n      render(this.render(this.uppy.getState()), uppyRootElement)\n      this.el = uppyRootElement\n      targetElement.appendChild(uppyRootElement)\n\n      // Set the text direction if the page has not defined one.\n      uppyRootElement.dir = this.opts.direction || getTextDirection(uppyRootElement) || 'ltr'\n\n      this.onMount()\n\n      return this.el\n    }\n\n    let targetPlugin\n    if (typeof target === 'object' && target instanceof UIPlugin) {\n      // Targeting a plugin *instance*\n      targetPlugin = target\n    } else if (typeof target === 'function') {\n      // Targeting a plugin type\n      const Target = target\n      // Find the target plugin instance.\n      this.uppy.iteratePlugins(p => {\n        if (p instanceof Target) {\n          targetPlugin = p\n        }\n      })\n    }\n\n    if (targetPlugin) {\n      this.uppy.log(`Installing ${callerPluginName} to ${targetPlugin.id}`)\n      this.parent = targetPlugin\n      this.el = targetPlugin.addTarget(plugin)\n\n      this.onMount()\n      return this.el\n    }\n\n    this.uppy.log(`Not installing ${callerPluginName}`)\n\n    let message = `Invalid target option given to ${callerPluginName}.`\n    if (typeof target === 'function') {\n      message += ' The given target is not a Plugin class. '\n        + 'Please check that you\\'re not specifying a React Component instead of a plugin. '\n        + 'If you are using @uppy/* packages directly, make sure you have only 1 version of @uppy/core installed: '\n        + 'run `npm ls @uppy/core` on the command line and verify that all the versions match and are deduped correctly.'\n    } else {\n      message += 'If you meant to target an HTML element, please make sure that the element exists. '\n        + 'Check that the <script> tag initializing Uppy is right before the closing </body> tag at the end of the page. '\n        + '(see https://github.com/transloadit/uppy/issues/1042)\\n\\n'\n        + 'If you meant to target a plugin, please confirm that your `import` statements or `require` calls are correct.'\n    }\n    throw new Error(message)\n  }\n\n  update (state) {\n    if (this.el != null) {\n      this.#updateUI?.(state)\n    }\n  }\n\n  unmount () {\n    if (this.isTargetDOMEl) {\n      this.el?.remove()\n    }\n    this.onUnmount()\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  onMount () {}\n\n  // eslint-disable-next-line class-methods-use-this\n  onUnmount () {}\n}\n\nexport default UIPlugin\n"]}