# 个人动态博客系统 - 部署检查清单

## 📋 部署前准备

### 1. 环境要求检查
- [ ] **Java环境**：JDK 1.8 或以上版本
- [ ] **数据库**：MySQL 8.0 或以上版本
- [ ] **Node.js**：Node.js 16+ 和 npm/yarn
- [ ] **Redis**：Redis 服务器（可选，用于缓存）

### 2. 服务器资源检查
- [ ] **内存**：至少 2GB 可用内存
- [ ] **磁盘空间**：至少 5GB 可用空间
- [ ] **网络**：确保可以访问外部网络（邮件发送需要）

## 🗄️ 数据库部署

### 1. 创建数据库
```bash
# 方法1：直接导入到新数据库
mysql -u root -p -e "CREATE DATABASE blog_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;"
mysql -u root -p blog_system < docs/database/blog_system.sql

# 方法2：脚本会自动创建数据库（注释掉的部分）
# mysql -u root -p < docs/database/blog_system.sql
```

**⚠️ 重要提醒**：
- 只使用 `docs/database/blog_system.sql` 脚本
- 这是经过完整验证的一键部署脚本
- 包含所有表结构和初始数据

### 2. 验证数据库
- [ ] **表结构检查**：确认15个表全部创建成功
  ```sql
  USE blog_system;
  SHOW TABLES;
  -- 应该显示15个表
  ```
- [ ] **初始数据检查**：
  ```sql
  -- 检查管理员账户
  SELECT username, role FROM user WHERE role = 'admin';
  
  -- 检查邮件模板
  SELECT COUNT(*) FROM email_template;
  -- 应该显示6个模板
  
  -- 检查系统配置
  SELECT COUNT(*) FROM config;
  -- 应该显示4个配置项
  ```

## 🔧 后端部署

### 1. 配置文件检查
- [ ] **数据库配置**：`application.yml` 中的数据库连接信息
- [ ] **Redis配置**：Redis连接信息（如果使用）
- [ ] **文件上传路径**：确保上传目录有写权限

### 2. 编译和启动
```bash
cd blog-server
mvn clean package -DskipTests
java -jar target/blog-server-0.0.1-SNAPSHOT.jar
```

### 3. 后端验证
- [ ] **启动成功**：查看日志确认启动无错误
- [ ] **API测试**：访问 `http://localhost:8080/api/health`
- [ ] **管理员登录**：使用 admin/admin123 登录测试

## 🌐 前端部署

### 1. 依赖安装和构建
```bash
cd blog-web
npm install
npm run build
```

### 2. 部署方式选择
- [ ] **开发模式**：`npm run dev`（端口3000）
- [ ] **生产模式**：将 `dist` 目录部署到 Nginx 等 Web 服务器

### 3. 前端验证
- [ ] **页面访问**：确认首页可以正常访问
- [ ] **登录功能**：测试用户登录功能
- [ ] **管理后台**：测试管理员后台功能

## 📧 邮件功能配置

### 1. 邮箱配置
- [ ] **SMTP服务器**：配置邮件发送服务器
- [ ] **授权码获取**：获取邮箱的SMTP授权码
- [ ] **配置保存**：在系统设置中保存邮件配置

### 2. 邮件功能测试
- [ ] **测试邮件**：使用"测试邮件配置"功能
- [ ] **评论通知**：测试评论邮件通知功能
- [ ] **模板管理**：测试邮件模板管理功能

## 🧪 功能验证

### 1. 核心功能测试
- [ ] **用户注册登录**：测试用户认证功能
- [ ] **文章发布**：测试文章发布和编辑功能
- [ ] **评论系统**：测试评论和回复功能
- [ ] **用户互动**：测试点赞、收藏、关注功能

### 2. 管理功能测试
- [ ] **用户管理**：测试用户状态管理
- [ ] **内容管理**：测试文章、评论管理
- [ ] **系统设置**：测试各项系统配置
- [ ] **仪表盘**：测试数据统计功能

## 🔒 安全检查

### 1. 权限验证
- [ ] **管理员权限**：确认只有管理员可以访问后台
- [ ] **API权限**：确认API接口权限控制正常
- [ ] **文件上传**：确认文件上传安全限制

### 2. 数据安全
- [ ] **密码加密**：确认用户密码已加密存储
- [ ] **SQL注入防护**：确认使用参数化查询
- [ ] **XSS防护**：确认前端输入过滤

## 📊 性能优化

### 1. 数据库优化
- [ ] **索引检查**：确认关键字段已建立索引
- [ ] **查询优化**：检查慢查询日志
- [ ] **连接池配置**：配置合适的数据库连接池

### 2. 应用优化
- [ ] **缓存配置**：配置Redis缓存（可选）
- [ ] **静态资源**：配置静态资源缓存
- [ ] **日志配置**：配置合适的日志级别

## 🚀 部署完成检查

### 1. 最终验证
- [ ] **完整功能测试**：按照测试指南进行完整测试
- [ ] **性能测试**：进行基本的性能测试
- [ ] **备份策略**：制定数据备份策略

### 2. 监控和维护
- [ ] **日志监控**：设置日志监控和告警
- [ ] **数据备份**：设置定期数据备份
- [ ] **更新计划**：制定系统更新和维护计划

---

## 📞 技术支持

如果在部署过程中遇到问题，请参考：
1. **[问题记录与解决方案.md](issues/问题记录与解决方案.md)** - 常见问题解决方案
2. **[各阶段测试指南](testing/)** - 详细的功能测试流程
3. **[系统配置.md](project/系统配置.md)** - 系统配置详细说明

**部署成功标志**：
- ✅ 数据库脚本执行成功，15个表全部创建
- ✅ 后端服务启动成功，API接口正常响应
- ✅ 前端页面正常访问，功能完整可用
- ✅ 邮件功能配置成功，通知邮件正常发送
- ✅ 管理后台功能正常，数据统计准确
