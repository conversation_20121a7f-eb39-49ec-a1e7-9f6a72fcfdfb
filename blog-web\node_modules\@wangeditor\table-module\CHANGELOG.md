# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.1.4](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/table-module@1.1.3...@wangeditor/table-module@1.1.4) (2022-09-27)

**Note:** Version bump only for package @wangeditor/table-module





## [1.1.3](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/table-module@1.1.2...@wangeditor/table-module@1.1.3) (2022-09-15)


### Bug Fixes

* 插入表格会删掉去掉 issue 4711 ([d4fac4e](https://github.com/wangeditor-team/wangEditor/commit/d4fac4efd06480457a95c2b06e7472cf6204de58))





## [1.1.2](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/table-module@1.1.1...@wangeditor/table-module@1.1.2) (2022-09-14)

**Note:** Version bump only for package @wangeditor/table-module





## [1.1.1](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/table-module@1.1.0...@wangeditor/table-module@1.1.1) (2022-07-11)


### Bug Fixes

* disabled 时，点击 table 会弹出菜单栏 ([9aa4b80](https://github.com/wangeditor-team/wangEditor/commit/9aa4b80a8c3cd29ca57dd62d69f5811868998f5c))





# [1.1.0](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/table-module@1.0.1...@wangeditor/table-module@1.1.0) (2022-05-25)


### Bug Fixes

* 从表格后面删除，删除最后一个单元格 ([b327fcd](https://github.com/wangeditor-team/wangEditor/commit/b327fcd4669b1b1fad0e8b38b7d88db04c300e37))


### Features

* enter menu ([988fc31](https://github.com/wangeditor-team/wangEditor/commit/988fc31f31de3d37dffbf54abb784cceb8e6118d))
* 表格拖拽列宽 ([46ea2c0](https://github.com/wangeditor-team/wangEditor/commit/46ea2c0f831b03ebca5fddfd59d682fed0b3476e))





## 1.0.1 (2022-04-18)


### Bug Fixes

* 部分菜单 disabled ([87f1233](https://github.com/wangeditor-team/wangEditor/commit/87f12332a087072406c1988dc5cef2eae8335375))
* 单元格内包含复杂样式内容时按tab未跳转到下一个单元格 ([db5e6f2](https://github.com/wangeditor-team/wangEditor/commit/db5e6f20c2c081d193fa80077f91d121be98c2a0))
* 更新各包之间依赖版本 ([75c552c](https://github.com/wangeditor-team/wangEditor/commit/75c552cc8ed54765bebb86a7ec5329a7fc79e85f))
* 两个表格不能紧挨着 ([5955b61](https://github.com/wangeditor-team/wangEditor/commit/5955b614cf92f65c9ebea47e6719047f3c0d27ea))
* 修复 pnpm 安装 @wangeditor/editor 出现警告的问题 ([4087fbe](https://github.com/wangeditor-team/wangEditor/commit/4087fbee01c76bdd55e747a5e86c5e4a8d6a8353))
* 移除了每个包下的 publishConfig directory 配置 ([16559f0](https://github.com/wangeditor-team/wangEditor/commit/16559f052545c111318be760e64291a521bdcc65))
* 优化 custom-types.d.ts 中类型声明，修复测试文件 ts 报错 ([3a6c455](https://github.com/wangeditor-team/wangEditor/commit/3a6c4553245bc734dae1e17d605af389971782a2))
* 优化表格 ([f240ca7](https://github.com/wangeditor-team/wangEditor/commit/f240ca71e31ccdea947233a767e3371434af0b6f))
* parse html - 有些 elem children 需要过滤 ([63cbb80](https://github.com/wangeditor-team/wangEditor/commit/63cbb804c8c7a778a4ee1f4ba8717a11b4b6b5a3))
* rename es module filename ([1821d4e](https://github.com/wangeditor-team/wangEditor/commit/1821d4eef49e64efcb41b848849ca7a5e6472044))
* table - 粘贴合并单元格的表格 ([56ecb63](https://github.com/wangeditor-team/wangEditor/commit/56ecb6392510d433e092653f0f08183361778a3d))
* table - disabled ([2b8717c](https://github.com/wangeditor-team/wangEditor/commit/2b8717c9a1c6853a3311fa6a667df6e0e75b61ee))
* table - elemToHtml ([e36e609](https://github.com/wangeditor-team/wangEditor/commit/e36e6092ef721723169afc8bf0560a47ac9f4dfc))
* table 不能是第一个元素 ([9407b79](https://github.com/wangeditor-team/wangEditor/commit/9407b79604163fece99dd96552487d21afd085e7))
* table insertDOMElem ([6c89177](https://github.com/wangeditor-team/wangEditor/commit/6c89177878461fd59f128aa44ac175b2a49c3bd6))
* table insertDOMElem ([3a42c37](https://github.com/wangeditor-team/wangEditor/commit/3a42c37c3bc38343e3a0b245d2bfb2abed0bd720))
* table-cell 全选 ([1ef4872](https://github.com/wangeditor-team/wangEditor/commit/1ef48729e6d99e7414bc89bc4ef0d66c172fc566))
* table内图片拖拽消失问题 ([a700a51](https://github.com/wangeditor-team/wangEditor/commit/a700a512fa7149da304f3d7c0ffaad8548a3def9))


### Features

* basic text paste ([f0a5b98](https://github.com/wangeditor-team/wangEditor/commit/f0a5b980c95fa1e2fc59a898c6e0d0723c276c28))
* i18n ([c11b244](https://github.com/wangeditor-team/wangEditor/commit/c11b2440f91b99d40bca18b675c66a22b6e160c9))
* parse html ([2a5eace](https://github.com/wangeditor-team/wangEditor/commit/2a5eace00f33cded50b68e8164748ec2480213fd))
* table module ([a397116](https://github.com/wangeditor-team/wangEditor/commit/a397116de73e088232d9c41828f30f8d56a22dd4))
* table module - header + fullWidth ([9a8a0e0](https://github.com/wangeditor-team/wangEditor/commit/9a8a0e093af944ee7deab674f47c2ec7baae0e63))
* table内按tab光标换到下一个单元格 ([02421ad](https://github.com/wangeditor-team/wangEditor/commit/02421ad7603d20ce8e0d627a0f046c8992ba4934))
* toHtml 机制 ([1c4d872](https://github.com/wangeditor-team/wangEditor/commit/1c4d8729f84aaab6a448f23064b34a20596305e9))
* upload video ([ac8e6f8](https://github.com/wangeditor-team/wangEditor/commit/ac8e6f8b5258e593714676a6f6be359ba525833c))
