{"version": 3, "file": "index.es.js", "sources": ["../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/esm/defineProperty.js", "../src/utils/weak-maps.ts", "../src/create-editor.ts", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../src/utils/string.ts", "../src/interfaces/element.ts", "../src/interfaces/editor.ts", "../src/interfaces/location.ts", "../src/interfaces/node.ts", "../src/interfaces/operation.ts", "../src/interfaces/path.ts", "../src/interfaces/path-ref.ts", "../src/interfaces/point.ts", "../src/interfaces/point-ref.ts", "../src/interfaces/range.ts", "../src/interfaces/range-ref.ts", "../src/utils/deep-equal.ts", "../src/interfaces/text.ts", "../src/transforms/general.ts", "../src/transforms/node.ts", "../src/transforms/selection.ts", "../src/transforms/text.ts", "../src/transforms/index.ts"], "sourcesContent": ["export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}", "import { Editor, Path, PathRef, PointRef, RangeRef } from '..'\n\nexport const DIRTY_PATHS: WeakMap<Editor, Path[]> = new WeakMap()\nexport const DIRTY_PATH_KEYS: WeakMap<Editor, Set<string>> = new WeakMap()\nexport const FLUSHING: WeakMap<Editor, boolean> = new WeakMap()\nexport const NORMALIZING: WeakMap<Editor, boolean> = new WeakMap()\nexport const PATH_REFS: WeakMap<Editor, Set<PathRef>> = new WeakMap()\nexport const POINT_REFS: WeakMap<Editor, Set<PointRef>> = new WeakMap()\nexport const RANGE_REFS: WeakMap<Editor, Set<RangeRef>> = new WeakMap()\n", "import {\n  Descendant,\n  Editor,\n  Element,\n  Node,\n  NodeEntry,\n  Operation,\n  Path,\n  PathRef,\n  PointRef,\n  Range,\n  RangeRef,\n  Text,\n  Transforms,\n} from './'\nimport { DIRTY_PATHS, DIRTY_PATH_KEYS, FLUSHING } from './utils/weak-maps'\n\n/**\n * Create a new Slate `Editor` object.\n */\n\nexport const createEditor = (): Editor => {\n  const editor: Editor = {\n    children: [],\n    operations: [],\n    selection: null,\n    marks: null,\n    isInline: () => false,\n    isVoid: () => false,\n    onChange: () => {},\n\n    apply: (op: Operation) => {\n      for (const ref of Editor.pathRefs(editor)) {\n        PathRef.transform(ref, op)\n      }\n\n      for (const ref of Editor.pointRefs(editor)) {\n        PointRef.transform(ref, op)\n      }\n\n      for (const ref of Editor.rangeRefs(editor)) {\n        RangeRef.transform(ref, op)\n      }\n\n      const oldDirtyPaths = DIRTY_PATHS.get(editor) || []\n      const oldDirtyPathKeys = DIRTY_PATH_KEYS.get(editor) || new Set()\n      let dirtyPaths: Path[]\n      let dirtyPathKeys: Set<string>\n\n      const add = (path: Path | null) => {\n        if (path) {\n          const key = path.join(',')\n\n          if (!dirtyPathKeys.has(key)) {\n            dirtyPathKeys.add(key)\n            dirtyPaths.push(path)\n          }\n        }\n      }\n\n      if (Path.operationCanTransformPath(op)) {\n        dirtyPaths = []\n        dirtyPathKeys = new Set()\n        for (const path of oldDirtyPaths) {\n          const newPath = Path.transform(path, op)\n          add(newPath)\n        }\n      } else {\n        dirtyPaths = oldDirtyPaths\n        dirtyPathKeys = oldDirtyPathKeys\n      }\n\n      const newDirtyPaths = getDirtyPaths(op)\n      for (const path of newDirtyPaths) {\n        add(path)\n      }\n\n      DIRTY_PATHS.set(editor, dirtyPaths)\n      DIRTY_PATH_KEYS.set(editor, dirtyPathKeys)\n      Transforms.transform(editor, op)\n      editor.operations.push(op)\n      Editor.normalize(editor)\n\n      // Clear any formats applied to the cursor if the selection changes.\n      if (op.type === 'set_selection') {\n        editor.marks = null\n      }\n\n      if (!FLUSHING.get(editor)) {\n        FLUSHING.set(editor, true)\n\n        Promise.resolve().then(() => {\n          FLUSHING.set(editor, false)\n          editor.onChange()\n          editor.operations = []\n        })\n      }\n    },\n\n    addMark: (key: string, value: any) => {\n      const { selection } = editor\n\n      if (selection) {\n        if (Range.isExpanded(selection)) {\n          Transforms.setNodes(\n            editor,\n            { [key]: value },\n            { match: Text.isText, split: true }\n          )\n        } else {\n          const marks = {\n            ...(Editor.marks(editor) || {}),\n            [key]: value,\n          }\n\n          editor.marks = marks\n          if (!FLUSHING.get(editor)) {\n            editor.onChange()\n          }\n        }\n      }\n    },\n\n    deleteBackward: (unit: 'character' | 'word' | 'line' | 'block') => {\n      const { selection } = editor\n\n      if (selection && Range.isCollapsed(selection)) {\n        Transforms.delete(editor, { unit, reverse: true })\n      }\n    },\n\n    deleteForward: (unit: 'character' | 'word' | 'line' | 'block') => {\n      const { selection } = editor\n\n      if (selection && Range.isCollapsed(selection)) {\n        Transforms.delete(editor, { unit })\n      }\n    },\n\n    deleteFragment: (direction?: 'forward' | 'backward') => {\n      const { selection } = editor\n\n      if (selection && Range.isExpanded(selection)) {\n        Transforms.delete(editor, { reverse: direction === 'backward' })\n      }\n    },\n\n    getFragment: () => {\n      const { selection } = editor\n\n      if (selection) {\n        return Node.fragment(editor, selection)\n      }\n      return []\n    },\n\n    insertBreak: () => {\n      Transforms.splitNodes(editor, { always: true })\n    },\n\n    insertFragment: (fragment: Node[]) => {\n      Transforms.insertFragment(editor, fragment)\n    },\n\n    insertNode: (node: Node) => {\n      Transforms.insertNodes(editor, node)\n    },\n\n    insertText: (text: string) => {\n      const { selection, marks } = editor\n\n      if (selection) {\n        if (marks) {\n          const node = { text, ...marks }\n          Transforms.insertNodes(editor, node)\n        } else {\n          Transforms.insertText(editor, text)\n        }\n\n        editor.marks = null\n      }\n    },\n\n    normalizeNode: (entry: NodeEntry) => {\n      const [node, path] = entry\n\n      // There are no core normalizations for text nodes.\n      if (Text.isText(node)) {\n        return\n      }\n\n      // Ensure that block and inline nodes have at least one text child.\n      if (Element.isElement(node) && node.children.length === 0) {\n        const child = { text: '' }\n        Transforms.insertNodes(editor, child, {\n          at: path.concat(0),\n          voids: true,\n        })\n        return\n      }\n\n      // Determine whether the node should have block or inline children.\n      const shouldHaveInlines = Editor.isEditor(node)\n        ? false\n        : Element.isElement(node) &&\n          (editor.isInline(node) ||\n            node.children.length === 0 ||\n            Text.isText(node.children[0]) ||\n            editor.isInline(node.children[0]))\n\n      // Since we'll be applying operations while iterating, keep track of an\n      // index that accounts for any added/removed nodes.\n      let n = 0\n\n      for (let i = 0; i < node.children.length; i++, n++) {\n        const currentNode = Node.get(editor, path)\n        if (Text.isText(currentNode)) continue\n        const child = node.children[i] as Descendant\n        const prev = currentNode.children[n - 1] as Descendant\n        const isLast = i === node.children.length - 1\n        const isInlineOrText =\n          Text.isText(child) ||\n          (Element.isElement(child) && editor.isInline(child))\n\n        // Only allow block nodes in the top-level children and parent blocks\n        // that only contain block nodes. Similarly, only allow inline nodes in\n        // other inline nodes, or parent blocks that only contain inlines and\n        // text.\n        if (isInlineOrText !== shouldHaveInlines) {\n          Transforms.removeNodes(editor, { at: path.concat(n), voids: true })\n          n--\n        } else if (Element.isElement(child)) {\n          // Ensure that inline nodes are surrounded by text nodes.\n          if (editor.isInline(child)) {\n            if (prev == null || !Text.isText(prev)) {\n              const newChild = { text: '' }\n              Transforms.insertNodes(editor, newChild, {\n                at: path.concat(n),\n                voids: true,\n              })\n              n++\n            } else if (isLast) {\n              const newChild = { text: '' }\n              Transforms.insertNodes(editor, newChild, {\n                at: path.concat(n + 1),\n                voids: true,\n              })\n              n++\n            }\n          }\n        } else {\n          // Merge adjacent text nodes that are empty or match.\n          if (prev != null && Text.isText(prev)) {\n            if (Text.equals(child, prev, { loose: true })) {\n              Transforms.mergeNodes(editor, { at: path.concat(n), voids: true })\n              n--\n            } else if (prev.text === '') {\n              Transforms.removeNodes(editor, {\n                at: path.concat(n - 1),\n                voids: true,\n              })\n              n--\n            } else if (child.text === '') {\n              Transforms.removeNodes(editor, {\n                at: path.concat(n),\n                voids: true,\n              })\n              n--\n            }\n          }\n        }\n      }\n    },\n\n    removeMark: (key: string) => {\n      const { selection } = editor\n\n      if (selection) {\n        if (Range.isExpanded(selection)) {\n          Transforms.unsetNodes(editor, key, {\n            match: Text.isText,\n            split: true,\n          })\n        } else {\n          const marks = { ...(Editor.marks(editor) || {}) }\n          delete marks[key]\n          editor.marks = marks\n          if (!FLUSHING.get(editor)) {\n            editor.onChange()\n          }\n        }\n      }\n    },\n  }\n\n  return editor\n}\n\n/**\n * Get the \"dirty\" paths generated from an operation.\n */\n\nconst getDirtyPaths = (op: Operation): Path[] => {\n  switch (op.type) {\n    case 'insert_text':\n    case 'remove_text':\n    case 'set_node': {\n      const { path } = op\n      return Path.levels(path)\n    }\n\n    case 'insert_node': {\n      const { node, path } = op\n      const levels = Path.levels(path)\n      const descendants = Text.isText(node)\n        ? []\n        : Array.from(Node.nodes(node), ([, p]) => path.concat(p))\n\n      return [...levels, ...descendants]\n    }\n\n    case 'merge_node': {\n      const { path } = op\n      const ancestors = Path.ancestors(path)\n      const previousPath = Path.previous(path)\n      return [...ancestors, previousPath]\n    }\n\n    case 'move_node': {\n      const { path, newPath } = op\n\n      if (Path.equals(path, newPath)) {\n        return []\n      }\n\n      const oldAncestors: Path[] = []\n      const newAncestors: Path[] = []\n\n      for (const ancestor of Path.ancestors(path)) {\n        const p = Path.transform(ancestor, op)\n        oldAncestors.push(p!)\n      }\n\n      for (const ancestor of Path.ancestors(newPath)) {\n        const p = Path.transform(ancestor, op)\n        newAncestors.push(p!)\n      }\n\n      const newParent = newAncestors[newAncestors.length - 1]\n      const newIndex = newPath[newPath.length - 1]\n      const resultPath = newParent.concat(newIndex)\n\n      return [...oldAncestors, ...newAncestors, resultPath]\n    }\n\n    case 'remove_node': {\n      const { path } = op\n      const ancestors = Path.ancestors(path)\n      return [...ancestors]\n    }\n\n    case 'split_node': {\n      const { path } = op\n      const levels = Path.levels(path)\n      const nextPath = Path.next(path)\n      return [...levels, nextPath]\n    }\n\n    default: {\n      return []\n    }\n  }\n}\n", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nexport default function _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}", "// Character (grapheme cluster) boundaries are determined according to\n// the default grapheme cluster boundary specification, extended grapheme clusters variant[1].\n//\n// References:\n//\n// [1] https://www.unicode.org/reports/tr29/#Default_Grapheme_Cluster_Table\n// [2] https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakProperty.txt\n// [3] https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakTest.html\n// [4] https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakTest.txt\n\n/**\n * Get the distance to the end of the first character in a string of text.\n */\n\nexport const getCharacterDistance = (str: string, isRTL = false): number => {\n  const isLTR = !isRTL\n  const codepoints = isRTL ? codepointsIteratorRTL(str) : str\n\n  let left: CodepointType = CodepointType.None\n  let right: CodepointType = CodepointType.None\n  let distance = 0\n  // Evaluation of these conditions are deferred.\n  let gb11: boolean | null = null // Is GB11 applicable?\n  let gb12Or13: boolean | null = null // Is GB12 or GB13 applicable?\n\n  for (const char of codepoints) {\n    const code = char.codePointAt(0)\n    if (!code) break\n\n    const type = getCodepointType(char, code)\n    ;[left, right] = isLTR ? [right, type] : [type, left]\n\n    if (\n      intersects(left, CodepointType.ZWJ) &&\n      intersects(right, CodepointType.ExtPict)\n    ) {\n      if (isLTR) {\n        gb11 = endsWithEmojiZWJ(str.substring(0, distance))\n      } else {\n        gb11 = endsWithEmojiZWJ(str.substring(0, str.length - distance))\n      }\n      if (!gb11) break\n    }\n\n    if (\n      intersects(left, CodepointType.RI) &&\n      intersects(right, CodepointType.RI)\n    ) {\n      if (gb12Or13 !== null) {\n        gb12Or13 = !gb12Or13\n      } else {\n        if (isLTR) {\n          gb12Or13 = true\n        } else {\n          gb12Or13 = endsWithOddNumberOfRIs(\n            str.substring(0, str.length - distance)\n          )\n        }\n      }\n      if (!gb12Or13) break\n    }\n\n    if (\n      left !== CodepointType.None &&\n      right !== CodepointType.None &&\n      isBoundaryPair(left, right)\n    ) {\n      break\n    }\n\n    distance += char.length\n  }\n\n  return distance || 1\n}\n\nconst SPACE = /\\s/\nconst PUNCTUATION = /[\\u0021-\\u0023\\u0025-\\u002A\\u002C-\\u002F\\u003A\\u003B\\u003F\\u0040\\u005B-\\u005D\\u005F\\u007B\\u007D\\u00A1\\u00A7\\u00AB\\u00B6\\u00B7\\u00BB\\u00BF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u0AF0\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E3B\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\nconst CHAMELEON = /['\\u2018\\u2019]/\n\n/**\n * Get the distance to the end of the first word in a string of text.\n */\n\nexport const getWordDistance = (text: string, isRTL = false): number => {\n  let dist = 0\n  let started = false\n\n  while (text.length > 0) {\n    const charDist = getCharacterDistance(text, isRTL)\n    const [char, remaining] = splitByCharacterDistance(text, charDist, isRTL)\n\n    if (isWordCharacter(char, remaining, isRTL)) {\n      started = true\n      dist += charDist\n    } else if (!started) {\n      dist += charDist\n    } else {\n      break\n    }\n\n    text = remaining\n  }\n\n  return dist\n}\n\n/**\n * Split a string in two parts at a given distance starting from the end when\n * `isRTL` is set to `true`.\n */\n\nexport const splitByCharacterDistance = (\n  str: string,\n  dist: number,\n  isRTL?: boolean\n): [string, string] => {\n  if (isRTL) {\n    const at = str.length - dist\n    return [str.slice(at, str.length), str.slice(0, at)]\n  }\n\n  return [str.slice(0, dist), str.slice(dist)]\n}\n\n/**\n * Check if a character is a word character. The `remaining` argument is used\n * because sometimes you must read subsequent characters to truly determine it.\n */\n\nconst isWordCharacter = (\n  char: string,\n  remaining: string,\n  isRTL = false\n): boolean => {\n  if (SPACE.test(char)) {\n    return false\n  }\n\n  // Chameleons count as word characters as long as they're in a word, so\n  // recurse to see if the next one is a word character or not.\n  if (CHAMELEON.test(char)) {\n    const charDist = getCharacterDistance(remaining, isRTL)\n    const [nextChar, nextRemaining] = splitByCharacterDistance(\n      remaining,\n      charDist,\n      isRTL\n    )\n\n    if (isWordCharacter(nextChar, nextRemaining, isRTL)) {\n      return true\n    }\n  }\n\n  if (PUNCTUATION.test(char)) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * Iterate on codepoints from right to left.\n */\n\nexport const codepointsIteratorRTL = function*(str: string) {\n  const end = str.length - 1\n\n  for (let i = 0; i < str.length; i++) {\n    const char1 = str.charAt(end - i)\n\n    if (isLowSurrogate(char1.charCodeAt(0))) {\n      const char2 = str.charAt(end - i - 1)\n      if (isHighSurrogate(char2.charCodeAt(0))) {\n        yield char2 + char1\n\n        i++\n        continue\n      }\n    }\n\n    yield char1\n  }\n}\n\n/**\n * Is `charCode` a high surrogate.\n *\n * https://en.wikipedia.org/wiki/Universal_Character_Set_characters#Surrogates\n */\n\nconst isHighSurrogate = (charCode: number) => {\n  return charCode >= 0xd800 && charCode <= 0xdbff\n}\n\n/**\n * Is `charCode` a low surrogate.\n *\n * https://en.wikipedia.org/wiki/Universal_Character_Set_characters#Surrogates\n */\n\nconst isLowSurrogate = (charCode: number) => {\n  return charCode >= 0xdc00 && charCode <= 0xdfff\n}\n\nenum CodepointType {\n  None = 0,\n  Extend = 1 << 0,\n  ZWJ = 1 << 1,\n  RI = 1 << 2,\n  Prepend = 1 << 3,\n  SpacingMark = 1 << 4,\n  L = 1 << 5,\n  V = 1 << 6,\n  T = 1 << 7,\n  LV = 1 << 8,\n  LVT = 1 << 9,\n  ExtPict = 1 << 10,\n  Any = 1 << 11,\n}\n\nconst reExtend = /^[\\p{Gr_Ext}\\p{EMod}]$/u\nconst rePrepend = /^[\\u0600-\\u0605\\u06DD\\u070F\\u0890-\\u0891\\u08E2\\u0D4E\\u{110BD}\\u{110CD}\\u{111C2}-\\u{111C3}\\u{1193F}\\u{11941}\\u{11A3A}\\u{11A84}-\\u{11A89}\\u{11D46}]$/u\nconst reSpacingMark = /^[\\u0903\\u093B\\u093E-\\u0940\\u0949-\\u094C\\u094E-\\u094F\\u0982-\\u0983\\u09BF-\\u09C0\\u09C7-\\u09C8\\u09CB-\\u09CC\\u0A03\\u0A3E-\\u0A40\\u0A83\\u0ABE-\\u0AC0\\u0AC9\\u0ACB-\\u0ACC\\u0B02-\\u0B03\\u0B40\\u0B47-\\u0B48\\u0B4B-\\u0B4C\\u0BBF\\u0BC1-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCC\\u0C01-\\u0C03\\u0C41-\\u0C44\\u0C82-\\u0C83\\u0CBE\\u0CC0-\\u0CC1\\u0CC3-\\u0CC4\\u0CC7-\\u0CC8\\u0CCA-\\u0CCB\\u0D02-\\u0D03\\u0D3F-\\u0D40\\u0D46-\\u0D48\\u0D4A-\\u0D4C\\u0D82-\\u0D83\\u0DD0-\\u0DD1\\u0DD8-\\u0DDE\\u0DF2-\\u0DF3\\u0E33\\u0EB3\\u0F3E-\\u0F3F\\u0F7F\\u1031\\u103B-\\u103C\\u1056-\\u1057\\u1084\\u1715\\u1734\\u17B6\\u17BE-\\u17C5\\u17C7-\\u17C8\\u1923-\\u1926\\u1929-\\u192B\\u1930-\\u1931\\u1933-\\u1938\\u1A19-\\u1A1A\\u1A55\\u1A57\\u1A6D-\\u1A72\\u1B04\\u1B3B\\u1B3D-\\u1B41\\u1B43-\\u1B44\\u1B82\\u1BA1\\u1BA6-\\u1BA7\\u1BAA\\u1BE7\\u1BEA-\\u1BEC\\u1BEE\\u1BF2-\\u1BF3\\u1C24-\\u1C2B\\u1C34-\\u1C35\\u1CE1\\u1CF7\\uA823-\\uA824\\uA827\\uA880-\\uA881\\uA8B4-\\uA8C3\\uA952-\\uA953\\uA983\\uA9B4-\\uA9B5\\uA9BA-\\uA9BB\\uA9BE-\\uA9C0\\uAA2F-\\uAA30\\uAA33-\\uAA34\\uAA4D\\uAAEB\\uAAEE-\\uAAEF\\uAAF5\\uABE3-\\uABE4\\uABE6-\\uABE7\\uABE9-\\uABEA\\uABEC\\u{11000}\\u{11002}\\u{11082}\\u{110B0}-\\u{110B2}\\u{110B7}-\\u{110B8}\\u{1112C}\\u{11145}-\\u{11146}\\u{11182}\\u{111B3}-\\u{111B5}\\u{111BF}-\\u{111C0}\\u{111CE}\\u{1122C}-\\u{1122E}\\u{11232}-\\u{11233}\\u{11235}\\u{112E0}-\\u{112E2}\\u{11302}-\\u{11303}\\u{1133F}\\u{11341}-\\u{11344}\\u{11347}-\\u{11348}\\u{1134B}-\\u{1134D}\\u{11362}-\\u{11363}\\u{11435}-\\u{11437}\\u{11440}-\\u{11441}\\u{11445}\\u{114B1}-\\u{114B2}\\u{114B9}\\u{114BB}-\\u{114BC}\\u{114BE}\\u{114C1}\\u{115B0}-\\u{115B1}\\u{115B8}-\\u{115BB}\\u{115BE}\\u{11630}-\\u{11632}\\u{1163B}-\\u{1163C}\\u{1163E}\\u{116AC}\\u{116AE}-\\u{116AF}\\u{116B6}\\u{11726}\\u{1182C}-\\u{1182E}\\u{11838}\\u{11931}-\\u{11935}\\u{11937}-\\u{11938}\\u{1193D}\\u{11940}\\u{11942}\\u{119D1}-\\u{119D3}\\u{119DC}-\\u{119DF}\\u{119E4}\\u{11A39}\\u{11A57}-\\u{11A58}\\u{11A97}\\u{11C2F}\\u{11C3E}\\u{11CA9}\\u{11CB1}\\u{11CB4}\\u{11D8A}-\\u{11D8E}\\u{11D93}-\\u{11D94}\\u{11D96}\\u{11EF5}-\\u{11EF6}\\u{16F51}-\\u{16F87}\\u{16FF0}-\\u{16FF1}\\u{1D166}\\u{1D16D}]$/u\nconst reL = /^[\\u1100-\\u115F\\uA960-\\uA97C]$/u\nconst reV = /^[\\u1160-\\u11A7\\uD7B0-\\uD7C6]$/u\nconst reT = /^[\\u11A8-\\u11FF\\uD7CB-\\uD7FB]$/u\nconst reLV = /^[\\uAC00\\uAC1C\\uAC38\\uAC54\\uAC70\\uAC8C\\uACA8\\uACC4\\uACE0\\uACFC\\uAD18\\uAD34\\uAD50\\uAD6C\\uAD88\\uADA4\\uADC0\\uADDC\\uADF8\\uAE14\\uAE30\\uAE4C\\uAE68\\uAE84\\uAEA0\\uAEBC\\uAED8\\uAEF4\\uAF10\\uAF2C\\uAF48\\uAF64\\uAF80\\uAF9C\\uAFB8\\uAFD4\\uAFF0\\uB00C\\uB028\\uB044\\uB060\\uB07C\\uB098\\uB0B4\\uB0D0\\uB0EC\\uB108\\uB124\\uB140\\uB15C\\uB178\\uB194\\uB1B0\\uB1CC\\uB1E8\\uB204\\uB220\\uB23C\\uB258\\uB274\\uB290\\uB2AC\\uB2C8\\uB2E4\\uB300\\uB31C\\uB338\\uB354\\uB370\\uB38C\\uB3A8\\uB3C4\\uB3E0\\uB3FC\\uB418\\uB434\\uB450\\uB46C\\uB488\\uB4A4\\uB4C0\\uB4DC\\uB4F8\\uB514\\uB530\\uB54C\\uB568\\uB584\\uB5A0\\uB5BC\\uB5D8\\uB5F4\\uB610\\uB62C\\uB648\\uB664\\uB680\\uB69C\\uB6B8\\uB6D4\\uB6F0\\uB70C\\uB728\\uB744\\uB760\\uB77C\\uB798\\uB7B4\\uB7D0\\uB7EC\\uB808\\uB824\\uB840\\uB85C\\uB878\\uB894\\uB8B0\\uB8CC\\uB8E8\\uB904\\uB920\\uB93C\\uB958\\uB974\\uB990\\uB9AC\\uB9C8\\uB9E4\\uBA00\\uBA1C\\uBA38\\uBA54\\uBA70\\uBA8C\\uBAA8\\uBAC4\\uBAE0\\uBAFC\\uBB18\\uBB34\\uBB50\\uBB6C\\uBB88\\uBBA4\\uBBC0\\uBBDC\\uBBF8\\uBC14\\uBC30\\uBC4C\\uBC68\\uBC84\\uBCA0\\uBCBC\\uBCD8\\uBCF4\\uBD10\\uBD2C\\uBD48\\uBD64\\uBD80\\uBD9C\\uBDB8\\uBDD4\\uBDF0\\uBE0C\\uBE28\\uBE44\\uBE60\\uBE7C\\uBE98\\uBEB4\\uBED0\\uBEEC\\uBF08\\uBF24\\uBF40\\uBF5C\\uBF78\\uBF94\\uBFB0\\uBFCC\\uBFE8\\uC004\\uC020\\uC03C\\uC058\\uC074\\uC090\\uC0AC\\uC0C8\\uC0E4\\uC100\\uC11C\\uC138\\uC154\\uC170\\uC18C\\uC1A8\\uC1C4\\uC1E0\\uC1FC\\uC218\\uC234\\uC250\\uC26C\\uC288\\uC2A4\\uC2C0\\uC2DC\\uC2F8\\uC314\\uC330\\uC34C\\uC368\\uC384\\uC3A0\\uC3BC\\uC3D8\\uC3F4\\uC410\\uC42C\\uC448\\uC464\\uC480\\uC49C\\uC4B8\\uC4D4\\uC4F0\\uC50C\\uC528\\uC544\\uC560\\uC57C\\uC598\\uC5B4\\uC5D0\\uC5EC\\uC608\\uC624\\uC640\\uC65C\\uC678\\uC694\\uC6B0\\uC6CC\\uC6E8\\uC704\\uC720\\uC73C\\uC758\\uC774\\uC790\\uC7AC\\uC7C8\\uC7E4\\uC800\\uC81C\\uC838\\uC854\\uC870\\uC88C\\uC8A8\\uC8C4\\uC8E0\\uC8FC\\uC918\\uC934\\uC950\\uC96C\\uC988\\uC9A4\\uC9C0\\uC9DC\\uC9F8\\uCA14\\uCA30\\uCA4C\\uCA68\\uCA84\\uCAA0\\uCABC\\uCAD8\\uCAF4\\uCB10\\uCB2C\\uCB48\\uCB64\\uCB80\\uCB9C\\uCBB8\\uCBD4\\uCBF0\\uCC0C\\uCC28\\uCC44\\uCC60\\uCC7C\\uCC98\\uCCB4\\uCCD0\\uCCEC\\uCD08\\uCD24\\uCD40\\uCD5C\\uCD78\\uCD94\\uCDB0\\uCDCC\\uCDE8\\uCE04\\uCE20\\uCE3C\\uCE58\\uCE74\\uCE90\\uCEAC\\uCEC8\\uCEE4\\uCF00\\uCF1C\\uCF38\\uCF54\\uCF70\\uCF8C\\uCFA8\\uCFC4\\uCFE0\\uCFFC\\uD018\\uD034\\uD050\\uD06C\\uD088\\uD0A4\\uD0C0\\uD0DC\\uD0F8\\uD114\\uD130\\uD14C\\uD168\\uD184\\uD1A0\\uD1BC\\uD1D8\\uD1F4\\uD210\\uD22C\\uD248\\uD264\\uD280\\uD29C\\uD2B8\\uD2D4\\uD2F0\\uD30C\\uD328\\uD344\\uD360\\uD37C\\uD398\\uD3B4\\uD3D0\\uD3EC\\uD408\\uD424\\uD440\\uD45C\\uD478\\uD494\\uD4B0\\uD4CC\\uD4E8\\uD504\\uD520\\uD53C\\uD558\\uD574\\uD590\\uD5AC\\uD5C8\\uD5E4\\uD600\\uD61C\\uD638\\uD654\\uD670\\uD68C\\uD6A8\\uD6C4\\uD6E0\\uD6FC\\uD718\\uD734\\uD750\\uD76C\\uD788]$/u\nconst reLVT = /^[\\uAC01-\\uAC1B\\uAC1D-\\uAC37\\uAC39-\\uAC53\\uAC55-\\uAC6F\\uAC71-\\uAC8B\\uAC8D-\\uACA7\\uACA9-\\uACC3\\uACC5-\\uACDF\\uACE1-\\uACFB\\uACFD-\\uAD17\\uAD19-\\uAD33\\uAD35-\\uAD4F\\uAD51-\\uAD6B\\uAD6D-\\uAD87\\uAD89-\\uADA3\\uADA5-\\uADBF\\uADC1-\\uADDB\\uADDD-\\uADF7\\uADF9-\\uAE13\\uAE15-\\uAE2F\\uAE31-\\uAE4B\\uAE4D-\\uAE67\\uAE69-\\uAE83\\uAE85-\\uAE9F\\uAEA1-\\uAEBB\\uAEBD-\\uAED7\\uAED9-\\uAEF3\\uAEF5-\\uAF0F\\uAF11-\\uAF2B\\uAF2D-\\uAF47\\uAF49-\\uAF63\\uAF65-\\uAF7F\\uAF81-\\uAF9B\\uAF9D-\\uAFB7\\uAFB9-\\uAFD3\\uAFD5-\\uAFEF\\uAFF1-\\uB00B\\uB00D-\\uB027\\uB029-\\uB043\\uB045-\\uB05F\\uB061-\\uB07B\\uB07D-\\uB097\\uB099-\\uB0B3\\uB0B5-\\uB0CF\\uB0D1-\\uB0EB\\uB0ED-\\uB107\\uB109-\\uB123\\uB125-\\uB13F\\uB141-\\uB15B\\uB15D-\\uB177\\uB179-\\uB193\\uB195-\\uB1AF\\uB1B1-\\uB1CB\\uB1CD-\\uB1E7\\uB1E9-\\uB203\\uB205-\\uB21F\\uB221-\\uB23B\\uB23D-\\uB257\\uB259-\\uB273\\uB275-\\uB28F\\uB291-\\uB2AB\\uB2AD-\\uB2C7\\uB2C9-\\uB2E3\\uB2E5-\\uB2FF\\uB301-\\uB31B\\uB31D-\\uB337\\uB339-\\uB353\\uB355-\\uB36F\\uB371-\\uB38B\\uB38D-\\uB3A7\\uB3A9-\\uB3C3\\uB3C5-\\uB3DF\\uB3E1-\\uB3FB\\uB3FD-\\uB417\\uB419-\\uB433\\uB435-\\uB44F\\uB451-\\uB46B\\uB46D-\\uB487\\uB489-\\uB4A3\\uB4A5-\\uB4BF\\uB4C1-\\uB4DB\\uB4DD-\\uB4F7\\uB4F9-\\uB513\\uB515-\\uB52F\\uB531-\\uB54B\\uB54D-\\uB567\\uB569-\\uB583\\uB585-\\uB59F\\uB5A1-\\uB5BB\\uB5BD-\\uB5D7\\uB5D9-\\uB5F3\\uB5F5-\\uB60F\\uB611-\\uB62B\\uB62D-\\uB647\\uB649-\\uB663\\uB665-\\uB67F\\uB681-\\uB69B\\uB69D-\\uB6B7\\uB6B9-\\uB6D3\\uB6D5-\\uB6EF\\uB6F1-\\uB70B\\uB70D-\\uB727\\uB729-\\uB743\\uB745-\\uB75F\\uB761-\\uB77B\\uB77D-\\uB797\\uB799-\\uB7B3\\uB7B5-\\uB7CF\\uB7D1-\\uB7EB\\uB7ED-\\uB807\\uB809-\\uB823\\uB825-\\uB83F\\uB841-\\uB85B\\uB85D-\\uB877\\uB879-\\uB893\\uB895-\\uB8AF\\uB8B1-\\uB8CB\\uB8CD-\\uB8E7\\uB8E9-\\uB903\\uB905-\\uB91F\\uB921-\\uB93B\\uB93D-\\uB957\\uB959-\\uB973\\uB975-\\uB98F\\uB991-\\uB9AB\\uB9AD-\\uB9C7\\uB9C9-\\uB9E3\\uB9E5-\\uB9FF\\uBA01-\\uBA1B\\uBA1D-\\uBA37\\uBA39-\\uBA53\\uBA55-\\uBA6F\\uBA71-\\uBA8B\\uBA8D-\\uBAA7\\uBAA9-\\uBAC3\\uBAC5-\\uBADF\\uBAE1-\\uBAFB\\uBAFD-\\uBB17\\uBB19-\\uBB33\\uBB35-\\uBB4F\\uBB51-\\uBB6B\\uBB6D-\\uBB87\\uBB89-\\uBBA3\\uBBA5-\\uBBBF\\uBBC1-\\uBBDB\\uBBDD-\\uBBF7\\uBBF9-\\uBC13\\uBC15-\\uBC2F\\uBC31-\\uBC4B\\uBC4D-\\uBC67\\uBC69-\\uBC83\\uBC85-\\uBC9F\\uBCA1-\\uBCBB\\uBCBD-\\uBCD7\\uBCD9-\\uBCF3\\uBCF5-\\uBD0F\\uBD11-\\uBD2B\\uBD2D-\\uBD47\\uBD49-\\uBD63\\uBD65-\\uBD7F\\uBD81-\\uBD9B\\uBD9D-\\uBDB7\\uBDB9-\\uBDD3\\uBDD5-\\uBDEF\\uBDF1-\\uBE0B\\uBE0D-\\uBE27\\uBE29-\\uBE43\\uBE45-\\uBE5F\\uBE61-\\uBE7B\\uBE7D-\\uBE97\\uBE99-\\uBEB3\\uBEB5-\\uBECF\\uBED1-\\uBEEB\\uBEED-\\uBF07\\uBF09-\\uBF23\\uBF25-\\uBF3F\\uBF41-\\uBF5B\\uBF5D-\\uBF77\\uBF79-\\uBF93\\uBF95-\\uBFAF\\uBFB1-\\uBFCB\\uBFCD-\\uBFE7\\uBFE9-\\uC003\\uC005-\\uC01F\\uC021-\\uC03B\\uC03D-\\uC057\\uC059-\\uC073\\uC075-\\uC08F\\uC091-\\uC0AB\\uC0AD-\\uC0C7\\uC0C9-\\uC0E3\\uC0E5-\\uC0FF\\uC101-\\uC11B\\uC11D-\\uC137\\uC139-\\uC153\\uC155-\\uC16F\\uC171-\\uC18B\\uC18D-\\uC1A7\\uC1A9-\\uC1C3\\uC1C5-\\uC1DF\\uC1E1-\\uC1FB\\uC1FD-\\uC217\\uC219-\\uC233\\uC235-\\uC24F\\uC251-\\uC26B\\uC26D-\\uC287\\uC289-\\uC2A3\\uC2A5-\\uC2BF\\uC2C1-\\uC2DB\\uC2DD-\\uC2F7\\uC2F9-\\uC313\\uC315-\\uC32F\\uC331-\\uC34B\\uC34D-\\uC367\\uC369-\\uC383\\uC385-\\uC39F\\uC3A1-\\uC3BB\\uC3BD-\\uC3D7\\uC3D9-\\uC3F3\\uC3F5-\\uC40F\\uC411-\\uC42B\\uC42D-\\uC447\\uC449-\\uC463\\uC465-\\uC47F\\uC481-\\uC49B\\uC49D-\\uC4B7\\uC4B9-\\uC4D3\\uC4D5-\\uC4EF\\uC4F1-\\uC50B\\uC50D-\\uC527\\uC529-\\uC543\\uC545-\\uC55F\\uC561-\\uC57B\\uC57D-\\uC597\\uC599-\\uC5B3\\uC5B5-\\uC5CF\\uC5D1-\\uC5EB\\uC5ED-\\uC607\\uC609-\\uC623\\uC625-\\uC63F\\uC641-\\uC65B\\uC65D-\\uC677\\uC679-\\uC693\\uC695-\\uC6AF\\uC6B1-\\uC6CB\\uC6CD-\\uC6E7\\uC6E9-\\uC703\\uC705-\\uC71F\\uC721-\\uC73B\\uC73D-\\uC757\\uC759-\\uC773\\uC775-\\uC78F\\uC791-\\uC7AB\\uC7AD-\\uC7C7\\uC7C9-\\uC7E3\\uC7E5-\\uC7FF\\uC801-\\uC81B\\uC81D-\\uC837\\uC839-\\uC853\\uC855-\\uC86F\\uC871-\\uC88B\\uC88D-\\uC8A7\\uC8A9-\\uC8C3\\uC8C5-\\uC8DF\\uC8E1-\\uC8FB\\uC8FD-\\uC917\\uC919-\\uC933\\uC935-\\uC94F\\uC951-\\uC96B\\uC96D-\\uC987\\uC989-\\uC9A3\\uC9A5-\\uC9BF\\uC9C1-\\uC9DB\\uC9DD-\\uC9F7\\uC9F9-\\uCA13\\uCA15-\\uCA2F\\uCA31-\\uCA4B\\uCA4D-\\uCA67\\uCA69-\\uCA83\\uCA85-\\uCA9F\\uCAA1-\\uCABB\\uCABD-\\uCAD7\\uCAD9-\\uCAF3\\uCAF5-\\uCB0F\\uCB11-\\uCB2B\\uCB2D-\\uCB47\\uCB49-\\uCB63\\uCB65-\\uCB7F\\uCB81-\\uCB9B\\uCB9D-\\uCBB7\\uCBB9-\\uCBD3\\uCBD5-\\uCBEF\\uCBF1-\\uCC0B\\uCC0D-\\uCC27\\uCC29-\\uCC43\\uCC45-\\uCC5F\\uCC61-\\uCC7B\\uCC7D-\\uCC97\\uCC99-\\uCCB3\\uCCB5-\\uCCCF\\uCCD1-\\uCCEB\\uCCED-\\uCD07\\uCD09-\\uCD23\\uCD25-\\uCD3F\\uCD41-\\uCD5B\\uCD5D-\\uCD77\\uCD79-\\uCD93\\uCD95-\\uCDAF\\uCDB1-\\uCDCB\\uCDCD-\\uCDE7\\uCDE9-\\uCE03\\uCE05-\\uCE1F\\uCE21-\\uCE3B\\uCE3D-\\uCE57\\uCE59-\\uCE73\\uCE75-\\uCE8F\\uCE91-\\uCEAB\\uCEAD-\\uCEC7\\uCEC9-\\uCEE3\\uCEE5-\\uCEFF\\uCF01-\\uCF1B\\uCF1D-\\uCF37\\uCF39-\\uCF53\\uCF55-\\uCF6F\\uCF71-\\uCF8B\\uCF8D-\\uCFA7\\uCFA9-\\uCFC3\\uCFC5-\\uCFDF\\uCFE1-\\uCFFB\\uCFFD-\\uD017\\uD019-\\uD033\\uD035-\\uD04F\\uD051-\\uD06B\\uD06D-\\uD087\\uD089-\\uD0A3\\uD0A5-\\uD0BF\\uD0C1-\\uD0DB\\uD0DD-\\uD0F7\\uD0F9-\\uD113\\uD115-\\uD12F\\uD131-\\uD14B\\uD14D-\\uD167\\uD169-\\uD183\\uD185-\\uD19F\\uD1A1-\\uD1BB\\uD1BD-\\uD1D7\\uD1D9-\\uD1F3\\uD1F5-\\uD20F\\uD211-\\uD22B\\uD22D-\\uD247\\uD249-\\uD263\\uD265-\\uD27F\\uD281-\\uD29B\\uD29D-\\uD2B7\\uD2B9-\\uD2D3\\uD2D5-\\uD2EF\\uD2F1-\\uD30B\\uD30D-\\uD327\\uD329-\\uD343\\uD345-\\uD35F\\uD361-\\uD37B\\uD37D-\\uD397\\uD399-\\uD3B3\\uD3B5-\\uD3CF\\uD3D1-\\uD3EB\\uD3ED-\\uD407\\uD409-\\uD423\\uD425-\\uD43F\\uD441-\\uD45B\\uD45D-\\uD477\\uD479-\\uD493\\uD495-\\uD4AF\\uD4B1-\\uD4CB\\uD4CD-\\uD4E7\\uD4E9-\\uD503\\uD505-\\uD51F\\uD521-\\uD53B\\uD53D-\\uD557\\uD559-\\uD573\\uD575-\\uD58F\\uD591-\\uD5AB\\uD5AD-\\uD5C7\\uD5C9-\\uD5E3\\uD5E5-\\uD5FF\\uD601-\\uD61B\\uD61D-\\uD637\\uD639-\\uD653\\uD655-\\uD66F\\uD671-\\uD68B\\uD68D-\\uD6A7\\uD6A9-\\uD6C3\\uD6C5-\\uD6DF\\uD6E1-\\uD6FB\\uD6FD-\\uD717\\uD719-\\uD733\\uD735-\\uD74F\\uD751-\\uD76B\\uD76D-\\uD787\\uD789-\\uD7A3]$/u\nconst reExtPict = /^\\p{ExtPict}$/u\n\nconst getCodepointType = (char: string, code: number): CodepointType => {\n  let type = CodepointType.Any\n  if (char.search(reExtend) !== -1) {\n    type |= CodepointType.Extend\n  }\n  if (code === 0x200d) {\n    type |= CodepointType.ZWJ\n  }\n  if (code >= 0x1f1e6 && code <= 0x1f1ff) {\n    type |= CodepointType.RI\n  }\n  if (char.search(rePrepend) !== -1) {\n    type |= CodepointType.Prepend\n  }\n  if (char.search(reSpacingMark) !== -1) {\n    type |= CodepointType.SpacingMark\n  }\n  if (char.search(reL) !== -1) {\n    type |= CodepointType.L\n  }\n  if (char.search(reV) !== -1) {\n    type |= CodepointType.V\n  }\n  if (char.search(reT) !== -1) {\n    type |= CodepointType.T\n  }\n  if (char.search(reLV) !== -1) {\n    type |= CodepointType.LV\n  }\n  if (char.search(reLVT) !== -1) {\n    type |= CodepointType.LVT\n  }\n  if (char.search(reExtPict) !== -1) {\n    type |= CodepointType.ExtPict\n  }\n\n  return type\n}\n\nfunction intersects(x: CodepointType, y: CodepointType) {\n  return (x & y) !== 0\n}\n\nconst NonBoundaryPairs: [CodepointType, CodepointType][] = [\n  // GB6\n  [\n    CodepointType.L,\n    CodepointType.L | CodepointType.V | CodepointType.LV | CodepointType.LVT,\n  ],\n  // GB7\n  [CodepointType.LV | CodepointType.V, CodepointType.V | CodepointType.T],\n  // GB8\n  [CodepointType.LVT | CodepointType.T, CodepointType.T],\n  // GB9\n  [CodepointType.Any, CodepointType.Extend | CodepointType.ZWJ],\n  // GB9a\n  [CodepointType.Any, CodepointType.SpacingMark],\n  // GB9b\n  [CodepointType.Prepend, CodepointType.Any],\n  // GB11\n  [CodepointType.ZWJ, CodepointType.ExtPict],\n  // GB12 and GB13\n  [CodepointType.RI, CodepointType.RI],\n]\n\nfunction isBoundaryPair(left: CodepointType, right: CodepointType) {\n  return (\n    NonBoundaryPairs.findIndex(\n      r => intersects(left, r[0]) && intersects(right, r[1])\n    ) === -1\n  )\n}\n\nconst endingEmojiZWJ = /\\p{ExtPict}[\\p{Gr_Ext}\\p{EMod}]*\\u200D$/u\nconst endsWithEmojiZWJ = (str: string): boolean => {\n  return str.search(endingEmojiZWJ) !== -1\n}\n\nconst endingRIs = /\\p{RI}+$/gu\nconst endsWithOddNumberOfRIs = (str: string): boolean => {\n  const match = str.match(endingRIs)\n  if (match === null) {\n    return false\n  } else {\n    // A RI is represented by a surrogate pair.\n    const numRIs = match[0].length / 2\n    return numRIs % 2 === 1\n  }\n}\n", "import { isPlainObject } from 'is-plain-object'\nimport { Editor, Node, Path, Descendant, ExtendedType, Ances<PERSON> } from '..'\n\n/**\n * `Element` objects are a type of node in a Slate document that contain other\n * element nodes or text nodes. They can be either \"blocks\" or \"inlines\"\n * depending on the Slate editor's configuration.\n */\n\nexport interface BaseElement {\n  children: Descendant[]\n}\n\nexport type Element = ExtendedType<'Element', BaseElement>\n\nexport interface ElementInterface {\n  isAncestor: (value: any) => value is Ancestor\n  isElement: (value: any) => value is Element\n  isElementList: (value: any) => value is Element[]\n  isElementProps: (props: any) => props is Partial<Element>\n  isElementType: <T extends Element>(\n    value: any,\n    elementVal: string,\n    elementKey?: string\n  ) => value is T\n  matches: (element: Element, props: Partial<Element>) => boolean\n}\n\n/**\n * Shared the function with isElementType utility\n */\nconst isElement = (value: any): value is Element => {\n  return (\n    isPlainObject(value) &&\n    Node.isNodeList(value.children) &&\n    !Editor.isEditor(value)\n  )\n}\n\nexport const Element: ElementInterface = {\n  /**\n   * Check if a value implements the 'Ancestor' interface.\n   */\n\n  isAncestor(value: any): value is Ancestor {\n    return isPlainObject(value) && Node.isNodeList(value.children)\n  },\n\n  /**\n   * Check if a value implements the `Element` interface.\n   */\n\n  isElement,\n  /**\n   * Check if a value is an array of `Element` objects.\n   */\n\n  isElementList(value: any): value is Element[] {\n    return Array.isArray(value) && value.every(val => Element.isElement(val))\n  },\n\n  /**\n   * Check if a set of props is a partial of Element.\n   */\n\n  isElementProps(props: any): props is Partial<Element> {\n    return (props as Partial<Element>).children !== undefined\n  },\n\n  /**\n   * Check if a value implements the `Element` interface and has elementKey with selected value.\n   * Default it check to `type` key value\n   */\n\n  isElementType: <T extends Element>(\n    value: any,\n    elementVal: string,\n    elementKey: string = 'type'\n  ): value is T => {\n    return isElement(value) && value[elementKey] === elementVal\n  },\n\n  /**\n   * Check if an element matches set of properties.\n   *\n   * Note: this checks custom properties, and it does not ensure that any\n   * children are equivalent.\n   */\n\n  matches(element: Element, props: Partial<Element>): boolean {\n    for (const key in props) {\n      if (key === 'children') {\n        continue\n      }\n\n      if (element[key] !== props[key]) {\n        return false\n      }\n    }\n\n    return true\n  },\n}\n\n/**\n * `ElementEntry` objects refer to an `Element` and the `Path` where it can be\n * found inside a root node.\n */\n\nexport type ElementEntry = [Element, Path]\n", "import { isPlainObject } from 'is-plain-object'\n\nimport {\n  Ances<PERSON>,\n  ExtendedType,\n  Location,\n  Node,\n  NodeEntry,\n  Operation,\n  Path,\n  PathRef,\n  Point,\n  PointRef,\n  Range,\n  RangeRef,\n  Span,\n  Text,\n  Transforms,\n} from '..'\nimport {\n  DIRTY_PATHS,\n  DIRTY_PATH_KEYS,\n  NORMALIZING,\n  PATH_REFS,\n  POINT_REFS,\n  RANGE_REFS,\n} from '../utils/weak-maps'\nimport {\n  getWordDistance,\n  getCharacterDistance,\n  splitByCharacterDistance,\n} from '../utils/string'\nimport { Descendant } from './node'\nimport { Element } from './element'\n\nexport type BaseSelection = Range | null\n\nexport type Selection = ExtendedType<'Selection', BaseSelection>\n\n/**\n * The `Editor` interface stores all the state of a Slate editor. It is extended\n * by plugins that wish to add their own helpers and implement new behaviors.\n */\n\nexport interface BaseEditor {\n  children: Descendant[]\n  selection: Selection\n  operations: Operation[]\n  marks: Omit<Text, 'text'> | null\n\n  // Schema-specific node behaviors.\n  isInline: (element: Element) => boolean\n  isVoid: (element: Element) => boolean\n  normalizeNode: (entry: NodeEntry) => void\n  onChange: () => void\n\n  // Overrideable core actions.\n  addMark: (key: string, value: any) => void\n  apply: (operation: Operation) => void\n  deleteBackward: (unit: 'character' | 'word' | 'line' | 'block') => void\n  deleteForward: (unit: 'character' | 'word' | 'line' | 'block') => void\n  deleteFragment: (direction?: 'forward' | 'backward') => void\n  getFragment: () => Descendant[]\n  insertBreak: () => void\n  insertFragment: (fragment: Node[]) => void\n  insertNode: (node: Node) => void\n  insertText: (text: string) => void\n  removeMark: (key: string) => void\n}\n\nexport type Editor = ExtendedType<'Editor', BaseEditor>\n\nexport interface EditorInterface {\n  above: <T extends Ancestor>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<T> | undefined\n  addMark: (editor: Editor, key: string, value: any) => void\n  after: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    }\n  ) => Point | undefined\n  before: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    }\n  ) => Point | undefined\n  deleteBackward: (\n    editor: Editor,\n    options?: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    }\n  ) => void\n  deleteForward: (\n    editor: Editor,\n    options?: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    }\n  ) => void\n  deleteFragment: (\n    editor: Editor,\n    options?: {\n      direction?: 'forward' | 'backward'\n    }\n  ) => void\n  edges: (editor: Editor, at: Location) => [Point, Point]\n  end: (editor: Editor, at: Location) => Point\n  first: (editor: Editor, at: Location) => NodeEntry\n  fragment: (editor: Editor, at: Location) => Descendant[]\n  hasBlocks: (editor: Editor, element: Element) => boolean\n  hasInlines: (editor: Editor, element: Element) => boolean\n  hasPath: (editor: Editor, path: Path) => boolean\n  hasTexts: (editor: Editor, element: Element) => boolean\n  insertBreak: (editor: Editor) => void\n  insertFragment: (editor: Editor, fragment: Node[]) => void\n  insertNode: (editor: Editor, node: Node) => void\n  insertText: (editor: Editor, text: string) => void\n  isBlock: (editor: Editor, value: any) => value is Element\n  isEditor: (value: any) => value is Editor\n  isEnd: (editor: Editor, point: Point, at: Location) => boolean\n  isEdge: (editor: Editor, point: Point, at: Location) => boolean\n  isEmpty: (editor: Editor, element: Element) => boolean\n  isInline: (editor: Editor, value: any) => value is Element\n  isNormalizing: (editor: Editor) => boolean\n  isStart: (editor: Editor, point: Point, at: Location) => boolean\n  isVoid: (editor: Editor, value: any) => value is Element\n  last: (editor: Editor, at: Location) => NodeEntry\n  leaf: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => NodeEntry<Text>\n  levels: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      reverse?: boolean\n      voids?: boolean\n    }\n  ) => Generator<NodeEntry<T>, void, undefined>\n  marks: (editor: Editor) => Omit<Text, 'text'> | null\n  next: <T extends Descendant>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<T> | undefined\n  node: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => NodeEntry\n  nodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location | Span\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      universal?: boolean\n      reverse?: boolean\n      voids?: boolean\n    }\n  ) => Generator<NodeEntry<T>, void, undefined>\n  normalize: (\n    editor: Editor,\n    options?: {\n      force?: boolean\n    }\n  ) => void\n  parent: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => NodeEntry<Ancestor>\n  path: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      depth?: number\n      edge?: 'start' | 'end'\n    }\n  ) => Path\n  pathRef: (\n    editor: Editor,\n    path: Path,\n    options?: {\n      affinity?: 'backward' | 'forward' | null\n    }\n  ) => PathRef\n  pathRefs: (editor: Editor) => Set<PathRef>\n  point: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      edge?: 'start' | 'end'\n    }\n  ) => Point\n  pointRef: (\n    editor: Editor,\n    point: Point,\n    options?: {\n      affinity?: 'backward' | 'forward' | null\n    }\n  ) => PointRef\n  pointRefs: (editor: Editor) => Set<PointRef>\n  positions: (\n    editor: Editor,\n    options?: {\n      at?: Location\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      voids?: boolean\n    }\n  ) => Generator<Point, void, undefined>\n  previous: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<T> | undefined\n  range: (editor: Editor, at: Location, to?: Location) => Range\n  rangeRef: (\n    editor: Editor,\n    range: Range,\n    options?: {\n      affinity?: 'backward' | 'forward' | 'outward' | 'inward' | null\n    }\n  ) => RangeRef\n  rangeRefs: (editor: Editor) => Set<RangeRef>\n  removeMark: (editor: Editor, key: string) => void\n  setNormalizing: (editor: Editor, isNormalizing: boolean) => void\n  start: (editor: Editor, at: Location) => Point\n  string: (\n    editor: Editor,\n    at: Location,\n    options?: {\n      voids?: boolean\n    }\n  ) => string\n  unhangRange: (\n    editor: Editor,\n    range: Range,\n    options?: {\n      voids?: boolean\n    }\n  ) => Range\n  void: (\n    editor: Editor,\n    options?: {\n      at?: Location\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => NodeEntry<Element> | undefined\n  withoutNormalizing: (editor: Editor, fn: () => void) => void\n}\n\nconst IS_EDITOR_CACHE = new WeakMap<object, boolean>()\n\nexport const Editor: EditorInterface = {\n  /**\n   * Get the ancestor above a location in the document.\n   */\n\n  above<T extends Ancestor>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<T> | undefined {\n    const {\n      voids = false,\n      mode = 'lowest',\n      at = editor.selection,\n      match,\n    } = options\n\n    if (!at) {\n      return\n    }\n\n    const path = Editor.path(editor, at)\n    const reverse = mode === 'lowest'\n\n    for (const [n, p] of Editor.levels(editor, {\n      at: path,\n      voids,\n      match,\n      reverse,\n    })) {\n      if (!Text.isText(n) && !Path.equals(path, p)) {\n        return [n, p]\n      }\n    }\n  },\n\n  /**\n   * Add a custom property to the leaf text nodes in the current selection.\n   *\n   * If the selection is currently collapsed, the marks will be added to the\n   * `editor.marks` property instead, and applied when text is inserted next.\n   */\n\n  addMark(editor: Editor, key: string, value: any): void {\n    editor.addMark(key, value)\n  },\n\n  /**\n   * Get the point after a location.\n   */\n\n  after(\n    editor: Editor,\n    at: Location,\n    options: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    } = {}\n  ): Point | undefined {\n    const anchor = Editor.point(editor, at, { edge: 'end' })\n    const focus = Editor.end(editor, [])\n    const range = { anchor, focus }\n    const { distance = 1 } = options\n    let d = 0\n    let target\n\n    for (const p of Editor.positions(editor, {\n      ...options,\n      at: range,\n    })) {\n      if (d > distance) {\n        break\n      }\n\n      if (d !== 0) {\n        target = p\n      }\n\n      d++\n    }\n\n    return target\n  },\n\n  /**\n   * Get the point before a location.\n   */\n\n  before(\n    editor: Editor,\n    at: Location,\n    options: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      voids?: boolean\n    } = {}\n  ): Point | undefined {\n    const anchor = Editor.start(editor, [])\n    const focus = Editor.point(editor, at, { edge: 'start' })\n    const range = { anchor, focus }\n    const { distance = 1 } = options\n    let d = 0\n    let target\n\n    for (const p of Editor.positions(editor, {\n      ...options,\n      at: range,\n      reverse: true,\n    })) {\n      if (d > distance) {\n        break\n      }\n\n      if (d !== 0) {\n        target = p\n      }\n\n      d++\n    }\n\n    return target\n  },\n\n  /**\n   * Delete content in the editor backward from the current selection.\n   */\n\n  deleteBackward(\n    editor: Editor,\n    options: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    } = {}\n  ): void {\n    const { unit = 'character' } = options\n    editor.deleteBackward(unit)\n  },\n\n  /**\n   * Delete content in the editor forward from the current selection.\n   */\n\n  deleteForward(\n    editor: Editor,\n    options: {\n      unit?: 'character' | 'word' | 'line' | 'block'\n    } = {}\n  ): void {\n    const { unit = 'character' } = options\n    editor.deleteForward(unit)\n  },\n\n  /**\n   * Delete the content in the current selection.\n   */\n\n  deleteFragment(\n    editor: Editor,\n    options: {\n      direction?: 'forward' | 'backward'\n    } = {}\n  ): void {\n    const { direction = 'forward' } = options\n    editor.deleteFragment(direction)\n  },\n\n  /**\n   * Get the start and end points of a location.\n   */\n\n  edges(editor: Editor, at: Location): [Point, Point] {\n    return [Editor.start(editor, at), Editor.end(editor, at)]\n  },\n\n  /**\n   * Get the end point of a location.\n   */\n\n  end(editor: Editor, at: Location): Point {\n    return Editor.point(editor, at, { edge: 'end' })\n  },\n\n  /**\n   * Get the first node at a location.\n   */\n\n  first(editor: Editor, at: Location): NodeEntry {\n    const path = Editor.path(editor, at, { edge: 'start' })\n    return Editor.node(editor, path)\n  },\n\n  /**\n   * Get the fragment at a location.\n   */\n\n  fragment(editor: Editor, at: Location): Descendant[] {\n    const range = Editor.range(editor, at)\n    const fragment = Node.fragment(editor, range)\n    return fragment\n  },\n  /**\n   * Check if a node has block children.\n   */\n\n  hasBlocks(editor: Editor, element: Element): boolean {\n    return element.children.some(n => Editor.isBlock(editor, n))\n  },\n\n  /**\n   * Check if a node has inline and text children.\n   */\n\n  hasInlines(editor: Editor, element: Element): boolean {\n    return element.children.some(\n      n => Text.isText(n) || Editor.isInline(editor, n)\n    )\n  },\n\n  /**\n   * Check if a node has text children.\n   */\n\n  hasTexts(editor: Editor, element: Element): boolean {\n    return element.children.every(n => Text.isText(n))\n  },\n\n  /**\n   * Insert a block break at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertBreak(editor: Editor): void {\n    editor.insertBreak()\n  },\n\n  /**\n   * Insert a fragment at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertFragment(editor: Editor, fragment: Node[]): void {\n    editor.insertFragment(fragment)\n  },\n\n  /**\n   * Insert a node at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertNode(editor: Editor, node: Node): void {\n    editor.insertNode(node)\n  },\n\n  /**\n   * Insert text at the current selection.\n   *\n   * If the selection is currently expanded, it will be deleted first.\n   */\n\n  insertText(editor: Editor, text: string): void {\n    editor.insertText(text)\n  },\n\n  /**\n   * Check if a value is a block `Element` object.\n   */\n\n  isBlock(editor: Editor, value: any): value is Element {\n    return Element.isElement(value) && !editor.isInline(value)\n  },\n\n  /**\n   * Check if a value is an `Editor` object.\n   */\n\n  isEditor(value: any): value is Editor {\n    if (!isPlainObject(value)) return false\n    const cachedIsEditor = IS_EDITOR_CACHE.get(value)\n    if (cachedIsEditor !== undefined) {\n      return cachedIsEditor\n    }\n    const isEditor =\n      typeof value.addMark === 'function' &&\n      typeof value.apply === 'function' &&\n      typeof value.deleteBackward === 'function' &&\n      typeof value.deleteForward === 'function' &&\n      typeof value.deleteFragment === 'function' &&\n      typeof value.insertBreak === 'function' &&\n      typeof value.insertFragment === 'function' &&\n      typeof value.insertNode === 'function' &&\n      typeof value.insertText === 'function' &&\n      typeof value.isInline === 'function' &&\n      typeof value.isVoid === 'function' &&\n      typeof value.normalizeNode === 'function' &&\n      typeof value.onChange === 'function' &&\n      typeof value.removeMark === 'function' &&\n      (value.marks === null || isPlainObject(value.marks)) &&\n      (value.selection === null || Range.isRange(value.selection)) &&\n      Node.isNodeList(value.children) &&\n      Operation.isOperationList(value.operations)\n    IS_EDITOR_CACHE.set(value, isEditor)\n    return isEditor\n  },\n\n  /**\n   * Check if a point is the end point of a location.\n   */\n\n  isEnd(editor: Editor, point: Point, at: Location): boolean {\n    const end = Editor.end(editor, at)\n    return Point.equals(point, end)\n  },\n\n  /**\n   * Check if a point is an edge of a location.\n   */\n\n  isEdge(editor: Editor, point: Point, at: Location): boolean {\n    return Editor.isStart(editor, point, at) || Editor.isEnd(editor, point, at)\n  },\n\n  /**\n   * Check if an element is empty, accounting for void nodes.\n   */\n\n  isEmpty(editor: Editor, element: Element): boolean {\n    const { children } = element\n    const [first] = children\n    return (\n      children.length === 0 ||\n      (children.length === 1 &&\n        Text.isText(first) &&\n        first.text === '' &&\n        !editor.isVoid(element))\n    )\n  },\n\n  /**\n   * Check if a value is an inline `Element` object.\n   */\n\n  isInline(editor: Editor, value: any): value is Element {\n    return Element.isElement(value) && editor.isInline(value)\n  },\n\n  /**\n   * Check if the editor is currently normalizing after each operation.\n   */\n\n  isNormalizing(editor: Editor): boolean {\n    const isNormalizing = NORMALIZING.get(editor)\n    return isNormalizing === undefined ? true : isNormalizing\n  },\n\n  /**\n   * Check if a point is the start point of a location.\n   */\n\n  isStart(editor: Editor, point: Point, at: Location): boolean {\n    // PERF: If the offset isn't `0` we know it's not the start.\n    if (point.offset !== 0) {\n      return false\n    }\n\n    const start = Editor.start(editor, at)\n    return Point.equals(point, start)\n  },\n\n  /**\n   * Check if a value is a void `Element` object.\n   */\n\n  isVoid(editor: Editor, value: any): value is Element {\n    return Element.isElement(value) && editor.isVoid(value)\n  },\n\n  /**\n   * Get the last node at a location.\n   */\n\n  last(editor: Editor, at: Location): NodeEntry {\n    const path = Editor.path(editor, at, { edge: 'end' })\n    return Editor.node(editor, path)\n  },\n\n  /**\n   * Get the leaf text node at a location.\n   */\n\n  leaf(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): NodeEntry<Text> {\n    const path = Editor.path(editor, at, options)\n    const node = Node.leaf(editor, path)\n    return [node, path]\n  },\n\n  /**\n   * Iterate through all of the levels at a location.\n   */\n\n  *levels<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      reverse?: boolean\n      voids?: boolean\n    } = {}\n  ): Generator<NodeEntry<T>, void, undefined> {\n    const { at = editor.selection, reverse = false, voids = false } = options\n    let { match } = options\n\n    if (match == null) {\n      match = () => true\n    }\n\n    if (!at) {\n      return\n    }\n\n    const levels: NodeEntry<T>[] = []\n    const path = Editor.path(editor, at)\n\n    for (const [n, p] of Node.levels(editor, path)) {\n      if (!match(n, p)) {\n        continue\n      }\n\n      levels.push([n, p])\n\n      if (!voids && Editor.isVoid(editor, n)) {\n        break\n      }\n    }\n\n    if (reverse) {\n      levels.reverse()\n    }\n\n    yield* levels\n  },\n\n  /**\n   * Get the marks that would be added to text at the current selection.\n   */\n\n  marks(editor: Editor): Omit<Text, 'text'> | null {\n    const { marks, selection } = editor\n\n    if (!selection) {\n      return null\n    }\n\n    if (marks) {\n      return marks\n    }\n\n    if (Range.isExpanded(selection)) {\n      const [match] = Editor.nodes(editor, { match: Text.isText })\n\n      if (match) {\n        const [node] = match as NodeEntry<Text>\n        const { text, ...rest } = node\n        return rest\n      } else {\n        return {}\n      }\n    }\n\n    const { anchor } = selection\n    const { path } = anchor\n    let [node] = Editor.leaf(editor, path)\n\n    if (anchor.offset === 0) {\n      const prev = Editor.previous(editor, { at: path, match: Text.isText })\n      const block = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n      })\n\n      if (prev && block) {\n        const [prevNode, prevPath] = prev\n        const [, blockPath] = block\n\n        if (Path.isAncestor(blockPath, prevPath)) {\n          node = prevNode as Text\n        }\n      }\n    }\n\n    const { text, ...rest } = node\n    return rest\n  },\n\n  /**\n   * Get the matching node in the branch of the document after a location.\n   */\n\n  next<T extends Descendant>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<T> | undefined {\n    const { mode = 'lowest', voids = false } = options\n    let { match, at = editor.selection } = options\n\n    if (!at) {\n      return\n    }\n\n    const pointAfterLocation = Editor.after(editor, at, { voids })\n\n    if (!pointAfterLocation) return\n\n    const [, to] = Editor.last(editor, [])\n\n    const span: Span = [pointAfterLocation.path, to]\n\n    if (Path.isPath(at) && at.length === 0) {\n      throw new Error(`Cannot get the next node from the root node!`)\n    }\n\n    if (match == null) {\n      if (Path.isPath(at)) {\n        const [parent] = Editor.parent(editor, at)\n        match = n => parent.children.includes(n)\n      } else {\n        match = () => true\n      }\n    }\n\n    const [next] = Editor.nodes(editor, { at: span, match, mode, voids })\n    return next\n  },\n\n  /**\n   * Get the node at a location.\n   */\n\n  node(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): NodeEntry {\n    const path = Editor.path(editor, at, options)\n    const node = Node.get(editor, path)\n    return [node, path]\n  },\n\n  /**\n   * Iterate through all of the nodes in the Editor.\n   */\n\n  *nodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location | Span\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      universal?: boolean\n      reverse?: boolean\n      voids?: boolean\n    } = {}\n  ): Generator<NodeEntry<T>, void, undefined> {\n    const {\n      at = editor.selection,\n      mode = 'all',\n      universal = false,\n      reverse = false,\n      voids = false,\n    } = options\n    let { match } = options\n\n    if (!match) {\n      match = () => true\n    }\n\n    if (!at) {\n      return\n    }\n\n    let from\n    let to\n\n    if (Span.isSpan(at)) {\n      from = at[0]\n      to = at[1]\n    } else {\n      const first = Editor.path(editor, at, { edge: 'start' })\n      const last = Editor.path(editor, at, { edge: 'end' })\n      from = reverse ? last : first\n      to = reverse ? first : last\n    }\n\n    const nodeEntries = Node.nodes(editor, {\n      reverse,\n      from,\n      to,\n      pass: ([n]) => (voids ? false : Editor.isVoid(editor, n)),\n    })\n\n    const matches: NodeEntry<T>[] = []\n    let hit: NodeEntry<T> | undefined\n\n    for (const [node, path] of nodeEntries) {\n      const isLower = hit && Path.compare(path, hit[1]) === 0\n\n      // In highest mode any node lower than the last hit is not a match.\n      if (mode === 'highest' && isLower) {\n        continue\n      }\n\n      if (!match(node, path)) {\n        // If we've arrived at a leaf text node that is not lower than the last\n        // hit, then we've found a branch that doesn't include a match, which\n        // means the match is not universal.\n        if (universal && !isLower && Text.isText(node)) {\n          return\n        } else {\n          continue\n        }\n      }\n\n      // If there's a match and it's lower than the last, update the hit.\n      if (mode === 'lowest' && isLower) {\n        hit = [node, path]\n        continue\n      }\n\n      // In lowest mode we emit the last hit, once it's guaranteed lowest.\n      const emit: NodeEntry<T> | undefined =\n        mode === 'lowest' ? hit : [node, path]\n\n      if (emit) {\n        if (universal) {\n          matches.push(emit)\n        } else {\n          yield emit\n        }\n      }\n\n      hit = [node, path]\n    }\n\n    // Since lowest is always emitting one behind, catch up at the end.\n    if (mode === 'lowest' && hit) {\n      if (universal) {\n        matches.push(hit)\n      } else {\n        yield hit\n      }\n    }\n\n    // Universal defers to ensure that the match occurs in every branch, so we\n    // yield all of the matches after iterating.\n    if (universal) {\n      yield* matches\n    }\n  },\n  /**\n   * Normalize any dirty objects in the editor.\n   */\n\n  normalize(\n    editor: Editor,\n    options: {\n      force?: boolean\n    } = {}\n  ): void {\n    const { force = false } = options\n    const getDirtyPaths = (editor: Editor) => {\n      return DIRTY_PATHS.get(editor) || []\n    }\n\n    const getDirtyPathKeys = (editor: Editor) => {\n      return DIRTY_PATH_KEYS.get(editor) || new Set()\n    }\n\n    const popDirtyPath = (editor: Editor): Path => {\n      const path = getDirtyPaths(editor).pop()!\n      const key = path.join(',')\n      getDirtyPathKeys(editor).delete(key)\n      return path\n    }\n\n    if (!Editor.isNormalizing(editor)) {\n      return\n    }\n\n    if (force) {\n      const allPaths = Array.from(Node.nodes(editor), ([, p]) => p)\n      const allPathKeys = new Set(allPaths.map(p => p.join(',')))\n      DIRTY_PATHS.set(editor, allPaths)\n      DIRTY_PATH_KEYS.set(editor, allPathKeys)\n    }\n\n    if (getDirtyPaths(editor).length === 0) {\n      return\n    }\n\n    Editor.withoutNormalizing(editor, () => {\n      /*\n        Fix dirty elements with no children.\n        editor.normalizeNode() does fix this, but some normalization fixes also require it to work.\n        Running an initial pass avoids the catch-22 race condition.\n      */\n      for (const dirtyPath of getDirtyPaths(editor)) {\n        if (Node.has(editor, dirtyPath)) {\n          const entry = Editor.node(editor, dirtyPath)\n          const [node, _] = entry\n\n          /*\n            The default normalizer inserts an empty text node in this scenario, but it can be customised.\n            So there is some risk here.\n\n            As long as the normalizer only inserts child nodes for this case it is safe to do in any order;\n            by definition adding children to an empty node can't cause other paths to change.\n          */\n          if (Element.isElement(node) && node.children.length === 0) {\n            editor.normalizeNode(entry)\n          }\n        }\n      }\n\n      const max = getDirtyPaths(editor).length * 42 // HACK: better way?\n      let m = 0\n\n      while (getDirtyPaths(editor).length !== 0) {\n        if (m > max) {\n          throw new Error(`\n            Could not completely normalize the editor after ${max} iterations! This is usually due to incorrect normalization logic that leaves a node in an invalid state.\n          `)\n        }\n\n        const dirtyPath = popDirtyPath(editor)\n\n        // If the node doesn't exist in the tree, it does not need to be normalized.\n        if (Node.has(editor, dirtyPath)) {\n          const entry = Editor.node(editor, dirtyPath)\n          editor.normalizeNode(entry)\n        }\n        m++\n      }\n    })\n  },\n\n  /**\n   * Get the parent node of a location.\n   */\n\n  parent(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): NodeEntry<Ancestor> {\n    const path = Editor.path(editor, at, options)\n    const parentPath = Path.parent(path)\n    const entry = Editor.node(editor, parentPath)\n    return entry as NodeEntry<Ancestor>\n  },\n\n  /**\n   * Get the path of a location.\n   */\n\n  path(\n    editor: Editor,\n    at: Location,\n    options: {\n      depth?: number\n      edge?: 'start' | 'end'\n    } = {}\n  ): Path {\n    const { depth, edge } = options\n\n    if (Path.isPath(at)) {\n      if (edge === 'start') {\n        const [, firstPath] = Node.first(editor, at)\n        at = firstPath\n      } else if (edge === 'end') {\n        const [, lastPath] = Node.last(editor, at)\n        at = lastPath\n      }\n    }\n\n    if (Range.isRange(at)) {\n      if (edge === 'start') {\n        at = Range.start(at)\n      } else if (edge === 'end') {\n        at = Range.end(at)\n      } else {\n        at = Path.common(at.anchor.path, at.focus.path)\n      }\n    }\n\n    if (Point.isPoint(at)) {\n      at = at.path\n    }\n\n    if (depth != null) {\n      at = at.slice(0, depth)\n    }\n\n    return at\n  },\n\n  hasPath(editor: Editor, path: Path): boolean {\n    return Node.has(editor, path)\n  },\n\n  /**\n   * Create a mutable ref for a `Path` object, which will stay in sync as new\n   * operations are applied to the editor.\n   */\n\n  pathRef(\n    editor: Editor,\n    path: Path,\n    options: {\n      affinity?: 'backward' | 'forward' | null\n    } = {}\n  ): PathRef {\n    const { affinity = 'forward' } = options\n    const ref: PathRef = {\n      current: path,\n      affinity,\n      unref() {\n        const { current } = ref\n        const pathRefs = Editor.pathRefs(editor)\n        pathRefs.delete(ref)\n        ref.current = null\n        return current\n      },\n    }\n\n    const refs = Editor.pathRefs(editor)\n    refs.add(ref)\n    return ref\n  },\n\n  /**\n   * Get the set of currently tracked path refs of the editor.\n   */\n\n  pathRefs(editor: Editor): Set<PathRef> {\n    let refs = PATH_REFS.get(editor)\n\n    if (!refs) {\n      refs = new Set()\n      PATH_REFS.set(editor, refs)\n    }\n\n    return refs\n  },\n\n  /**\n   * Get the start or end point of a location.\n   */\n\n  point(\n    editor: Editor,\n    at: Location,\n    options: {\n      edge?: 'start' | 'end'\n    } = {}\n  ): Point {\n    const { edge = 'start' } = options\n\n    if (Path.isPath(at)) {\n      let path\n\n      if (edge === 'end') {\n        const [, lastPath] = Node.last(editor, at)\n        path = lastPath\n      } else {\n        const [, firstPath] = Node.first(editor, at)\n        path = firstPath\n      }\n\n      const node = Node.get(editor, path)\n\n      if (!Text.isText(node)) {\n        throw new Error(\n          `Cannot get the ${edge} point in the node at path [${at}] because it has no ${edge} text node.`\n        )\n      }\n\n      return { path, offset: edge === 'end' ? node.text.length : 0 }\n    }\n\n    if (Range.isRange(at)) {\n      const [start, end] = Range.edges(at)\n      return edge === 'start' ? start : end\n    }\n\n    return at\n  },\n\n  /**\n   * Create a mutable ref for a `Point` object, which will stay in sync as new\n   * operations are applied to the editor.\n   */\n\n  pointRef(\n    editor: Editor,\n    point: Point,\n    options: {\n      affinity?: 'backward' | 'forward' | null\n    } = {}\n  ): PointRef {\n    const { affinity = 'forward' } = options\n    const ref: PointRef = {\n      current: point,\n      affinity,\n      unref() {\n        const { current } = ref\n        const pointRefs = Editor.pointRefs(editor)\n        pointRefs.delete(ref)\n        ref.current = null\n        return current\n      },\n    }\n\n    const refs = Editor.pointRefs(editor)\n    refs.add(ref)\n    return ref\n  },\n\n  /**\n   * Get the set of currently tracked point refs of the editor.\n   */\n\n  pointRefs(editor: Editor): Set<PointRef> {\n    let refs = POINT_REFS.get(editor)\n\n    if (!refs) {\n      refs = new Set()\n      POINT_REFS.set(editor, refs)\n    }\n\n    return refs\n  },\n\n  /**\n   * Return all the positions in `at` range where a `Point` can be placed.\n   *\n   * By default, moves forward by individual offsets at a time, but\n   * the `unit` option can be used to to move by character, word, line, or block.\n   *\n   * The `reverse` option can be used to change iteration direction.\n   *\n   * Note: By default void nodes are treated as a single point and iteration\n   * will not happen inside their content unless you pass in true for the\n   * `voids` option, then iteration will occur.\n   */\n\n  *positions(\n    editor: Editor,\n    options: {\n      at?: Location\n      unit?: 'offset' | 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      voids?: boolean\n    } = {}\n  ): Generator<Point, void, undefined> {\n    const {\n      at = editor.selection,\n      unit = 'offset',\n      reverse = false,\n      voids = false,\n    } = options\n\n    if (!at) {\n      return\n    }\n\n    /**\n     * Algorithm notes:\n     *\n     * Each step `distance` is dynamic depending on the underlying text\n     * and the `unit` specified.  Each step, e.g., a line or word, may\n     * span multiple text nodes, so we iterate through the text both on\n     * two levels in step-sync:\n     *\n     * `leafText` stores the text on a text leaf level, and is advanced\n     * through using the counters `leafTextOffset` and `leafTextRemaining`.\n     *\n     * `blockText` stores the text on a block level, and is shortened\n     * by `distance` every time it is advanced.\n     *\n     * We only maintain a window of one blockText and one leafText because\n     * a block node always appears before all of its leaf nodes.\n     */\n\n    const range = Editor.range(editor, at)\n    const [start, end] = Range.edges(range)\n    const first = reverse ? end : start\n    let isNewBlock = false\n    let blockText = ''\n    let distance = 0 // Distance for leafText to catch up to blockText.\n    let leafTextRemaining = 0\n    let leafTextOffset = 0\n\n    // Iterate through all nodes in range, grabbing entire textual content\n    // of block nodes in blockText, and text nodes in leafText.\n    // Exploits the fact that nodes are sequenced in such a way that we first\n    // encounter the block node, then all of its text nodes, so when iterating\n    // through the blockText and leafText we just need to remember a window of\n    // one block node and leaf node, respectively.\n    for (const [node, path] of Editor.nodes(editor, { at, reverse, voids })) {\n      /*\n       * ELEMENT NODE - Yield position(s) for voids, collect blockText for blocks\n       */\n      if (Element.isElement(node)) {\n        // Void nodes are a special case, so by default we will always\n        // yield their first point. If the `voids` option is set to true,\n        // then we will iterate over their content.\n        if (!voids && editor.isVoid(node)) {\n          yield Editor.start(editor, path)\n          continue\n        }\n\n        // Inline element nodes are ignored as they don't themselves\n        // contribute to `blockText` or `leafText` - their parent and\n        // children do.\n        if (editor.isInline(node)) continue\n\n        // Block element node - set `blockText` to its text content.\n        if (Editor.hasInlines(editor, node)) {\n          // We always exhaust block nodes before encountering a new one:\n          //   console.assert(blockText === '',\n          //     `blockText='${blockText}' - `+\n          //     `not exhausted before new block node`, path)\n\n          // Ensure range considered is capped to `range`, in the\n          // start/end edge cases where block extends beyond range.\n          // Equivalent to this, but presumably more performant:\n          //   blockRange = Editor.range(editor, ...Editor.edges(editor, path))\n          //   blockRange = Range.intersection(range, blockRange) // intersect\n          //   blockText = Editor.string(editor, blockRange, { voids })\n          const e = Path.isAncestor(path, end.path)\n            ? end\n            : Editor.end(editor, path)\n          const s = Path.isAncestor(path, start.path)\n            ? start\n            : Editor.start(editor, path)\n\n          blockText = Editor.string(editor, { anchor: s, focus: e }, { voids })\n          isNewBlock = true\n        }\n      }\n\n      /*\n       * TEXT LEAF NODE - Iterate through text content, yielding\n       * positions every `distance` offset according to `unit`.\n       */\n      if (Text.isText(node)) {\n        const isFirst = Path.equals(path, first.path)\n\n        // Proof that we always exhaust text nodes before encountering a new one:\n        //   console.assert(leafTextRemaining <= 0,\n        //     `leafTextRemaining=${leafTextRemaining} - `+\n        //     `not exhausted before new leaf text node`, path)\n\n        // Reset `leafText` counters for new text node.\n        if (isFirst) {\n          leafTextRemaining = reverse\n            ? first.offset\n            : node.text.length - first.offset\n          leafTextOffset = first.offset // Works for reverse too.\n        } else {\n          leafTextRemaining = node.text.length\n          leafTextOffset = reverse ? leafTextRemaining : 0\n        }\n\n        // Yield position at the start of node (potentially).\n        if (isFirst || isNewBlock || unit === 'offset') {\n          yield { path, offset: leafTextOffset }\n          isNewBlock = false\n        }\n\n        // Yield positions every (dynamically calculated) `distance` offset.\n        while (true) {\n          // If `leafText` has caught up with `blockText` (distance=0),\n          // and if blockText is exhausted, break to get another block node,\n          // otherwise advance blockText forward by the new `distance`.\n          if (distance === 0) {\n            if (blockText === '') break\n            distance = calcDistance(blockText, unit, reverse)\n            // Split the string at the previously found distance and use the\n            // remaining string for the next iteration.\n            blockText = splitByCharacterDistance(\n              blockText,\n              distance,\n              reverse\n            )[1]\n          }\n\n          // Advance `leafText` by the current `distance`.\n          leafTextOffset = reverse\n            ? leafTextOffset - distance\n            : leafTextOffset + distance\n          leafTextRemaining = leafTextRemaining - distance\n\n          // If `leafText` is exhausted, break to get a new leaf node\n          // and set distance to the overflow amount, so we'll (maybe)\n          // catch up to blockText in the next leaf text node.\n          if (leafTextRemaining < 0) {\n            distance = -leafTextRemaining\n            break\n          }\n\n          // Successfully walked `distance` offsets through `leafText`\n          // to catch up with `blockText`, so we can reset `distance`\n          // and yield this position in this node.\n          distance = 0\n          yield { path, offset: leafTextOffset }\n        }\n      }\n    }\n    // Proof that upon completion, we've exahusted both leaf and block text:\n    //   console.assert(leafTextRemaining <= 0, \"leafText wasn't exhausted\")\n    //   console.assert(blockText === '', \"blockText wasn't exhausted\")\n\n    // Helper:\n    // Return the distance in offsets for a step of size `unit` on given string.\n    function calcDistance(text: string, unit: string, reverse?: boolean) {\n      if (unit === 'character') {\n        return getCharacterDistance(text, reverse)\n      } else if (unit === 'word') {\n        return getWordDistance(text, reverse)\n      } else if (unit === 'line' || unit === 'block') {\n        return text.length\n      }\n      return 1\n    }\n  },\n\n  /**\n   * Get the matching node in the branch of the document before a location.\n   */\n\n  previous<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<T> | undefined {\n    const { mode = 'lowest', voids = false } = options\n    let { match, at = editor.selection } = options\n\n    if (!at) {\n      return\n    }\n\n    const pointBeforeLocation = Editor.before(editor, at, { voids })\n\n    if (!pointBeforeLocation) {\n      return\n    }\n\n    const [, to] = Editor.first(editor, [])\n\n    // The search location is from the start of the document to the path of\n    // the point before the location passed in\n    const span: Span = [pointBeforeLocation.path, to]\n\n    if (Path.isPath(at) && at.length === 0) {\n      throw new Error(`Cannot get the previous node from the root node!`)\n    }\n\n    if (match == null) {\n      if (Path.isPath(at)) {\n        const [parent] = Editor.parent(editor, at)\n        match = n => parent.children.includes(n)\n      } else {\n        match = () => true\n      }\n    }\n\n    const [previous] = Editor.nodes(editor, {\n      reverse: true,\n      at: span,\n      match,\n      mode,\n      voids,\n    })\n\n    return previous\n  },\n\n  /**\n   * Get a range of a location.\n   */\n\n  range(editor: Editor, at: Location, to?: Location): Range {\n    if (Range.isRange(at) && !to) {\n      return at\n    }\n\n    const start = Editor.start(editor, at)\n    const end = Editor.end(editor, to || at)\n    return { anchor: start, focus: end }\n  },\n\n  /**\n   * Create a mutable ref for a `Range` object, which will stay in sync as new\n   * operations are applied to the editor.\n   */\n\n  rangeRef(\n    editor: Editor,\n    range: Range,\n    options: {\n      affinity?: 'backward' | 'forward' | 'outward' | 'inward' | null\n    } = {}\n  ): RangeRef {\n    const { affinity = 'forward' } = options\n    const ref: RangeRef = {\n      current: range,\n      affinity,\n      unref() {\n        const { current } = ref\n        const rangeRefs = Editor.rangeRefs(editor)\n        rangeRefs.delete(ref)\n        ref.current = null\n        return current\n      },\n    }\n\n    const refs = Editor.rangeRefs(editor)\n    refs.add(ref)\n    return ref\n  },\n\n  /**\n   * Get the set of currently tracked range refs of the editor.\n   */\n\n  rangeRefs(editor: Editor): Set<RangeRef> {\n    let refs = RANGE_REFS.get(editor)\n\n    if (!refs) {\n      refs = new Set()\n      RANGE_REFS.set(editor, refs)\n    }\n\n    return refs\n  },\n\n  /**\n   * Remove a custom property from all of the leaf text nodes in the current\n   * selection.\n   *\n   * If the selection is currently collapsed, the removal will be stored on\n   * `editor.marks` and applied to the text inserted next.\n   */\n\n  removeMark(editor: Editor, key: string): void {\n    editor.removeMark(key)\n  },\n\n  /**\n   * Manually set if the editor should currently be normalizing.\n   *\n   * Note: Using this incorrectly can leave the editor in an invalid state.\n   *\n   */\n  setNormalizing(editor: Editor, isNormalizing: boolean): void {\n    NORMALIZING.set(editor, isNormalizing)\n  },\n\n  /**\n   * Get the start point of a location.\n   */\n\n  start(editor: Editor, at: Location): Point {\n    return Editor.point(editor, at, { edge: 'start' })\n  },\n\n  /**\n   * Get the text string content of a location.\n   *\n   * Note: by default the text of void nodes is considered to be an empty\n   * string, regardless of content, unless you pass in true for the voids option\n   */\n\n  string(\n    editor: Editor,\n    at: Location,\n    options: {\n      voids?: boolean\n    } = {}\n  ): string {\n    const { voids = false } = options\n    const range = Editor.range(editor, at)\n    const [start, end] = Range.edges(range)\n    let text = ''\n\n    for (const [node, path] of Editor.nodes(editor, {\n      at: range,\n      match: Text.isText,\n      voids,\n    })) {\n      let t = node.text\n\n      if (Path.equals(path, end.path)) {\n        t = t.slice(0, end.offset)\n      }\n\n      if (Path.equals(path, start.path)) {\n        t = t.slice(start.offset)\n      }\n\n      text += t\n    }\n\n    return text\n  },\n\n  /**\n   * Convert a range into a non-hanging one.\n   */\n\n  unhangRange(\n    editor: Editor,\n    range: Range,\n    options: {\n      voids?: boolean\n    } = {}\n  ): Range {\n    const { voids = false } = options\n    let [start, end] = Range.edges(range)\n\n    // PERF: exit early if we can guarantee that the range isn't hanging.\n    if (start.offset !== 0 || end.offset !== 0 || Range.isCollapsed(range)) {\n      return range\n    }\n\n    const endBlock = Editor.above(editor, {\n      at: end,\n      match: n => Editor.isBlock(editor, n),\n    })\n    const blockPath = endBlock ? endBlock[1] : []\n    const first = Editor.start(editor, start)\n    const before = { anchor: first, focus: end }\n    let skip = true\n\n    for (const [node, path] of Editor.nodes(editor, {\n      at: before,\n      match: Text.isText,\n      reverse: true,\n      voids,\n    })) {\n      if (skip) {\n        skip = false\n        continue\n      }\n\n      if (node.text !== '' || Path.isBefore(path, blockPath)) {\n        end = { path, offset: node.text.length }\n        break\n      }\n    }\n\n    return { anchor: start, focus: end }\n  },\n\n  /**\n   * Match a void node in the current branch of the editor.\n   */\n\n  void(\n    editor: Editor,\n    options: {\n      at?: Location\n      mode?: 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): NodeEntry<Element> | undefined {\n    return Editor.above(editor, {\n      ...options,\n      match: n => Editor.isVoid(editor, n),\n    })\n  },\n\n  /**\n   * Call a function, deferring normalization until after it completes.\n   */\n\n  withoutNormalizing(editor: Editor, fn: () => void): void {\n    const value = Editor.isNormalizing(editor)\n    Editor.setNormalizing(editor, false)\n    try {\n      fn()\n    } finally {\n      Editor.setNormalizing(editor, value)\n    }\n    Editor.normalize(editor)\n  },\n}\n\n/**\n * A helper type for narrowing matched nodes with a predicate.\n */\n\nexport type NodeMatch<T extends Node> =\n  | ((node: Node, path: Path) => node is T)\n  | ((node: Node, path: Path) => boolean)\n", "import { Path, Point, Range } from '..'\n\n/**\n * The `Location` interface is a union of the ways to refer to a specific\n * location in a Slate document: paths, points or ranges.\n *\n * Methods will often accept a `Location` instead of requiring only a `Path`,\n * `Point` or `Range`. This eliminates the need for developers to manage\n * converting between the different interfaces in their own code base.\n */\n\nexport type Location = Path | Point | Range\n\nexport interface LocationInterface {\n  isLocation: (value: any) => value is Location\n}\n\nexport const Location: LocationInterface = {\n  /**\n   * Check if a value implements the `Location` interface.\n   */\n\n  isLocation(value: any): value is Location {\n    return Path.isPath(value) || Point.isPoint(value) || Range.isRange(value)\n  },\n}\n\n/**\n * The `Span` interface is a low-level way to refer to locations in nodes\n * without using `Point` which requires leaf text nodes to be present.\n */\n\nexport type Span = [Path, Path]\n\nexport interface SpanInterface {\n  isSpan: (value: any) => value is Span\n}\n\nexport const Span: SpanInterface = {\n  /**\n   * Check if a value implements the `Span` interface.\n   */\n\n  isSpan(value: any): value is Span {\n    return (\n      Array.isArray(value) && value.length === 2 && value.every(Path.isPath)\n    )\n  },\n}\n", "import { produce } from 'immer'\nimport { Editor, Path, Range, Text } from '..'\nimport { Element, ElementEntry } from './element'\n\n/**\n * The `Node` union type represents all of the different types of nodes that\n * occur in a Slate document tree.\n */\n\nexport type BaseNode = Editor | Element | Text\nexport type Node = Editor | Element | Text\n\nexport interface NodeInterface {\n  ancestor: (root: Node, path: Path) => Ancestor\n  ancestors: (\n    root: Node,\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Generator<NodeEntry<Ancestor>, void, undefined>\n  child: (root: Node, index: number) => Descendant\n  children: (\n    root: Node,\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Generator<NodeEntry<Descendant>, void, undefined>\n  common: (root: Node, path: Path, another: Path) => NodeEntry\n  descendant: (root: Node, path: Path) => Descendant\n  descendants: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    }\n  ) => Generator<NodeEntry<Descendant>, void, undefined>\n  elements: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    }\n  ) => Generator<ElementEntry, void, undefined>\n  extractProps: (node: Node) => NodeProps\n  first: (root: Node, path: Path) => NodeEntry\n  fragment: (root: Node, range: Range) => Descendant[]\n  get: (root: Node, path: Path) => Node\n  has: (root: Node, path: Path) => boolean\n  isNode: (value: any) => value is Node\n  isNodeList: (value: any) => value is Node[]\n  last: (root: Node, path: Path) => NodeEntry\n  leaf: (root: Node, path: Path) => Text\n  levels: (\n    root: Node,\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Generator<NodeEntry, void, undefined>\n  matches: (node: Node, props: Partial<Node>) => boolean\n  nodes: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (entry: NodeEntry) => boolean\n    }\n  ) => Generator<NodeEntry, void, undefined>\n  parent: (root: Node, path: Path) => Ancestor\n  string: (node: Node) => string\n  texts: (\n    root: Node,\n    options?: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    }\n  ) => Generator<NodeEntry<Text>, void, undefined>\n}\n\nconst IS_NODE_LIST_CACHE = new WeakMap<any[], boolean>()\n\nexport const Node: NodeInterface = {\n  /**\n   * Get the node at a specific path, asserting that it's an ancestor node.\n   */\n\n  ancestor(root: Node, path: Path): Ancestor {\n    const node = Node.get(root, path)\n\n    if (Text.isText(node)) {\n      throw new Error(\n        `Cannot get the ancestor node at path [${path}] because it refers to a text node instead: ${node}`\n      )\n    }\n\n    return node\n  },\n\n  /**\n   * Return a generator of all the ancestor nodes above a specific path.\n   *\n   * By default the order is bottom-up, from lowest to highest ancestor in\n   * the tree, but you can pass the `reverse: true` option to go top-down.\n   */\n\n  *ancestors(\n    root: Node,\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Generator<NodeEntry<Ancestor>, void, undefined> {\n    for (const p of Path.ancestors(path, options)) {\n      const n = Node.ancestor(root, p)\n      const entry: NodeEntry<Ancestor> = [n, p]\n      yield entry\n    }\n  },\n\n  /**\n   * Get the child of a node at a specific index.\n   */\n\n  child(root: Node, index: number): Descendant {\n    if (Text.isText(root)) {\n      throw new Error(\n        `Cannot get the child of a text node: ${JSON.stringify(root)}`\n      )\n    }\n\n    const c = root.children[index] as Descendant\n\n    if (c == null) {\n      throw new Error(\n        `Cannot get child at index \\`${index}\\` in node: ${JSON.stringify(\n          root\n        )}`\n      )\n    }\n\n    return c\n  },\n\n  /**\n   * Iterate over the children of a node at a specific path.\n   */\n\n  *children(\n    root: Node,\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Generator<NodeEntry<Descendant>, void, undefined> {\n    const { reverse = false } = options\n    const ancestor = Node.ancestor(root, path)\n    const { children } = ancestor\n    let index = reverse ? children.length - 1 : 0\n\n    while (reverse ? index >= 0 : index < children.length) {\n      const child = Node.child(ancestor, index)\n      const childPath = path.concat(index)\n      yield [child, childPath]\n      index = reverse ? index - 1 : index + 1\n    }\n  },\n\n  /**\n   * Get an entry for the common ancesetor node of two paths.\n   */\n\n  common(root: Node, path: Path, another: Path): NodeEntry {\n    const p = Path.common(path, another)\n    const n = Node.get(root, p)\n    return [n, p]\n  },\n\n  /**\n   * Get the node at a specific path, asserting that it's a descendant node.\n   */\n\n  descendant(root: Node, path: Path): Descendant {\n    const node = Node.get(root, path)\n\n    if (Editor.isEditor(node)) {\n      throw new Error(\n        `Cannot get the descendant node at path [${path}] because it refers to the root editor node instead: ${node}`\n      )\n    }\n\n    return node\n  },\n\n  /**\n   * Return a generator of all the descendant node entries inside a root node.\n   */\n\n  *descendants(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    } = {}\n  ): Generator<NodeEntry<Descendant>, void, undefined> {\n    for (const [node, path] of Node.nodes(root, options)) {\n      if (path.length !== 0) {\n        // NOTE: we have to coerce here because checking the path's length does\n        // guarantee that `node` is not a `Editor`, but TypeScript doesn't know.\n        yield [node, path] as NodeEntry<Descendant>\n      }\n    }\n  },\n\n  /**\n   * Return a generator of all the element nodes inside a root node. Each iteration\n   * will return an `ElementEntry` tuple consisting of `[Element, Path]`. If the\n   * root node is an element it will be included in the iteration as well.\n   */\n\n  *elements(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    } = {}\n  ): Generator<ElementEntry, void, undefined> {\n    for (const [node, path] of Node.nodes(root, options)) {\n      if (Element.isElement(node)) {\n        yield [node, path]\n      }\n    }\n  },\n\n  /**\n   * Extract props from a Node.\n   */\n\n  extractProps(node: Node): NodeProps {\n    if (Element.isAncestor(node)) {\n      const { children, ...properties } = node\n\n      return properties\n    } else {\n      const { text, ...properties } = node\n\n      return properties\n    }\n  },\n\n  /**\n   * Get the first node entry in a root node from a path.\n   */\n\n  first(root: Node, path: Path): NodeEntry {\n    const p = path.slice()\n    let n = Node.get(root, p)\n\n    while (n) {\n      if (Text.isText(n) || n.children.length === 0) {\n        break\n      } else {\n        n = n.children[0]\n        p.push(0)\n      }\n    }\n\n    return [n, p]\n  },\n\n  /**\n   * Get the sliced fragment represented by a range inside a root node.\n   */\n\n  fragment(root: Node, range: Range): Descendant[] {\n    if (Text.isText(root)) {\n      throw new Error(\n        `Cannot get a fragment starting from a root text node: ${JSON.stringify(\n          root\n        )}`\n      )\n    }\n\n    const newRoot = produce({ children: root.children }, r => {\n      const [start, end] = Range.edges(range)\n      const nodeEntries = Node.nodes(r, {\n        reverse: true,\n        pass: ([, path]) => !Range.includes(range, path),\n      })\n\n      for (const [, path] of nodeEntries) {\n        if (!Range.includes(range, path)) {\n          const parent = Node.parent(r, path)\n          const index = path[path.length - 1]\n          parent.children.splice(index, 1)\n        }\n\n        if (Path.equals(path, end.path)) {\n          const leaf = Node.leaf(r, path)\n          leaf.text = leaf.text.slice(0, end.offset)\n        }\n\n        if (Path.equals(path, start.path)) {\n          const leaf = Node.leaf(r, path)\n          leaf.text = leaf.text.slice(start.offset)\n        }\n      }\n\n      if (Editor.isEditor(r)) {\n        r.selection = null\n      }\n    })\n\n    return newRoot.children\n  },\n\n  /**\n   * Get the descendant node referred to by a specific path. If the path is an\n   * empty array, it refers to the root node itself.\n   */\n\n  get(root: Node, path: Path): Node {\n    let node = root\n\n    for (let i = 0; i < path.length; i++) {\n      const p = path[i]\n\n      if (Text.isText(node) || !node.children[p]) {\n        throw new Error(\n          `Cannot find a descendant at path [${path}] in node: ${JSON.stringify(\n            root\n          )}`\n        )\n      }\n\n      node = node.children[p]\n    }\n\n    return node\n  },\n\n  /**\n   * Check if a descendant node exists at a specific path.\n   */\n\n  has(root: Node, path: Path): boolean {\n    let node = root\n\n    for (let i = 0; i < path.length; i++) {\n      const p = path[i]\n\n      if (Text.isText(node) || !node.children[p]) {\n        return false\n      }\n\n      node = node.children[p]\n    }\n\n    return true\n  },\n\n  /**\n   * Check if a value implements the `Node` interface.\n   */\n\n  isNode(value: any): value is Node {\n    return (\n      Text.isText(value) || Element.isElement(value) || Editor.isEditor(value)\n    )\n  },\n\n  /**\n   * Check if a value is a list of `Node` objects.\n   */\n\n  isNodeList(value: any): value is Node[] {\n    if (!Array.isArray(value)) {\n      return false\n    }\n    const cachedResult = IS_NODE_LIST_CACHE.get(value)\n    if (cachedResult !== undefined) {\n      return cachedResult\n    }\n    const isNodeList = value.every(val => Node.isNode(val))\n    IS_NODE_LIST_CACHE.set(value, isNodeList)\n    return isNodeList\n  },\n\n  /**\n   * Get the last node entry in a root node from a path.\n   */\n\n  last(root: Node, path: Path): NodeEntry {\n    const p = path.slice()\n    let n = Node.get(root, p)\n\n    while (n) {\n      if (Text.isText(n) || n.children.length === 0) {\n        break\n      } else {\n        const i = n.children.length - 1\n        n = n.children[i]\n        p.push(i)\n      }\n    }\n\n    return [n, p]\n  },\n\n  /**\n   * Get the node at a specific path, ensuring it's a leaf text node.\n   */\n\n  leaf(root: Node, path: Path): Text {\n    const node = Node.get(root, path)\n\n    if (!Text.isText(node)) {\n      throw new Error(\n        `Cannot get the leaf node at path [${path}] because it refers to a non-leaf node: ${node}`\n      )\n    }\n\n    return node\n  },\n\n  /**\n   * Return a generator of the in a branch of the tree, from a specific path.\n   *\n   * By default the order is top-down, from lowest to highest node in the tree,\n   * but you can pass the `reverse: true` option to go bottom-up.\n   */\n\n  *levels(\n    root: Node,\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Generator<NodeEntry, void, undefined> {\n    for (const p of Path.levels(path, options)) {\n      const n = Node.get(root, p)\n      yield [n, p]\n    }\n  },\n\n  /**\n   * Check if a node matches a set of props.\n   */\n\n  matches(node: Node, props: Partial<Node>): boolean {\n    return (\n      (Element.isElement(node) &&\n        Element.isElementProps(props) &&\n        Element.matches(node, props)) ||\n      (Text.isText(node) &&\n        Text.isTextProps(props) &&\n        Text.matches(node, props))\n    )\n  },\n\n  /**\n   * Return a generator of all the node entries of a root node. Each entry is\n   * returned as a `[Node, Path]` tuple, with the path referring to the node's\n   * position inside the root node.\n   */\n\n  *nodes(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (entry: NodeEntry) => boolean\n    } = {}\n  ): Generator<NodeEntry, void, undefined> {\n    const { pass, reverse = false } = options\n    const { from = [], to } = options\n    const visited = new Set()\n    let p: Path = []\n    let n = root\n\n    while (true) {\n      if (to && (reverse ? Path.isBefore(p, to) : Path.isAfter(p, to))) {\n        break\n      }\n\n      if (!visited.has(n)) {\n        yield [n, p]\n      }\n\n      // If we're allowed to go downward and we haven't descended yet, do.\n      if (\n        !visited.has(n) &&\n        !Text.isText(n) &&\n        n.children.length !== 0 &&\n        (pass == null || pass([n, p]) === false)\n      ) {\n        visited.add(n)\n        let nextIndex = reverse ? n.children.length - 1 : 0\n\n        if (Path.isAncestor(p, from)) {\n          nextIndex = from[p.length]\n        }\n\n        p = p.concat(nextIndex)\n        n = Node.get(root, p)\n        continue\n      }\n\n      // If we're at the root and we can't go down, we're done.\n      if (p.length === 0) {\n        break\n      }\n\n      // If we're going forward...\n      if (!reverse) {\n        const newPath = Path.next(p)\n\n        if (Node.has(root, newPath)) {\n          p = newPath\n          n = Node.get(root, p)\n          continue\n        }\n      }\n\n      // If we're going backward...\n      if (reverse && p[p.length - 1] !== 0) {\n        const newPath = Path.previous(p)\n        p = newPath\n        n = Node.get(root, p)\n        continue\n      }\n\n      // Otherwise we're going upward...\n      p = Path.parent(p)\n      n = Node.get(root, p)\n      visited.add(n)\n    }\n  },\n\n  /**\n   * Get the parent of a node at a specific path.\n   */\n\n  parent(root: Node, path: Path): Ancestor {\n    const parentPath = Path.parent(path)\n    const p = Node.get(root, parentPath)\n\n    if (Text.isText(p)) {\n      throw new Error(\n        `Cannot get the parent of path [${path}] because it does not exist in the root.`\n      )\n    }\n\n    return p\n  },\n\n  /**\n   * Get the concatenated text string of a node's content.\n   *\n   * Note that this will not include spaces or line breaks between block nodes.\n   * It is not a user-facing string, but a string for performing offset-related\n   * computations for a node.\n   */\n\n  string(node: Node): string {\n    if (Text.isText(node)) {\n      return node.text\n    } else {\n      return node.children.map(Node.string).join('')\n    }\n  },\n\n  /**\n   * Return a generator of all leaf text nodes in a root node.\n   */\n\n  *texts(\n    root: Node,\n    options: {\n      from?: Path\n      to?: Path\n      reverse?: boolean\n      pass?: (node: NodeEntry) => boolean\n    } = {}\n  ): Generator<NodeEntry<Text>, void, undefined> {\n    for (const [node, path] of Node.nodes(root, options)) {\n      if (Text.isText(node)) {\n        yield [node, path]\n      }\n    }\n  },\n}\n\n/**\n * The `Descendant` union type represents nodes that are descendants in the\n * tree. It is returned as a convenience in certain cases to narrow a value\n * further than the more generic `Node` union.\n */\n\nexport type Descendant = Element | Text\n\n/**\n * The `Ancestor` union type represents nodes that are ancestors in the tree.\n * It is returned as a convenience in certain cases to narrow a value further\n * than the more generic `Node` union.\n */\n\nexport type Ancestor = Editor | Element\n\n/**\n * `NodeEntry` objects are returned when iterating over the nodes in a Slate\n * document tree. They consist of the node and its `Path` relative to the root\n * node in the document.\n */\n\nexport type NodeEntry<T extends Node = Node> = [T, Path]\n\n/**\n * Convenience type for returning the props of a node.\n */\nexport type NodeProps =\n  | Omit<Editor, 'children'>\n  | Omit<Element, 'children'>\n  | Omit<Text, 'text'>\n", "import { ExtendedType, Node, Path, Range } from '..'\nimport { isPlainObject } from 'is-plain-object'\n\nexport type BaseInsertNodeOperation = {\n  type: 'insert_node'\n  path: Path\n  node: Node\n}\n\nexport type InsertNodeOperation = ExtendedType<\n  'InsertNodeOperation',\n  BaseInsertNodeOperation\n>\n\nexport type BaseInsertTextOperation = {\n  type: 'insert_text'\n  path: Path\n  offset: number\n  text: string\n}\n\nexport type InsertTextOperation = ExtendedType<\n  'InsertTextOperation',\n  BaseInsertTextOperation\n>\n\nexport type BaseMergeNodeOperation = {\n  type: 'merge_node'\n  path: Path\n  position: number\n  properties: Partial<Node>\n}\n\nexport type MergeNodeOperation = ExtendedType<\n  'MergeNodeOperation',\n  BaseMergeNodeOperation\n>\n\nexport type BaseMoveNodeOperation = {\n  type: 'move_node'\n  path: Path\n  newPath: Path\n}\n\nexport type MoveNodeOperation = ExtendedType<\n  'MoveNodeOperation',\n  BaseMoveNodeOperation\n>\n\nexport type BaseRemoveNodeOperation = {\n  type: 'remove_node'\n  path: Path\n  node: Node\n}\n\nexport type RemoveNodeOperation = ExtendedType<\n  'RemoveNodeOperation',\n  BaseRemoveNodeOperation\n>\n\nexport type BaseRemoveTextOperation = {\n  type: 'remove_text'\n  path: Path\n  offset: number\n  text: string\n}\n\nexport type RemoveTextOperation = ExtendedType<\n  'RemoveTextOperation',\n  BaseRemoveTextOperation\n>\n\nexport type BaseSetNodeOperation = {\n  type: 'set_node'\n  path: Path\n  properties: Partial<Node>\n  newProperties: Partial<Node>\n}\n\nexport type SetNodeOperation = ExtendedType<\n  'SetNodeOperation',\n  BaseSetNodeOperation\n>\n\nexport type BaseSetSelectionOperation =\n  | {\n      type: 'set_selection'\n      properties: null\n      newProperties: Range\n    }\n  | {\n      type: 'set_selection'\n      properties: Partial<Range>\n      newProperties: Partial<Range>\n    }\n  | {\n      type: 'set_selection'\n      properties: Range\n      newProperties: null\n    }\n\nexport type SetSelectionOperation = ExtendedType<\n  'SetSelectionOperation',\n  BaseSetSelectionOperation\n>\n\nexport type BaseSplitNodeOperation = {\n  type: 'split_node'\n  path: Path\n  position: number\n  properties: Partial<Node>\n}\n\nexport type SplitNodeOperation = ExtendedType<\n  'SplitNodeOperation',\n  BaseSplitNodeOperation\n>\n\nexport type NodeOperation =\n  | InsertNodeOperation\n  | MergeNodeOperation\n  | MoveNodeOperation\n  | RemoveNodeOperation\n  | SetNodeOperation\n  | SplitNodeOperation\n\nexport type SelectionOperation = SetSelectionOperation\n\nexport type TextOperation = InsertTextOperation | RemoveTextOperation\n\n/**\n * `Operation` objects define the low-level instructions that Slate editors use\n * to apply changes to their internal state. Representing all changes as\n * operations is what allows Slate editors to easily implement history,\n * collaboration, and other features.\n */\n\nexport type BaseOperation = NodeOperation | SelectionOperation | TextOperation\nexport type Operation = ExtendedType<'Operation', BaseOperation>\n\nexport interface OperationInterface {\n  isNodeOperation: (value: any) => value is NodeOperation\n  isOperation: (value: any) => value is Operation\n  isOperationList: (value: any) => value is Operation[]\n  isSelectionOperation: (value: any) => value is SelectionOperation\n  isTextOperation: (value: any) => value is TextOperation\n  inverse: (op: Operation) => Operation\n}\n\nexport const Operation: OperationInterface = {\n  /**\n   * Check of a value is a `NodeOperation` object.\n   */\n\n  isNodeOperation(value: any): value is NodeOperation {\n    return Operation.isOperation(value) && value.type.endsWith('_node')\n  },\n\n  /**\n   * Check of a value is an `Operation` object.\n   */\n\n  isOperation(value: any): value is Operation {\n    if (!isPlainObject(value)) {\n      return false\n    }\n\n    switch (value.type) {\n      case 'insert_node':\n        return Path.isPath(value.path) && Node.isNode(value.node)\n      case 'insert_text':\n        return (\n          typeof value.offset === 'number' &&\n          typeof value.text === 'string' &&\n          Path.isPath(value.path)\n        )\n      case 'merge_node':\n        return (\n          typeof value.position === 'number' &&\n          Path.isPath(value.path) &&\n          isPlainObject(value.properties)\n        )\n      case 'move_node':\n        return Path.isPath(value.path) && Path.isPath(value.newPath)\n      case 'remove_node':\n        return Path.isPath(value.path) && Node.isNode(value.node)\n      case 'remove_text':\n        return (\n          typeof value.offset === 'number' &&\n          typeof value.text === 'string' &&\n          Path.isPath(value.path)\n        )\n      case 'set_node':\n        return (\n          Path.isPath(value.path) &&\n          isPlainObject(value.properties) &&\n          isPlainObject(value.newProperties)\n        )\n      case 'set_selection':\n        return (\n          (value.properties === null && Range.isRange(value.newProperties)) ||\n          (value.newProperties === null && Range.isRange(value.properties)) ||\n          (isPlainObject(value.properties) &&\n            isPlainObject(value.newProperties))\n        )\n      case 'split_node':\n        return (\n          Path.isPath(value.path) &&\n          typeof value.position === 'number' &&\n          isPlainObject(value.properties)\n        )\n      default:\n        return false\n    }\n  },\n\n  /**\n   * Check if a value is a list of `Operation` objects.\n   */\n\n  isOperationList(value: any): value is Operation[] {\n    return (\n      Array.isArray(value) && value.every(val => Operation.isOperation(val))\n    )\n  },\n\n  /**\n   * Check of a value is a `SelectionOperation` object.\n   */\n\n  isSelectionOperation(value: any): value is SelectionOperation {\n    return Operation.isOperation(value) && value.type.endsWith('_selection')\n  },\n\n  /**\n   * Check of a value is a `TextOperation` object.\n   */\n\n  isTextOperation(value: any): value is TextOperation {\n    return Operation.isOperation(value) && value.type.endsWith('_text')\n  },\n\n  /**\n   * Invert an operation, returning a new operation that will exactly undo the\n   * original when applied.\n   */\n\n  inverse(op: Operation): Operation {\n    switch (op.type) {\n      case 'insert_node': {\n        return { ...op, type: 'remove_node' }\n      }\n\n      case 'insert_text': {\n        return { ...op, type: 'remove_text' }\n      }\n\n      case 'merge_node': {\n        return { ...op, type: 'split_node', path: Path.previous(op.path) }\n      }\n\n      case 'move_node': {\n        const { newPath, path } = op\n\n        // PERF: in this case the move operation is a no-op anyways.\n        if (Path.equals(newPath, path)) {\n          return op\n        }\n\n        // If the move happens completely within a single parent the path and\n        // newPath are stable with respect to each other.\n        if (Path.isSibling(path, newPath)) {\n          return { ...op, path: newPath, newPath: path }\n        }\n\n        // If the move does not happen within a single parent it is possible\n        // for the move to impact the true path to the location where the node\n        // was removed from and where it was inserted. We have to adjust for this\n        // and find the original path. We can accomplish this (only in non-sibling)\n        // moves by looking at the impact of the move operation on the node\n        // after the original move path.\n        const inversePath = Path.transform(path, op)!\n        const inverseNewPath = Path.transform(Path.next(path), op)!\n        return { ...op, path: inversePath, newPath: inverseNewPath }\n      }\n\n      case 'remove_node': {\n        return { ...op, type: 'insert_node' }\n      }\n\n      case 'remove_text': {\n        return { ...op, type: 'insert_text' }\n      }\n\n      case 'set_node': {\n        const { properties, newProperties } = op\n        return { ...op, properties: newProperties, newProperties: properties }\n      }\n\n      case 'set_selection': {\n        const { properties, newProperties } = op\n\n        if (properties == null) {\n          return {\n            ...op,\n            properties: newProperties as Range,\n            newProperties: null,\n          }\n        } else if (newProperties == null) {\n          return {\n            ...op,\n            properties: null,\n            newProperties: properties as Range,\n          }\n        } else {\n          return { ...op, properties: newProperties, newProperties: properties }\n        }\n      }\n\n      case 'split_node': {\n        return { ...op, type: 'merge_node', path: Path.next(op.path) }\n      }\n    }\n  },\n}\n", "import { produce } from 'immer'\nimport { Operation } from '..'\n\n/**\n * `Path` arrays are a list of indexes that describe a node's exact position in\n * a Slate node tree. Although they are usually relative to the root `Editor`\n * object, they can be relative to any `Node` object.\n */\n\nexport type Path = number[]\n\nexport interface PathInterface {\n  ancestors: (path: Path, options?: { reverse?: boolean }) => Path[]\n  common: (path: Path, another: Path) => Path\n  compare: (path: Path, another: Path) => -1 | 0 | 1\n  endsAfter: (path: Path, another: Path) => boolean\n  endsAt: (path: Path, another: Path) => boolean\n  endsBefore: (path: Path, another: Path) => boolean\n  equals: (path: Path, another: Path) => boolean\n  hasPrevious: (path: Path) => boolean\n  isAfter: (path: Path, another: Path) => boolean\n  isAncestor: (path: Path, another: Path) => boolean\n  isBefore: (path: Path, another: Path) => boolean\n  isChild: (path: Path, another: Path) => boolean\n  isCommon: (path: Path, another: Path) => boolean\n  isDescendant: (path: Path, another: Path) => boolean\n  isParent: (path: Path, another: Path) => boolean\n  isPath: (value: any) => value is Path\n  isSibling: (path: Path, another: Path) => boolean\n  levels: (\n    path: Path,\n    options?: {\n      reverse?: boolean\n    }\n  ) => Path[]\n  next: (path: Path) => Path\n  operationCanTransformPath: (operation: Operation) => boolean\n  parent: (path: Path) => Path\n  previous: (path: Path) => Path\n  relative: (path: Path, ancestor: Path) => Path\n  transform: (\n    path: Path,\n    operation: Operation,\n    options?: { affinity?: 'forward' | 'backward' | null }\n  ) => Path | null\n}\n\nexport const Path: PathInterface = {\n  /**\n   * Get a list of ancestor paths for a given path.\n   *\n   * The paths are sorted from deepest to shallowest ancestor. However, if the\n   * `reverse: true` option is passed, they are reversed.\n   */\n\n  ancestors(path: Path, options: { reverse?: boolean } = {}): Path[] {\n    const { reverse = false } = options\n    let paths = Path.levels(path, options)\n\n    if (reverse) {\n      paths = paths.slice(1)\n    } else {\n      paths = paths.slice(0, -1)\n    }\n\n    return paths\n  },\n\n  /**\n   * Get the common ancestor path of two paths.\n   */\n\n  common(path: Path, another: Path): Path {\n    const common: Path = []\n\n    for (let i = 0; i < path.length && i < another.length; i++) {\n      const av = path[i]\n      const bv = another[i]\n\n      if (av !== bv) {\n        break\n      }\n\n      common.push(av)\n    }\n\n    return common\n  },\n\n  /**\n   * Compare a path to another, returning an integer indicating whether the path\n   * was before, at, or after the other.\n   *\n   * Note: Two paths of unequal length can still receive a `0` result if one is\n   * directly above or below the other. If you want exact matching, use\n   * [[Path.equals]] instead.\n   */\n\n  compare(path: Path, another: Path): -1 | 0 | 1 {\n    const min = Math.min(path.length, another.length)\n\n    for (let i = 0; i < min; i++) {\n      if (path[i] < another[i]) return -1\n      if (path[i] > another[i]) return 1\n    }\n\n    return 0\n  },\n\n  /**\n   * Check if a path ends after one of the indexes in another.\n   */\n\n  endsAfter(path: Path, another: Path): boolean {\n    const i = path.length - 1\n    const as = path.slice(0, i)\n    const bs = another.slice(0, i)\n    const av = path[i]\n    const bv = another[i]\n    return Path.equals(as, bs) && av > bv\n  },\n\n  /**\n   * Check if a path ends at one of the indexes in another.\n   */\n\n  endsAt(path: Path, another: Path): boolean {\n    const i = path.length\n    const as = path.slice(0, i)\n    const bs = another.slice(0, i)\n    return Path.equals(as, bs)\n  },\n\n  /**\n   * Check if a path ends before one of the indexes in another.\n   */\n\n  endsBefore(path: Path, another: Path): boolean {\n    const i = path.length - 1\n    const as = path.slice(0, i)\n    const bs = another.slice(0, i)\n    const av = path[i]\n    const bv = another[i]\n    return Path.equals(as, bs) && av < bv\n  },\n\n  /**\n   * Check if a path is exactly equal to another.\n   */\n\n  equals(path: Path, another: Path): boolean {\n    return (\n      path.length === another.length && path.every((n, i) => n === another[i])\n    )\n  },\n\n  /**\n   * Check if the path of previous sibling node exists\n   */\n\n  hasPrevious(path: Path): boolean {\n    return path[path.length - 1] > 0\n  },\n\n  /**\n   * Check if a path is after another.\n   */\n\n  isAfter(path: Path, another: Path): boolean {\n    return Path.compare(path, another) === 1\n  },\n\n  /**\n   * Check if a path is an ancestor of another.\n   */\n\n  isAncestor(path: Path, another: Path): boolean {\n    return path.length < another.length && Path.compare(path, another) === 0\n  },\n\n  /**\n   * Check if a path is before another.\n   */\n\n  isBefore(path: Path, another: Path): boolean {\n    return Path.compare(path, another) === -1\n  },\n\n  /**\n   * Check if a path is a child of another.\n   */\n\n  isChild(path: Path, another: Path): boolean {\n    return (\n      path.length === another.length + 1 && Path.compare(path, another) === 0\n    )\n  },\n\n  /**\n   * Check if a path is equal to or an ancestor of another.\n   */\n\n  isCommon(path: Path, another: Path): boolean {\n    return path.length <= another.length && Path.compare(path, another) === 0\n  },\n\n  /**\n   * Check if a path is a descendant of another.\n   */\n\n  isDescendant(path: Path, another: Path): boolean {\n    return path.length > another.length && Path.compare(path, another) === 0\n  },\n\n  /**\n   * Check if a path is the parent of another.\n   */\n\n  isParent(path: Path, another: Path): boolean {\n    return (\n      path.length + 1 === another.length && Path.compare(path, another) === 0\n    )\n  },\n\n  /**\n   * Check is a value implements the `Path` interface.\n   */\n\n  isPath(value: any): value is Path {\n    return (\n      Array.isArray(value) &&\n      (value.length === 0 || typeof value[0] === 'number')\n    )\n  },\n\n  /**\n   * Check if a path is a sibling of another.\n   */\n\n  isSibling(path: Path, another: Path): boolean {\n    if (path.length !== another.length) {\n      return false\n    }\n\n    const as = path.slice(0, -1)\n    const bs = another.slice(0, -1)\n    const al = path[path.length - 1]\n    const bl = another[another.length - 1]\n    return al !== bl && Path.equals(as, bs)\n  },\n\n  /**\n   * Get a list of paths at every level down to a path. Note: this is the same\n   * as `Path.ancestors`, but including the path itself.\n   *\n   * The paths are sorted from shallowest to deepest. However, if the `reverse:\n   * true` option is passed, they are reversed.\n   */\n\n  levels(\n    path: Path,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): Path[] {\n    const { reverse = false } = options\n    const list: Path[] = []\n\n    for (let i = 0; i <= path.length; i++) {\n      list.push(path.slice(0, i))\n    }\n\n    if (reverse) {\n      list.reverse()\n    }\n\n    return list\n  },\n\n  /**\n   * Given a path, get the path to the next sibling node.\n   */\n\n  next(path: Path): Path {\n    if (path.length === 0) {\n      throw new Error(\n        `Cannot get the next path of a root path [${path}], because it has no next index.`\n      )\n    }\n\n    const last = path[path.length - 1]\n    return path.slice(0, -1).concat(last + 1)\n  },\n\n  /**\n   * Returns whether this operation can affect paths or not. Used as an\n   * optimization when updating dirty paths during normalization\n   *\n   * NOTE: This *must* be kept in sync with the implementation of 'transform'\n   * below\n   */\n  operationCanTransformPath(operation: Operation): boolean {\n    switch (operation.type) {\n      case 'insert_node':\n      case 'remove_node':\n      case 'merge_node':\n      case 'split_node':\n      case 'move_node':\n        return true\n      default:\n        return false\n    }\n  },\n\n  /**\n   * Given a path, return a new path referring to the parent node above it.\n   */\n\n  parent(path: Path): Path {\n    if (path.length === 0) {\n      throw new Error(`Cannot get the parent path of the root path [${path}].`)\n    }\n\n    return path.slice(0, -1)\n  },\n\n  /**\n   * Given a path, get the path to the previous sibling node.\n   */\n\n  previous(path: Path): Path {\n    if (path.length === 0) {\n      throw new Error(\n        `Cannot get the previous path of a root path [${path}], because it has no previous index.`\n      )\n    }\n\n    const last = path[path.length - 1]\n\n    if (last <= 0) {\n      throw new Error(\n        `Cannot get the previous path of a first child path [${path}] because it would result in a negative index.`\n      )\n    }\n\n    return path.slice(0, -1).concat(last - 1)\n  },\n\n  /**\n   * Get a path relative to an ancestor.\n   */\n\n  relative(path: Path, ancestor: Path): Path {\n    if (!Path.isAncestor(ancestor, path) && !Path.equals(path, ancestor)) {\n      throw new Error(\n        `Cannot get the relative path of [${path}] inside ancestor [${ancestor}], because it is not above or equal to the path.`\n      )\n    }\n\n    return path.slice(ancestor.length)\n  },\n\n  /**\n   * Transform a path by an operation.\n   */\n\n  transform(\n    path: Path | null,\n    operation: Operation,\n    options: { affinity?: 'forward' | 'backward' | null } = {}\n  ): Path | null {\n    return produce(path, p => {\n      const { affinity = 'forward' } = options\n\n      // PERF: Exit early if the operation is guaranteed not to have an effect.\n      if (!path || path?.length === 0) {\n        return\n      }\n\n      if (p === null) {\n        return null\n      }\n\n      switch (operation.type) {\n        case 'insert_node': {\n          const { path: op } = operation\n\n          if (\n            Path.equals(op, p) ||\n            Path.endsBefore(op, p) ||\n            Path.isAncestor(op, p)\n          ) {\n            p[op.length - 1] += 1\n          }\n\n          break\n        }\n\n        case 'remove_node': {\n          const { path: op } = operation\n\n          if (Path.equals(op, p) || Path.isAncestor(op, p)) {\n            return null\n          } else if (Path.endsBefore(op, p)) {\n            p[op.length - 1] -= 1\n          }\n\n          break\n        }\n\n        case 'merge_node': {\n          const { path: op, position } = operation\n\n          if (Path.equals(op, p) || Path.endsBefore(op, p)) {\n            p[op.length - 1] -= 1\n          } else if (Path.isAncestor(op, p)) {\n            p[op.length - 1] -= 1\n            p[op.length] += position\n          }\n\n          break\n        }\n\n        case 'split_node': {\n          const { path: op, position } = operation\n\n          if (Path.equals(op, p)) {\n            if (affinity === 'forward') {\n              p[p.length - 1] += 1\n            } else if (affinity === 'backward') {\n              // Nothing, because it still refers to the right path.\n            } else {\n              return null\n            }\n          } else if (Path.endsBefore(op, p)) {\n            p[op.length - 1] += 1\n          } else if (Path.isAncestor(op, p) && path[op.length] >= position) {\n            p[op.length - 1] += 1\n            p[op.length] -= position\n          }\n\n          break\n        }\n\n        case 'move_node': {\n          const { path: op, newPath: onp } = operation\n\n          // If the old and new path are the same, it's a no-op.\n          if (Path.equals(op, onp)) {\n            return\n          }\n\n          if (Path.isAncestor(op, p) || Path.equals(op, p)) {\n            const copy = onp.slice()\n\n            if (Path.endsBefore(op, onp) && op.length < onp.length) {\n              copy[op.length - 1] -= 1\n            }\n\n            return copy.concat(p.slice(op.length))\n          } else if (\n            Path.isSibling(op, onp) &&\n            (Path.isAncestor(onp, p) || Path.equals(onp, p))\n          ) {\n            if (Path.endsBefore(op, p)) {\n              p[op.length - 1] -= 1\n            } else {\n              p[op.length - 1] += 1\n            }\n          } else if (\n            Path.endsBefore(onp, p) ||\n            Path.equals(onp, p) ||\n            Path.isAncestor(onp, p)\n          ) {\n            if (Path.endsBefore(op, p)) {\n              p[op.length - 1] -= 1\n            }\n\n            p[onp.length - 1] += 1\n          } else if (Path.endsBefore(op, p)) {\n            if (Path.equals(onp, p)) {\n              p[onp.length - 1] += 1\n            }\n\n            p[op.length - 1] -= 1\n          }\n\n          break\n        }\n      }\n    })\n  },\n}\n", "import { Operation, Path } from '..'\n\n/**\n * `PathRef` objects keep a specific path in a document synced over time as new\n * operations are applied to the editor. You can access their `current` property\n * at any time for the up-to-date path value.\n */\n\nexport interface PathRef {\n  current: Path | null\n  affinity: 'forward' | 'backward' | null\n  unref(): Path | null\n}\n\nexport interface PathRefInterface {\n  transform: (ref: PathRef, op: Operation) => void\n}\n\nexport const PathRef: PathRefInterface = {\n  /**\n   * Transform the path ref's current value by an operation.\n   */\n\n  transform(ref: PathRef, op: Operation): void {\n    const { current, affinity } = ref\n\n    if (current == null) {\n      return\n    }\n\n    const path = Path.transform(current, op, { affinity })\n    ref.current = path\n\n    if (path == null) {\n      ref.unref()\n    }\n  },\n}\n", "import { isPlainObject } from 'is-plain-object'\nimport { produce } from 'immer'\nimport { ExtendedType, Operation, Path } from '..'\n\n/**\n * `Point` objects refer to a specific location in a text node in a Slate\n * document. Its path refers to the location of the node in the tree, and its\n * offset refers to the distance into the node's string of text. Points can\n * only refer to `Text` nodes.\n */\n\nexport interface BasePoint {\n  path: Path\n  offset: number\n}\n\nexport type Point = ExtendedType<'Point', BasePoint>\n\nexport interface PointInterface {\n  compare: (point: Point, another: Point) => -1 | 0 | 1\n  isAfter: (point: Point, another: Point) => boolean\n  isBefore: (point: Point, another: Point) => boolean\n  equals: (point: Point, another: Point) => boolean\n  isPoint: (value: any) => value is Point\n  transform: (\n    point: Point,\n    op: Operation,\n    options?: { affinity?: 'forward' | 'backward' | null }\n  ) => Point | null\n}\n\nexport const Point: PointInterface = {\n  /**\n   * Compare a point to another, returning an integer indicating whether the\n   * point was before, at, or after the other.\n   */\n\n  compare(point: Point, another: Point): -1 | 0 | 1 {\n    const result = Path.compare(point.path, another.path)\n\n    if (result === 0) {\n      if (point.offset < another.offset) return -1\n      if (point.offset > another.offset) return 1\n      return 0\n    }\n\n    return result\n  },\n\n  /**\n   * Check if a point is after another.\n   */\n\n  isAfter(point: Point, another: Point): boolean {\n    return Point.compare(point, another) === 1\n  },\n\n  /**\n   * Check if a point is before another.\n   */\n\n  isBefore(point: Point, another: Point): boolean {\n    return Point.compare(point, another) === -1\n  },\n\n  /**\n   * Check if a point is exactly equal to another.\n   */\n\n  equals(point: Point, another: Point): boolean {\n    // PERF: ensure the offsets are equal first since they are cheaper to check.\n    return (\n      point.offset === another.offset && Path.equals(point.path, another.path)\n    )\n  },\n\n  /**\n   * Check if a value implements the `Point` interface.\n   */\n\n  isPoint(value: any): value is Point {\n    return (\n      isPlainObject(value) &&\n      typeof value.offset === 'number' &&\n      Path.isPath(value.path)\n    )\n  },\n\n  /**\n   * Transform a point by an operation.\n   */\n\n  transform(\n    point: Point | null,\n    op: Operation,\n    options: { affinity?: 'forward' | 'backward' | null } = {}\n  ): Point | null {\n    return produce(point, p => {\n      if (p === null) {\n        return null\n      }\n      const { affinity = 'forward' } = options\n      const { path, offset } = p\n\n      switch (op.type) {\n        case 'insert_node':\n        case 'move_node': {\n          p.path = Path.transform(path, op, options)!\n          break\n        }\n\n        case 'insert_text': {\n          if (Path.equals(op.path, path) && op.offset <= offset) {\n            p.offset += op.text.length\n          }\n\n          break\n        }\n\n        case 'merge_node': {\n          if (Path.equals(op.path, path)) {\n            p.offset += op.position\n          }\n\n          p.path = Path.transform(path, op, options)!\n          break\n        }\n\n        case 'remove_text': {\n          if (Path.equals(op.path, path) && op.offset <= offset) {\n            p.offset -= Math.min(offset - op.offset, op.text.length)\n          }\n\n          break\n        }\n\n        case 'remove_node': {\n          if (Path.equals(op.path, path) || Path.isAncestor(op.path, path)) {\n            return null\n          }\n\n          p.path = Path.transform(path, op, options)!\n          break\n        }\n\n        case 'split_node': {\n          if (Path.equals(op.path, path)) {\n            if (op.position === offset && affinity == null) {\n              return null\n            } else if (\n              op.position < offset ||\n              (op.position === offset && affinity === 'forward')\n            ) {\n              p.offset -= op.position\n\n              p.path = Path.transform(path, op, {\n                ...options,\n                affinity: 'forward',\n              })!\n            }\n          } else {\n            p.path = Path.transform(path, op, options)!\n          }\n\n          break\n        }\n      }\n    })\n  },\n}\n\n/**\n * `PointEntry` objects are returned when iterating over `Point` objects that\n * belong to a range.\n */\n\nexport type PointEntry = [Point, 'anchor' | 'focus']\n", "import { Operation, Point } from '..'\n\n/**\n * `PointRef` objects keep a specific point in a document synced over time as new\n * operations are applied to the editor. You can access their `current` property\n * at any time for the up-to-date point value.\n */\n\nexport interface PointRef {\n  current: Point | null\n  affinity: 'forward' | 'backward' | null\n  unref(): Point | null\n}\n\nexport interface PointRefInterface {\n  transform: (ref: PointRef, op: Operation) => void\n}\n\nexport const PointRef: PointRefInterface = {\n  /**\n   * Transform the point ref's current value by an operation.\n   */\n\n  transform(ref: PointRef, op: Operation): void {\n    const { current, affinity } = ref\n\n    if (current == null) {\n      return\n    }\n\n    const point = Point.transform(current, op, { affinity })\n    ref.current = point\n\n    if (point == null) {\n      ref.unref()\n    }\n  },\n}\n", "import { produce } from 'immer'\nimport { isPlainObject } from 'is-plain-object'\nimport { ExtendedType, Operation, Path, Point, PointEntry } from '..'\n\n/**\n * `Range` objects are a set of points that refer to a specific span of a Slate\n * document. They can define a span inside a single node or a can span across\n * multiple nodes.\n */\n\nexport interface BaseRange {\n  anchor: Point\n  focus: Point\n}\n\nexport type Range = ExtendedType<'Range', BaseRange>\n\nexport interface RangeInterface {\n  edges: (\n    range: Range,\n    options?: {\n      reverse?: boolean\n    }\n  ) => [Point, Point]\n  end: (range: Range) => Point\n  equals: (range: Range, another: Range) => boolean\n  includes: (range: Range, target: Path | Point | Range) => boolean\n  intersection: (range: Range, another: Range) => Range | null\n  isBackward: (range: Range) => boolean\n  isCollapsed: (range: Range) => boolean\n  isExpanded: (range: Range) => boolean\n  isForward: (range: Range) => boolean\n  isRange: (value: any) => value is Range\n  points: (range: Range) => Generator<PointEntry, void, undefined>\n  start: (range: Range) => Point\n  transform: (\n    range: Range,\n    op: Operation,\n    options?: {\n      affinity?: 'forward' | 'backward' | 'outward' | 'inward' | null\n    }\n  ) => Range | null\n}\n\nexport const Range: RangeInterface = {\n  /**\n   * Get the start and end points of a range, in the order in which they appear\n   * in the document.\n   */\n\n  edges(\n    range: Range,\n    options: {\n      reverse?: boolean\n    } = {}\n  ): [Point, Point] {\n    const { reverse = false } = options\n    const { anchor, focus } = range\n    return Range.isBackward(range) === reverse\n      ? [anchor, focus]\n      : [focus, anchor]\n  },\n\n  /**\n   * Get the end point of a range.\n   */\n\n  end(range: Range): Point {\n    const [, end] = Range.edges(range)\n    return end\n  },\n\n  /**\n   * Check if a range is exactly equal to another.\n   */\n\n  equals(range: Range, another: Range): boolean {\n    return (\n      Point.equals(range.anchor, another.anchor) &&\n      Point.equals(range.focus, another.focus)\n    )\n  },\n\n  /**\n   * Check if a range includes a path, a point or part of another range.\n   */\n\n  includes(range: Range, target: Path | Point | Range): boolean {\n    if (Range.isRange(target)) {\n      if (\n        Range.includes(range, target.anchor) ||\n        Range.includes(range, target.focus)\n      ) {\n        return true\n      }\n\n      const [rs, re] = Range.edges(range)\n      const [ts, te] = Range.edges(target)\n      return Point.isBefore(rs, ts) && Point.isAfter(re, te)\n    }\n\n    const [start, end] = Range.edges(range)\n    let isAfterStart = false\n    let isBeforeEnd = false\n\n    if (Point.isPoint(target)) {\n      isAfterStart = Point.compare(target, start) >= 0\n      isBeforeEnd = Point.compare(target, end) <= 0\n    } else {\n      isAfterStart = Path.compare(target, start.path) >= 0\n      isBeforeEnd = Path.compare(target, end.path) <= 0\n    }\n\n    return isAfterStart && isBeforeEnd\n  },\n\n  /**\n   * Get the intersection of a range with another.\n   */\n\n  intersection(range: Range, another: Range): Range | null {\n    const { anchor, focus, ...rest } = range\n    const [s1, e1] = Range.edges(range)\n    const [s2, e2] = Range.edges(another)\n    const start = Point.isBefore(s1, s2) ? s2 : s1\n    const end = Point.isBefore(e1, e2) ? e1 : e2\n\n    if (Point.isBefore(end, start)) {\n      return null\n    } else {\n      return { anchor: start, focus: end, ...rest }\n    }\n  },\n\n  /**\n   * Check if a range is backward, meaning that its anchor point appears in the\n   * document _after_ its focus point.\n   */\n\n  isBackward(range: Range): boolean {\n    const { anchor, focus } = range\n    return Point.isAfter(anchor, focus)\n  },\n\n  /**\n   * Check if a range is collapsed, meaning that both its anchor and focus\n   * points refer to the exact same position in the document.\n   */\n\n  isCollapsed(range: Range): boolean {\n    const { anchor, focus } = range\n    return Point.equals(anchor, focus)\n  },\n\n  /**\n   * Check if a range is expanded.\n   *\n   * This is the opposite of [[Range.isCollapsed]] and is provided for legibility.\n   */\n\n  isExpanded(range: Range): boolean {\n    return !Range.isCollapsed(range)\n  },\n\n  /**\n   * Check if a range is forward.\n   *\n   * This is the opposite of [[Range.isBackward]] and is provided for legibility.\n   */\n\n  isForward(range: Range): boolean {\n    return !Range.isBackward(range)\n  },\n\n  /**\n   * Check if a value implements the [[Range]] interface.\n   */\n\n  isRange(value: any): value is Range {\n    return (\n      isPlainObject(value) &&\n      Point.isPoint(value.anchor) &&\n      Point.isPoint(value.focus)\n    )\n  },\n\n  /**\n   * Iterate through all of the point entries in a range.\n   */\n\n  *points(range: Range): Generator<PointEntry, void, undefined> {\n    yield [range.anchor, 'anchor']\n    yield [range.focus, 'focus']\n  },\n\n  /**\n   * Get the start point of a range.\n   */\n\n  start(range: Range): Point {\n    const [start] = Range.edges(range)\n    return start\n  },\n\n  /**\n   * Transform a range by an operation.\n   */\n\n  transform(\n    range: Range | null,\n    op: Operation,\n    options: {\n      affinity?: 'forward' | 'backward' | 'outward' | 'inward' | null\n    } = {}\n  ): Range | null {\n    return produce(range, r => {\n      if (r === null) {\n        return null\n      }\n      const { affinity = 'inward' } = options\n      let affinityAnchor: 'forward' | 'backward' | null\n      let affinityFocus: 'forward' | 'backward' | null\n\n      if (affinity === 'inward') {\n        // If the range is collapsed, make sure to use the same affinity to\n        // avoid the two points passing each other and expanding in the opposite\n        // direction\n        const isCollapsed = Range.isCollapsed(r)\n        if (Range.isForward(r)) {\n          affinityAnchor = 'forward'\n          affinityFocus = isCollapsed ? affinityAnchor : 'backward'\n        } else {\n          affinityAnchor = 'backward'\n          affinityFocus = isCollapsed ? affinityAnchor : 'forward'\n        }\n      } else if (affinity === 'outward') {\n        if (Range.isForward(r)) {\n          affinityAnchor = 'backward'\n          affinityFocus = 'forward'\n        } else {\n          affinityAnchor = 'forward'\n          affinityFocus = 'backward'\n        }\n      } else {\n        affinityAnchor = affinity\n        affinityFocus = affinity\n      }\n      const anchor = Point.transform(r.anchor, op, { affinity: affinityAnchor })\n      const focus = Point.transform(r.focus, op, { affinity: affinityFocus })\n\n      if (!anchor || !focus) {\n        return null\n      }\n\n      r.anchor = anchor\n      r.focus = focus\n    })\n  },\n}\n", "import { Operation, Range } from '..'\n\n/**\n * `RangeRef` objects keep a specific range in a document synced over time as new\n * operations are applied to the editor. You can access their `current` property\n * at any time for the up-to-date range value.\n */\n\nexport interface RangeRef {\n  current: Range | null\n  affinity: 'forward' | 'backward' | 'outward' | 'inward' | null\n  unref(): Range | null\n}\n\nexport interface RangeRefInterface {\n  transform: (ref: RangeRef, op: Operation) => void\n}\n\nexport const RangeRef: RangeRefInterface = {\n  /**\n   * Transform the range ref's current value by an operation.\n   */\n\n  transform(ref: RangeRef, op: Operation): void {\n    const { current, affinity } = ref\n\n    if (current == null) {\n      return\n    }\n\n    const path = Range.transform(current, op, { affinity })\n    ref.current = path\n\n    if (path == null) {\n      ref.unref()\n    }\n  },\n}\n", "import { isPlainObject } from 'is-plain-object'\n\n/*\n  Custom deep equal comparison for Slate nodes.\n\n  We don't need general purpose deep equality;\n  Slate only supports plain values, Arrays, and nested objects.\n  Complex values nested inside Arrays are not supported.\n\n  Slate objects are designed to be serialised, so\n  missing keys are deliberately normalised to undefined.\n */\nexport const isDeepEqual = (\n  node: Record<string, any>,\n  another: Record<string, any>\n): boolean => {\n  for (const key in node) {\n    const a = node[key]\n    const b = another[key]\n    if (isPlainObject(a) && isPlainObject(b)) {\n      if (!isDeepEqual(a, b)) return false\n    } else if (Array.isArray(a) && Array.isArray(b)) {\n      if (a.length !== b.length) return false\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) return false\n      }\n    } else if (a !== b) {\n      return false\n    }\n  }\n\n  /*\n    Deep object equality is only necessary in one direction; in the reverse direction\n    we are only looking for keys that are missing.\n    As above, undefined keys are normalised to missing.\n  */\n\n  for (const key in another) {\n    if (node[key] === undefined && another[key] !== undefined) {\n      return false\n    }\n  }\n\n  return true\n}\n", "import { isPlainObject } from 'is-plain-object'\nimport { Range } from '..'\nimport { ExtendedType } from './custom-types'\nimport { isDeepEqual } from '../utils/deep-equal'\n\n/**\n * `Text` objects represent the nodes that contain the actual text content of a\n * Slate document along with any formatting properties. They are always leaf\n * nodes in the document tree as they cannot contain any children.\n */\n\nexport interface BaseText {\n  text: string\n}\n\nexport type Text = ExtendedType<'Text', BaseText>\n\nexport interface TextInterface {\n  equals: (text: Text, another: Text, options?: { loose?: boolean }) => boolean\n  isText: (value: any) => value is Text\n  isTextList: (value: any) => value is Text[]\n  isTextProps: (props: any) => props is Partial<Text>\n  matches: (text: Text, props: Partial<Text>) => boolean\n  decorations: (node: Text, decorations: Range[]) => Text[]\n}\n\nexport const Text: TextInterface = {\n  /**\n   * Check if two text nodes are equal.\n   *\n   * When loose is set, the text is not compared. This is\n   * used to check whether sibling text nodes can be merged.\n   */\n  equals(\n    text: Text,\n    another: Text,\n    options: { loose?: boolean } = {}\n  ): boolean {\n    const { loose = false } = options\n\n    function omitText(obj: Record<any, any>) {\n      const { text, ...rest } = obj\n\n      return rest\n    }\n\n    return isDeepEqual(\n      loose ? omitText(text) : text,\n      loose ? omitText(another) : another\n    )\n  },\n\n  /**\n   * Check if a value implements the `Text` interface.\n   */\n\n  isText(value: any): value is Text {\n    return isPlainObject(value) && typeof value.text === 'string'\n  },\n\n  /**\n   * Check if a value is a list of `Text` objects.\n   */\n\n  isTextList(value: any): value is Text[] {\n    return Array.isArray(value) && value.every(val => Text.isText(val))\n  },\n\n  /**\n   * Check if some props are a partial of Text.\n   */\n\n  isTextProps(props: any): props is Partial<Text> {\n    return (props as Partial<Text>).text !== undefined\n  },\n\n  /**\n   * Check if an text matches set of properties.\n   *\n   * Note: this is for matching custom properties, and it does not ensure that\n   * the `text` property are two nodes equal.\n   */\n\n  matches(text: Text, props: Partial<Text>): boolean {\n    for (const key in props) {\n      if (key === 'text') {\n        continue\n      }\n\n      if (!text.hasOwnProperty(key) || text[key] !== props[key]) {\n        return false\n      }\n    }\n\n    return true\n  },\n\n  /**\n   * Get the leaves for a text node given decorations.\n   */\n\n  decorations(node: Text, decorations: Range[]): Text[] {\n    let leaves: Text[] = [{ ...node }]\n\n    for (const dec of decorations) {\n      const { anchor, focus, ...rest } = dec\n      const [start, end] = Range.edges(dec)\n      const next = []\n      let o = 0\n\n      for (const leaf of leaves) {\n        const { length } = leaf.text\n        const offset = o\n        o += length\n\n        // If the range encompases the entire leaf, add the range.\n        if (start.offset <= offset && end.offset >= o) {\n          Object.assign(leaf, rest)\n          next.push(leaf)\n          continue\n        }\n\n        // If the range expanded and match the leaf, or starts after, or ends before it, continue.\n        if (\n          (start.offset !== end.offset &&\n            (start.offset === o || end.offset === offset)) ||\n          start.offset > o ||\n          end.offset < offset ||\n          (end.offset === offset && offset !== 0)\n        ) {\n          next.push(leaf)\n          continue\n        }\n\n        // Otherwise we need to split the leaf, at the start, end, or both,\n        // and add the range to the middle intersecting section. Do the end\n        // split first since we don't need to update the offset that way.\n        let middle = leaf\n        let before\n        let after\n\n        if (end.offset < o) {\n          const off = end.offset - offset\n          after = { ...middle, text: middle.text.slice(off) }\n          middle = { ...middle, text: middle.text.slice(0, off) }\n        }\n\n        if (start.offset > offset) {\n          const off = start.offset - offset\n          before = { ...middle, text: middle.text.slice(0, off) }\n          middle = { ...middle, text: middle.text.slice(off) }\n        }\n\n        Object.assign(middle, rest)\n\n        if (before) {\n          next.push(before)\n        }\n\n        next.push(middle)\n\n        if (after) {\n          next.push(after)\n        }\n      }\n\n      leaves = next\n    }\n\n    return leaves\n  },\n}\n", "import { createDraft, finishDraft, isDraft } from 'immer'\nimport {\n  Node,\n  Editor,\n  Selection,\n  Range,\n  Point,\n  Text,\n  Element,\n  Operation,\n  Descendant,\n  NodeEntry,\n  Path,\n  Ancestor,\n} from '..'\n\nexport interface GeneralTransforms {\n  transform: (editor: Editor, op: Operation) => void\n}\n\nconst applyToDraft = (editor: Editor, selection: Selection, op: Operation) => {\n  switch (op.type) {\n    case 'insert_node': {\n      const { path, node } = op\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n\n      if (index > parent.children.length) {\n        throw new Error(\n          `Cannot apply an \"insert_node\" operation at path [${path}] because the destination is past the end of the node.`\n        )\n      }\n\n      parent.children.splice(index, 0, node)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'insert_text': {\n      const { path, offset, text } = op\n      if (text.length === 0) break\n      const node = Node.leaf(editor, path)\n      const before = node.text.slice(0, offset)\n      const after = node.text.slice(offset)\n      node.text = before + text + after\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'merge_node': {\n      const { path } = op\n      const node = Node.get(editor, path)\n      const prevPath = Path.previous(path)\n      const prev = Node.get(editor, prevPath)\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n\n      if (Text.isText(node) && Text.isText(prev)) {\n        prev.text += node.text\n      } else if (!Text.isText(node) && !Text.isText(prev)) {\n        prev.children.push(...node.children)\n      } else {\n        throw new Error(\n          `Cannot apply a \"merge_node\" operation at path [${path}] to nodes of different interfaces: ${node} ${prev}`\n        )\n      }\n\n      parent.children.splice(index, 1)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'move_node': {\n      const { path, newPath } = op\n\n      if (Path.isAncestor(path, newPath)) {\n        throw new Error(\n          `Cannot move a path [${path}] to new path [${newPath}] because the destination is inside itself.`\n        )\n      }\n\n      const node = Node.get(editor, path)\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n\n      // This is tricky, but since the `path` and `newPath` both refer to\n      // the same snapshot in time, there's a mismatch. After either\n      // removing the original position, the second step's path can be out\n      // of date. So instead of using the `op.newPath` directly, we\n      // transform `op.path` to ascertain what the `newPath` would be after\n      // the operation was applied.\n      parent.children.splice(index, 1)\n      const truePath = Path.transform(path, op)!\n      const newParent = Node.get(editor, Path.parent(truePath)) as Ancestor\n      const newIndex = truePath[truePath.length - 1]\n\n      newParent.children.splice(newIndex, 0, node)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'remove_node': {\n      const { path } = op\n      const index = path[path.length - 1]\n      const parent = Node.parent(editor, path)\n      parent.children.splice(index, 1)\n\n      // Transform all of the points in the value, but if the point was in the\n      // node that was removed we need to update the range or remove it.\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          const result = Point.transform(point, op)\n\n          if (selection != null && result != null) {\n            selection[key] = result\n          } else {\n            let prev: NodeEntry<Text> | undefined\n            let next: NodeEntry<Text> | undefined\n\n            for (const [n, p] of Node.texts(editor)) {\n              if (Path.compare(p, path) === -1) {\n                prev = [n, p]\n              } else {\n                next = [n, p]\n                break\n              }\n            }\n\n            let preferNext = false\n            if (prev && next) {\n              if (Path.equals(next[1], path)) {\n                preferNext = !Path.hasPrevious(next[1])\n              } else {\n                preferNext =\n                  Path.common(prev[1], path).length <\n                  Path.common(next[1], path).length\n              }\n            }\n\n            if (prev && !preferNext) {\n              point.path = prev[1]\n              point.offset = prev[0].text.length\n            } else if (next) {\n              point.path = next[1]\n              point.offset = 0\n            } else {\n              selection = null\n            }\n          }\n        }\n      }\n\n      break\n    }\n\n    case 'remove_text': {\n      const { path, offset, text } = op\n      if (text.length === 0) break\n      const node = Node.leaf(editor, path)\n      const before = node.text.slice(0, offset)\n      const after = node.text.slice(offset + text.length)\n      node.text = before + after\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n\n    case 'set_node': {\n      const { path, properties, newProperties } = op\n\n      if (path.length === 0) {\n        throw new Error(`Cannot set properties on the root node!`)\n      }\n\n      const node = Node.get(editor, path)\n\n      for (const key in newProperties) {\n        if (key === 'children' || key === 'text') {\n          throw new Error(`Cannot set the \"${key}\" property of nodes!`)\n        }\n\n        const value = newProperties[key]\n\n        if (value == null) {\n          delete node[key]\n        } else {\n          node[key] = value\n        }\n      }\n\n      // properties that were previously defined, but are now missing, must be deleted\n      for (const key in properties) {\n        if (!newProperties.hasOwnProperty(key)) {\n          delete node[key]\n        }\n      }\n\n      break\n    }\n\n    case 'set_selection': {\n      const { newProperties } = op\n\n      if (newProperties == null) {\n        selection = newProperties\n      } else {\n        if (selection == null) {\n          if (!Range.isRange(newProperties)) {\n            throw new Error(\n              `Cannot apply an incomplete \"set_selection\" operation properties ${JSON.stringify(\n                newProperties\n              )} when there is no current selection.`\n            )\n          }\n\n          selection = { ...newProperties }\n        }\n\n        for (const key in newProperties) {\n          const value = newProperties[key]\n\n          if (value == null) {\n            if (key === 'anchor' || key === 'focus') {\n              throw new Error(`Cannot remove the \"${key}\" selection property`)\n            }\n\n            delete selection[key]\n          } else {\n            selection[key] = value\n          }\n        }\n      }\n\n      break\n    }\n\n    case 'split_node': {\n      const { path, position, properties } = op\n\n      if (path.length === 0) {\n        throw new Error(\n          `Cannot apply a \"split_node\" operation at path [${path}] because the root node cannot be split.`\n        )\n      }\n\n      const node = Node.get(editor, path)\n      const parent = Node.parent(editor, path)\n      const index = path[path.length - 1]\n      let newNode: Descendant\n\n      if (Text.isText(node)) {\n        const before = node.text.slice(0, position)\n        const after = node.text.slice(position)\n        node.text = before\n        newNode = {\n          ...(properties as Partial<Text>),\n          text: after,\n        }\n      } else {\n        const before = node.children.slice(0, position)\n        const after = node.children.slice(position)\n        node.children = before\n\n        newNode = {\n          ...(properties as Partial<Element>),\n          children: after,\n        }\n      }\n\n      parent.children.splice(index + 1, 0, newNode)\n\n      if (selection) {\n        for (const [point, key] of Range.points(selection)) {\n          selection[key] = Point.transform(point, op)!\n        }\n      }\n\n      break\n    }\n  }\n  return selection\n}\n\nexport const GeneralTransforms: GeneralTransforms = {\n  /**\n   * Transform the editor by an operation.\n   */\n\n  transform(editor: Editor, op: Operation): void {\n    editor.children = createDraft(editor.children)\n    let selection = editor.selection && createDraft(editor.selection)\n\n    try {\n      selection = applyToDraft(editor, selection, op)\n    } finally {\n      editor.children = finishDraft(editor.children)\n\n      if (selection) {\n        editor.selection = isDraft(selection)\n          ? (finishDraft(selection) as Range)\n          : selection\n      } else {\n        editor.selection = null\n      }\n    }\n  },\n}\n", "import {\n  Editor,\n  Element,\n  Location,\n  Node,\n  Path,\n  Point,\n  Range,\n  Text,\n  Transforms,\n  NodeEntry,\n  Ancestor,\n} from '..'\nimport { NodeMatch } from '../interfaces/editor'\n\nexport interface NodeTransforms {\n  insertNodes: <T extends Node>(\n    editor: Editor,\n    nodes: Node | Node[],\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      select?: boolean\n      voids?: boolean\n    }\n  ) => void\n  liftNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    }\n  ) => void\n  mergeNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  moveNodes: <T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      to: Path\n      voids?: boolean\n    }\n  ) => void\n  removeNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  setNodes: <T extends Node>(\n    editor: Editor,\n    props: Partial<T>,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      hanging?: boolean\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n  splitNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      always?: boolean\n      height?: number\n      voids?: boolean\n    }\n  ) => void\n  unsetNodes: <T extends Node>(\n    editor: Editor,\n    props: string | string[],\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n  unwrapNodes: <T extends Node>(\n    editor: Editor,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n  wrapNodes: <T extends Node>(\n    editor: Editor,\n    element: Element,\n    options?: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    }\n  ) => void\n}\n\nexport const NodeTransforms: NodeTransforms = {\n  /**\n   * Insert nodes at a specific location in the Editor.\n   */\n\n  insertNodes<T extends Node>(\n    editor: Editor,\n    nodes: Node | Node[],\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      select?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { hanging = false, voids = false, mode = 'lowest' } = options\n      let { at, match, select } = options\n\n      if (Node.isNode(nodes)) {\n        nodes = [nodes]\n      }\n\n      if (nodes.length === 0) {\n        return\n      }\n\n      const [node] = nodes\n\n      // By default, use the selection as the target location. But if there is\n      // no selection, insert at the end of the document since that is such a\n      // common use case when inserting from a non-selected state.\n      if (!at) {\n        if (editor.selection) {\n          at = editor.selection\n        } else if (editor.children.length > 0) {\n          at = Editor.end(editor, [])\n        } else {\n          at = [0]\n        }\n\n        select = true\n      }\n\n      if (select == null) {\n        select = false\n      }\n\n      if (Range.isRange(at)) {\n        if (!hanging) {\n          at = Editor.unhangRange(editor, at)\n        }\n\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const [, end] = Range.edges(at)\n          const pointRef = Editor.pointRef(editor, end)\n          Transforms.delete(editor, { at })\n          at = pointRef.unref()!\n        }\n      }\n\n      if (Point.isPoint(at)) {\n        if (match == null) {\n          if (Text.isText(node)) {\n            match = n => Text.isText(n)\n          } else if (editor.isInline(node)) {\n            match = n => Text.isText(n) || Editor.isInline(editor, n)\n          } else {\n            match = n => Editor.isBlock(editor, n)\n          }\n        }\n\n        const [entry] = Editor.nodes(editor, {\n          at: at.path,\n          match,\n          mode,\n          voids,\n        })\n\n        if (entry) {\n          const [, matchPath] = entry\n          const pathRef = Editor.pathRef(editor, matchPath)\n          const isAtEnd = Editor.isEnd(editor, at, matchPath)\n          Transforms.splitNodes(editor, { at, match, mode, voids })\n          const path = pathRef.unref()!\n          at = isAtEnd ? Path.next(path) : path\n        } else {\n          return\n        }\n      }\n\n      const parentPath = Path.parent(at)\n      let index = at[at.length - 1]\n\n      if (!voids && Editor.void(editor, { at: parentPath })) {\n        return\n      }\n\n      for (const node of nodes) {\n        const path = parentPath.concat(index)\n        index++\n        editor.apply({ type: 'insert_node', path, node })\n        at = Path.next(at)\n      }\n      at = Path.previous(at)\n\n      if (select) {\n        const point = Editor.end(editor, at)\n\n        if (point) {\n          Transforms.select(editor, point)\n        }\n      }\n    })\n  },\n\n  /**\n   * Lift nodes at a specific location upwards in the document tree, splitting\n   * their parent in two if necessary.\n   */\n\n  liftNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { at = editor.selection, mode = 'lowest', voids = false } = options\n      let { match } = options\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (!at) {\n        return\n      }\n\n      const matches = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(matches, ([, p]) => Editor.pathRef(editor, p))\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n\n        if (path.length < 2) {\n          throw new Error(\n            `Cannot lift node at a path [${path}] because it has a depth of less than \\`2\\`.`\n          )\n        }\n\n        const parentNodeEntry = Editor.node(editor, Path.parent(path))\n        const [parent, parentPath] = parentNodeEntry as NodeEntry<Ancestor>\n        const index = path[path.length - 1]\n        const { length } = parent.children\n\n        if (length === 1) {\n          const toPath = Path.next(parentPath)\n          Transforms.moveNodes(editor, { at: path, to: toPath, voids })\n          Transforms.removeNodes(editor, { at: parentPath, voids })\n        } else if (index === 0) {\n          Transforms.moveNodes(editor, { at: path, to: parentPath, voids })\n        } else if (index === length - 1) {\n          const toPath = Path.next(parentPath)\n          Transforms.moveNodes(editor, { at: path, to: toPath, voids })\n        } else {\n          const splitPath = Path.next(path)\n          const toPath = Path.next(parentPath)\n          Transforms.splitNodes(editor, { at: splitPath, voids })\n          Transforms.moveNodes(editor, { at: path, to: toPath, voids })\n        }\n      }\n    })\n  },\n\n  /**\n   * Merge a node at a location with the previous node of the same depth,\n   * removing any empty containing nodes after the merge if necessary.\n   */\n\n  mergeNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      let { match, at = editor.selection } = options\n      const { hanging = false, voids = false, mode = 'lowest' } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        if (Path.isPath(at)) {\n          const [parent] = Editor.parent(editor, at)\n          match = n => parent.children.includes(n)\n        } else {\n          match = n => Editor.isBlock(editor, n)\n        }\n      }\n\n      if (!hanging && Range.isRange(at)) {\n        at = Editor.unhangRange(editor, at)\n      }\n\n      if (Range.isRange(at)) {\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const [, end] = Range.edges(at)\n          const pointRef = Editor.pointRef(editor, end)\n          Transforms.delete(editor, { at })\n          at = pointRef.unref()!\n\n          if (options.at == null) {\n            Transforms.select(editor, at)\n          }\n        }\n      }\n\n      const [current] = Editor.nodes(editor, { at, match, voids, mode })\n      const prev = Editor.previous(editor, { at, match, voids, mode })\n\n      if (!current || !prev) {\n        return\n      }\n\n      const [node, path] = current\n      const [prevNode, prevPath] = prev\n\n      if (path.length === 0 || prevPath.length === 0) {\n        return\n      }\n\n      const newPath = Path.next(prevPath)\n      const commonPath = Path.common(path, prevPath)\n      const isPreviousSibling = Path.isSibling(path, prevPath)\n      const levels = Array.from(Editor.levels(editor, { at: path }), ([n]) => n)\n        .slice(commonPath.length)\n        .slice(0, -1)\n\n      // Determine if the merge will leave an ancestor of the path empty as a\n      // result, in which case we'll want to remove it after merging.\n      const emptyAncestor = Editor.above(editor, {\n        at: path,\n        mode: 'highest',\n        match: n => levels.includes(n) && hasSingleChildNest(editor, n),\n      })\n\n      const emptyRef = emptyAncestor && Editor.pathRef(editor, emptyAncestor[1])\n      let properties\n      let position\n\n      // Ensure that the nodes are equivalent, and figure out what the position\n      // and extra properties of the merge will be.\n      if (Text.isText(node) && Text.isText(prevNode)) {\n        const { text, ...rest } = node\n        position = prevNode.text.length\n        properties = rest as Partial<Text>\n      } else if (Element.isElement(node) && Element.isElement(prevNode)) {\n        const { children, ...rest } = node\n        position = prevNode.children.length\n        properties = rest as Partial<Element>\n      } else {\n        throw new Error(\n          `Cannot merge the node at path [${path}] with the previous sibling because it is not the same kind: ${JSON.stringify(\n            node\n          )} ${JSON.stringify(prevNode)}`\n        )\n      }\n\n      // If the node isn't already the next sibling of the previous node, move\n      // it so that it is before merging.\n      if (!isPreviousSibling) {\n        Transforms.moveNodes(editor, { at: path, to: newPath, voids })\n      }\n\n      // If there was going to be an empty ancestor of the node that was merged,\n      // we remove it from the tree.\n      if (emptyRef) {\n        Transforms.removeNodes(editor, { at: emptyRef.current!, voids })\n      }\n\n      // If the target node that we're merging with is empty, remove it instead\n      // of merging the two. This is a common rich text editor behavior to\n      // prevent losing formatting when deleting entire nodes when you have a\n      // hanging selection.\n      // if prevNode is first child in parent,don't remove it.\n      if (\n        (Element.isElement(prevNode) && Editor.isEmpty(editor, prevNode)) ||\n        (Text.isText(prevNode) &&\n          prevNode.text === '' &&\n          prevPath[prevPath.length - 1] !== 0)\n      ) {\n        Transforms.removeNodes(editor, { at: prevPath, voids })\n      } else {\n        editor.apply({\n          type: 'merge_node',\n          path: newPath,\n          position,\n          properties,\n        })\n      }\n\n      if (emptyRef) {\n        emptyRef.unref()\n      }\n    })\n  },\n\n  /**\n   * Move the nodes at a location to a new location.\n   */\n\n  moveNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      to: Path\n      voids?: boolean\n    }\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const {\n        to,\n        at = editor.selection,\n        mode = 'lowest',\n        voids = false,\n      } = options\n      let { match } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      const toRef = Editor.pathRef(editor, to)\n      const targets = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(targets, ([, p]) => Editor.pathRef(editor, p))\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n        const newPath = toRef.current!\n\n        if (path.length !== 0) {\n          editor.apply({ type: 'move_node', path, newPath })\n        }\n\n        if (\n          toRef.current &&\n          Path.isSibling(newPath, path) &&\n          Path.isAfter(newPath, path)\n        ) {\n          // When performing a sibling move to a later index, the path at the destination is shifted\n          // to before the insertion point instead of after. To ensure our group of nodes are inserted\n          // in the correct order we increment toRef to account for that\n          toRef.current = Path.next(toRef.current)\n        }\n      }\n\n      toRef.unref()\n    })\n  },\n\n  /**\n   * Remove the nodes at a specific location in the document.\n   */\n\n  removeNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { hanging = false, voids = false, mode = 'lowest' } = options\n      let { at = editor.selection, match } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (!hanging && Range.isRange(at)) {\n        at = Editor.unhangRange(editor, at)\n      }\n\n      const depths = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(depths, ([, p]) => Editor.pathRef(editor, p))\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n\n        if (path) {\n          const [node] = Editor.node(editor, path)\n          editor.apply({ type: 'remove_node', path, node })\n        }\n      }\n    })\n  },\n\n  /**\n   * Set new properties on the nodes at a location.\n   */\n\n  setNodes<T extends Node>(\n    editor: Editor,\n    props: Partial<Node>,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      hanging?: boolean\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      let { match, at = editor.selection } = options\n      const {\n        hanging = false,\n        mode = 'lowest',\n        split = false,\n        voids = false,\n      } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (!hanging && Range.isRange(at)) {\n        at = Editor.unhangRange(editor, at)\n      }\n\n      if (split && Range.isRange(at)) {\n        if (\n          Range.isCollapsed(at) &&\n          Editor.leaf(editor, at.anchor)[0].text.length > 0\n        ) {\n          // If the range is collapsed in a non-empty node and 'split' is true, there's nothing to\n          // set that won't get normalized away\n          return\n        }\n        const rangeRef = Editor.rangeRef(editor, at, { affinity: 'inward' })\n        const [start, end] = Range.edges(at)\n        const splitMode = mode === 'lowest' ? 'lowest' : 'highest'\n        const endAtEndOfNode = Editor.isEnd(editor, end, end.path)\n        Transforms.splitNodes(editor, {\n          at: end,\n          match,\n          mode: splitMode,\n          voids,\n          always: !endAtEndOfNode,\n        })\n        const startAtStartOfNode = Editor.isStart(editor, start, start.path)\n        Transforms.splitNodes(editor, {\n          at: start,\n          match,\n          mode: splitMode,\n          voids,\n          always: !startAtStartOfNode,\n        })\n        at = rangeRef.unref()!\n\n        if (options.at == null) {\n          Transforms.select(editor, at)\n        }\n      }\n\n      for (const [node, path] of Editor.nodes(editor, {\n        at,\n        match,\n        mode,\n        voids,\n      })) {\n        const properties: Partial<Node> = {}\n        const newProperties: Partial<Node> = {}\n\n        // You can't set properties on the editor node.\n        if (path.length === 0) {\n          continue\n        }\n\n        let hasChanges = false\n\n        for (const k in props) {\n          if (k === 'children' || k === 'text') {\n            continue\n          }\n\n          if (props[k] !== node[k]) {\n            hasChanges = true\n            // Omit new properties from the old properties list\n            if (node.hasOwnProperty(k)) properties[k] = node[k]\n            // Omit properties that have been removed from the new properties list\n            if (props[k] != null) newProperties[k] = props[k]\n          }\n        }\n\n        if (hasChanges) {\n          editor.apply({\n            type: 'set_node',\n            path,\n            properties,\n            newProperties,\n          })\n        }\n      }\n    })\n  },\n\n  /**\n   * Split the nodes at a specific location.\n   */\n\n  splitNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'highest' | 'lowest'\n      always?: boolean\n      height?: number\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { mode = 'lowest', voids = false } = options\n      let { match, at = editor.selection, height = 0, always = false } = options\n\n      if (match == null) {\n        match = n => Editor.isBlock(editor, n)\n      }\n\n      if (Range.isRange(at)) {\n        at = deleteRange(editor, at)\n      }\n\n      // If the target is a path, the default height-skipping and position\n      // counters need to account for us potentially splitting at a non-leaf.\n      if (Path.isPath(at)) {\n        const path = at\n        const point = Editor.point(editor, path)\n        const [parent] = Editor.parent(editor, path)\n        match = n => n === parent\n        height = point.path.length - path.length + 1\n        at = point\n        always = true\n      }\n\n      if (!at) {\n        return\n      }\n\n      const beforeRef = Editor.pointRef(editor, at, {\n        affinity: 'backward',\n      })\n      const [highest] = Editor.nodes(editor, { at, match, mode, voids })\n\n      if (!highest) {\n        return\n      }\n\n      const voidMatch = Editor.void(editor, { at, mode: 'highest' })\n      const nudge = 0\n\n      if (!voids && voidMatch) {\n        const [voidNode, voidPath] = voidMatch\n\n        if (Element.isElement(voidNode) && editor.isInline(voidNode)) {\n          let after = Editor.after(editor, voidPath)\n\n          if (!after) {\n            const text = { text: '' }\n            const afterPath = Path.next(voidPath)\n            Transforms.insertNodes(editor, text, { at: afterPath, voids })\n            after = Editor.point(editor, afterPath)!\n          }\n\n          at = after\n          always = true\n        }\n\n        const siblingHeight = at.path.length - voidPath.length\n        height = siblingHeight + 1\n        always = true\n      }\n\n      const afterRef = Editor.pointRef(editor, at)\n      const depth = at.path.length - height\n      const [, highestPath] = highest\n      const lowestPath = at.path.slice(0, depth)\n      let position = height === 0 ? at.offset : at.path[depth] + nudge\n\n      for (const [node, path] of Editor.levels(editor, {\n        at: lowestPath,\n        reverse: true,\n        voids,\n      })) {\n        let split = false\n\n        if (\n          path.length < highestPath.length ||\n          path.length === 0 ||\n          (!voids && Editor.isVoid(editor, node))\n        ) {\n          break\n        }\n\n        const point = beforeRef.current!\n        const isEnd = Editor.isEnd(editor, point, path)\n\n        if (always || !beforeRef || !Editor.isEdge(editor, point, path)) {\n          split = true\n          const properties = Node.extractProps(node)\n          editor.apply({\n            type: 'split_node',\n            path,\n            position,\n            properties,\n          })\n        }\n\n        position = path[path.length - 1] + (split || isEnd ? 1 : 0)\n      }\n\n      if (options.at == null) {\n        const point = afterRef.current || Editor.end(editor, [])\n        Transforms.select(editor, point)\n      }\n\n      beforeRef.unref()\n      afterRef.unref()\n    })\n  },\n\n  /**\n   * Unset properties on the nodes at a location.\n   */\n\n  unsetNodes<T extends Node>(\n    editor: Editor,\n    props: string | string[],\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    if (!Array.isArray(props)) {\n      props = [props]\n    }\n\n    const obj = {}\n\n    for (const key of props) {\n      obj[key] = null\n    }\n\n    Transforms.setNodes(editor, obj, options)\n  },\n\n  /**\n   * Unwrap the nodes at a location from a parent node, splitting the parent if\n   * necessary to ensure that only the content in the range is unwrapped.\n   */\n\n  unwrapNodes<T extends Node>(\n    editor: Editor,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { mode = 'lowest', split = false, voids = false } = options\n      let { at = editor.selection, match } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        match = Path.isPath(at)\n          ? matchPath(editor, at)\n          : n => Editor.isBlock(editor, n)\n      }\n\n      if (Path.isPath(at)) {\n        at = Editor.range(editor, at)\n      }\n\n      const rangeRef = Range.isRange(at) ? Editor.rangeRef(editor, at) : null\n      const matches = Editor.nodes(editor, { at, match, mode, voids })\n      const pathRefs = Array.from(\n        matches,\n        ([, p]) => Editor.pathRef(editor, p)\n        // unwrapNode will call liftNode which does not support splitting the node when nested.\n        // If we do not reverse the order and call it from top to the bottom, it will remove all blocks\n        // that wrap target node. So we reverse the order.\n      ).reverse()\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n        const [node] = Editor.node(editor, path)\n        let range = Editor.range(editor, path)\n\n        if (split && rangeRef) {\n          range = Range.intersection(rangeRef.current!, range)!\n        }\n\n        Transforms.liftNodes(editor, {\n          at: range,\n          match: n => Element.isAncestor(node) && node.children.includes(n),\n          voids,\n        })\n      }\n\n      if (rangeRef) {\n        rangeRef.unref()\n      }\n    })\n  },\n\n  /**\n   * Wrap the nodes at a location in a new container node, splitting the edges\n   * of the range first to ensure that only the content in the range is wrapped.\n   */\n\n  wrapNodes<T extends Node>(\n    editor: Editor,\n    element: Element,\n    options: {\n      at?: Location\n      match?: NodeMatch<T>\n      mode?: 'all' | 'highest' | 'lowest'\n      split?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { mode = 'lowest', split = false, voids = false } = options\n      let { match, at = editor.selection } = options\n\n      if (!at) {\n        return\n      }\n\n      if (match == null) {\n        if (Path.isPath(at)) {\n          match = matchPath(editor, at)\n        } else if (editor.isInline(element)) {\n          match = n => Editor.isInline(editor, n) || Text.isText(n)\n        } else {\n          match = n => Editor.isBlock(editor, n)\n        }\n      }\n\n      if (split && Range.isRange(at)) {\n        const [start, end] = Range.edges(at)\n        const rangeRef = Editor.rangeRef(editor, at, {\n          affinity: 'inward',\n        })\n        Transforms.splitNodes(editor, { at: end, match, voids })\n        Transforms.splitNodes(editor, { at: start, match, voids })\n        at = rangeRef.unref()!\n\n        if (options.at == null) {\n          Transforms.select(editor, at)\n        }\n      }\n\n      const roots = Array.from(\n        Editor.nodes(editor, {\n          at,\n          match: editor.isInline(element)\n            ? n => Editor.isBlock(editor, n)\n            : n => Editor.isEditor(n),\n          mode: 'lowest',\n          voids,\n        })\n      )\n\n      for (const [, rootPath] of roots) {\n        const a = Range.isRange(at)\n          ? Range.intersection(at, Editor.range(editor, rootPath))\n          : at\n\n        if (!a) {\n          continue\n        }\n\n        const matches = Array.from(\n          Editor.nodes(editor, { at: a, match, mode, voids })\n        )\n\n        if (matches.length > 0) {\n          const [first] = matches\n          const last = matches[matches.length - 1]\n          const [, firstPath] = first\n          const [, lastPath] = last\n\n          if (firstPath.length === 0 && lastPath.length === 0) {\n            // if there's no matching parent - usually means the node is an editor - don't do anything\n            continue\n          }\n\n          const commonPath = Path.equals(firstPath, lastPath)\n            ? Path.parent(firstPath)\n            : Path.common(firstPath, lastPath)\n\n          const range = Editor.range(editor, firstPath, lastPath)\n          const commonNodeEntry = Editor.node(editor, commonPath)\n          const [commonNode] = commonNodeEntry\n          const depth = commonPath.length + 1\n          const wrapperPath = Path.next(lastPath.slice(0, depth))\n          const wrapper = { ...element, children: [] }\n          Transforms.insertNodes(editor, wrapper, { at: wrapperPath, voids })\n\n          Transforms.moveNodes(editor, {\n            at: range,\n            match: n =>\n              Element.isAncestor(commonNode) && commonNode.children.includes(n),\n            to: wrapperPath.concat(0),\n            voids,\n          })\n        }\n      }\n    })\n  },\n}\n\nconst hasSingleChildNest = (editor: Editor, node: Node): boolean => {\n  if (Element.isElement(node)) {\n    const element = node as Element\n    if (Editor.isVoid(editor, node)) {\n      return true\n    } else if (element.children.length === 1) {\n      return hasSingleChildNest(editor, element.children[0])\n    } else {\n      return false\n    }\n  } else if (Editor.isEditor(node)) {\n    return false\n  } else {\n    return true\n  }\n}\n\n/**\n * Convert a range into a point by deleting it's content.\n */\n\nconst deleteRange = (editor: Editor, range: Range): Point | null => {\n  if (Range.isCollapsed(range)) {\n    return range.anchor\n  } else {\n    const [, end] = Range.edges(range)\n    const pointRef = Editor.pointRef(editor, end)\n    Transforms.delete(editor, { at: range })\n    return pointRef.unref()\n  }\n}\n\nconst matchPath = (editor: Editor, path: Path): ((node: Node) => boolean) => {\n  const [node] = Editor.node(editor, path)\n  return n => n === node\n}\n", "import { Editor, Location, Point, Range, Transforms } from '..'\n\nexport interface SelectionTransforms {\n  collapse: (\n    editor: Editor,\n    options?: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    }\n  ) => void\n  deselect: (editor: Editor) => void\n  move: (\n    editor: Editor,\n    options?: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line'\n      reverse?: boolean\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    }\n  ) => void\n  select: (editor: Editor, target: Location) => void\n  setPoint: (\n    editor: Editor,\n    props: Partial<Point>,\n    options?: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    }\n  ) => void\n  setSelection: (editor: Editor, props: Partial<Range>) => void\n}\n\nexport const SelectionTransforms: SelectionTransforms = {\n  /**\n   * Collapse the selection.\n   */\n\n  collapse(\n    editor: Editor,\n    options: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    } = {}\n  ): void {\n    const { edge = 'anchor' } = options\n    const { selection } = editor\n\n    if (!selection) {\n      return\n    } else if (edge === 'anchor') {\n      Transforms.select(editor, selection.anchor)\n    } else if (edge === 'focus') {\n      Transforms.select(editor, selection.focus)\n    } else if (edge === 'start') {\n      const [start] = Range.edges(selection)\n      Transforms.select(editor, start)\n    } else if (edge === 'end') {\n      const [, end] = Range.edges(selection)\n      Transforms.select(editor, end)\n    }\n  },\n\n  /**\n   * Unset the selection.\n   */\n\n  deselect(editor: Editor): void {\n    const { selection } = editor\n\n    if (selection) {\n      editor.apply({\n        type: 'set_selection',\n        properties: selection,\n        newProperties: null,\n      })\n    }\n  },\n\n  /**\n   * Move the selection's point forward or backward.\n   */\n\n  move(\n    editor: Editor,\n    options: {\n      distance?: number\n      unit?: 'offset' | 'character' | 'word' | 'line'\n      reverse?: boolean\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    } = {}\n  ): void {\n    const { selection } = editor\n    const { distance = 1, unit = 'character', reverse = false } = options\n    let { edge = null } = options\n\n    if (!selection) {\n      return\n    }\n\n    if (edge === 'start') {\n      edge = Range.isBackward(selection) ? 'focus' : 'anchor'\n    }\n\n    if (edge === 'end') {\n      edge = Range.isBackward(selection) ? 'anchor' : 'focus'\n    }\n\n    const { anchor, focus } = selection\n    const opts = { distance, unit }\n    const props: Partial<Range> = {}\n\n    if (edge == null || edge === 'anchor') {\n      const point = reverse\n        ? Editor.before(editor, anchor, opts)\n        : Editor.after(editor, anchor, opts)\n\n      if (point) {\n        props.anchor = point\n      }\n    }\n\n    if (edge == null || edge === 'focus') {\n      const point = reverse\n        ? Editor.before(editor, focus, opts)\n        : Editor.after(editor, focus, opts)\n\n      if (point) {\n        props.focus = point\n      }\n    }\n\n    Transforms.setSelection(editor, props)\n  },\n\n  /**\n   * Set the selection to a new value.\n   */\n\n  select(editor: Editor, target: Location): void {\n    const { selection } = editor\n    target = Editor.range(editor, target)\n\n    if (selection) {\n      Transforms.setSelection(editor, target)\n      return\n    }\n\n    if (!Range.isRange(target)) {\n      throw new Error(\n        `When setting the selection and the current selection is \\`null\\` you must provide at least an \\`anchor\\` and \\`focus\\`, but you passed: ${JSON.stringify(\n          target\n        )}`\n      )\n    }\n\n    editor.apply({\n      type: 'set_selection',\n      properties: selection,\n      newProperties: target,\n    })\n  },\n\n  /**\n   * Set new properties on one of the selection's points.\n   */\n\n  setPoint(\n    editor: Editor,\n    props: Partial<Point>,\n    options: {\n      edge?: 'anchor' | 'focus' | 'start' | 'end'\n    } = {}\n  ): void {\n    const { selection } = editor\n    let { edge = 'both' } = options\n\n    if (!selection) {\n      return\n    }\n\n    if (edge === 'start') {\n      edge = Range.isBackward(selection) ? 'focus' : 'anchor'\n    }\n\n    if (edge === 'end') {\n      edge = Range.isBackward(selection) ? 'anchor' : 'focus'\n    }\n\n    const { anchor, focus } = selection\n    const point = edge === 'anchor' ? anchor : focus\n\n    Transforms.setSelection(editor, {\n      [edge === 'anchor' ? 'anchor' : 'focus']: { ...point, ...props },\n    })\n  },\n\n  /**\n   * Set new properties on the selection.\n   */\n\n  setSelection(editor: Editor, props: Partial<Range>): void {\n    const { selection } = editor\n    const oldProps: Partial<Range> | null = {}\n    const newProps: Partial<Range> = {}\n\n    if (!selection) {\n      return\n    }\n\n    for (const k in props) {\n      if (\n        (k === 'anchor' &&\n          props.anchor != null &&\n          !Point.equals(props.anchor, selection.anchor)) ||\n        (k === 'focus' &&\n          props.focus != null &&\n          !Point.equals(props.focus, selection.focus)) ||\n        (k !== 'anchor' && k !== 'focus' && props[k] !== selection[k])\n      ) {\n        oldProps[k] = selection[k]\n        newProps[k] = props[k]\n      }\n    }\n\n    if (Object.keys(oldProps).length > 0) {\n      editor.apply({\n        type: 'set_selection',\n        properties: oldProps,\n        newProperties: newProps,\n      })\n    }\n  },\n}\n", "import {\n  Editor,\n  Element,\n  Location,\n  Node,\n  NodeEntry,\n  Path,\n  Text,\n  Point,\n  Range,\n  Transforms,\n} from '..'\n\nexport interface TextTransforms {\n  delete: (\n    editor: Editor,\n    options?: {\n      at?: Location\n      distance?: number\n      unit?: 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  insertFragment: (\n    editor: Editor,\n    fragment: Node[],\n    options?: {\n      at?: Location\n      hanging?: boolean\n      voids?: boolean\n    }\n  ) => void\n  insertText: (\n    editor: Editor,\n    text: string,\n    options?: {\n      at?: Location\n      voids?: boolean\n    }\n  ) => void\n}\n\nexport const TextTransforms: TextTransforms = {\n  /**\n   * Delete content in the editor.\n   */\n\n  delete(\n    editor: Editor,\n    options: {\n      at?: Location\n      distance?: number\n      unit?: 'character' | 'word' | 'line' | 'block'\n      reverse?: boolean\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const {\n        reverse = false,\n        unit = 'character',\n        distance = 1,\n        voids = false,\n      } = options\n      let { at = editor.selection, hanging = false } = options\n\n      if (!at) {\n        return\n      }\n\n      if (Range.isRange(at) && Range.isCollapsed(at)) {\n        at = at.anchor\n      }\n\n      if (Point.isPoint(at)) {\n        const furthestVoid = Editor.void(editor, { at, mode: 'highest' })\n\n        if (!voids && furthestVoid) {\n          const [, voidPath] = furthestVoid\n          at = voidPath\n        } else {\n          const opts = { unit, distance }\n          const target = reverse\n            ? Editor.before(editor, at, opts) || Editor.start(editor, [])\n            : Editor.after(editor, at, opts) || Editor.end(editor, [])\n          at = { anchor: at, focus: target }\n          hanging = true\n        }\n      }\n\n      if (Path.isPath(at)) {\n        Transforms.removeNodes(editor, { at, voids })\n        return\n      }\n\n      if (Range.isCollapsed(at)) {\n        return\n      }\n\n      if (!hanging) {\n        const [, end] = Range.edges(at)\n        const endOfDoc = Editor.end(editor, [])\n\n        if (!Point.equals(end, endOfDoc)) {\n          at = Editor.unhangRange(editor, at, { voids })\n        }\n      }\n\n      let [start, end] = Range.edges(at)\n      const startBlock = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n        at: start,\n        voids,\n      })\n      const endBlock = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n        at: end,\n        voids,\n      })\n      const isAcrossBlocks =\n        startBlock && endBlock && !Path.equals(startBlock[1], endBlock[1])\n      const isSingleText = Path.equals(start.path, end.path)\n      const startVoid = voids\n        ? null\n        : Editor.void(editor, { at: start, mode: 'highest' })\n      const endVoid = voids\n        ? null\n        : Editor.void(editor, { at: end, mode: 'highest' })\n\n      // If the start or end points are inside an inline void, nudge them out.\n      if (startVoid) {\n        const before = Editor.before(editor, start)\n\n        if (\n          before &&\n          startBlock &&\n          Path.isAncestor(startBlock[1], before.path)\n        ) {\n          start = before\n        }\n      }\n\n      if (endVoid) {\n        const after = Editor.after(editor, end)\n\n        if (after && endBlock && Path.isAncestor(endBlock[1], after.path)) {\n          end = after\n        }\n      }\n\n      // Get the highest nodes that are completely inside the range, as well as\n      // the start and end nodes.\n      const matches: NodeEntry[] = []\n      let lastPath: Path | undefined\n\n      for (const entry of Editor.nodes(editor, { at, voids })) {\n        const [node, path] = entry\n\n        if (lastPath && Path.compare(path, lastPath) === 0) {\n          continue\n        }\n\n        if (\n          (!voids && Editor.isVoid(editor, node)) ||\n          (!Path.isCommon(path, start.path) && !Path.isCommon(path, end.path))\n        ) {\n          matches.push(entry)\n          lastPath = path\n        }\n      }\n\n      const pathRefs = Array.from(matches, ([, p]) => Editor.pathRef(editor, p))\n      const startRef = Editor.pointRef(editor, start)\n      const endRef = Editor.pointRef(editor, end)\n\n      if (!isSingleText && !startVoid) {\n        const point = startRef.current!\n        const [node] = Editor.leaf(editor, point)\n        const { path } = point\n        const { offset } = start\n        const text = node.text.slice(offset)\n        if (text.length > 0)\n          editor.apply({ type: 'remove_text', path, offset, text })\n      }\n\n      for (const pathRef of pathRefs) {\n        const path = pathRef.unref()!\n        Transforms.removeNodes(editor, { at: path, voids })\n      }\n\n      if (!endVoid) {\n        const point = endRef.current!\n        const [node] = Editor.leaf(editor, point)\n        const { path } = point\n        const offset = isSingleText ? start.offset : 0\n        const text = node.text.slice(offset, end.offset)\n        if (text.length > 0)\n          editor.apply({ type: 'remove_text', path, offset, text })\n      }\n\n      if (\n        !isSingleText &&\n        isAcrossBlocks &&\n        endRef.current &&\n        startRef.current\n      ) {\n        Transforms.mergeNodes(editor, {\n          at: endRef.current,\n          hanging: true,\n          voids,\n        })\n      }\n\n      const point = reverse\n        ? startRef.unref() || endRef.unref()\n        : endRef.unref() || startRef.unref()\n\n      if (options.at == null && point) {\n        Transforms.select(editor, point)\n      }\n    })\n  },\n\n  /**\n   * Insert a fragment at a specific location in the editor.\n   */\n\n  insertFragment(\n    editor: Editor,\n    fragment: Node[],\n    options: {\n      at?: Location\n      hanging?: boolean\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { hanging = false, voids = false } = options\n      let { at = editor.selection } = options\n\n      if (!fragment.length) {\n        return\n      }\n\n      if (!at) {\n        return\n      } else if (Range.isRange(at)) {\n        if (!hanging) {\n          at = Editor.unhangRange(editor, at)\n        }\n\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const [, end] = Range.edges(at)\n\n          if (!voids && Editor.void(editor, { at: end })) {\n            return\n          }\n\n          const pointRef = Editor.pointRef(editor, end)\n          Transforms.delete(editor, { at })\n          at = pointRef.unref()!\n        }\n      } else if (Path.isPath(at)) {\n        at = Editor.start(editor, at)\n      }\n\n      if (!voids && Editor.void(editor, { at })) {\n        return\n      }\n\n      // If the insert point is at the edge of an inline node, move it outside\n      // instead since it will need to be split otherwise.\n      const inlineElementMatch = Editor.above(editor, {\n        at,\n        match: n => Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })\n\n      if (inlineElementMatch) {\n        const [, inlinePath] = inlineElementMatch\n\n        if (Editor.isEnd(editor, at, inlinePath)) {\n          const after = Editor.after(editor, inlinePath)!\n          at = after\n        } else if (Editor.isStart(editor, at, inlinePath)) {\n          const before = Editor.before(editor, inlinePath)!\n          at = before\n        }\n      }\n\n      const blockMatch = Editor.above(editor, {\n        match: n => Editor.isBlock(editor, n),\n        at,\n        voids,\n      })!\n      const [, blockPath] = blockMatch\n      const isBlockStart = Editor.isStart(editor, at, blockPath)\n      const isBlockEnd = Editor.isEnd(editor, at, blockPath)\n      const isBlockEmpty = isBlockStart && isBlockEnd\n      const mergeStart = !isBlockStart || (isBlockStart && isBlockEnd)\n      const mergeEnd = !isBlockEnd\n      const [, firstPath] = Node.first({ children: fragment }, [])\n      const [, lastPath] = Node.last({ children: fragment }, [])\n\n      const matches: NodeEntry[] = []\n      const matcher = ([n, p]: NodeEntry) => {\n        const isRoot = p.length === 0\n        if (isRoot) {\n          return false\n        }\n\n        if (isBlockEmpty) {\n          return true\n        }\n\n        if (\n          mergeStart &&\n          Path.isAncestor(p, firstPath) &&\n          Element.isElement(n) &&\n          !editor.isVoid(n) &&\n          !editor.isInline(n)\n        ) {\n          return false\n        }\n\n        if (\n          mergeEnd &&\n          Path.isAncestor(p, lastPath) &&\n          Element.isElement(n) &&\n          !editor.isVoid(n) &&\n          !editor.isInline(n)\n        ) {\n          return false\n        }\n\n        return true\n      }\n\n      for (const entry of Node.nodes(\n        { children: fragment },\n        { pass: matcher }\n      )) {\n        if (matcher(entry)) {\n          matches.push(entry)\n        }\n      }\n\n      const starts = []\n      const middles = []\n      const ends = []\n      let starting = true\n      let hasBlocks = false\n\n      for (const [node] of matches) {\n        if (Element.isElement(node) && !editor.isInline(node)) {\n          starting = false\n          hasBlocks = true\n          middles.push(node)\n        } else if (starting) {\n          starts.push(node)\n        } else {\n          ends.push(node)\n        }\n      }\n\n      const [inlineMatch] = Editor.nodes(editor, {\n        at,\n        match: n => Text.isText(n) || Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })!\n\n      const [, inlinePath] = inlineMatch\n      const isInlineStart = Editor.isStart(editor, at, inlinePath)\n      const isInlineEnd = Editor.isEnd(editor, at, inlinePath)\n\n      const middleRef = Editor.pathRef(\n        editor,\n        isBlockEnd ? Path.next(blockPath) : blockPath\n      )\n\n      const endRef = Editor.pathRef(\n        editor,\n        isInlineEnd ? Path.next(inlinePath) : inlinePath\n      )\n\n      const blockPathRef = Editor.pathRef(editor, blockPath)\n\n      Transforms.splitNodes(editor, {\n        at,\n        match: n =>\n          hasBlocks\n            ? Editor.isBlock(editor, n)\n            : Text.isText(n) || Editor.isInline(editor, n),\n        mode: hasBlocks ? 'lowest' : 'highest',\n        voids,\n      })\n\n      const startRef = Editor.pathRef(\n        editor,\n        !isInlineStart || (isInlineStart && isInlineEnd)\n          ? Path.next(inlinePath)\n          : inlinePath\n      )\n\n      Transforms.insertNodes(editor, starts, {\n        at: startRef.current!,\n        match: n => Text.isText(n) || Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })\n\n      if (isBlockEmpty && middles.length) {\n        Transforms.delete(editor, { at: blockPathRef.unref()!, voids })\n      }\n\n      Transforms.insertNodes(editor, middles, {\n        at: middleRef.current!,\n        match: n => Editor.isBlock(editor, n),\n        mode: 'lowest',\n        voids,\n      })\n\n      Transforms.insertNodes(editor, ends, {\n        at: endRef.current!,\n        match: n => Text.isText(n) || Editor.isInline(editor, n),\n        mode: 'highest',\n        voids,\n      })\n\n      if (!options.at) {\n        let path\n\n        if (ends.length > 0) {\n          path = Path.previous(endRef.current!)\n        } else if (middles.length > 0) {\n          path = Path.previous(middleRef.current!)\n        } else {\n          path = Path.previous(startRef.current!)\n        }\n\n        const end = Editor.end(editor, path)\n        Transforms.select(editor, end)\n      }\n\n      startRef.unref()\n      middleRef.unref()\n      endRef.unref()\n    })\n  },\n\n  /**\n   * Insert a string of text in the Editor.\n   */\n\n  insertText(\n    editor: Editor,\n    text: string,\n    options: {\n      at?: Location\n      voids?: boolean\n    } = {}\n  ): void {\n    Editor.withoutNormalizing(editor, () => {\n      const { voids = false } = options\n      let { at = editor.selection } = options\n\n      if (!at) {\n        return\n      }\n\n      if (Path.isPath(at)) {\n        at = Editor.range(editor, at)\n      }\n\n      if (Range.isRange(at)) {\n        if (Range.isCollapsed(at)) {\n          at = at.anchor\n        } else {\n          const end = Range.end(at)\n          if (!voids && Editor.void(editor, { at: end })) {\n            return\n          }\n          const start = Range.start(at)\n          const pointRef = Editor.pointRef(editor, start)\n          Transforms.delete(editor, { at, voids })\n          at = pointRef.unref()!\n          Transforms.setSelection(editor, { anchor: at, focus: at })\n        }\n      }\n\n      if (!voids && Editor.void(editor, { at })) {\n        return\n      }\n\n      const { path, offset } = at\n      if (text.length > 0)\n        editor.apply({ type: 'insert_text', path, offset, text })\n    })\n  },\n}\n", "import { GeneralTransforms } from './general'\nimport { NodeTransforms } from './node'\nimport { SelectionTransforms } from './selection'\nimport { TextTransforms } from './text'\n\nexport const Transforms: GeneralTransforms &\n  NodeTransforms &\n  SelectionTransforms &\n  TextTransforms = {\n  ...GeneralTransforms,\n  ...NodeTransforms,\n  ...SelectionTransforms,\n  ...TextTransforms,\n}\n"], "names": ["DIRTY_PATHS", "WeakMap", "DIRTY_PATH_KEYS", "FLUSHING", "NORMALIZING", "PATH_REFS", "POINT_REFS", "RANGE_REFS", "createEditor", "editor", "children", "operations", "selection", "marks", "isInline", "isVoid", "onChange", "apply", "op", "ref", "Editor", "pathRefs", "PathRef", "transform", "pointRefs", "PointRef", "rangeRefs", "RangeRef", "old<PERSON><PERSON><PERSON><PERSON><PERSON>s", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "dirtyPaths", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "path", "key", "join", "has", "push", "Path", "operationCanTransformPath", "newPath", "newDirty<PERSON><PERSON><PERSON>", "getDirty<PERSON><PERSON>s", "set", "Transforms", "normalize", "type", "Promise", "resolve", "then", "addMark", "value", "Range", "isExpanded", "setNodes", "match", "Text", "isText", "split", "deleteBackward", "unit", "isCollapsed", "delete", "reverse", "deleteForward", "deleteFragment", "direction", "getFragment", "Node", "fragment", "insertBreak", "splitNodes", "always", "insertFragment", "insertNode", "node", "insertNodes", "insertText", "text", "normalizeNode", "entry", "Element", "isElement", "length", "child", "at", "concat", "voids", "shouldHaveInlines", "isEditor", "n", "i", "currentNode", "prev", "isLast", "isInlineOrText", "removeNodes", "<PERSON><PERSON><PERSON><PERSON>", "equals", "loose", "mergeNodes", "removeMark", "unsetNodes", "levels", "descendants", "Array", "from", "nodes", "p", "ancestors", "previousPath", "previous", "oldAncestors", "newAncestors", "ancestor", "newParent", "newIndex", "resultPath", "nextPath", "next", "objectWithoutPropertiesLoose", "getCharacterDistance", "str", "isRTL", "isLTR", "codepoints", "codepointsIteratorRTL", "left", "CodepointType", "None", "right", "distance", "gb11", "gb12Or13", "char", "code", "codePointAt", "getCodepointType", "intersects", "ZWJ", "ExtPict", "endsWithEmojiZWJ", "substring", "RI", "endsWithOddNumberOfRIs", "isBoundaryPair", "SPACE", "PUNCTUATION", "CHAMELEON", "getWordDistance", "dist", "started", "charDist", "remaining", "splitByCharacterDistance", "isWordCharacter", "slice", "test", "nextChar", "nextRemaining", "end", "char1", "char<PERSON>t", "isLowSurrogate", "charCodeAt", "char2", "isHighSurrogate", "charCode", "reExtend", "rePrepend", "reSpacingMark", "reL", "reV", "reT", "reLV", "reLVT", "reExtPict", "Any", "search", "Extend", "Prepend", "SpacingMark", "L", "V", "T", "LV", "LVT", "x", "y", "NonBoundaryPairs", "findIndex", "r", "endingEmojiZWJ", "endingRIs", "numRIs", "isPlainObject", "isNodeList", "isAncestor", "isElementList", "isArray", "every", "val", "isElementProps", "props", "undefined", "isElementType", "elementVal", "elementKey", "matches", "element", "IS_EDITOR_CACHE", "above", "options", "mode", "after", "anchor", "point", "edge", "focus", "range", "d", "target", "positions", "before", "start", "edges", "first", "hasBlocks", "some", "isBlock", "hasInlines", "hasTexts", "cachedIsEditor", "isRange", "Operation", "isOperationList", "isEnd", "Point", "isEdge", "isStart", "isEmpty", "isNormalizing", "offset", "last", "leaf", "rest", "block", "prevNode", "prevPath", "blockPath", "pointAfterLocation", "to", "span", "isPath", "Error", "parent", "includes", "universal", "Span", "isSpan", "nodeEntries", "pass", "hit", "isLower", "compare", "emit", "force", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popDirtyPath", "pop", "allPaths", "allPathKeys", "map", "withoutNormalizing", "<PERSON><PERSON><PERSON>", "_", "max", "m", "parentPath", "depth", "firstPath", "last<PERSON><PERSON>", "common", "isPoint", "<PERSON><PERSON><PERSON>", "pathRef", "affinity", "current", "unref", "refs", "pointRef", "isNewBlock", "blockText", "leafTextRemaining", "leafTextOffset", "e", "s", "string", "<PERSON><PERSON><PERSON><PERSON>", "calcDistance", "pointBeforeLocation", "rangeRef", "setNormalizing", "t", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "endBlock", "skip", "isBefore", "void", "fn", "Location", "isLocation", "IS_NODE_LIST_CACHE", "root", "index", "JSON", "stringify", "c", "child<PERSON><PERSON>", "another", "descendant", "elements", "extractProps", "properties", "newRoot", "produce", "splice", "isNode", "cachedResult", "isTextProps", "visited", "isAfter", "nextIndex", "texts", "isNodeOperation", "isOperation", "endsWith", "position", "newProperties", "isSelectionOperation", "isTextOperation", "inverse", "is<PERSON><PERSON>ling", "inversePath", "inverseNewPath", "paths", "av", "bv", "min", "Math", "endsAfter", "as", "bs", "endsAt", "endsBefore", "has<PERSON>revious", "<PERSON><PERSON><PERSON><PERSON>", "isCommon", "isDescendant", "isParent", "al", "bl", "list", "operation", "relative", "onp", "copy", "result", "isBackward", "rs", "re", "ts", "te", "isAfterStart", "isBeforeEnd", "intersection", "s1", "e1", "s2", "e2", "isForward", "points", "affinityAnchor", "affinityFocus", "isDeepEqual", "a", "b", "omitText", "obj", "isTextList", "hasOwnProperty", "decorations", "leaves", "dec", "o", "Object", "assign", "middle", "off", "applyToDraft", "truePath", "preferNext", "newNode", "GeneralTransforms", "createDraft", "finishDraft", "isDraft", "NodeTransforms", "hanging", "select", "matchPath", "isAtEnd", "liftNodes", "parentNodeEntry", "to<PERSON><PERSON>", "moveNodes", "splitPath", "commonPath", "isPreviousSibling", "emptyAncestor", "hasSingleChildNest", "emptyRef", "toRef", "targets", "depths", "splitMode", "endAtEndOfNode", "startAtStartOfNode", "has<PERSON><PERSON><PERSON>", "k", "height", "deleteRange", "beforeRef", "highest", "voidMatch", "nudge", "voidNode", "voidPath", "after<PERSON><PERSON>", "siblingHeight", "afterRef", "highestPath", "lowestPath", "unwrapNodes", "wrapNodes", "roots", "rootPath", "commonNodeEntry", "commonNode", "wrapperPath", "wrapper", "SelectionTransforms", "collapse", "deselect", "move", "opts", "setSelection", "setPoint", "oldProps", "newProps", "keys", "TextTransforms", "furthestVoid", "endOfDoc", "startBlock", "isAcrossBlocks", "isSingleText", "startVoid", "endVoid", "startRef", "endRef", "inlineElementMatch", "inlinePath", "blockMatch", "isBlockStart", "isBlockEnd", "isBlockEmpty", "mergeStart", "mergeEnd", "matcher", "isRoot", "starts", "middles", "ends", "starting", "inlineMatch", "isInlineStart", "isInlineEnd", "middleRef", "blockPathRef"], "mappings": ";;;AAAe,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AACzD,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;AAClB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACpC,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC,CAAC;AACP,GAAG,MAAM;AACT,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb;;ACXO,IAAMA,WAAW,GAA4B,IAAIC,OAAJ,EAA7C;AACA,IAAMC,eAAe,GAAiC,IAAID,OAAJ,EAAtD;AACA,IAAME,QAAQ,GAA6B,IAAIF,OAAJ,EAA3C;AACA,IAAMG,WAAW,GAA6B,IAAIH,OAAJ,EAA9C;AACA,IAAMI,SAAS,GAAkC,IAAIJ,OAAJ,EAAjD;AACA,IAAMK,UAAU,GAAmC,IAAIL,OAAJ,EAAnD;AACA,IAAMM,UAAU,GAAmC,IAAIN,OAAJ,EAAnD;;;;;ACSP;;;;IAIaO,YAAY,GAAG;AAC1B,MAAMC,MAAM,GAAW;AACrBC,IAAAA,QAAQ,EAAE,EADW;AAErBC,IAAAA,UAAU,EAAE,EAFS;AAGrBC,IAAAA,SAAS,EAAE,IAHU;AAIrBC,IAAAA,KAAK,EAAE,IAJc;AAKrBC,IAAAA,QAAQ,EAAE,MAAM,KALK;AAMrBC,IAAAA,MAAM,EAAE,MAAM,KANO;AAOrBC,IAAAA,QAAQ,EAAE,QAPW;AASrBC,IAAAA,KAAK,EAAGC,EAAD;AACL,WAAK,IAAMC,GAAX,IAAkBC,MAAM,CAACC,QAAP,CAAgBZ,MAAhB,CAAlB,EAA2C;AACzCa,QAAAA,OAAO,CAACC,SAAR,CAAkBJ,GAAlB,EAAuBD,EAAvB;AACD;;AAED,WAAK,IAAMC,IAAX,IAAkBC,MAAM,CAACI,SAAP,CAAiBf,MAAjB,CAAlB,EAA4C;AAC1CgB,QAAAA,QAAQ,CAACF,SAAT,CAAmBJ,IAAnB,EAAwBD,EAAxB;AACD;;AAED,WAAK,IAAMC,KAAX,IAAkBC,MAAM,CAACM,SAAP,CAAiBjB,MAAjB,CAAlB,EAA4C;AAC1CkB,QAAAA,QAAQ,CAACJ,SAAT,CAAmBJ,KAAnB,EAAwBD,EAAxB;AACD;;AAED,UAAMU,aAAa,GAAG5B,WAAW,CAAC6B,GAAZ,CAAgBpB,MAAhB,KAA2B,EAAjD;AACA,UAAMqB,gBAAgB,GAAG5B,eAAe,CAAC2B,GAAhB,CAAoBpB,MAApB,KAA+B,IAAIsB,GAAJ,EAAxD;AACA,UAAIC,UAAJ;AACA,UAAIC,aAAJ;;AAEA,UAAMC,GAAG,GAAIC,IAAD;AACV,YAAIA,IAAJ,EAAU;AACR,cAAMC,GAAG,GAAGD,IAAI,CAACE,IAAL,CAAU,GAAV,CAAZ;;AAEA,cAAI,CAACJ,aAAa,CAACK,GAAd,CAAkBF,GAAlB,CAAL,EAA6B;AAC3BH,YAAAA,aAAa,CAACC,GAAd,CAAkBE,GAAlB;AACAJ,YAAAA,UAAU,CAACO,IAAX,CAAgBJ,IAAhB;AACD;AACF;AACF,OATD;;AAWA,UAAIK,IAAI,CAACC,yBAAL,CAA+BvB,EAA/B,CAAJ,EAAwC;AACtCc,QAAAA,UAAU,GAAG,EAAb;AACAC,QAAAA,aAAa,GAAG,IAAIF,GAAJ,EAAhB;;AACA,aAAK,IAAMI,IAAX,IAAmBP,aAAnB,EAAkC;AAChC,cAAMc,OAAO,GAAGF,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,CAAhB;AACAgB,UAAAA,GAAG,CAACQ,OAAD,CAAH;AACD;AACF,OAPD,MAOO;AACLV,QAAAA,UAAU,GAAGJ,aAAb;AACAK,QAAAA,aAAa,GAAGH,gBAAhB;AACD;;AAED,UAAMa,aAAa,GAAGC,aAAa,CAAC1B,EAAD,CAAnC;;AACA,WAAK,IAAMiB,KAAX,IAAmBQ,aAAnB,EAAkC;AAChCT,QAAAA,GAAG,CAACC,KAAD,CAAH;AACD;;AAEDnC,MAAAA,WAAW,CAAC6C,GAAZ,CAAgBpC,MAAhB,EAAwBuB,UAAxB;AACA9B,MAAAA,eAAe,CAAC2C,GAAhB,CAAoBpC,MAApB,EAA4BwB,aAA5B;AACAa,MAAAA,UAAU,CAACvB,SAAX,CAAqBd,MAArB,EAA6BS,EAA7B;AACAT,MAAAA,MAAM,CAACE,UAAP,CAAkB4B,IAAlB,CAAuBrB,EAAvB;AACAE,MAAAA,MAAM,CAAC2B,SAAP,CAAiBtC,MAAjB;;AAGA,UAAIS,EAAE,CAAC8B,IAAH,KAAY,eAAhB,EAAiC;AAC/BvC,QAAAA,MAAM,CAACI,KAAP,GAAe,IAAf;AACD;;AAED,UAAI,CAACV,QAAQ,CAAC0B,GAAT,CAAapB,MAAb,CAAL,EAA2B;AACzBN,QAAAA,QAAQ,CAAC0C,GAAT,CAAapC,MAAb,EAAqB,IAArB;AAEAwC,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB;AACrBhD,UAAAA,QAAQ,CAAC0C,GAAT,CAAapC,MAAb,EAAqB,KAArB;AACAA,UAAAA,MAAM,CAACO,QAAP;AACAP,UAAAA,MAAM,CAACE,UAAP,GAAoB,EAApB;AACD,SAJD;AAKD;AACF,KA3EoB;AA6ErByC,IAAAA,OAAO,EAAE,CAAChB,GAAD,EAAciB,KAAd;AACP,UAAM;AAAEzC,QAAAA;AAAF,UAAgBH,MAAtB;;AAEA,UAAIG,SAAJ,EAAe;AACb,YAAI0C,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAJ,EAAiC;AAC/BkC,UAAAA,UAAU,CAACU,QAAX,CACE/C,MADF,EAEE;AAAE,aAAC2B,GAAD,GAAOiB;AAAT,WAFF,EAGE;AAAEI,YAAAA,KAAK,EAAEC,IAAI,CAACC,MAAd;AAAsBC,YAAAA,KAAK,EAAE;AAA7B,WAHF;AAKD,SAND,MAMO;AACL,cAAM/C,KAAK,uCACLO,MAAM,CAACP,KAAP,CAAaJ,MAAb,KAAwB,EADnB;AAET,aAAC2B,GAAD,GAAOiB;AAFE,YAAX;;AAKA5C,UAAAA,MAAM,CAACI,KAAP,GAAeA,KAAf;;AACA,cAAI,CAACV,QAAQ,CAAC0B,GAAT,CAAapB,MAAb,CAAL,EAA2B;AACzBA,YAAAA,MAAM,CAACO,QAAP;AACD;AACF;AACF;AACF,KAnGoB;AAqGrB6C,IAAAA,cAAc,EAAGC,IAAD;AACd,UAAM;AAAElD,QAAAA;AAAF,UAAgBH,MAAtB;;AAEA,UAAIG,SAAS,IAAI0C,KAAK,CAACS,WAAN,CAAkBnD,SAAlB,CAAjB,EAA+C;AAC7CkC,QAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAEqD,UAAAA,IAAF;AAAQG,UAAAA,OAAO,EAAE;AAAjB,SAA1B;AACD;AACF,KA3GoB;AA6GrBC,IAAAA,aAAa,EAAGJ,IAAD;AACb,UAAM;AAAElD,QAAAA;AAAF,UAAgBH,MAAtB;;AAEA,UAAIG,SAAS,IAAI0C,KAAK,CAACS,WAAN,CAAkBnD,SAAlB,CAAjB,EAA+C;AAC7CkC,QAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAEqD,UAAAA;AAAF,SAA1B;AACD;AACF,KAnHoB;AAqHrBK,IAAAA,cAAc,EAAGC,SAAD;AACd,UAAM;AAAExD,QAAAA;AAAF,UAAgBH,MAAtB;;AAEA,UAAIG,SAAS,IAAI0C,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAjB,EAA8C;AAC5CkC,QAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAEwD,UAAAA,OAAO,EAAEG,SAAS,KAAK;AAAzB,SAA1B;AACD;AACF,KA3HoB;AA6HrBC,IAAAA,WAAW,EAAE;AACX,UAAM;AAAEzD,QAAAA;AAAF,UAAgBH,MAAtB;;AAEA,UAAIG,SAAJ,EAAe;AACb,eAAO0D,IAAI,CAACC,QAAL,CAAc9D,MAAd,EAAsBG,SAAtB,CAAP;AACD;;AACD,aAAO,EAAP;AACD,KApIoB;AAsIrB4D,IAAAA,WAAW,EAAE;AACX1B,MAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAAEiE,QAAAA,MAAM,EAAE;AAAV,OAA9B;AACD,KAxIoB;AA0IrBC,IAAAA,cAAc,EAAGJ,QAAD;AACdzB,MAAAA,UAAU,CAAC6B,cAAX,CAA0BlE,MAA1B,EAAkC8D,QAAlC;AACD,KA5IoB;AA8IrBK,IAAAA,UAAU,EAAGC,IAAD;AACV/B,MAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+BoE,IAA/B;AACD,KAhJoB;AAkJrBE,IAAAA,UAAU,EAAGC,IAAD;AACV,UAAM;AAAEpE,QAAAA,SAAF;AAAaC,QAAAA;AAAb,UAAuBJ,MAA7B;;AAEA,UAAIG,SAAJ,EAAe;AACb,YAAIC,KAAJ,EAAW;AACT,cAAMgE,IAAI;AAAKG,YAAAA;AAAL,aAAcnE,KAAd,CAAV;;AACAiC,UAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+BoE,IAA/B;AACD,SAHD,MAGO;AACL/B,UAAAA,UAAU,CAACiC,UAAX,CAAsBtE,MAAtB,EAA8BuE,IAA9B;AACD;;AAEDvE,QAAAA,MAAM,CAACI,KAAP,GAAe,IAAf;AACD;AACF,KA/JoB;AAiKrBoE,IAAAA,aAAa,EAAGC,KAAD;AACb,UAAM,CAACL,IAAD,EAAO1C,IAAP,IAAe+C,KAArB;;AAGA,UAAIxB,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAJ,EAAuB;AACrB;AACD;;;AAGD,UAAIM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2BA,IAAI,CAACnE,QAAL,CAAc2E,MAAd,KAAyB,CAAxD,EAA2D;AACzD,YAAMC,KAAK,GAAG;AAAEN,UAAAA,IAAI,EAAE;AAAR,SAAd;AACAlC,QAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B6E,KAA/B,EAAsC;AACpCC,UAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAY,CAAZ,CADgC;AAEpCC,UAAAA,KAAK,EAAE;AAF6B,SAAtC;AAIA;AACD;;;AAGD,UAAMC,iBAAiB,GAAGtE,MAAM,CAACuE,QAAP,CAAgBd,IAAhB,IACtB,KADsB,GAEtBM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,MACCpE,MAAM,CAACK,QAAP,CAAgB+D,IAAhB,KACCA,IAAI,CAACnE,QAAL,CAAc2E,MAAd,KAAyB,CAD1B,IAEC3B,IAAI,CAACC,MAAL,CAAYkB,IAAI,CAACnE,QAAL,CAAc,CAAd,CAAZ,CAFD,IAGCD,MAAM,CAACK,QAAP,CAAgB+D,IAAI,CAACnE,QAAL,CAAc,CAAd,CAAhB,CAJF,CAFJ;AASA;;AACA,UAAIkF,CAAC,GAAG,CAAR;;AAEA,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhB,IAAI,CAACnE,QAAL,CAAc2E,MAAlC,EAA0CQ,CAAC,IAAID,CAAC,EAAhD,EAAoD;AAClD,YAAME,WAAW,GAAGxB,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,IAAjB,CAApB;AACA,YAAIuB,IAAI,CAACC,MAAL,CAAYmC,WAAZ,CAAJ,EAA8B;AAC9B,YAAMR,MAAK,GAAGT,IAAI,CAACnE,QAAL,CAAcmF,CAAd,CAAd;AACA,YAAME,IAAI,GAAGD,WAAW,CAACpF,QAAZ,CAAqBkF,CAAC,GAAG,CAAzB,CAAb;AACA,YAAMI,MAAM,GAAGH,CAAC,KAAKhB,IAAI,CAACnE,QAAL,CAAc2E,MAAd,GAAuB,CAA5C;AACA,YAAMY,cAAc,GAClBvC,IAAI,CAACC,MAAL,CAAY2B,MAAZ,KACCH,OAAO,CAACC,SAAR,CAAkBE,MAAlB,KAA4B7E,MAAM,CAACK,QAAP,CAAgBwE,MAAhB,CAF/B,CANkD;AAWlD;AACA;AACA;;AACA,YAAIW,cAAc,KAAKP,iBAAvB,EAA0C;AACxC5C,UAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAAE8E,YAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAYI,CAAZ,CAAN;AAAsBH,YAAAA,KAAK,EAAE;AAA7B,WAA/B;AACAG,UAAAA,CAAC;AACF,SAHD,MAGO,IAAIT,OAAO,CAACC,SAAR,CAAkBE,MAAlB,CAAJ,EAA8B;AACnC;AACA,cAAI7E,MAAM,CAACK,QAAP,CAAgBwE,MAAhB,CAAJ,EAA4B;AAC1B,gBAAIS,IAAI,IAAI,IAAR,IAAgB,CAACrC,IAAI,CAACC,MAAL,CAAYoC,IAAZ,CAArB,EAAwC;AACtC,kBAAMI,QAAQ,GAAG;AAAEnB,gBAAAA,IAAI,EAAE;AAAR,eAAjB;AACAlC,cAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B0F,QAA/B,EAAyC;AACvCZ,gBAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAYI,CAAZ,CADmC;AAEvCH,gBAAAA,KAAK,EAAE;AAFgC,eAAzC;AAIAG,cAAAA,CAAC;AACF,aAPD,MAOO,IAAII,MAAJ,EAAY;AACjB,kBAAMG,SAAQ,GAAG;AAAEnB,gBAAAA,IAAI,EAAE;AAAR,eAAjB;AACAlC,cAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B0F,SAA/B,EAAyC;AACvCZ,gBAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAYI,CAAC,GAAG,CAAhB,CADmC;AAEvCH,gBAAAA,KAAK,EAAE;AAFgC,eAAzC;AAIAG,cAAAA,CAAC;AACF;AACF;AACF,SAnBM,MAmBA;AACL;AACA,cAAIG,IAAI,IAAI,IAAR,IAAgBrC,IAAI,CAACC,MAAL,CAAYoC,IAAZ,CAApB,EAAuC;AACrC,gBAAIrC,IAAI,CAAC0C,MAAL,CAAYd,MAAZ,EAAmBS,IAAnB,EAAyB;AAAEM,cAAAA,KAAK,EAAE;AAAT,aAAzB,CAAJ,EAA+C;AAC7CvD,cAAAA,UAAU,CAACwD,UAAX,CAAsB7F,MAAtB,EAA8B;AAAE8E,gBAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAYI,CAAZ,CAAN;AAAsBH,gBAAAA,KAAK,EAAE;AAA7B,eAA9B;AACAG,cAAAA,CAAC;AACF,aAHD,MAGO,IAAIG,IAAI,CAACf,IAAL,KAAc,EAAlB,EAAsB;AAC3BlC,cAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAC7B8E,gBAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAYI,CAAC,GAAG,CAAhB,CADyB;AAE7BH,gBAAAA,KAAK,EAAE;AAFsB,eAA/B;AAIAG,cAAAA,CAAC;AACF,aANM,MAMA,IAAIN,MAAK,CAACN,IAAN,KAAe,EAAnB,EAAuB;AAC5BlC,cAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAC7B8E,gBAAAA,EAAE,EAAEpD,IAAI,CAACqD,MAAL,CAAYI,CAAZ,CADyB;AAE7BH,gBAAAA,KAAK,EAAE;AAFsB,eAA/B;AAIAG,cAAAA,CAAC;AACF;AACF;AACF;AACF;AACF,KA1PoB;AA4PrBW,IAAAA,UAAU,EAAGnE,GAAD;AACV,UAAM;AAAExB,QAAAA;AAAF,UAAgBH,MAAtB;;AAEA,UAAIG,SAAJ,EAAe;AACb,YAAI0C,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAJ,EAAiC;AAC/BkC,UAAAA,UAAU,CAAC0D,UAAX,CAAsB/F,MAAtB,EAA8B2B,GAA9B,EAAmC;AACjCqB,YAAAA,KAAK,EAAEC,IAAI,CAACC,MADqB;AAEjCC,YAAAA,KAAK,EAAE;AAF0B,WAAnC;AAID,SALD,MAKO;AACL,cAAM/C,KAAK,uBAASO,MAAM,CAACP,KAAP,CAAaJ,MAAb,KAAwB,EAAjC,CAAX;;AACA,iBAAOI,KAAK,CAACuB,GAAD,CAAZ;AACA3B,UAAAA,MAAM,CAACI,KAAP,GAAeA,KAAf;;AACA,cAAI,CAACV,QAAQ,CAAC0B,GAAT,CAAapB,MAAb,CAAL,EAA2B;AACzBA,YAAAA,MAAM,CAACO,QAAP;AACD;AACF;AACF;AACF;AA9QoB,GAAvB;AAiRA,SAAOP,MAAP;AACD;AAED;;;;AAIA,IAAMmC,aAAa,GAAI1B,EAAD;AACpB,UAAQA,EAAE,CAAC8B,IAAX;AACE,SAAK,aAAL;AACA,SAAK,aAAL;AACA,SAAK,UAAL;AAAiB;AACf,YAAM;AAAEb,UAAAA;AAAF,YAAWjB,EAAjB;AACA,eAAOsB,IAAI,CAACiE,MAAL,CAAYtE,IAAZ,CAAP;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAM;AAAE0C,UAAAA,IAAF;AAAQ1C,UAAAA,IAAI,EAAJA;AAAR,YAAiBjB,EAAvB;AACA,YAAMuF,MAAM,GAAGjE,IAAI,CAACiE,MAAL,CAAYtE,MAAZ,CAAf;AACA,YAAMuE,WAAW,GAAGhD,IAAI,CAACC,MAAL,CAAYkB,IAAZ,IAChB,EADgB,GAEhB8B,KAAK,CAACC,IAAN,CAAWtC,IAAI,CAACuC,KAAL,CAAWhC,IAAX,CAAX,EAA6B;AAAA,cAAC,GAAGiC,CAAH,CAAD;AAAA,iBAAW3E,MAAI,CAACqD,MAAL,CAAYsB,CAAZ,CAAX;AAAA,SAA7B,CAFJ;AAIA,eAAO,CAAC,GAAGL,MAAJ,EAAY,GAAGC,WAAf,CAAP;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAM;AAAEvE,UAAAA,IAAI,EAAJA;AAAF,YAAWjB,EAAjB;AACA,YAAM6F,SAAS,GAAGvE,IAAI,CAACuE,SAAL,CAAe5E,MAAf,CAAlB;AACA,YAAM6E,YAAY,GAAGxE,IAAI,CAACyE,QAAL,CAAc9E,MAAd,CAArB;AACA,eAAO,CAAC,GAAG4E,SAAJ,EAAeC,YAAf,CAAP;AACD;;AAED,SAAK,WAAL;AAAkB;AAChB,YAAM;AAAE7E,UAAAA,IAAI,EAAJA,MAAF;AAAQO,UAAAA;AAAR,YAAoBxB,EAA1B;;AAEA,YAAIsB,IAAI,CAAC4D,MAAL,CAAYjE,MAAZ,EAAkBO,OAAlB,CAAJ,EAAgC;AAC9B,iBAAO,EAAP;AACD;;AAED,YAAMwE,YAAY,GAAW,EAA7B;AACA,YAAMC,YAAY,GAAW,EAA7B;;AAEA,aAAK,IAAMC,QAAX,IAAuB5E,IAAI,CAACuE,SAAL,CAAe5E,MAAf,CAAvB,EAA6C;AAC3C,cAAM2E,CAAC,GAAGtE,IAAI,CAACjB,SAAL,CAAe6F,QAAf,EAAyBlG,EAAzB,CAAV;AACAgG,UAAAA,YAAY,CAAC3E,IAAb,CAAkBuE,CAAlB;AACD;;AAED,aAAK,IAAMM,SAAX,IAAuB5E,IAAI,CAACuE,SAAL,CAAerE,OAAf,CAAvB,EAAgD;AAC9C,cAAMoE,EAAC,GAAGtE,IAAI,CAACjB,SAAL,CAAe6F,SAAf,EAAyBlG,EAAzB,CAAV;;AACAiG,UAAAA,YAAY,CAAC5E,IAAb,CAAkBuE,EAAlB;AACD;;AAED,YAAMO,SAAS,GAAGF,YAAY,CAACA,YAAY,CAAC9B,MAAb,GAAsB,CAAvB,CAA9B;AACA,YAAMiC,QAAQ,GAAG5E,OAAO,CAACA,OAAO,CAAC2C,MAAR,GAAiB,CAAlB,CAAxB;AACA,YAAMkC,UAAU,GAAGF,SAAS,CAAC7B,MAAV,CAAiB8B,QAAjB,CAAnB;AAEA,eAAO,CAAC,GAAGJ,YAAJ,EAAkB,GAAGC,YAArB,EAAmCI,UAAnC,CAAP;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAM;AAAEpF,UAAAA,IAAI,EAAJA;AAAF,YAAWjB,EAAjB;;AACA,YAAM6F,UAAS,GAAGvE,IAAI,CAACuE,SAAL,CAAe5E,MAAf,CAAlB;;AACA,eAAO,CAAC,GAAG4E,UAAJ,CAAP;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAM;AAAE5E,UAAAA,IAAI,EAAJA;AAAF,YAAWjB,EAAjB;;AACA,YAAMuF,OAAM,GAAGjE,IAAI,CAACiE,MAAL,CAAYtE,MAAZ,CAAf;;AACA,YAAMqF,QAAQ,GAAGhF,IAAI,CAACiF,IAAL,CAAUtF,MAAV,CAAjB;AACA,eAAO,CAAC,GAAGsE,OAAJ,EAAYe,QAAZ,CAAP;AACD;;AAED;AAAS;AACP,eAAO,EAAP;AACD;AAnEH;AAqED,CAtED;;AC9Se,SAAS,6BAA6B,CAAC,MAAM,EAAE,QAAQ,EAAE;AACxE,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS;AAC7C,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;;ACZe,SAAS,wBAAwB,CAAC,MAAM,EAAE,QAAQ,EAAE;AACnE,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAGE,6BAA4B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9D,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA,EAAE,IAAI,MAAM,CAAC,qBAAqB,EAAE;AACpC,IAAI,IAAI,gBAAgB,GAAG,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AAChE;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,MAAM,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS;AAC/C,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,SAAS;AAC7E,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAIO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACC,GAAD;MAAcC,4EAAQ;AACxD,MAAMC,KAAK,GAAG,CAACD,KAAf;AACA,MAAME,UAAU,GAAGF,KAAK,GAAGG,qBAAqB,CAACJ,GAAD,CAAxB,GAAgCA,GAAxD;AAEA,MAAIK,IAAI,GAAkBC,aAAa,CAACC,IAAxC;AACA,MAAIC,KAAK,GAAkBF,aAAa,CAACC,IAAzC;AACA,MAAIE,QAAQ,GAAG,CAAf;;AAEA,MAAIC,IAAI,GAAmB,IAA3B;;AACA,MAAIC,QAAQ,GAAmB,IAA/B;;AAEA,OAAK,IAAMC,IAAX,IAAmBT,UAAnB,EAA+B;AAC7B,QAAMU,IAAI,GAAGD,IAAI,CAACE,WAAL,CAAiB,CAAjB,CAAb;AACA,QAAI,CAACD,IAAL,EAAW;AAEX,QAAMzF,IAAI,GAAG2F,gBAAgB,CAACH,IAAD,EAAOC,IAAP,CAA7B;AACC,KAACR,IAAD,EAAOG,KAAP,IAAgBN,KAAK,GAAG,CAACM,KAAD,EAAQpF,IAAR,CAAH,GAAmB,CAACA,IAAD,EAAOiF,IAAP,CAAxC;;AAED,QACEW,UAAU,CAACX,IAAD,EAAOC,aAAa,CAACW,GAArB,CAAV,IACAD,UAAU,CAACR,KAAD,EAAQF,aAAa,CAACY,OAAtB,CAFZ,EAGE;AACA,UAAIhB,KAAJ,EAAW;AACTQ,QAAAA,IAAI,GAAGS,gBAAgB,CAACnB,GAAG,CAACoB,SAAJ,CAAc,CAAd,EAAiBX,QAAjB,CAAD,CAAvB;AACD,OAFD,MAEO;AACLC,QAAAA,IAAI,GAAGS,gBAAgB,CAACnB,GAAG,CAACoB,SAAJ,CAAc,CAAd,EAAiBpB,GAAG,CAACvC,MAAJ,GAAagD,QAA9B,CAAD,CAAvB;AACD;;AACD,UAAI,CAACC,IAAL,EAAW;AACZ;;AAED,QACEM,UAAU,CAACX,IAAD,EAAOC,aAAa,CAACe,EAArB,CAAV,IACAL,UAAU,CAACR,KAAD,EAAQF,aAAa,CAACe,EAAtB,CAFZ,EAGE;AACA,UAAIV,QAAQ,KAAK,IAAjB,EAAuB;AACrBA,QAAAA,QAAQ,GAAG,CAACA,QAAZ;AACD,OAFD,MAEO;AACL,YAAIT,KAAJ,EAAW;AACTS,UAAAA,QAAQ,GAAG,IAAX;AACD,SAFD,MAEO;AACLA,UAAAA,QAAQ,GAAGW,sBAAsB,CAC/BtB,GAAG,CAACoB,SAAJ,CAAc,CAAd,EAAiBpB,GAAG,CAACvC,MAAJ,GAAagD,QAA9B,CAD+B,CAAjC;AAGD;AACF;;AACD,UAAI,CAACE,QAAL,EAAe;AAChB;;AAED,QACEN,IAAI,KAAKC,aAAa,CAACC,IAAvB,IACAC,KAAK,KAAKF,aAAa,CAACC,IADxB,IAEAgB,cAAc,CAAClB,IAAD,EAAOG,KAAP,CAHhB,EAIE;AACA;AACD;;AAEDC,IAAAA,QAAQ,IAAIG,IAAI,CAACnD,MAAjB;AACD;;AAED,SAAOgD,QAAQ,IAAI,CAAnB;AACD,CA5DM;AA8DP,IAAMe,KAAK,GAAG,IAAd;AACA,IAAMC,WAAW,GAAG,oyCAApB;AACA,IAAMC,SAAS,GAAG,iBAAlB;AAEA;;;;AAIO,IAAMC,eAAe,GAAG,SAAlBA,eAAkB,CAACvE,IAAD;MAAe6C,4EAAQ;AACpD,MAAI2B,IAAI,GAAG,CAAX;AACA,MAAIC,OAAO,GAAG,KAAd;;AAEA,SAAOzE,IAAI,CAACK,MAAL,GAAc,CAArB,EAAwB;AACtB,QAAMqE,QAAQ,GAAG/B,oBAAoB,CAAC3C,IAAD,EAAO6C,KAAP,CAArC;AACA,QAAM,CAACW,IAAD,EAAOmB,SAAP,IAAoBC,wBAAwB,CAAC5E,IAAD,EAAO0E,QAAP,EAAiB7B,KAAjB,CAAlD;;AAEA,QAAIgC,eAAe,CAACrB,IAAD,EAAOmB,SAAP,EAAkB9B,KAAlB,CAAnB,EAA6C;AAC3C4B,MAAAA,OAAO,GAAG,IAAV;AACAD,MAAAA,IAAI,IAAIE,QAAR;AACD,KAHD,MAGO,IAAI,CAACD,OAAL,EAAc;AACnBD,MAAAA,IAAI,IAAIE,QAAR;AACD,KAFM,MAEA;AACL;AACD;;AAED1E,IAAAA,IAAI,GAAG2E,SAAP;AACD;;AAED,SAAOH,IAAP;AACD,CArBM;AAuBP;;;;;AAKO,IAAMI,wBAAwB,GAAG,CACtChC,GADsC,EAEtC4B,IAFsC,EAGtC3B,KAHsC;AAKtC,MAAIA,KAAJ,EAAW;AACT,QAAMtC,EAAE,GAAGqC,GAAG,CAACvC,MAAJ,GAAamE,IAAxB;AACA,WAAO,CAAC5B,GAAG,CAACkC,KAAJ,CAAUvE,EAAV,EAAcqC,GAAG,CAACvC,MAAlB,CAAD,EAA4BuC,GAAG,CAACkC,KAAJ,CAAU,CAAV,EAAavE,EAAb,CAA5B,CAAP;AACD;;AAED,SAAO,CAACqC,GAAG,CAACkC,KAAJ,CAAU,CAAV,EAAaN,IAAb,CAAD,EAAqB5B,GAAG,CAACkC,KAAJ,CAAUN,IAAV,CAArB,CAAP;AACD,CAXM;AAaP;;;;;AAKA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CACtBrB,IADsB,EAEtBmB,SAFsB;MAGtB9B,4EAAQ;;AAER,MAAIuB,KAAK,CAACW,IAAN,CAAWvB,IAAX,CAAJ,EAAsB;AACpB,WAAO,KAAP;AACD;AAGD;;;AACA,MAAIc,SAAS,CAACS,IAAV,CAAevB,IAAf,CAAJ,EAA0B;AACxB,QAAMkB,QAAQ,GAAG/B,oBAAoB,CAACgC,SAAD,EAAY9B,KAAZ,CAArC;AACA,QAAM,CAACmC,QAAD,EAAWC,aAAX,IAA4BL,wBAAwB,CACxDD,SADwD,EAExDD,QAFwD,EAGxD7B,KAHwD,CAA1D;;AAMA,QAAIgC,eAAe,CAACG,QAAD,EAAWC,aAAX,EAA0BpC,KAA1B,CAAnB,EAAqD;AACnD,aAAO,IAAP;AACD;AACF;;AAED,MAAIwB,WAAW,CAACU,IAAZ,CAAiBvB,IAAjB,CAAJ,EAA4B;AAC1B,WAAO,KAAP;AACD;;AAED,SAAO,IAAP;AACD,CA7BD;AA+BA;;;;;AAIO,IAAMR,qBAAqB,GAAG,UAAxBA,qBAAwB,CAAUJ,GAAV;AACnC,MAAMsC,GAAG,GAAGtC,GAAG,CAACvC,MAAJ,GAAa,CAAzB;;AAEA,OAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+B,GAAG,CAACvC,MAAxB,EAAgCQ,CAAC,EAAjC,EAAqC;AACnC,QAAMsE,KAAK,GAAGvC,GAAG,CAACwC,MAAJ,CAAWF,GAAG,GAAGrE,CAAjB,CAAd;;AAEA,QAAIwE,cAAc,CAACF,KAAK,CAACG,UAAN,CAAiB,CAAjB,CAAD,CAAlB,EAAyC;AACvC,UAAMC,KAAK,GAAG3C,GAAG,CAACwC,MAAJ,CAAWF,GAAG,GAAGrE,CAAN,GAAU,CAArB,CAAd;;AACA,UAAI2E,eAAe,CAACD,KAAK,CAACD,UAAN,CAAiB,CAAjB,CAAD,CAAnB,EAA0C;AACxC,cAAMC,KAAK,GAAGJ,KAAd;AAEAtE,QAAAA,CAAC;AACD;AACD;AACF;;AAED,UAAMsE,KAAN;AACD;AACF,CAlBM;AAoBP;;;;;;AAMA,IAAMK,eAAe,GAAIC,QAAD;AACtB,SAAOA,QAAQ,IAAI,MAAZ,IAAsBA,QAAQ,IAAI,MAAzC;AACD,CAFD;AAIA;;;;;;;AAMA,IAAMJ,cAAc,GAAII,QAAD;AACrB,SAAOA,QAAQ,IAAI,MAAZ,IAAsBA,QAAQ,IAAI,MAAzC;AACD,CAFD;;AAIA,IAAKvC,aAAL;;AAAA,WAAKA;AACHA,EAAAA,wCAAA,SAAA;AACAA,EAAAA,0CAAA,WAAA;AACAA,EAAAA,uCAAA,QAAA;AACAA,EAAAA,sCAAA,OAAA;AACAA,EAAAA,2CAAA,YAAA;AACAA,EAAAA,gDAAA,gBAAA;AACAA,EAAAA,sCAAA,MAAA;AACAA,EAAAA,sCAAA,MAAA;AACAA,EAAAA,uCAAA,MAAA;AACAA,EAAAA,wCAAA,OAAA;AACAA,EAAAA,yCAAA,QAAA;AACAA,EAAAA,8CAAA,YAAA;AACAA,EAAAA,0CAAA,QAAA;AACD,CAdD,EAAKA,aAAa,KAAbA,aAAa,KAAA,CAAlB;;AAgBA,IAAMwC,QAAQ,GAAG,6nHAAjB;AACA,IAAMC,SAAS,GAAG,iJAAlB;AACA,IAAMC,aAAa,GAAG,8lDAAtB;AACA,IAAMC,GAAG,GAAG,gCAAZ;AACA,IAAMC,GAAG,GAAG,gCAAZ;AACA,IAAMC,GAAG,GAAG,gCAAZ;AACA,IAAMC,IAAI,GAAG,g2EAAb;AACA,IAAMC,KAAK,GAAG,ykKAAd;AACA,IAAMC,SAAS,GAAG,oyBAAlB;;AAEA,IAAMvC,gBAAgB,GAAG,CAACH,IAAD,EAAeC,IAAf;AACvB,MAAIzF,IAAI,GAAGkF,aAAa,CAACiD,GAAzB;;AACA,MAAI3C,IAAI,CAAC4C,MAAL,CAAYV,QAAZ,MAA0B,CAAC,CAA/B,EAAkC;AAChC1H,IAAAA,IAAI,IAAIkF,aAAa,CAACmD,MAAtB;AACD;;AACD,MAAI5C,IAAI,KAAK,MAAb,EAAqB;AACnBzF,IAAAA,IAAI,IAAIkF,aAAa,CAACW,GAAtB;AACD;;AACD,MAAIJ,IAAI,IAAI,OAAR,IAAmBA,IAAI,IAAI,OAA/B,EAAwC;AACtCzF,IAAAA,IAAI,IAAIkF,aAAa,CAACe,EAAtB;AACD;;AACD,MAAIT,IAAI,CAAC4C,MAAL,CAAYT,SAAZ,MAA2B,CAAC,CAAhC,EAAmC;AACjC3H,IAAAA,IAAI,IAAIkF,aAAa,CAACoD,OAAtB;AACD;;AACD,MAAI9C,IAAI,CAAC4C,MAAL,CAAYR,aAAZ,MAA+B,CAAC,CAApC,EAAuC;AACrC5H,IAAAA,IAAI,IAAIkF,aAAa,CAACqD,WAAtB;AACD;;AACD,MAAI/C,IAAI,CAAC4C,MAAL,CAAYP,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AAC3B7H,IAAAA,IAAI,IAAIkF,aAAa,CAACsD,CAAtB;AACD;;AACD,MAAIhD,IAAI,CAAC4C,MAAL,CAAYN,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AAC3B9H,IAAAA,IAAI,IAAIkF,aAAa,CAACuD,CAAtB;AACD;;AACD,MAAIjD,IAAI,CAAC4C,MAAL,CAAYL,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AAC3B/H,IAAAA,IAAI,IAAIkF,aAAa,CAACwD,CAAtB;AACD;;AACD,MAAIlD,IAAI,CAAC4C,MAAL,CAAYJ,IAAZ,MAAsB,CAAC,CAA3B,EAA8B;AAC5BhI,IAAAA,IAAI,IAAIkF,aAAa,CAACyD,EAAtB;AACD;;AACD,MAAInD,IAAI,CAAC4C,MAAL,CAAYH,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;AAC7BjI,IAAAA,IAAI,IAAIkF,aAAa,CAAC0D,GAAtB;AACD;;AACD,MAAIpD,IAAI,CAAC4C,MAAL,CAAYF,SAAZ,MAA2B,CAAC,CAAhC,EAAmC;AACjClI,IAAAA,IAAI,IAAIkF,aAAa,CAACY,OAAtB;AACD;;AAED,SAAO9F,IAAP;AACD,CArCD;;AAuCA,SAAS4F,UAAT,CAAoBiD,CAApB,EAAsCC,CAAtC;AACE,SAAO,CAACD,CAAC,GAAGC,CAAL,MAAY,CAAnB;AACD;;AAED,IAAMC,gBAAgB,GAAqC;AAEzD,CACE7D,aAAa,CAACsD,CADhB,EAEEtD,aAAa,CAACsD,CAAd,GAAkBtD,aAAa,CAACuD,CAAhC,GAAoCvD,aAAa,CAACyD,EAAlD,GAAuDzD,aAAa,CAAC0D,GAFvE,CAFyD;AAOzD,CAAC1D,aAAa,CAACyD,EAAd,GAAmBzD,aAAa,CAACuD,CAAlC,EAAqCvD,aAAa,CAACuD,CAAd,GAAkBvD,aAAa,CAACwD,CAArE,CAPyD;AASzD,CAACxD,aAAa,CAAC0D,GAAd,GAAoB1D,aAAa,CAACwD,CAAnC,EAAsCxD,aAAa,CAACwD,CAApD,CATyD;AAWzD,CAACxD,aAAa,CAACiD,GAAf,EAAoBjD,aAAa,CAACmD,MAAd,GAAuBnD,aAAa,CAACW,GAAzD,CAXyD;AAazD,CAACX,aAAa,CAACiD,GAAf,EAAoBjD,aAAa,CAACqD,WAAlC,CAbyD;AAezD,CAACrD,aAAa,CAACoD,OAAf,EAAwBpD,aAAa,CAACiD,GAAtC,CAfyD;AAiBzD,CAACjD,aAAa,CAACW,GAAf,EAAoBX,aAAa,CAACY,OAAlC,CAjByD;AAmBzD,CAACZ,aAAa,CAACe,EAAf,EAAmBf,aAAa,CAACe,EAAjC,CAnByD,CAA3D;;AAsBA,SAASE,cAAT,CAAwBlB,IAAxB,EAA6CG,KAA7C;AACE,SACE2D,gBAAgB,CAACC,SAAjB,CACEC,CAAC,IAAIrD,UAAU,CAACX,IAAD,EAAOgE,CAAC,CAAC,CAAD,CAAR,CAAV,IAA0BrD,UAAU,CAACR,KAAD,EAAQ6D,CAAC,CAAC,CAAD,CAAT,CAD3C,MAEM,CAAC,CAHT;AAKD;;AAED,IAAMC,cAAc,GAAG,m6IAAvB;;AACA,IAAMnD,gBAAgB,GAAInB,GAAD;AACvB,SAAOA,GAAG,CAACwD,MAAJ,CAAWc,cAAX,MAA+B,CAAC,CAAvC;AACD,CAFD;;AAIA,IAAMC,SAAS,GAAG,8BAAlB;;AACA,IAAMjD,sBAAsB,GAAItB,GAAD;AAC7B,MAAMnE,KAAK,GAAGmE,GAAG,CAACnE,KAAJ,CAAU0I,SAAV,CAAd;;AACA,MAAI1I,KAAK,KAAK,IAAd,EAAoB;AAClB,WAAO,KAAP;AACD,GAFD,MAEO;AACL;AACA,QAAM2I,MAAM,GAAG3I,KAAK,CAAC,CAAD,CAAL,CAAS4B,MAAT,GAAkB,CAAjC;AACA,WAAO+G,MAAM,GAAG,CAAT,KAAe,CAAtB;AACD;AACF,CATD;;AC1RA;;;;AAGA,IAAMhH,SAAS,GAAI/B,KAAD;AAChB,SACEgJ,aAAa,CAAChJ,KAAD,CAAb,IACAiB,IAAI,CAACgI,UAAL,CAAgBjJ,KAAK,CAAC3C,QAAtB,CADA,IAEA,CAACU,MAAM,CAACuE,QAAP,CAAgBtC,KAAhB,CAHH;AAKD,CAND;;IAQa8B,OAAO,GAAqB;AACvC;;;AAIAoH,EAAAA,UAAU,CAAClJ,KAAD;AACR,WAAOgJ,aAAa,CAAChJ,KAAD,CAAb,IAAwBiB,IAAI,CAACgI,UAAL,CAAgBjJ,KAAK,CAAC3C,QAAtB,CAA/B;AACD,GAPsC;;AASvC;;;AAIA0E,EAAAA,SAbuC;;AAcvC;;;AAIAoH,EAAAA,aAAa,CAACnJ,KAAD;AACX,WAAOsD,KAAK,CAAC8F,OAAN,CAAcpJ,KAAd,KAAwBA,KAAK,CAACqJ,KAAN,CAAYC,GAAG,IAAIxH,OAAO,CAACC,SAAR,CAAkBuH,GAAlB,CAAnB,CAA/B;AACD,GApBsC;;AAsBvC;;;AAIAC,EAAAA,cAAc,CAACC,KAAD;AACZ,WAAQA,KAA0B,CAACnM,QAA3B,KAAwCoM,SAAhD;AACD,GA5BsC;;AA8BvC;;;;AAKAC,EAAAA,aAAa,EAAE,uBACb1J,KADa,EAEb2J,UAFa;QAGbC,iFAAqB;AAErB,WAAO7H,SAAS,CAAC/B,KAAD,CAAT,IAAoBA,KAAK,CAAC4J,UAAD,CAAL,KAAsBD,UAAjD;AACD,GAzCsC;;AA2CvC;;;;;;AAOAE,EAAAA,OAAO,CAACC,OAAD,EAAmBN,KAAnB;AACL,SAAK,IAAMzK,GAAX,IAAkByK,KAAlB,EAAyB;AACvB,UAAIzK,GAAG,KAAK,UAAZ,EAAwB;AACtB;AACD;;AAED,UAAI+K,OAAO,CAAC/K,GAAD,CAAP,KAAiByK,KAAK,CAACzK,GAAD,CAA1B,EAAiC;AAC/B,eAAO,KAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD;;AA9DsC;;;;;;;;ACwPzC,IAAMgL,eAAe,GAAG,IAAInN,OAAJ,EAAxB;IAEamB,MAAM,GAAoB;AACrC;;;AAIAiM,EAAAA,KAAK,CACH5M,MADG;QAEH6M,8EAKI;AAEJ,QAAM;AACJ7H,MAAAA,KAAK,GAAG,KADJ;AAEJ8H,MAAAA,IAAI,GAAG,QAFH;AAGJhI,MAAAA,EAAE,GAAG9E,MAAM,CAACG,SAHR;AAIJ6C,MAAAA;AAJI,QAKF6J,OALJ;;AAOA,QAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,QAAMpD,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,CAAb;AACA,QAAMtB,OAAO,GAAGsJ,IAAI,KAAK,QAAzB;;AAEA,SAAK,IAAM,CAAC3H,CAAD,EAAIkB,CAAJ,CAAX,IAAqB1F,MAAM,CAACqF,MAAP,CAAchG,MAAd,EAAsB;AACzC8E,MAAAA,EAAE,EAAEpD,IADqC;AAEzCsD,MAAAA,KAFyC;AAGzChC,MAAAA,KAHyC;AAIzCQ,MAAAA;AAJyC,KAAtB,CAArB,EAKI;AACF,UAAI,CAACP,IAAI,CAACC,MAAL,CAAYiC,CAAZ,CAAD,IAAmB,CAACpD,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkB2E,CAAlB,CAAxB,EAA8C;AAC5C,eAAO,CAAClB,CAAD,EAAIkB,CAAJ,CAAP;AACD;AACF;AACF,GAtCoC;;AAwCrC;;;;;;AAOA1D,EAAAA,OAAO,CAAC3C,MAAD,EAAiB2B,GAAjB,EAA8BiB,KAA9B;AACL5C,IAAAA,MAAM,CAAC2C,OAAP,CAAehB,GAAf,EAAoBiB,KAApB;AACD,GAjDoC;;AAmDrC;;;AAIAmK,EAAAA,KAAK,CACH/M,MADG,EAEH8E,EAFG;QAGH+H,8EAII;AAEJ,QAAMG,MAAM,GAAGrM,MAAM,CAACsM,KAAP,CAAajN,MAAb,EAAqB8E,EAArB,EAAyB;AAAEoI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAf;AACA,QAAMC,KAAK,GAAGxM,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB,EAAnB,CAAd;AACA,QAAMoN,KAAK,GAAG;AAAEJ,MAAAA,MAAF;AAAUG,MAAAA;AAAV,KAAd;AACA,QAAM;AAAEvF,MAAAA,QAAQ,GAAG;AAAb,QAAmBiF,OAAzB;AACA,QAAIQ,CAAC,GAAG,CAAR;AACA,QAAIC,MAAJ;;AAEA,SAAK,IAAMjH,CAAX,IAAgB1F,MAAM,CAAC4M,SAAP,CAAiBvN,MAAjB,sCACX6M,OADW;AAEd/H,MAAAA,EAAE,EAAEsI;AAFU,OAAhB,EAGI;AACF,UAAIC,CAAC,GAAGzF,QAAR,EAAkB;AAChB;AACD;;AAED,UAAIyF,CAAC,KAAK,CAAV,EAAa;AACXC,QAAAA,MAAM,GAAGjH,CAAT;AACD;;AAEDgH,MAAAA,CAAC;AACF;;AAED,WAAOC,MAAP;AACD,GAvFoC;;AAyFrC;;;AAIAE,EAAAA,MAAM,CACJxN,MADI,EAEJ8E,EAFI;QAGJ+H,8EAII;AAEJ,QAAMG,MAAM,GAAGrM,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB,EAArB,CAAf;AACA,QAAMmN,KAAK,GAAGxM,MAAM,CAACsM,KAAP,CAAajN,MAAb,EAAqB8E,EAArB,EAAyB;AAAEoI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAd;AACA,QAAME,KAAK,GAAG;AAAEJ,MAAAA,MAAF;AAAUG,MAAAA;AAAV,KAAd;AACA,QAAM;AAAEvF,MAAAA,QAAQ,GAAG;AAAb,QAAmBiF,OAAzB;AACA,QAAIQ,CAAC,GAAG,CAAR;AACA,QAAIC,MAAJ;;AAEA,SAAK,IAAMjH,CAAX,IAAgB1F,MAAM,CAAC4M,SAAP,CAAiBvN,MAAjB,sCACX6M,OADW;AAEd/H,MAAAA,EAAE,EAAEsI,KAFU;AAGd5J,MAAAA,OAAO,EAAE;AAHK,OAAhB,EAII;AACF,UAAI6J,CAAC,GAAGzF,QAAR,EAAkB;AAChB;AACD;;AAED,UAAIyF,CAAC,KAAK,CAAV,EAAa;AACXC,QAAAA,MAAM,GAAGjH,CAAT;AACD;;AAEDgH,MAAAA,CAAC;AACF;;AAED,WAAOC,MAAP;AACD,GA9HoC;;AAgIrC;;;AAIAlK,EAAAA,cAAc,CACZpD,MADY;QAEZ6M,8EAEI;AAEJ,QAAM;AAAExJ,MAAAA,IAAI,GAAG;AAAT,QAAyBwJ,OAA/B;AACA7M,IAAAA,MAAM,CAACoD,cAAP,CAAsBC,IAAtB;AACD,GA5IoC;;AA8IrC;;;AAIAI,EAAAA,aAAa,CACXzD,MADW;QAEX6M,8EAEI;AAEJ,QAAM;AAAExJ,MAAAA,IAAI,GAAG;AAAT,QAAyBwJ,OAA/B;AACA7M,IAAAA,MAAM,CAACyD,aAAP,CAAqBJ,IAArB;AACD,GA1JoC;;AA4JrC;;;AAIAK,EAAAA,cAAc,CACZ1D,MADY;QAEZ6M,8EAEI;AAEJ,QAAM;AAAElJ,MAAAA,SAAS,GAAG;AAAd,QAA4BkJ,OAAlC;AACA7M,IAAAA,MAAM,CAAC0D,cAAP,CAAsBC,SAAtB;AACD,GAxKoC;;AA0KrC;;;AAIA+J,EAAAA,KAAK,CAAC1N,MAAD,EAAiB8E,EAAjB;AACH,WAAO,CAACnE,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB8E,EAArB,CAAD,EAA2BnE,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB8E,EAAnB,CAA3B,CAAP;AACD,GAhLoC;;AAkLrC;;;AAIA2E,EAAAA,GAAG,CAACzJ,MAAD,EAAiB8E,EAAjB;AACD,WAAOnE,MAAM,CAACsM,KAAP,CAAajN,MAAb,EAAqB8E,EAArB,EAAyB;AAAEoI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAP;AACD,GAxLoC;;AA0LrC;;;AAIAS,EAAAA,KAAK,CAAC3N,MAAD,EAAiB8E,EAAjB;AACH,QAAMpD,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB;AAAEoI,MAAAA,IAAI,EAAE;AAAR,KAAxB,CAAb;AACA,WAAOvM,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB0B,IAApB,CAAP;AACD,GAjMoC;;AAmMrC;;;AAIAoC,EAAAA,QAAQ,CAAC9D,MAAD,EAAiB8E,EAAjB;AACN,QAAMsI,KAAK,GAAGzM,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqB8E,EAArB,CAAd;AACA,QAAMhB,QAAQ,GAAGD,IAAI,CAACC,QAAL,CAAc9D,MAAd,EAAsBoN,KAAtB,CAAjB;AACA,WAAOtJ,QAAP;AACD,GA3MoC;;AA4MrC;;;AAIA8J,EAAAA,SAAS,CAAC5N,MAAD,EAAiB0M,OAAjB;AACP,WAAOA,OAAO,CAACzM,QAAR,CAAiB4N,IAAjB,CAAsB1I,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAA3B,CAAP;AACD,GAlNoC;;AAoNrC;;;AAIA4I,EAAAA,UAAU,CAAC/N,MAAD,EAAiB0M,OAAjB;AACR,WAAOA,OAAO,CAACzM,QAAR,CAAiB4N,IAAjB,CACL1I,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CADlB,CAAP;AAGD,GA5NoC;;AA8NrC;;;AAIA6I,EAAAA,QAAQ,CAAChO,MAAD,EAAiB0M,OAAjB;AACN,WAAOA,OAAO,CAACzM,QAAR,CAAiBgM,KAAjB,CAAuB9G,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,CAA5B,CAAP;AACD,GApOoC;;AAsOrC;;;;;AAMApB,EAAAA,WAAW,CAAC/D,MAAD;AACTA,IAAAA,MAAM,CAAC+D,WAAP;AACD,GA9OoC;;AAgPrC;;;;;AAMAG,EAAAA,cAAc,CAAClE,MAAD,EAAiB8D,QAAjB;AACZ9D,IAAAA,MAAM,CAACkE,cAAP,CAAsBJ,QAAtB;AACD,GAxPoC;;AA0PrC;;;;;AAMAK,EAAAA,UAAU,CAACnE,MAAD,EAAiBoE,IAAjB;AACRpE,IAAAA,MAAM,CAACmE,UAAP,CAAkBC,IAAlB;AACD,GAlQoC;;AAoQrC;;;;;AAMAE,EAAAA,UAAU,CAACtE,MAAD,EAAiBuE,IAAjB;AACRvE,IAAAA,MAAM,CAACsE,UAAP,CAAkBC,IAAlB;AACD,GA5QoC;;AA8QrC;;;AAIAuJ,EAAAA,OAAO,CAAC9N,MAAD,EAAiB4C,KAAjB;AACL,WAAO8B,OAAO,CAACC,SAAR,CAAkB/B,KAAlB,KAA4B,CAAC5C,MAAM,CAACK,QAAP,CAAgBuC,KAAhB,CAApC;AACD,GApRoC;;AAsRrC;;;AAIAsC,EAAAA,QAAQ,CAACtC,KAAD;AACN,QAAI,CAACgJ,aAAa,CAAChJ,KAAD,CAAlB,EAA2B,OAAO,KAAP;AAC3B,QAAMqL,cAAc,GAAGtB,eAAe,CAACvL,GAAhB,CAAoBwB,KAApB,CAAvB;;AACA,QAAIqL,cAAc,KAAK5B,SAAvB,EAAkC;AAChC,aAAO4B,cAAP;AACD;;AACD,QAAM/I,QAAQ,GACZ,OAAOtC,KAAK,CAACD,OAAb,KAAyB,UAAzB,IACA,OAAOC,KAAK,CAACpC,KAAb,KAAuB,UADvB,IAEA,OAAOoC,KAAK,CAACQ,cAAb,KAAgC,UAFhC,IAGA,OAAOR,KAAK,CAACa,aAAb,KAA+B,UAH/B,IAIA,OAAOb,KAAK,CAACc,cAAb,KAAgC,UAJhC,IAKA,OAAOd,KAAK,CAACmB,WAAb,KAA6B,UAL7B,IAMA,OAAOnB,KAAK,CAACsB,cAAb,KAAgC,UANhC,IAOA,OAAOtB,KAAK,CAACuB,UAAb,KAA4B,UAP5B,IAQA,OAAOvB,KAAK,CAAC0B,UAAb,KAA4B,UAR5B,IASA,OAAO1B,KAAK,CAACvC,QAAb,KAA0B,UAT1B,IAUA,OAAOuC,KAAK,CAACtC,MAAb,KAAwB,UAVxB,IAWA,OAAOsC,KAAK,CAAC4B,aAAb,KAA+B,UAX/B,IAYA,OAAO5B,KAAK,CAACrC,QAAb,KAA0B,UAZ1B,IAaA,OAAOqC,KAAK,CAACkD,UAAb,KAA4B,UAb5B,KAcClD,KAAK,CAACxC,KAAN,KAAgB,IAAhB,IAAwBwL,aAAa,CAAChJ,KAAK,CAACxC,KAAP,CAdtC,MAeCwC,KAAK,CAACzC,SAAN,KAAoB,IAApB,IAA4B0C,KAAK,CAACqL,OAAN,CAActL,KAAK,CAACzC,SAApB,CAf7B,KAgBA0D,IAAI,CAACgI,UAAL,CAAgBjJ,KAAK,CAAC3C,QAAtB,CAhBA,IAiBAkO,SAAS,CAACC,eAAV,CAA0BxL,KAAK,CAAC1C,UAAhC,CAlBF;AAmBAyM,IAAAA,eAAe,CAACvK,GAAhB,CAAoBQ,KAApB,EAA2BsC,QAA3B;AACA,WAAOA,QAAP;AACD,GArToC;;AAuTrC;;;AAIAmJ,EAAAA,KAAK,CAACrO,MAAD,EAAiBiN,KAAjB,EAA+BnI,EAA/B;AACH,QAAM2E,GAAG,GAAG9I,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB8E,EAAnB,CAAZ;AACA,WAAOwJ,KAAK,CAAC3I,MAAN,CAAasH,KAAb,EAAoBxD,GAApB,CAAP;AACD,GA9ToC;;AAgUrC;;;AAIA8E,EAAAA,MAAM,CAACvO,MAAD,EAAiBiN,KAAjB,EAA+BnI,EAA/B;AACJ,WAAOnE,MAAM,CAAC6N,OAAP,CAAexO,MAAf,EAAuBiN,KAAvB,EAA8BnI,EAA9B,KAAqCnE,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqBiN,KAArB,EAA4BnI,EAA5B,CAA5C;AACD,GAtUoC;;AAwUrC;;;AAIA2J,EAAAA,OAAO,CAACzO,MAAD,EAAiB0M,OAAjB;AACL,QAAM;AAAEzM,MAAAA;AAAF,QAAeyM,OAArB;AACA,QAAM,CAACiB,KAAD,IAAU1N,QAAhB;AACA,WACEA,QAAQ,CAAC2E,MAAT,KAAoB,CAApB,IACC3E,QAAQ,CAAC2E,MAAT,KAAoB,CAApB,IACC3B,IAAI,CAACC,MAAL,CAAYyK,KAAZ,CADD,IAECA,KAAK,CAACpJ,IAAN,KAAe,EAFhB,IAGC,CAACvE,MAAM,CAACM,MAAP,CAAcoM,OAAd,CALL;AAOD,GAtVoC;;AAwVrC;;;AAIArM,EAAAA,QAAQ,CAACL,MAAD,EAAiB4C,KAAjB;AACN,WAAO8B,OAAO,CAACC,SAAR,CAAkB/B,KAAlB,KAA4B5C,MAAM,CAACK,QAAP,CAAgBuC,KAAhB,CAAnC;AACD,GA9VoC;;AAgWrC;;;AAIA8L,EAAAA,aAAa,CAAC1O,MAAD;AACX,QAAM0O,aAAa,GAAG/O,WAAW,CAACyB,GAAZ,CAAgBpB,MAAhB,CAAtB;AACA,WAAO0O,aAAa,KAAKrC,SAAlB,GAA8B,IAA9B,GAAqCqC,aAA5C;AACD,GAvWoC;;AAyWrC;;;AAIAF,EAAAA,OAAO,CAACxO,MAAD,EAAiBiN,KAAjB,EAA+BnI,EAA/B;AACL;AACA,QAAImI,KAAK,CAAC0B,MAAN,KAAiB,CAArB,EAAwB;AACtB,aAAO,KAAP;AACD;;AAED,QAAMlB,KAAK,GAAG9M,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB8E,EAArB,CAAd;AACA,WAAOwJ,KAAK,CAAC3I,MAAN,CAAasH,KAAb,EAAoBQ,KAApB,CAAP;AACD,GArXoC;;AAuXrC;;;AAIAnN,EAAAA,MAAM,CAACN,MAAD,EAAiB4C,KAAjB;AACJ,WAAO8B,OAAO,CAACC,SAAR,CAAkB/B,KAAlB,KAA4B5C,MAAM,CAACM,MAAP,CAAcsC,KAAd,CAAnC;AACD,GA7XoC;;AA+XrC;;;AAIAgM,EAAAA,IAAI,CAAC5O,MAAD,EAAiB8E,EAAjB;AACF,QAAMpD,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB;AAAEoI,MAAAA,IAAI,EAAE;AAAR,KAAxB,CAAb;AACA,WAAOvM,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB0B,IAApB,CAAP;AACD,GAtYoC;;AAwYrC;;;AAIAmN,EAAAA,IAAI,CACF7O,MADE,EAEF8E,EAFE;QAGF+H,8EAGI;AAEJ,QAAMnL,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB+H,OAAxB,CAAb;AACA,QAAMzI,IAAI,GAAGP,IAAI,CAACgL,IAAL,CAAU7O,MAAV,EAAkB0B,IAAlB,CAAb;AACA,WAAO,CAAC0C,IAAD,EAAO1C,IAAP,CAAP;AACD,GAvZoC;;AAyZrC;;;AAIA,GAACsE,MAAD,CACEhG,MADF;QAEE6M,8EAKI;AAEJ,QAAM;AAAE/H,MAAAA,EAAE,GAAG9E,MAAM,CAACG,SAAd;AAAyBqD,MAAAA,OAAO,GAAG,KAAnC;AAA0CwB,MAAAA,KAAK,GAAG;AAAlD,QAA4D6H,OAAlE;AACA,QAAI;AAAE7J,MAAAA;AAAF,QAAY6J,OAAhB;;AAEA,QAAI7J,KAAK,IAAI,IAAb,EAAmB;AACjBA,MAAAA,KAAK,GAAG,MAAM,IAAd;AACD;;AAED,QAAI,CAAC8B,EAAL,EAAS;AACP;AACD;;AAED,QAAMkB,MAAM,GAAmB,EAA/B;AACA,QAAMtE,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,CAAb;;AAEA,SAAK,IAAM,CAACK,CAAD,EAAIkB,CAAJ,CAAX,IAAqBxC,IAAI,CAACmC,MAAL,CAAYhG,MAAZ,EAAoB0B,IAApB,CAArB,EAAgD;AAC9C,UAAI,CAACsB,KAAK,CAACmC,CAAD,EAAIkB,CAAJ,CAAV,EAAkB;AAChB;AACD;;AAEDL,MAAAA,MAAM,CAAClE,IAAP,CAAY,CAACqD,CAAD,EAAIkB,CAAJ,CAAZ;;AAEA,UAAI,CAACrB,KAAD,IAAUrE,MAAM,CAACL,MAAP,CAAcN,MAAd,EAAsBmF,CAAtB,CAAd,EAAwC;AACtC;AACD;AACF;;AAED,QAAI3B,OAAJ,EAAa;AACXwC,MAAAA,MAAM,CAACxC,OAAP;AACD;;AAED,WAAOwC,MAAP;AACD,GArcoC;;AAucrC;;;AAIA5F,EAAAA,KAAK,CAACJ,MAAD;AACH,QAAM;AAAEI,MAAAA,KAAF;AAASD,MAAAA;AAAT,QAAuBH,MAA7B;;AAEA,QAAI,CAACG,SAAL,EAAgB;AACd,aAAO,IAAP;AACD;;AAED,QAAIC,KAAJ,EAAW;AACT,aAAOA,KAAP;AACD;;AAED,QAAIyC,KAAK,CAACC,UAAN,CAAiB3C,SAAjB,CAAJ,EAAiC;AAC/B,UAAM,CAAC6C,KAAD,IAAUrC,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAEgD,QAAAA,KAAK,EAAEC,IAAI,CAACC;AAAd,OAArB,CAAhB;;AAEA,UAAIF,KAAJ,EAAW;AACT,YAAM,CAACoB,KAAD,IAASpB,KAAf;;AACA,YAAiB8L,KAAjB,4BAA0B1K,KAA1B;;AACA,eAAO0K,KAAP;AACD,OAJD,MAIO;AACL,eAAO,EAAP;AACD;AACF;;AAED,QAAM;AAAE9B,MAAAA;AAAF,QAAa7M,SAAnB;AACA,QAAM;AAAEuB,MAAAA;AAAF,QAAWsL,MAAjB;AACA,QAAI,CAAC5I,IAAD,IAASzD,MAAM,CAACkO,IAAP,CAAY7O,MAAZ,EAAoB0B,IAApB,CAAb;;AAEA,QAAIsL,MAAM,CAAC2B,MAAP,KAAkB,CAAtB,EAAyB;AACvB,UAAMrJ,IAAI,GAAG3E,MAAM,CAAC6F,QAAP,CAAgBxG,MAAhB,EAAwB;AAAE8E,QAAAA,EAAE,EAAEpD,IAAN;AAAYsB,QAAAA,KAAK,EAAEC,IAAI,CAACC;AAAxB,OAAxB,CAAb;AACA,UAAM6L,KAAK,GAAGpO,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AACjCgD,QAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB;AADqB,OAArB,CAAd;;AAIA,UAAIG,IAAI,IAAIyJ,KAAZ,EAAmB;AACjB,YAAM,CAACC,QAAD,EAAWC,QAAX,IAAuB3J,IAA7B;AACA,YAAM,GAAG4J,SAAH,IAAgBH,KAAtB;;AAEA,YAAIhN,IAAI,CAAC+J,UAAL,CAAgBoD,SAAhB,EAA2BD,QAA3B,CAAJ,EAA0C;AACxC7K,UAAAA,IAAI,GAAG4K,QAAP;AACD;AACF;AACF;;AAED,QAAiBF,IAAjB,4BAA0B1K,IAA1B;;AACA,WAAO0K,IAAP;AACD,GAxfoC;;AA0frC;;;AAIA9H,EAAAA,IAAI,CACFhH,MADE;QAEF6M,8EAKI;AAEJ,QAAM;AAAEC,MAAAA,IAAI,GAAG,QAAT;AAAmB9H,MAAAA,KAAK,GAAG;AAA3B,QAAqC6H,OAA3C;AACA,QAAI;AAAE7J,MAAAA,KAAF;AAAS8B,MAAAA,EAAE,GAAG9E,MAAM,CAACG;AAArB,QAAmC0M,OAAvC;;AAEA,QAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,QAAMqK,kBAAkB,GAAGxO,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqB8E,EAArB,EAAyB;AAAEE,MAAAA;AAAF,KAAzB,CAA3B;AAEA,QAAI,CAACmK,kBAAL,EAAyB;AAEzB,QAAM,GAAGC,EAAH,IAASzO,MAAM,CAACiO,IAAP,CAAY5O,MAAZ,EAAoB,EAApB,CAAf;AAEA,QAAMqP,IAAI,GAAS,CAACF,kBAAkB,CAACzN,IAApB,EAA0B0N,EAA1B,CAAnB;;AAEA,QAAIrN,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,KAAmBA,EAAE,CAACF,MAAH,KAAc,CAArC,EAAwC;AACtC,YAAM,IAAI2K,KAAJ,gDAAN;AACD;;AAED,QAAIvM,KAAK,IAAI,IAAb,EAAmB;AACjB,UAAIjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB,YAAM,CAAC0K,MAAD,IAAW7O,MAAM,CAAC6O,MAAP,CAAcxP,MAAd,EAAsB8E,EAAtB,CAAjB;;AACA9B,QAAAA,KAAK,GAAGmC,CAAC,IAAIqK,MAAM,CAACvP,QAAP,CAAgBwP,QAAhB,CAAyBtK,CAAzB,CAAb;AACD,OAHD,MAGO;AACLnC,QAAAA,KAAK,GAAG,MAAM,IAAd;AACD;AACF;;AAED,QAAM,CAACgE,IAAD,IAASrG,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,MAAAA,EAAE,EAAEuK,IAAN;AAAYrM,MAAAA,KAAZ;AAAmB8J,MAAAA,IAAnB;AAAyB9H,MAAAA;AAAzB,KAArB,CAAf;AACA,WAAOgC,IAAP;AACD,GAriBoC;;AAuiBrC;;;AAIA5C,EAAAA,IAAI,CACFpE,MADE,EAEF8E,EAFE;QAGF+H,8EAGI;AAEJ,QAAMnL,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB+H,OAAxB,CAAb;AACA,QAAMzI,IAAI,GAAGP,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,IAAjB,CAAb;AACA,WAAO,CAAC0C,IAAD,EAAO1C,IAAP,CAAP;AACD,GAtjBoC;;AAwjBrC;;;AAIA,GAAC0E,KAAD,CACEpG,MADF;QAEE6M,8EAOI;AAEJ,QAAM;AACJ/H,MAAAA,EAAE,GAAG9E,MAAM,CAACG,SADR;AAEJ2M,MAAAA,IAAI,GAAG,KAFH;AAGJ4C,MAAAA,SAAS,GAAG,KAHR;AAIJlM,MAAAA,OAAO,GAAG,KAJN;AAKJwB,MAAAA,KAAK,GAAG;AALJ,QAMF6H,OANJ;AAOA,QAAI;AAAE7J,MAAAA;AAAF,QAAY6J,OAAhB;;AAEA,QAAI,CAAC7J,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,MAAM,IAAd;AACD;;AAED,QAAI,CAAC8B,EAAL,EAAS;AACP;AACD;;AAED,QAAIqB,IAAJ;AACA,QAAIiJ,EAAJ;;AAEA,QAAIO,IAAI,CAACC,MAAL,CAAY9K,EAAZ,CAAJ,EAAqB;AACnBqB,MAAAA,IAAI,GAAGrB,EAAE,CAAC,CAAD,CAAT;AACAsK,MAAAA,EAAE,GAAGtK,EAAE,CAAC,CAAD,CAAP;AACD,KAHD,MAGO;AACL,UAAM6I,KAAK,GAAGhN,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB;AAAEoI,QAAAA,IAAI,EAAE;AAAR,OAAxB,CAAd;AACA,UAAM0B,IAAI,GAAGjO,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB;AAAEoI,QAAAA,IAAI,EAAE;AAAR,OAAxB,CAAb;AACA/G,MAAAA,IAAI,GAAG3C,OAAO,GAAGoL,IAAH,GAAUjB,KAAxB;AACAyB,MAAAA,EAAE,GAAG5L,OAAO,GAAGmK,KAAH,GAAWiB,IAAvB;AACD;;AAED,QAAMiB,WAAW,GAAGhM,IAAI,CAACuC,KAAL,CAAWpG,MAAX,EAAmB;AACrCwD,MAAAA,OADqC;AAErC2C,MAAAA,IAFqC;AAGrCiJ,MAAAA,EAHqC;AAIrCU,MAAAA,IAAI,EAAE;AAAA,YAAC,CAAC3K,CAAD,CAAD;AAAA,eAAUH,KAAK,GAAG,KAAH,GAAWrE,MAAM,CAACL,MAAP,CAAcN,MAAd,EAAsBmF,CAAtB,CAA1B;AAAA;AAJ+B,KAAnB,CAApB;AAOA,QAAMsH,OAAO,GAAmB,EAAhC;AACA,QAAIsD,GAAJ;;AAEA,SAAK,IAAM,CAAC3L,IAAD,EAAO1C,IAAP,CAAX,IAA2BmO,WAA3B,EAAwC;AACtC,UAAMG,OAAO,GAAGD,GAAG,IAAIhO,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmBqO,GAAG,CAAC,CAAD,CAAtB,MAA+B,CAAtD,CADsC;;AAItC,UAAIjD,IAAI,KAAK,SAAT,IAAsBkD,OAA1B,EAAmC;AACjC;AACD;;AAED,UAAI,CAAChN,KAAK,CAACoB,IAAD,EAAO1C,IAAP,CAAV,EAAwB;AACtB;AACA;AACA;AACA,YAAIgO,SAAS,IAAI,CAACM,OAAd,IAAyB/M,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAA7B,EAAgD;AAC9C;AACD,SAFD,MAEO;AACL;AACD;AACF,OAjBqC;;;AAoBtC,UAAI0I,IAAI,KAAK,QAAT,IAAqBkD,OAAzB,EAAkC;AAChCD,QAAAA,GAAG,GAAG,CAAC3L,IAAD,EAAO1C,IAAP,CAAN;AACA;AACD,OAvBqC;;;AA0BtC,UAAMwO,IAAI,GACRpD,IAAI,KAAK,QAAT,GAAoBiD,GAApB,GAA0B,CAAC3L,IAAD,EAAO1C,IAAP,CAD5B;;AAGA,UAAIwO,IAAJ,EAAU;AACR,YAAIR,SAAJ,EAAe;AACbjD,UAAAA,OAAO,CAAC3K,IAAR,CAAaoO,IAAb;AACD,SAFD,MAEO;AACL,gBAAMA,IAAN;AACD;AACF;;AAEDH,MAAAA,GAAG,GAAG,CAAC3L,IAAD,EAAO1C,IAAP,CAAN;AACD;;;AAGD,QAAIoL,IAAI,KAAK,QAAT,IAAqBiD,GAAzB,EAA8B;AAC5B,UAAIL,SAAJ,EAAe;AACbjD,QAAAA,OAAO,CAAC3K,IAAR,CAAaiO,GAAb;AACD,OAFD,MAEO;AACL,cAAMA,GAAN;AACD;AACF;AAGD;;;AACA,QAAIL,SAAJ,EAAe;AACb,aAAOjD,OAAP;AACD;AACF,GArqBoC;;AAsqBrC;;;AAIAnK,EAAAA,SAAS,CACPtC,MADO;QAEP6M,8EAEI;AAEJ,QAAM;AAAEsD,MAAAA,KAAK,GAAG;AAAV,QAAoBtD,OAA1B;;AACA,QAAM1K,aAAa,GAAInC,MAAD;AACpB,aAAOT,WAAW,CAAC6B,GAAZ,CAAgBpB,MAAhB,KAA2B,EAAlC;AACD,KAFD;;AAIA,QAAMoQ,gBAAgB,GAAIpQ,MAAD;AACvB,aAAOP,eAAe,CAAC2B,GAAhB,CAAoBpB,MAApB,KAA+B,IAAIsB,GAAJ,EAAtC;AACD,KAFD;;AAIA,QAAM+O,YAAY,GAAIrQ,MAAD;AACnB,UAAM0B,IAAI,GAAGS,aAAa,CAACnC,MAAD,CAAb,CAAsBsQ,GAAtB,EAAb;AACA,UAAM3O,GAAG,GAAGD,IAAI,CAACE,IAAL,CAAU,GAAV,CAAZ;AACAwO,MAAAA,gBAAgB,CAACpQ,MAAD,CAAhB,CAAyBuD,MAAzB,CAAgC5B,GAAhC;AACA,aAAOD,IAAP;AACD,KALD;;AAOA,QAAI,CAACf,MAAM,CAAC+N,aAAP,CAAqB1O,MAArB,CAAL,EAAmC;AACjC;AACD;;AAED,QAAImQ,KAAJ,EAAW;AACT,UAAMI,QAAQ,GAAGrK,KAAK,CAACC,IAAN,CAAWtC,IAAI,CAACuC,KAAL,CAAWpG,MAAX,CAAX,EAA+B;AAAA,YAAC,GAAGqG,CAAH,CAAD;AAAA,eAAWA,CAAX;AAAA,OAA/B,CAAjB;AACA,UAAMmK,WAAW,GAAG,IAAIlP,GAAJ,CAAQiP,QAAQ,CAACE,GAAT,CAAapK,CAAC,IAAIA,CAAC,CAACzE,IAAF,CAAO,GAAP,CAAlB,CAAR,CAApB;AACArC,MAAAA,WAAW,CAAC6C,GAAZ,CAAgBpC,MAAhB,EAAwBuQ,QAAxB;AACA9Q,MAAAA,eAAe,CAAC2C,GAAhB,CAAoBpC,MAApB,EAA4BwQ,WAA5B;AACD;;AAED,QAAIrO,aAAa,CAACnC,MAAD,CAAb,CAAsB4E,MAAtB,KAAiC,CAArC,EAAwC;AACtC;AACD;;AAEDjE,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC;;;;;AAKA,WAAK,IAAM2Q,SAAX,IAAwBxO,aAAa,CAACnC,MAAD,CAArC,EAA+C;AAC7C,YAAI6D,IAAI,CAAChC,GAAL,CAAS7B,MAAT,EAAiB2Q,SAAjB,CAAJ,EAAiC;AAC/B,cAAMlM,KAAK,GAAG9D,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB2Q,SAApB,CAAd;AACA,cAAM,CAACvM,IAAD,EAAOwM,CAAP,IAAYnM,KAAlB;AAEA;;;;;;;AAOA,cAAIC,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2BA,IAAI,CAACnE,QAAL,CAAc2E,MAAd,KAAyB,CAAxD,EAA2D;AACzD5E,YAAAA,MAAM,CAACwE,aAAP,CAAqBC,KAArB;AACD;AACF;AACF;;AAED,UAAMoM,GAAG,GAAG1O,aAAa,CAACnC,MAAD,CAAb,CAAsB4E,MAAtB,GAA+B,EAA3C;;AACA,UAAIkM,CAAC,GAAG,CAAR;;AAEA,aAAO3O,aAAa,CAACnC,MAAD,CAAb,CAAsB4E,MAAtB,KAAiC,CAAxC,EAA2C;AACzC,YAAIkM,CAAC,GAAGD,GAAR,EAAa;AACX,gBAAM,IAAItB,KAAJ,yEAC8CsB,GAD9C,2HAAN;AAGD;;AAED,YAAMF,UAAS,GAAGN,YAAY,CAACrQ,MAAD,CAA9B,CAPyC;;;AAUzC,YAAI6D,IAAI,CAAChC,GAAL,CAAS7B,MAAT,EAAiB2Q,UAAjB,CAAJ,EAAiC;AAC/B,cAAMlM,MAAK,GAAG9D,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB2Q,UAApB,CAAd;;AACA3Q,UAAAA,MAAM,CAACwE,aAAP,CAAqBC,MAArB;AACD;;AACDqM,QAAAA,CAAC;AACF;AACF,KA3CD;AA4CD,GA3vBoC;;AA6vBrC;;;AAIAtB,EAAAA,MAAM,CACJxP,MADI,EAEJ8E,EAFI;QAGJ+H,8EAGI;AAEJ,QAAMnL,IAAI,GAAGf,MAAM,CAACe,IAAP,CAAY1B,MAAZ,EAAoB8E,EAApB,EAAwB+H,OAAxB,CAAb;AACA,QAAMkE,UAAU,GAAGhP,IAAI,CAACyN,MAAL,CAAY9N,IAAZ,CAAnB;AACA,QAAM+C,KAAK,GAAG9D,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB+Q,UAApB,CAAd;AACA,WAAOtM,KAAP;AACD,GA7wBoC;;AA+wBrC;;;AAIA/C,EAAAA,IAAI,CACF1B,MADE,EAEF8E,EAFE;QAGF+H,8EAGI;AAEJ,QAAM;AAAEmE,MAAAA,KAAF;AAAS9D,MAAAA;AAAT,QAAkBL,OAAxB;;AAEA,QAAI9K,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB,UAAIoI,IAAI,KAAK,OAAb,EAAsB;AACpB,YAAM,GAAG+D,SAAH,IAAgBpN,IAAI,CAAC8J,KAAL,CAAW3N,MAAX,EAAmB8E,EAAnB,CAAtB;AACAA,QAAAA,EAAE,GAAGmM,SAAL;AACD,OAHD,MAGO,IAAI/D,IAAI,KAAK,KAAb,EAAoB;AACzB,YAAM,GAAGgE,QAAH,IAAerN,IAAI,CAAC+K,IAAL,CAAU5O,MAAV,EAAkB8E,EAAlB,CAArB;AACAA,QAAAA,EAAE,GAAGoM,QAAL;AACD;AACF;;AAED,QAAIrO,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AACrB,UAAIoI,IAAI,KAAK,OAAb,EAAsB;AACpBpI,QAAAA,EAAE,GAAGjC,KAAK,CAAC4K,KAAN,CAAY3I,EAAZ,CAAL;AACD,OAFD,MAEO,IAAIoI,IAAI,KAAK,KAAb,EAAoB;AACzBpI,QAAAA,EAAE,GAAGjC,KAAK,CAAC4G,GAAN,CAAU3E,EAAV,CAAL;AACD,OAFM,MAEA;AACLA,QAAAA,EAAE,GAAG/C,IAAI,CAACoP,MAAL,CAAYrM,EAAE,CAACkI,MAAH,CAAUtL,IAAtB,EAA4BoD,EAAE,CAACqI,KAAH,CAASzL,IAArC,CAAL;AACD;AACF;;AAED,QAAI4M,KAAK,CAAC8C,OAAN,CAActM,EAAd,CAAJ,EAAuB;AACrBA,MAAAA,EAAE,GAAGA,EAAE,CAACpD,IAAR;AACD;;AAED,QAAIsP,KAAK,IAAI,IAAb,EAAmB;AACjBlM,MAAAA,EAAE,GAAGA,EAAE,CAACuE,KAAH,CAAS,CAAT,EAAY2H,KAAZ,CAAL;AACD;;AAED,WAAOlM,EAAP;AACD,GA1zBoC;;AA4zBrCuM,EAAAA,OAAO,CAACrR,MAAD,EAAiB0B,IAAjB;AACL,WAAOmC,IAAI,CAAChC,GAAL,CAAS7B,MAAT,EAAiB0B,IAAjB,CAAP;AACD,GA9zBoC;;AAg0BrC;;;;AAKA4P,EAAAA,OAAO,CACLtR,MADK,EAEL0B,IAFK;QAGLmL,8EAEI;AAEJ,QAAM;AAAE0E,MAAAA,QAAQ,GAAG;AAAb,QAA2B1E,OAAjC;AACA,QAAMnM,GAAG,GAAY;AACnB8Q,MAAAA,OAAO,EAAE9P,IADU;AAEnB6P,MAAAA,QAFmB;;AAGnBE,MAAAA,KAAK;AACH,YAAM;AAAED,UAAAA;AAAF,YAAc9Q,GAApB;AACA,YAAME,QAAQ,GAAGD,MAAM,CAACC,QAAP,CAAgBZ,MAAhB,CAAjB;AACAY,QAAAA,QAAQ,CAAC2C,MAAT,CAAgB7C,GAAhB;AACAA,QAAAA,GAAG,CAAC8Q,OAAJ,GAAc,IAAd;AACA,eAAOA,OAAP;AACD;;AATkB,KAArB;AAYA,QAAME,IAAI,GAAG/Q,MAAM,CAACC,QAAP,CAAgBZ,MAAhB,CAAb;AACA0R,IAAAA,IAAI,CAACjQ,GAAL,CAASf,GAAT;AACA,WAAOA,GAAP;AACD,GA51BoC;;AA81BrC;;;AAIAE,EAAAA,QAAQ,CAACZ,MAAD;AACN,QAAI0R,IAAI,GAAG9R,SAAS,CAACwB,GAAV,CAAcpB,MAAd,CAAX;;AAEA,QAAI,CAAC0R,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIpQ,GAAJ,EAAP;AACA1B,MAAAA,SAAS,CAACwC,GAAV,CAAcpC,MAAd,EAAsB0R,IAAtB;AACD;;AAED,WAAOA,IAAP;AACD,GA32BoC;;AA62BrC;;;AAIAzE,EAAAA,KAAK,CACHjN,MADG,EAEH8E,EAFG;QAGH+H,8EAEI;AAEJ,QAAM;AAAEK,MAAAA,IAAI,GAAG;AAAT,QAAqBL,OAA3B;;AAEA,QAAI9K,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB,UAAIpD,IAAJ;;AAEA,UAAIwL,IAAI,KAAK,KAAb,EAAoB;AAClB,YAAM,GAAGgE,QAAH,IAAerN,IAAI,CAAC+K,IAAL,CAAU5O,MAAV,EAAkB8E,EAAlB,CAArB;AACApD,QAAAA,IAAI,GAAGwP,QAAP;AACD,OAHD,MAGO;AACL,YAAM,GAAGD,SAAH,IAAgBpN,IAAI,CAAC8J,KAAL,CAAW3N,MAAX,EAAmB8E,EAAnB,CAAtB;AACApD,QAAAA,IAAI,GAAGuP,SAAP;AACD;;AAED,UAAM7M,IAAI,GAAGP,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,IAAjB,CAAb;;AAEA,UAAI,CAACuB,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAL,EAAwB;AACtB,cAAM,IAAImL,KAAJ,0BACcrC,IADd,yCACiDpI,EADjD,iCAC0EoI,IAD1E,iBAAN;AAGD;;AAED,aAAO;AAAExL,QAAAA,IAAF;AAAQiN,QAAAA,MAAM,EAAEzB,IAAI,KAAK,KAAT,GAAiB9I,IAAI,CAACG,IAAL,CAAUK,MAA3B,GAAoC;AAApD,OAAP;AACD;;AAED,QAAI/B,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AACrB,UAAM,CAAC2I,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAArB;AACA,aAAOoI,IAAI,KAAK,OAAT,GAAmBO,KAAnB,GAA2BhE,GAAlC;AACD;;AAED,WAAO3E,EAAP;AACD,GAt5BoC;;AAw5BrC;;;;AAKA6M,EAAAA,QAAQ,CACN3R,MADM,EAENiN,KAFM;QAGNJ,8EAEI;AAEJ,QAAM;AAAE0E,MAAAA,QAAQ,GAAG;AAAb,QAA2B1E,OAAjC;AACA,QAAMnM,GAAG,GAAa;AACpB8Q,MAAAA,OAAO,EAAEvE,KADW;AAEpBsE,MAAAA,QAFoB;;AAGpBE,MAAAA,KAAK;AACH,YAAM;AAAED,UAAAA;AAAF,YAAc9Q,GAApB;AACA,YAAMK,SAAS,GAAGJ,MAAM,CAACI,SAAP,CAAiBf,MAAjB,CAAlB;AACAe,QAAAA,SAAS,CAACwC,MAAV,CAAiB7C,GAAjB;AACAA,QAAAA,GAAG,CAAC8Q,OAAJ,GAAc,IAAd;AACA,eAAOA,OAAP;AACD;;AATmB,KAAtB;AAYA,QAAME,IAAI,GAAG/Q,MAAM,CAACI,SAAP,CAAiBf,MAAjB,CAAb;AACA0R,IAAAA,IAAI,CAACjQ,GAAL,CAASf,GAAT;AACA,WAAOA,GAAP;AACD,GAp7BoC;;AAs7BrC;;;AAIAK,EAAAA,SAAS,CAACf,MAAD;AACP,QAAI0R,IAAI,GAAG7R,UAAU,CAACuB,GAAX,CAAepB,MAAf,CAAX;;AAEA,QAAI,CAAC0R,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIpQ,GAAJ,EAAP;AACAzB,MAAAA,UAAU,CAACuC,GAAX,CAAepC,MAAf,EAAuB0R,IAAvB;AACD;;AAED,WAAOA,IAAP;AACD,GAn8BoC;;AAq8BrC;;;;;;;;;;;;AAaA,GAACnE,SAAD,CACEvN,MADF;QAEE6M,8EAKI;AAEJ,QAAM;AACJ/H,MAAAA,EAAE,GAAG9E,MAAM,CAACG,SADR;AAEJkD,MAAAA,IAAI,GAAG,QAFH;AAGJG,MAAAA,OAAO,GAAG,KAHN;AAIJwB,MAAAA,KAAK,GAAG;AAJJ,QAKF6H,OALJ;;AAOA,QAAI,CAAC/H,EAAL,EAAS;AACP;AACD;AAED;;;;;;;;;;;;;;;;;;;AAkBA,QAAMsI,KAAK,GAAGzM,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqB8E,EAArB,CAAd;AACA,QAAM,CAAC2I,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAArB;AACA,QAAMO,KAAK,GAAGnK,OAAO,GAAGiG,GAAH,GAASgE,KAA9B;AACA,QAAImE,UAAU,GAAG,KAAjB;AACA,QAAIC,SAAS,GAAG,EAAhB;AACA,QAAIjK,QAAQ,GAAG,CAAf;;AACA,QAAIkK,iBAAiB,GAAG,CAAxB;AACA,QAAIC,cAAc,GAAG,CAArB;AAGA;AACA;AACA;AACA;AACA;;AACA,SAAK,IAAM,CAAC3N,IAAD,EAAO1C,IAAP,CAAX,IAA2Bf,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,MAAAA,EAAF;AAAMtB,MAAAA,OAAN;AAAewB,MAAAA;AAAf,KAArB,CAA3B,EAAyE;AACvE;;;AAGA,UAAIN,OAAO,CAACC,SAAR,CAAkBP,IAAlB,CAAJ,EAA6B;AAC3B;AACA;AACA;AACA,YAAI,CAACY,KAAD,IAAUhF,MAAM,CAACM,MAAP,CAAc8D,IAAd,CAAd,EAAmC;AACjC,gBAAMzD,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB0B,IAArB,CAAN;AACA;AACD,SAP0B;AAU3B;AACA;;;AACA,YAAI1B,MAAM,CAACK,QAAP,CAAgB+D,IAAhB,CAAJ,EAA2B,SAZA;;AAe3B,YAAIzD,MAAM,CAACoN,UAAP,CAAkB/N,MAAlB,EAA0BoE,IAA1B,CAAJ,EAAqC;AACnC;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,cAAM4N,CAAC,GAAGjQ,IAAI,CAAC+J,UAAL,CAAgBpK,IAAhB,EAAsB+H,GAAG,CAAC/H,IAA1B,IACN+H,GADM,GAEN9I,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB0B,IAAnB,CAFJ;AAGA,cAAMuQ,CAAC,GAAGlQ,IAAI,CAAC+J,UAAL,CAAgBpK,IAAhB,EAAsB+L,KAAK,CAAC/L,IAA5B,IACN+L,KADM,GAEN9M,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB0B,IAArB,CAFJ;AAIAmQ,UAAAA,SAAS,GAAGlR,MAAM,CAACuR,MAAP,CAAclS,MAAd,EAAsB;AAAEgN,YAAAA,MAAM,EAAEiF,CAAV;AAAa9E,YAAAA,KAAK,EAAE6E;AAApB,WAAtB,EAA+C;AAAEhN,YAAAA;AAAF,WAA/C,CAAZ;AACA4M,UAAAA,UAAU,GAAG,IAAb;AACD;AACF;AAED;;;;;;AAIA,UAAI3O,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAJ,EAAuB;AACrB,YAAM+N,OAAO,GAAGpQ,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkBiM,KAAK,CAACjM,IAAxB,CAAhB,CADqB;AAIrB;AACA;AACA;AAEA;;AACA,YAAIyQ,OAAJ,EAAa;AACXL,UAAAA,iBAAiB,GAAGtO,OAAO,GACvBmK,KAAK,CAACgB,MADiB,GAEvBvK,IAAI,CAACG,IAAL,CAAUK,MAAV,GAAmB+I,KAAK,CAACgB,MAF7B;AAGAoD,UAAAA,cAAc,GAAGpE,KAAK,CAACgB,MAAvB,CAJW;AAKZ,SALD,MAKO;AACLmD,UAAAA,iBAAiB,GAAG1N,IAAI,CAACG,IAAL,CAAUK,MAA9B;AACAmN,UAAAA,cAAc,GAAGvO,OAAO,GAAGsO,iBAAH,GAAuB,CAA/C;AACD,SAjBoB;;;AAoBrB,YAAIK,OAAO,IAAIP,UAAX,IAAyBvO,IAAI,KAAK,QAAtC,EAAgD;AAC9C,gBAAM;AAAE3B,YAAAA,IAAF;AAAQiN,YAAAA,MAAM,EAAEoD;AAAhB,WAAN;AACAH,UAAAA,UAAU,GAAG,KAAb;AACD,SAvBoB;;;AA0BrB,eAAO,IAAP,EAAa;AACX;AACA;AACA;AACA,cAAIhK,QAAQ,KAAK,CAAjB,EAAoB;AAClB,gBAAIiK,SAAS,KAAK,EAAlB,EAAsB;AACtBjK,YAAAA,QAAQ,GAAGwK,YAAY,CAACP,SAAD,EAAYxO,IAAZ,EAAkBG,OAAlB,CAAvB,CAFkB;AAIlB;;AACAqO,YAAAA,SAAS,GAAG1I,wBAAwB,CAClC0I,SADkC,EAElCjK,QAFkC,EAGlCpE,OAHkC,CAAxB,CAIV,CAJU,CAAZ;AAKD,WAdU;;;AAiBXuO,UAAAA,cAAc,GAAGvO,OAAO,GACpBuO,cAAc,GAAGnK,QADG,GAEpBmK,cAAc,GAAGnK,QAFrB;AAGAkK,UAAAA,iBAAiB,GAAGA,iBAAiB,GAAGlK,QAAxC,CApBW;AAuBX;AACA;;AACA,cAAIkK,iBAAiB,GAAG,CAAxB,EAA2B;AACzBlK,YAAAA,QAAQ,GAAG,CAACkK,iBAAZ;AACA;AACD,WA5BU;AA+BX;AACA;;;AACAlK,UAAAA,QAAQ,GAAG,CAAX;AACA,gBAAM;AAAElG,YAAAA,IAAF;AAAQiN,YAAAA,MAAM,EAAEoD;AAAhB,WAAN;AACD;AACF;AACF;AAED;AACA;AAEA;AACA;;;AACA,aAASK,YAAT,CAAsB7N,IAAtB,EAAoClB,IAApC,EAAkDG,OAAlD;AACE,UAAIH,IAAI,KAAK,WAAb,EAA0B;AACxB,eAAO6D,oBAAoB,CAAC3C,IAAD,EAAOf,OAAP,CAA3B;AACD,OAFD,MAEO,IAAIH,IAAI,KAAK,MAAb,EAAqB;AAC1B,eAAOyF,eAAe,CAACvE,IAAD,EAAOf,OAAP,CAAtB;AACD,OAFM,MAEA,IAAIH,IAAI,KAAK,MAAT,IAAmBA,IAAI,KAAK,OAAhC,EAAyC;AAC9C,eAAOkB,IAAI,CAACK,MAAZ;AACD;;AACD,aAAO,CAAP;AACD;AACF,GAtoCoC;;AAwoCrC;;;AAIA4B,EAAAA,QAAQ,CACNxG,MADM;QAEN6M,8EAKI;AAEJ,QAAM;AAAEC,MAAAA,IAAI,GAAG,QAAT;AAAmB9H,MAAAA,KAAK,GAAG;AAA3B,QAAqC6H,OAA3C;AACA,QAAI;AAAE7J,MAAAA,KAAF;AAAS8B,MAAAA,EAAE,GAAG9E,MAAM,CAACG;AAArB,QAAmC0M,OAAvC;;AAEA,QAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,QAAMuN,mBAAmB,GAAG1R,MAAM,CAAC6M,MAAP,CAAcxN,MAAd,EAAsB8E,EAAtB,EAA0B;AAAEE,MAAAA;AAAF,KAA1B,CAA5B;;AAEA,QAAI,CAACqN,mBAAL,EAA0B;AACxB;AACD;;AAED,QAAM,GAAGjD,EAAH,IAASzO,MAAM,CAACgN,KAAP,CAAa3N,MAAb,EAAqB,EAArB,CAAf;AAGA;;AACA,QAAMqP,IAAI,GAAS,CAACgD,mBAAmB,CAAC3Q,IAArB,EAA2B0N,EAA3B,CAAnB;;AAEA,QAAIrN,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,KAAmBA,EAAE,CAACF,MAAH,KAAc,CAArC,EAAwC;AACtC,YAAM,IAAI2K,KAAJ,oDAAN;AACD;;AAED,QAAIvM,KAAK,IAAI,IAAb,EAAmB;AACjB,UAAIjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB,YAAM,CAAC0K,MAAD,IAAW7O,MAAM,CAAC6O,MAAP,CAAcxP,MAAd,EAAsB8E,EAAtB,CAAjB;;AACA9B,QAAAA,KAAK,GAAGmC,CAAC,IAAIqK,MAAM,CAACvP,QAAP,CAAgBwP,QAAhB,CAAyBtK,CAAzB,CAAb;AACD,OAHD,MAGO;AACLnC,QAAAA,KAAK,GAAG,MAAM,IAAd;AACD;AACF;;AAED,QAAM,CAACwD,QAAD,IAAa7F,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AACtCwD,MAAAA,OAAO,EAAE,IAD6B;AAEtCsB,MAAAA,EAAE,EAAEuK,IAFkC;AAGtCrM,MAAAA,KAHsC;AAItC8J,MAAAA,IAJsC;AAKtC9H,MAAAA;AALsC,KAArB,CAAnB;AAQA,WAAOwB,QAAP;AACD,GA9rCoC;;AAgsCrC;;;AAIA4G,EAAAA,KAAK,CAACpN,MAAD,EAAiB8E,EAAjB,EAA+BsK,EAA/B;AACH,QAAIvM,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,KAAqB,CAACsK,EAA1B,EAA8B;AAC5B,aAAOtK,EAAP;AACD;;AAED,QAAM2I,KAAK,GAAG9M,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB8E,EAArB,CAAd;AACA,QAAM2E,GAAG,GAAG9I,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmBoP,EAAE,IAAItK,EAAzB,CAAZ;AACA,WAAO;AAAEkI,MAAAA,MAAM,EAAES,KAAV;AAAiBN,MAAAA,KAAK,EAAE1D;AAAxB,KAAP;AACD,GA5sCoC;;AA8sCrC;;;;AAKA6I,EAAAA,QAAQ,CACNtS,MADM,EAENoN,KAFM;QAGNP,8EAEI;AAEJ,QAAM;AAAE0E,MAAAA,QAAQ,GAAG;AAAb,QAA2B1E,OAAjC;AACA,QAAMnM,GAAG,GAAa;AACpB8Q,MAAAA,OAAO,EAAEpE,KADW;AAEpBmE,MAAAA,QAFoB;;AAGpBE,MAAAA,KAAK;AACH,YAAM;AAAED,UAAAA;AAAF,YAAc9Q,GAApB;AACA,YAAMO,SAAS,GAAGN,MAAM,CAACM,SAAP,CAAiBjB,MAAjB,CAAlB;AACAiB,QAAAA,SAAS,CAACsC,MAAV,CAAiB7C,GAAjB;AACAA,QAAAA,GAAG,CAAC8Q,OAAJ,GAAc,IAAd;AACA,eAAOA,OAAP;AACD;;AATmB,KAAtB;AAYA,QAAME,IAAI,GAAG/Q,MAAM,CAACM,SAAP,CAAiBjB,MAAjB,CAAb;AACA0R,IAAAA,IAAI,CAACjQ,GAAL,CAASf,GAAT;AACA,WAAOA,GAAP;AACD,GA1uCoC;;AA4uCrC;;;AAIAO,EAAAA,SAAS,CAACjB,MAAD;AACP,QAAI0R,IAAI,GAAG5R,UAAU,CAACsB,GAAX,CAAepB,MAAf,CAAX;;AAEA,QAAI,CAAC0R,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIpQ,GAAJ,EAAP;AACAxB,MAAAA,UAAU,CAACsC,GAAX,CAAepC,MAAf,EAAuB0R,IAAvB;AACD;;AAED,WAAOA,IAAP;AACD,GAzvCoC;;AA2vCrC;;;;;;;AAQA5L,EAAAA,UAAU,CAAC9F,MAAD,EAAiB2B,GAAjB;AACR3B,IAAAA,MAAM,CAAC8F,UAAP,CAAkBnE,GAAlB;AACD,GArwCoC;;AAuwCrC;;;;;;AAMA4Q,EAAAA,cAAc,CAACvS,MAAD,EAAiB0O,aAAjB;AACZ/O,IAAAA,WAAW,CAACyC,GAAZ,CAAgBpC,MAAhB,EAAwB0O,aAAxB;AACD,GA/wCoC;;AAixCrC;;;AAIAjB,EAAAA,KAAK,CAACzN,MAAD,EAAiB8E,EAAjB;AACH,WAAOnE,MAAM,CAACsM,KAAP,CAAajN,MAAb,EAAqB8E,EAArB,EAAyB;AAAEoI,MAAAA,IAAI,EAAE;AAAR,KAAzB,CAAP;AACD,GAvxCoC;;AAyxCrC;;;;;;AAOAgF,EAAAA,MAAM,CACJlS,MADI,EAEJ8E,EAFI;QAGJ+H,8EAEI;AAEJ,QAAM;AAAE7H,MAAAA,KAAK,GAAG;AAAV,QAAoB6H,OAA1B;AACA,QAAMO,KAAK,GAAGzM,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqB8E,EAArB,CAAd;AACA,QAAM,CAAC2I,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAArB;AACA,QAAI7I,IAAI,GAAG,EAAX;;AAEA,SAAK,IAAM,CAACH,IAAD,EAAO1C,IAAP,CAAX,IAA2Bf,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAC9C8E,MAAAA,EAAE,EAAEsI,KAD0C;AAE9CpK,MAAAA,KAAK,EAAEC,IAAI,CAACC,MAFkC;AAG9C8B,MAAAA;AAH8C,KAArB,CAA3B,EAII;AACF,UAAIwN,CAAC,GAAGpO,IAAI,CAACG,IAAb;;AAEA,UAAIxC,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkB+H,GAAG,CAAC/H,IAAtB,CAAJ,EAAiC;AAC/B8Q,QAAAA,CAAC,GAAGA,CAAC,CAACnJ,KAAF,CAAQ,CAAR,EAAWI,GAAG,CAACkF,MAAf,CAAJ;AACD;;AAED,UAAI5M,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkB+L,KAAK,CAAC/L,IAAxB,CAAJ,EAAmC;AACjC8Q,QAAAA,CAAC,GAAGA,CAAC,CAACnJ,KAAF,CAAQoE,KAAK,CAACkB,MAAd,CAAJ;AACD;;AAEDpK,MAAAA,IAAI,IAAIiO,CAAR;AACD;;AAED,WAAOjO,IAAP;AACD,GA/zCoC;;AAi0CrC;;;AAIAkO,EAAAA,WAAW,CACTzS,MADS,EAEToN,KAFS;QAGTP,8EAEI;AAEJ,QAAM;AAAE7H,MAAAA,KAAK,GAAG;AAAV,QAAoB6H,OAA1B;AACA,QAAI,CAACY,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAAnB;;AAGA,QAAIK,KAAK,CAACkB,MAAN,KAAiB,CAAjB,IAAsBlF,GAAG,CAACkF,MAAJ,KAAe,CAArC,IAA0C9L,KAAK,CAACS,WAAN,CAAkB8J,KAAlB,CAA9C,EAAwE;AACtE,aAAOA,KAAP;AACD;;AAED,QAAMsF,QAAQ,GAAG/R,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AACpC8E,MAAAA,EAAE,EAAE2E,GADgC;AAEpCzG,MAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB;AAFwB,KAArB,CAAjB;AAIA,QAAM+J,SAAS,GAAGwD,QAAQ,GAAGA,QAAQ,CAAC,CAAD,CAAX,GAAiB,EAA3C;AACA,QAAM/E,KAAK,GAAGhN,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqByN,KAArB,CAAd;AACA,QAAMD,MAAM,GAAG;AAAER,MAAAA,MAAM,EAAEW,KAAV;AAAiBR,MAAAA,KAAK,EAAE1D;AAAxB,KAAf;AACA,QAAIkJ,IAAI,GAAG,IAAX;;AAEA,SAAK,IAAM,CAACvO,IAAD,EAAO1C,IAAP,CAAX,IAA2Bf,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAC9C8E,MAAAA,EAAE,EAAE0I,MAD0C;AAE9CxK,MAAAA,KAAK,EAAEC,IAAI,CAACC,MAFkC;AAG9CM,MAAAA,OAAO,EAAE,IAHqC;AAI9CwB,MAAAA;AAJ8C,KAArB,CAA3B,EAKI;AACF,UAAI2N,IAAJ,EAAU;AACRA,QAAAA,IAAI,GAAG,KAAP;AACA;AACD;;AAED,UAAIvO,IAAI,CAACG,IAAL,KAAc,EAAd,IAAoBxC,IAAI,CAAC6Q,QAAL,CAAclR,IAAd,EAAoBwN,SAApB,CAAxB,EAAwD;AACtDzF,QAAAA,GAAG,GAAG;AAAE/H,UAAAA,IAAF;AAAQiN,UAAAA,MAAM,EAAEvK,IAAI,CAACG,IAAL,CAAUK;AAA1B,SAAN;AACA;AACD;AACF;;AAED,WAAO;AAAEoI,MAAAA,MAAM,EAAES,KAAV;AAAiBN,MAAAA,KAAK,EAAE1D;AAAxB,KAAP;AACD,GA/2CoC;;AAi3CrC;;;AAIAoJ,EAAAA,IAAI,CACF7S,MADE;QAEF6M,8EAII;AAEJ,WAAOlM,MAAM,CAACiM,KAAP,CAAa5M,MAAb,sCACF6M,OADE;AAEL7J,MAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACL,MAAP,CAAcN,MAAd,EAAsBmF,CAAtB;AAFP,OAAP;AAID,GAj4CoC;;AAm4CrC;;;AAIAuL,EAAAA,kBAAkB,CAAC1Q,MAAD,EAAiB8S,EAAjB;AAChB,QAAMlQ,KAAK,GAAGjC,MAAM,CAAC+N,aAAP,CAAqB1O,MAArB,CAAd;AACAW,IAAAA,MAAM,CAAC4R,cAAP,CAAsBvS,MAAtB,EAA8B,KAA9B;;AACA,QAAI;AACF8S,MAAAA,EAAE;AACH,KAFD,SAEU;AACRnS,MAAAA,MAAM,CAAC4R,cAAP,CAAsBvS,MAAtB,EAA8B4C,KAA9B;AACD;;AACDjC,IAAAA,MAAM,CAAC2B,SAAP,CAAiBtC,MAAjB;AACD;;AAh5CoC;;IChR1B+S,QAAQ,GAAsB;AACzC;;;AAIAC,EAAAA,UAAU,CAACpQ,KAAD;AACR,WAAOb,IAAI,CAACuN,MAAL,CAAY1M,KAAZ,KAAsB0L,KAAK,CAAC8C,OAAN,CAAcxO,KAAd,CAAtB,IAA8CC,KAAK,CAACqL,OAAN,CAActL,KAAd,CAArD;AACD;;AAPwC;IAqB9B+M,IAAI,GAAkB;AACjC;;;AAIAC,EAAAA,MAAM,CAAChN,KAAD;AACJ,WACEsD,KAAK,CAAC8F,OAAN,CAAcpJ,KAAd,KAAwBA,KAAK,CAACgC,MAAN,KAAiB,CAAzC,IAA8ChC,KAAK,CAACqJ,KAAN,CAAYlK,IAAI,CAACuN,MAAjB,CADhD;AAGD;;AATgC;;;;ACkDnC,IAAM2D,kBAAkB,GAAG,IAAIzT,OAAJ,EAA3B;IAEaqE,IAAI,GAAkB;AACjC;;;AAIA8C,EAAAA,QAAQ,CAACuM,IAAD,EAAaxR,IAAb;AACN,QAAM0C,IAAI,GAAGP,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAexR,IAAf,CAAb;;AAEA,QAAIuB,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAJ,EAAuB;AACrB,YAAM,IAAImL,KAAJ,iDACqC7N,IADrC,yDACwF0C,IADxF,EAAN;AAGD;;AAED,WAAOA,IAAP;AACD,GAfgC;;AAiBjC;;;;;;AAOA,GAACkC,SAAD,CACE4M,IADF,EAEExR,IAFF;QAGEmL,8EAEI;;AAEJ,SAAK,IAAMxG,CAAX,IAAgBtE,IAAI,CAACuE,SAAL,CAAe5E,IAAf,EAAqBmL,OAArB,CAAhB,EAA+C;AAC7C,UAAM1H,CAAC,GAAGtB,IAAI,CAAC8C,QAAL,CAAcuM,IAAd,EAAoB7M,CAApB,CAAV;AACA,UAAM5B,KAAK,GAAwB,CAACU,CAAD,EAAIkB,CAAJ,CAAnC;AACA,YAAM5B,KAAN;AACD;AACF,GApCgC;;AAsCjC;;;AAIAI,EAAAA,KAAK,CAACqO,IAAD,EAAaC,KAAb;AACH,QAAIlQ,IAAI,CAACC,MAAL,CAAYgQ,IAAZ,CAAJ,EAAuB;AACrB,YAAM,IAAI3D,KAAJ,gDACoC6D,IAAI,CAACC,SAAL,CAAeH,IAAf,CADpC,EAAN;AAGD;;AAED,QAAMI,CAAC,GAAGJ,IAAI,CAACjT,QAAL,CAAckT,KAAd,CAAV;;AAEA,QAAIG,CAAC,IAAI,IAAT,EAAe;AACb,YAAM,IAAI/D,KAAJ,sCAC2B4D,KAD3B,wBAC+CC,IAAI,CAACC,SAAL,CACjDH,IADiD,CAD/C,EAAN;AAKD;;AAED,WAAOI,CAAP;AACD,GA5DgC;;AA8DjC;;;AAIA,GAACrT,QAAD,CACEiT,IADF,EAEExR,IAFF;QAGEmL,8EAEI;AAEJ,QAAM;AAAErJ,MAAAA,OAAO,GAAG;AAAZ,QAAsBqJ,OAA5B;AACA,QAAMlG,QAAQ,GAAG9C,IAAI,CAAC8C,QAAL,CAAcuM,IAAd,EAAoBxR,IAApB,CAAjB;AACA,QAAM;AAAEzB,MAAAA;AAAF,QAAe0G,QAArB;AACA,QAAIwM,KAAK,GAAG3P,OAAO,GAAGvD,QAAQ,CAAC2E,MAAT,GAAkB,CAArB,GAAyB,CAA5C;;AAEA,WAAOpB,OAAO,GAAG2P,KAAK,IAAI,CAAZ,GAAgBA,KAAK,GAAGlT,QAAQ,CAAC2E,MAA/C,EAAuD;AACrD,UAAMC,KAAK,GAAGhB,IAAI,CAACgB,KAAL,CAAW8B,QAAX,EAAqBwM,KAArB,CAAd;AACA,UAAMI,SAAS,GAAG7R,IAAI,CAACqD,MAAL,CAAYoO,KAAZ,CAAlB;AACA,YAAM,CAACtO,KAAD,EAAQ0O,SAAR,CAAN;AACAJ,MAAAA,KAAK,GAAG3P,OAAO,GAAG2P,KAAK,GAAG,CAAX,GAAeA,KAAK,GAAG,CAAtC;AACD;AACF,GApFgC;;AAsFjC;;;AAIAhC,EAAAA,MAAM,CAAC+B,IAAD,EAAaxR,IAAb,EAAyB8R,OAAzB;AACJ,QAAMnN,CAAC,GAAGtE,IAAI,CAACoP,MAAL,CAAYzP,IAAZ,EAAkB8R,OAAlB,CAAV;AACA,QAAMrO,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAV;AACA,WAAO,CAAClB,CAAD,EAAIkB,CAAJ,CAAP;AACD,GA9FgC;;AAgGjC;;;AAIAoN,EAAAA,UAAU,CAACP,IAAD,EAAaxR,IAAb;AACR,QAAM0C,IAAI,GAAGP,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAexR,IAAf,CAAb;;AAEA,QAAIf,MAAM,CAACuE,QAAP,CAAgBd,IAAhB,CAAJ,EAA2B;AACzB,YAAM,IAAImL,KAAJ,mDACuC7N,IADvC,kEACmG0C,IADnG,EAAN;AAGD;;AAED,WAAOA,IAAP;AACD,GA9GgC;;AAgHjC;;;AAIA,GAAC6B,WAAD,CACEiN,IADF;QAEErG,8EAKI;;AAEJ,SAAK,IAAM,CAACzI,IAAD,EAAO1C,IAAP,CAAX,IAA2BmC,IAAI,CAACuC,KAAL,CAAW8M,IAAX,EAAiBrG,OAAjB,CAA3B,EAAsD;AACpD,UAAInL,IAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB;AACA;AACA,cAAM,CAACR,IAAD,EAAO1C,IAAP,CAAN;AACD;AACF;AACF,GApIgC;;AAsIjC;;;;;AAMA,GAACgS,QAAD,CACER,IADF;QAEErG,8EAKI;;AAEJ,SAAK,IAAM,CAACzI,IAAD,EAAO1C,IAAP,CAAX,IAA2BmC,IAAI,CAACuC,KAAL,CAAW8M,IAAX,EAAiBrG,OAAjB,CAA3B,EAAsD;AACpD,UAAInI,OAAO,CAACC,SAAR,CAAkBP,IAAlB,CAAJ,EAA6B;AAC3B,cAAM,CAACA,IAAD,EAAO1C,IAAP,CAAN;AACD;AACF;AACF,GA1JgC;;AA4JjC;;;AAIAiS,EAAAA,YAAY,CAACvP,IAAD;AACV,QAAIM,OAAO,CAACoH,UAAR,CAAmB1H,IAAnB,CAAJ,EAA8B;AAC5B,UAAqBwP,UAArB,4BAAoCxP,IAApC;;AAEA,aAAOwP,UAAP;AACD,KAJD,MAIO;AACL,UAAiBA,UAAjB,4BAAgCxP,IAAhC;;AAEA,aAAOwP,UAAP;AACD;AACF,GA1KgC;;AA4KjC;;;AAIAjG,EAAAA,KAAK,CAACuF,IAAD,EAAaxR,IAAb;AACH,QAAM2E,CAAC,GAAG3E,IAAI,CAAC2H,KAAL,EAAV;AACA,QAAIlE,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAR;;AAEA,WAAOlB,CAAP,EAAU;AACR,UAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBA,CAAC,CAAClF,QAAF,CAAW2E,MAAX,KAAsB,CAA5C,EAA+C;AAC7C;AACD,OAFD,MAEO;AACLO,QAAAA,CAAC,GAAGA,CAAC,CAAClF,QAAF,CAAW,CAAX,CAAJ;AACAoG,QAAAA,CAAC,CAACvE,IAAF,CAAO,CAAP;AACD;AACF;;AAED,WAAO,CAACqD,CAAD,EAAIkB,CAAJ,CAAP;AACD,GA9LgC;;AAgMjC;;;AAIAvC,EAAAA,QAAQ,CAACoP,IAAD,EAAa9F,KAAb;AACN,QAAInK,IAAI,CAACC,MAAL,CAAYgQ,IAAZ,CAAJ,EAAuB;AACrB,YAAM,IAAI3D,KAAJ,iEACqD6D,IAAI,CAACC,SAAL,CACvDH,IADuD,CADrD,EAAN;AAKD;;AAED,QAAMW,OAAO,GAAGC,OAAO,CAAC;AAAE7T,MAAAA,QAAQ,EAAEiT,IAAI,CAACjT;AAAjB,KAAD,EAA8BuL,CAAC;AACpD,UAAM,CAACiC,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAArB;AACA,UAAMyC,WAAW,GAAGhM,IAAI,CAACuC,KAAL,CAAWoF,CAAX,EAAc;AAChChI,QAAAA,OAAO,EAAE,IADuB;AAEhCsM,QAAAA,IAAI,EAAE;AAAA,cAAC,GAAGpO,IAAH,CAAD;AAAA,iBAAc,CAACmB,KAAK,CAAC4M,QAAN,CAAerC,KAAf,EAAsB1L,IAAtB,CAAf;AAAA;AAF0B,OAAd,CAApB;;AAKA,WAAK,IAAM,GAAGA,IAAH,CAAX,IAAuBmO,WAAvB,EAAoC;AAClC,YAAI,CAAChN,KAAK,CAAC4M,QAAN,CAAerC,KAAf,EAAsB1L,IAAtB,CAAL,EAAkC;AAChC,cAAM8N,MAAM,GAAG3L,IAAI,CAAC2L,MAAL,CAAYhE,CAAZ,EAAe9J,IAAf,CAAf;AACA,cAAMyR,KAAK,GAAGzR,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB;AACA4K,UAAAA,MAAM,CAACvP,QAAP,CAAgB8T,MAAhB,CAAuBZ,KAAvB,EAA8B,CAA9B;AACD;;AAED,YAAIpR,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkB+H,GAAG,CAAC/H,IAAtB,CAAJ,EAAiC;AAC/B,cAAMmN,IAAI,GAAGhL,IAAI,CAACgL,IAAL,CAAUrD,CAAV,EAAa9J,IAAb,CAAb;AACAmN,UAAAA,IAAI,CAACtK,IAAL,GAAYsK,IAAI,CAACtK,IAAL,CAAU8E,KAAV,CAAgB,CAAhB,EAAmBI,GAAG,CAACkF,MAAvB,CAAZ;AACD;;AAED,YAAI5M,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkB+L,KAAK,CAAC/L,IAAxB,CAAJ,EAAmC;AACjC,cAAMmN,KAAI,GAAGhL,IAAI,CAACgL,IAAL,CAAUrD,CAAV,EAAa9J,IAAb,CAAb;;AACAmN,UAAAA,KAAI,CAACtK,IAAL,GAAYsK,KAAI,CAACtK,IAAL,CAAU8E,KAAV,CAAgBoE,KAAK,CAACkB,MAAtB,CAAZ;AACD;AACF;;AAED,UAAIhO,MAAM,CAACuE,QAAP,CAAgBsG,CAAhB,CAAJ,EAAwB;AACtBA,QAAAA,CAAC,CAACrL,SAAF,GAAc,IAAd;AACD;AACF,KA5BsB,CAAvB;AA8BA,WAAO0T,OAAO,CAAC5T,QAAf;AACD,GA5OgC;;AA8OjC;;;;AAKAmB,EAAAA,GAAG,CAAC8R,IAAD,EAAaxR,IAAb;AACD,QAAI0C,IAAI,GAAG8O,IAAX;;AAEA,SAAK,IAAI9N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1D,IAAI,CAACkD,MAAzB,EAAiCQ,CAAC,EAAlC,EAAsC;AACpC,UAAMiB,CAAC,GAAG3E,IAAI,CAAC0D,CAAD,CAAd;;AAEA,UAAInC,IAAI,CAACC,MAAL,CAAYkB,IAAZ,KAAqB,CAACA,IAAI,CAACnE,QAAL,CAAcoG,CAAd,CAA1B,EAA4C;AAC1C,cAAM,IAAIkJ,KAAJ,6CACiC7N,IADjC,wBACmD0R,IAAI,CAACC,SAAL,CACrDH,IADqD,CADnD,EAAN;AAKD;;AAED9O,MAAAA,IAAI,GAAGA,IAAI,CAACnE,QAAL,CAAcoG,CAAd,CAAP;AACD;;AAED,WAAOjC,IAAP;AACD,GArQgC;;AAuQjC;;;AAIAvC,EAAAA,GAAG,CAACqR,IAAD,EAAaxR,IAAb;AACD,QAAI0C,IAAI,GAAG8O,IAAX;;AAEA,SAAK,IAAI9N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1D,IAAI,CAACkD,MAAzB,EAAiCQ,CAAC,EAAlC,EAAsC;AACpC,UAAMiB,CAAC,GAAG3E,IAAI,CAAC0D,CAAD,CAAd;;AAEA,UAAInC,IAAI,CAACC,MAAL,CAAYkB,IAAZ,KAAqB,CAACA,IAAI,CAACnE,QAAL,CAAcoG,CAAd,CAA1B,EAA4C;AAC1C,eAAO,KAAP;AACD;;AAEDjC,MAAAA,IAAI,GAAGA,IAAI,CAACnE,QAAL,CAAcoG,CAAd,CAAP;AACD;;AAED,WAAO,IAAP;AACD,GAzRgC;;AA2RjC;;;AAIA2N,EAAAA,MAAM,CAACpR,KAAD;AACJ,WACEK,IAAI,CAACC,MAAL,CAAYN,KAAZ,KAAsB8B,OAAO,CAACC,SAAR,CAAkB/B,KAAlB,CAAtB,IAAkDjC,MAAM,CAACuE,QAAP,CAAgBtC,KAAhB,CADpD;AAGD,GAnSgC;;AAqSjC;;;AAIAiJ,EAAAA,UAAU,CAACjJ,KAAD;AACR,QAAI,CAACsD,KAAK,CAAC8F,OAAN,CAAcpJ,KAAd,CAAL,EAA2B;AACzB,aAAO,KAAP;AACD;;AACD,QAAMqR,YAAY,GAAGhB,kBAAkB,CAAC7R,GAAnB,CAAuBwB,KAAvB,CAArB;;AACA,QAAIqR,YAAY,KAAK5H,SAArB,EAAgC;AAC9B,aAAO4H,YAAP;AACD;;AACD,QAAMpI,UAAU,GAAGjJ,KAAK,CAACqJ,KAAN,CAAYC,GAAG,IAAIrI,IAAI,CAACmQ,MAAL,CAAY9H,GAAZ,CAAnB,CAAnB;AACA+G,IAAAA,kBAAkB,CAAC7Q,GAAnB,CAAuBQ,KAAvB,EAA8BiJ,UAA9B;AACA,WAAOA,UAAP;AACD,GApTgC;;AAsTjC;;;AAIA+C,EAAAA,IAAI,CAACsE,IAAD,EAAaxR,IAAb;AACF,QAAM2E,CAAC,GAAG3E,IAAI,CAAC2H,KAAL,EAAV;AACA,QAAIlE,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAR;;AAEA,WAAOlB,CAAP,EAAU;AACR,UAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBA,CAAC,CAAClF,QAAF,CAAW2E,MAAX,KAAsB,CAA5C,EAA+C;AAC7C;AACD,OAFD,MAEO;AACL,YAAMQ,CAAC,GAAGD,CAAC,CAAClF,QAAF,CAAW2E,MAAX,GAAoB,CAA9B;AACAO,QAAAA,CAAC,GAAGA,CAAC,CAAClF,QAAF,CAAWmF,CAAX,CAAJ;AACAiB,QAAAA,CAAC,CAACvE,IAAF,CAAOsD,CAAP;AACD;AACF;;AAED,WAAO,CAACD,CAAD,EAAIkB,CAAJ,CAAP;AACD,GAzUgC;;AA2UjC;;;AAIAwI,EAAAA,IAAI,CAACqE,IAAD,EAAaxR,IAAb;AACF,QAAM0C,IAAI,GAAGP,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAexR,IAAf,CAAb;;AAEA,QAAI,CAACuB,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAL,EAAwB;AACtB,YAAM,IAAImL,KAAJ,6CACiC7N,IADjC,qDACgF0C,IADhF,EAAN;AAGD;;AAED,WAAOA,IAAP;AACD,GAzVgC;;AA2VjC;;;;;;AAOA,GAAC4B,MAAD,CACEkN,IADF,EAEExR,IAFF;QAGEmL,8EAEI;;AAEJ,SAAK,IAAMxG,CAAX,IAAgBtE,IAAI,CAACiE,MAAL,CAAYtE,IAAZ,EAAkBmL,OAAlB,CAAhB,EAA4C;AAC1C,UAAM1H,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAV;AACA,YAAM,CAAClB,CAAD,EAAIkB,CAAJ,CAAN;AACD;AACF,GA7WgC;;AA+WjC;;;AAIAoG,EAAAA,OAAO,CAACrI,IAAD,EAAagI,KAAb;AACL,WACG1H,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KACCM,OAAO,CAACyH,cAAR,CAAuBC,KAAvB,CADD,IAEC1H,OAAO,CAAC+H,OAAR,CAAgBrI,IAAhB,EAAsBgI,KAAtB,CAFF,IAGCnJ,IAAI,CAACC,MAAL,CAAYkB,IAAZ,KACCnB,IAAI,CAACiR,WAAL,CAAiB9H,KAAjB,CADD,IAECnJ,IAAI,CAACwJ,OAAL,CAAarI,IAAb,EAAmBgI,KAAnB,CANJ;AAQD,GA5XgC;;AA8XjC;;;;;AAMA,GAAChG,KAAD,CACE8M,IADF;QAEErG,8EAKI;AAEJ,QAAM;AAAEiD,MAAAA,IAAF;AAAQtM,MAAAA,OAAO,GAAG;AAAlB,QAA4BqJ,OAAlC;AACA,QAAM;AAAE1G,MAAAA,IAAI,GAAG,EAAT;AAAaiJ,MAAAA;AAAb,QAAoBvC,OAA1B;AACA,QAAMsH,OAAO,GAAG,IAAI7S,GAAJ,EAAhB;AACA,QAAI+E,CAAC,GAAS,EAAd;AACA,QAAIlB,CAAC,GAAG+N,IAAR;;AAEA,WAAO,IAAP,EAAa;AACX,UAAI9D,EAAE,KAAK5L,OAAO,GAAGzB,IAAI,CAAC6Q,QAAL,CAAcvM,CAAd,EAAiB+I,EAAjB,CAAH,GAA0BrN,IAAI,CAACqS,OAAL,CAAa/N,CAAb,EAAgB+I,EAAhB,CAAtC,CAAN,EAAkE;AAChE;AACD;;AAED,UAAI,CAAC+E,OAAO,CAACtS,GAAR,CAAYsD,CAAZ,CAAL,EAAqB;AACnB,cAAM,CAACA,CAAD,EAAIkB,CAAJ,CAAN;AACD,OAPU;;;AAUX,UACE,CAAC8N,OAAO,CAACtS,GAAR,CAAYsD,CAAZ,CAAD,IACA,CAAClC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,CADD,IAEAA,CAAC,CAAClF,QAAF,CAAW2E,MAAX,KAAsB,CAFtB,KAGCkL,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAAC,CAAC3K,CAAD,EAAIkB,CAAJ,CAAD,CAAJ,KAAiB,KAHlC,CADF,EAKE;AACA8N,QAAAA,OAAO,CAAC1S,GAAR,CAAY0D,CAAZ;AACA,YAAIkP,SAAS,GAAG7Q,OAAO,GAAG2B,CAAC,CAAClF,QAAF,CAAW2E,MAAX,GAAoB,CAAvB,GAA2B,CAAlD;;AAEA,YAAI7C,IAAI,CAAC+J,UAAL,CAAgBzF,CAAhB,EAAmBF,IAAnB,CAAJ,EAA8B;AAC5BkO,UAAAA,SAAS,GAAGlO,IAAI,CAACE,CAAC,CAACzB,MAAH,CAAhB;AACD;;AAEDyB,QAAAA,CAAC,GAAGA,CAAC,CAACtB,MAAF,CAASsP,SAAT,CAAJ;AACAlP,QAAAA,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAJ;AACA;AACD,OA1BU;;;AA6BX,UAAIA,CAAC,CAACzB,MAAF,KAAa,CAAjB,EAAoB;AAClB;AACD,OA/BU;;;AAkCX,UAAI,CAACpB,OAAL,EAAc;AACZ,YAAMvB,OAAO,GAAGF,IAAI,CAACiF,IAAL,CAAUX,CAAV,CAAhB;;AAEA,YAAIxC,IAAI,CAAChC,GAAL,CAASqR,IAAT,EAAejR,OAAf,CAAJ,EAA6B;AAC3BoE,UAAAA,CAAC,GAAGpE,OAAJ;AACAkD,UAAAA,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAJ;AACA;AACD;AACF,OA1CU;;;AA6CX,UAAI7C,OAAO,IAAI6C,CAAC,CAACA,CAAC,CAACzB,MAAF,GAAW,CAAZ,CAAD,KAAoB,CAAnC,EAAsC;AACpC,YAAM3C,QAAO,GAAGF,IAAI,CAACyE,QAAL,CAAcH,CAAd,CAAhB;;AACAA,QAAAA,CAAC,GAAGpE,QAAJ;AACAkD,QAAAA,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAJ;AACA;AACD,OAlDU;;;AAqDXA,MAAAA,CAAC,GAAGtE,IAAI,CAACyN,MAAL,CAAYnJ,CAAZ,CAAJ;AACAlB,MAAAA,CAAC,GAAGtB,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAe7M,CAAf,CAAJ;AACA8N,MAAAA,OAAO,CAAC1S,GAAR,CAAY0D,CAAZ;AACD;AACF,GA5cgC;;AA8cjC;;;AAIAqK,EAAAA,MAAM,CAAC0D,IAAD,EAAaxR,IAAb;AACJ,QAAMqP,UAAU,GAAGhP,IAAI,CAACyN,MAAL,CAAY9N,IAAZ,CAAnB;AACA,QAAM2E,CAAC,GAAGxC,IAAI,CAACzC,GAAL,CAAS8R,IAAT,EAAenC,UAAf,CAAV;;AAEA,QAAI9N,IAAI,CAACC,MAAL,CAAYmD,CAAZ,CAAJ,EAAoB;AAClB,YAAM,IAAIkJ,KAAJ,0CAC8B7N,IAD9B,8CAAN;AAGD;;AAED,WAAO2E,CAAP;AACD,GA7dgC;;AA+djC;;;;;;;AAQA6L,EAAAA,MAAM,CAAC9N,IAAD;AACJ,QAAInB,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAJ,EAAuB;AACrB,aAAOA,IAAI,CAACG,IAAZ;AACD,KAFD,MAEO;AACL,aAAOH,IAAI,CAACnE,QAAL,CAAcwQ,GAAd,CAAkB5M,IAAI,CAACqO,MAAvB,EAA+BtQ,IAA/B,CAAoC,EAApC,CAAP;AACD;AACF,GA7egC;;AA+ejC;;;AAIA,GAAC0S,KAAD,CACEpB,IADF;QAEErG,8EAKI;;AAEJ,SAAK,IAAM,CAACzI,IAAD,EAAO1C,IAAP,CAAX,IAA2BmC,IAAI,CAACuC,KAAL,CAAW8M,IAAX,EAAiBrG,OAAjB,CAA3B,EAAsD;AACpD,UAAI5J,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAJ,EAAuB;AACrB,cAAM,CAACA,IAAD,EAAO1C,IAAP,CAAN;AACD;AACF;AACF;;AAjgBgC;;;;;IC2DtByM,SAAS,GAAuB;AAC3C;;;AAIAoG,EAAAA,eAAe,CAAC3R,KAAD;AACb,WAAOuL,SAAS,CAACqG,WAAV,CAAsB5R,KAAtB,KAAgCA,KAAK,CAACL,IAAN,CAAWkS,QAAX,CAAoB,OAApB,CAAvC;AACD,GAP0C;;AAS3C;;;AAIAD,EAAAA,WAAW,CAAC5R,KAAD;AACT,QAAI,CAACgJ,aAAa,CAAChJ,KAAD,CAAlB,EAA2B;AACzB,aAAO,KAAP;AACD;;AAED,YAAQA,KAAK,CAACL,IAAd;AACE,WAAK,aAAL;AACE,eAAOR,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,KAA2BmC,IAAI,CAACmQ,MAAL,CAAYpR,KAAK,CAACwB,IAAlB,CAAlC;;AACF,WAAK,aAAL;AACE,eACE,OAAOxB,KAAK,CAAC+L,MAAb,KAAwB,QAAxB,IACA,OAAO/L,KAAK,CAAC2B,IAAb,KAAsB,QADtB,IAEAxC,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,CAHF;;AAKF,WAAK,YAAL;AACE,eACE,OAAOkB,KAAK,CAAC8R,QAAb,KAA0B,QAA1B,IACA3S,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,CADA,IAEAkK,aAAa,CAAChJ,KAAK,CAACgR,UAAP,CAHf;;AAKF,WAAK,WAAL;AACE,eAAO7R,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,KAA2BK,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAACX,OAAlB,CAAlC;;AACF,WAAK,aAAL;AACE,eAAOF,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,KAA2BmC,IAAI,CAACmQ,MAAL,CAAYpR,KAAK,CAACwB,IAAlB,CAAlC;;AACF,WAAK,aAAL;AACE,eACE,OAAOxB,KAAK,CAAC+L,MAAb,KAAwB,QAAxB,IACA,OAAO/L,KAAK,CAAC2B,IAAb,KAAsB,QADtB,IAEAxC,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,CAHF;;AAKF,WAAK,UAAL;AACE,eACEK,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,KACAkK,aAAa,CAAChJ,KAAK,CAACgR,UAAP,CADb,IAEAhI,aAAa,CAAChJ,KAAK,CAAC+R,aAAP,CAHf;;AAKF,WAAK,eAAL;AACE,eACG/R,KAAK,CAACgR,UAAN,KAAqB,IAArB,IAA6B/Q,KAAK,CAACqL,OAAN,CAActL,KAAK,CAAC+R,aAApB,CAA9B,IACC/R,KAAK,CAAC+R,aAAN,KAAwB,IAAxB,IAAgC9R,KAAK,CAACqL,OAAN,CAActL,KAAK,CAACgR,UAApB,CADjC,IAEChI,aAAa,CAAChJ,KAAK,CAACgR,UAAP,CAAb,IACChI,aAAa,CAAChJ,KAAK,CAAC+R,aAAP,CAJjB;;AAMF,WAAK,YAAL;AACE,eACE5S,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,KACA,OAAOkB,KAAK,CAAC8R,QAAb,KAA0B,QAD1B,IAEA9I,aAAa,CAAChJ,KAAK,CAACgR,UAAP,CAHf;;AAKF;AACE,eAAO,KAAP;AA7CJ;AA+CD,GAjE0C;;AAmE3C;;;AAIAxF,EAAAA,eAAe,CAACxL,KAAD;AACb,WACEsD,KAAK,CAAC8F,OAAN,CAAcpJ,KAAd,KAAwBA,KAAK,CAACqJ,KAAN,CAAYC,GAAG,IAAIiC,SAAS,CAACqG,WAAV,CAAsBtI,GAAtB,CAAnB,CAD1B;AAGD,GA3E0C;;AA6E3C;;;AAIA0I,EAAAA,oBAAoB,CAAChS,KAAD;AAClB,WAAOuL,SAAS,CAACqG,WAAV,CAAsB5R,KAAtB,KAAgCA,KAAK,CAACL,IAAN,CAAWkS,QAAX,CAAoB,YAApB,CAAvC;AACD,GAnF0C;;AAqF3C;;;AAIAI,EAAAA,eAAe,CAACjS,KAAD;AACb,WAAOuL,SAAS,CAACqG,WAAV,CAAsB5R,KAAtB,KAAgCA,KAAK,CAACL,IAAN,CAAWkS,QAAX,CAAoB,OAApB,CAAvC;AACD,GA3F0C;;AA6F3C;;;;AAKAK,EAAAA,OAAO,CAACrU,EAAD;AACL,YAAQA,EAAE,CAAC8B,IAAX;AACE,WAAK,aAAL;AAAoB;AAClB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,aAAL;AAAoB;AAClB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,YAAL;AAAmB;AACjB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE,YAAtB;AAAoCb,YAAAA,IAAI,EAAEK,IAAI,CAACyE,QAAL,CAAc/F,EAAE,CAACiB,IAAjB;AAA1C;AACD;;AAED,WAAK,WAAL;AAAkB;AAChB,cAAM;AAAEO,YAAAA,OAAF;AAAWP,YAAAA;AAAX,cAAoBjB,EAA1B,CADgB;;AAIhB,cAAIsB,IAAI,CAAC4D,MAAL,CAAY1D,OAAZ,EAAqBP,IAArB,CAAJ,EAAgC;AAC9B,mBAAOjB,EAAP;AACD,WANe;AAShB;;;AACA,cAAIsB,IAAI,CAACgT,SAAL,CAAerT,IAAf,EAAqBO,OAArB,CAAJ,EAAmC;AACjC,uDAAYxB,EAAZ;AAAgBiB,cAAAA,IAAI,EAAEO,OAAtB;AAA+BA,cAAAA,OAAO,EAAEP;AAAxC;AACD,WAZe;AAehB;AACA;AACA;AACA;AACA;;;AACA,cAAMsT,WAAW,GAAGjT,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,CAApB;AACA,cAAMwU,cAAc,GAAGlT,IAAI,CAACjB,SAAL,CAAeiB,IAAI,CAACiF,IAAL,CAAUtF,IAAV,CAAf,EAAgCjB,EAAhC,CAAvB;AACA,qDAAYA,EAAZ;AAAgBiB,YAAAA,IAAI,EAAEsT,WAAtB;AAAmC/S,YAAAA,OAAO,EAAEgT;AAA5C;AACD;;AAED,WAAK,aAAL;AAAoB;AAClB,qDAAYxU,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,aAAL;AAAoB;AAClB,qDAAY9B,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE;AAAtB;AACD;;AAED,WAAK,UAAL;AAAiB;AACf,cAAM;AAAEqR,YAAAA,UAAF;AAAce,YAAAA;AAAd,cAAgClU,EAAtC;AACA,qDAAYA,EAAZ;AAAgBmT,YAAAA,UAAU,EAAEe,aAA5B;AAA2CA,YAAAA,aAAa,EAAEf;AAA1D;AACD;;AAED,WAAK,eAAL;AAAsB;AACpB,cAAM;AAAEA,YAAAA,UAAU,EAAVA,WAAF;AAAce,YAAAA,aAAa,EAAbA;AAAd,cAAgClU,EAAtC;;AAEA,cAAImT,WAAU,IAAI,IAAlB,EAAwB;AACtB,uDACKnT,EADL;AAEEmT,cAAAA,UAAU,EAAEe,cAFd;AAGEA,cAAAA,aAAa,EAAE;AAHjB;AAKD,WAND,MAMO,IAAIA,cAAa,IAAI,IAArB,EAA2B;AAChC,uDACKlU,EADL;AAEEmT,cAAAA,UAAU,EAAE,IAFd;AAGEe,cAAAA,aAAa,EAAEf;AAHjB;AAKD,WANM,MAMA;AACL,uDAAYnT,EAAZ;AAAgBmT,cAAAA,UAAU,EAAEe,cAA5B;AAA2CA,cAAAA,aAAa,EAAEf;AAA1D;AACD;AACF;;AAED,WAAK,YAAL;AAAmB;AACjB,qDAAYnT,EAAZ;AAAgB8B,YAAAA,IAAI,EAAE,YAAtB;AAAoCb,YAAAA,IAAI,EAAEK,IAAI,CAACiF,IAAL,CAAUvG,EAAE,CAACiB,IAAb;AAA1C;AACD;AAzEH;AA2ED;;AA9K0C;;ICtGhCK,IAAI,GAAkB;AACjC;;;;;;AAOAuE,EAAAA,SAAS,CAAC5E,IAAD;QAAamL,8EAAiC;AACrD,QAAM;AAAErJ,MAAAA,OAAO,GAAG;AAAZ,QAAsBqJ,OAA5B;AACA,QAAIqI,KAAK,GAAGnT,IAAI,CAACiE,MAAL,CAAYtE,IAAZ,EAAkBmL,OAAlB,CAAZ;;AAEA,QAAIrJ,OAAJ,EAAa;AACX0R,MAAAA,KAAK,GAAGA,KAAK,CAAC7L,KAAN,CAAY,CAAZ,CAAR;AACD,KAFD,MAEO;AACL6L,MAAAA,KAAK,GAAGA,KAAK,CAAC7L,KAAN,CAAY,CAAZ,EAAe,CAAC,CAAhB,CAAR;AACD;;AAED,WAAO6L,KAAP;AACD,GAnBgC;;AAqBjC;;;AAIA/D,EAAAA,MAAM,CAACzP,IAAD,EAAa8R,OAAb;AACJ,QAAMrC,MAAM,GAAS,EAArB;;AAEA,SAAK,IAAI/L,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1D,IAAI,CAACkD,MAAT,IAAmBQ,CAAC,GAAGoO,OAAO,CAAC5O,MAA/C,EAAuDQ,CAAC,EAAxD,EAA4D;AAC1D,UAAM+P,EAAE,GAAGzT,IAAI,CAAC0D,CAAD,CAAf;AACA,UAAMgQ,EAAE,GAAG5B,OAAO,CAACpO,CAAD,CAAlB;;AAEA,UAAI+P,EAAE,KAAKC,EAAX,EAAe;AACb;AACD;;AAEDjE,MAAAA,MAAM,CAACrP,IAAP,CAAYqT,EAAZ;AACD;;AAED,WAAOhE,MAAP;AACD,GAxCgC;;AA0CjC;;;;;;;;AASAlB,EAAAA,OAAO,CAACvO,IAAD,EAAa8R,OAAb;AACL,QAAM6B,GAAG,GAAGC,IAAI,CAACD,GAAL,CAAS3T,IAAI,CAACkD,MAAd,EAAsB4O,OAAO,CAAC5O,MAA9B,CAAZ;;AAEA,SAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiQ,GAApB,EAAyBjQ,CAAC,EAA1B,EAA8B;AAC5B,UAAI1D,IAAI,CAAC0D,CAAD,CAAJ,GAAUoO,OAAO,CAACpO,CAAD,CAArB,EAA0B,OAAO,CAAC,CAAR;AAC1B,UAAI1D,IAAI,CAAC0D,CAAD,CAAJ,GAAUoO,OAAO,CAACpO,CAAD,CAArB,EAA0B,OAAO,CAAP;AAC3B;;AAED,WAAO,CAAP;AACD,GA5DgC;;AA8DjC;;;AAIAmQ,EAAAA,SAAS,CAAC7T,IAAD,EAAa8R,OAAb;AACP,QAAMpO,CAAC,GAAG1D,IAAI,CAACkD,MAAL,GAAc,CAAxB;AACA,QAAM4Q,EAAE,GAAG9T,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAcjE,CAAd,CAAX;AACA,QAAMqQ,EAAE,GAAGjC,OAAO,CAACnK,KAAR,CAAc,CAAd,EAAiBjE,CAAjB,CAAX;AACA,QAAM+P,EAAE,GAAGzT,IAAI,CAAC0D,CAAD,CAAf;AACA,QAAMgQ,EAAE,GAAG5B,OAAO,CAACpO,CAAD,CAAlB;AACA,WAAOrD,IAAI,CAAC4D,MAAL,CAAY6P,EAAZ,EAAgBC,EAAhB,KAAuBN,EAAE,GAAGC,EAAnC;AACD,GAzEgC;;AA2EjC;;;AAIAM,EAAAA,MAAM,CAAChU,IAAD,EAAa8R,OAAb;AACJ,QAAMpO,CAAC,GAAG1D,IAAI,CAACkD,MAAf;AACA,QAAM4Q,EAAE,GAAG9T,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAcjE,CAAd,CAAX;AACA,QAAMqQ,EAAE,GAAGjC,OAAO,CAACnK,KAAR,CAAc,CAAd,EAAiBjE,CAAjB,CAAX;AACA,WAAOrD,IAAI,CAAC4D,MAAL,CAAY6P,EAAZ,EAAgBC,EAAhB,CAAP;AACD,GApFgC;;AAsFjC;;;AAIAE,EAAAA,UAAU,CAACjU,IAAD,EAAa8R,OAAb;AACR,QAAMpO,CAAC,GAAG1D,IAAI,CAACkD,MAAL,GAAc,CAAxB;AACA,QAAM4Q,EAAE,GAAG9T,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAcjE,CAAd,CAAX;AACA,QAAMqQ,EAAE,GAAGjC,OAAO,CAACnK,KAAR,CAAc,CAAd,EAAiBjE,CAAjB,CAAX;AACA,QAAM+P,EAAE,GAAGzT,IAAI,CAAC0D,CAAD,CAAf;AACA,QAAMgQ,EAAE,GAAG5B,OAAO,CAACpO,CAAD,CAAlB;AACA,WAAOrD,IAAI,CAAC4D,MAAL,CAAY6P,EAAZ,EAAgBC,EAAhB,KAAuBN,EAAE,GAAGC,EAAnC;AACD,GAjGgC;;AAmGjC;;;AAIAzP,EAAAA,MAAM,CAACjE,IAAD,EAAa8R,OAAb;AACJ,WACE9R,IAAI,CAACkD,MAAL,KAAgB4O,OAAO,CAAC5O,MAAxB,IAAkClD,IAAI,CAACuK,KAAL,CAAW,CAAC9G,CAAD,EAAIC,CAAJ,KAAUD,CAAC,KAAKqO,OAAO,CAACpO,CAAD,CAAlC,CADpC;AAGD,GA3GgC;;AA6GjC;;;AAIAwQ,EAAAA,WAAW,CAAClU,IAAD;AACT,WAAOA,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAJ,GAAwB,CAA/B;AACD,GAnHgC;;AAqHjC;;;AAIAwP,EAAAA,OAAO,CAAC1S,IAAD,EAAa8R,OAAb;AACL,WAAOzR,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CAAvC;AACD,GA3HgC;;AA6HjC;;;AAIA1H,EAAAA,UAAU,CAACpK,IAAD,EAAa8R,OAAb;AACR,WAAO9R,IAAI,CAACkD,MAAL,GAAc4O,OAAO,CAAC5O,MAAtB,IAAgC7C,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CAAvE;AACD,GAnIgC;;AAqIjC;;;AAIAZ,EAAAA,QAAQ,CAAClR,IAAD,EAAa8R,OAAb;AACN,WAAOzR,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CAAC,CAAxC;AACD,GA3IgC;;AA6IjC;;;AAIAqC,EAAAA,OAAO,CAACnU,IAAD,EAAa8R,OAAb;AACL,WACE9R,IAAI,CAACkD,MAAL,KAAgB4O,OAAO,CAAC5O,MAAR,GAAiB,CAAjC,IAAsC7C,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CADxE;AAGD,GArJgC;;AAuJjC;;;AAIAsC,EAAAA,QAAQ,CAACpU,IAAD,EAAa8R,OAAb;AACN,WAAO9R,IAAI,CAACkD,MAAL,IAAe4O,OAAO,CAAC5O,MAAvB,IAAiC7C,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CAAxE;AACD,GA7JgC;;AA+JjC;;;AAIAuC,EAAAA,YAAY,CAACrU,IAAD,EAAa8R,OAAb;AACV,WAAO9R,IAAI,CAACkD,MAAL,GAAc4O,OAAO,CAAC5O,MAAtB,IAAgC7C,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CAAvE;AACD,GArKgC;;AAuKjC;;;AAIAwC,EAAAA,QAAQ,CAACtU,IAAD,EAAa8R,OAAb;AACN,WACE9R,IAAI,CAACkD,MAAL,GAAc,CAAd,KAAoB4O,OAAO,CAAC5O,MAA5B,IAAsC7C,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmB8R,OAAnB,MAAgC,CADxE;AAGD,GA/KgC;;AAiLjC;;;AAIAlE,EAAAA,MAAM,CAAC1M,KAAD;AACJ,WACEsD,KAAK,CAAC8F,OAAN,CAAcpJ,KAAd,MACCA,KAAK,CAACgC,MAAN,KAAiB,CAAjB,IAAsB,OAAOhC,KAAK,CAAC,CAAD,CAAZ,KAAoB,QAD3C,CADF;AAID,GA1LgC;;AA4LjC;;;AAIAmS,EAAAA,SAAS,CAACrT,IAAD,EAAa8R,OAAb;AACP,QAAI9R,IAAI,CAACkD,MAAL,KAAgB4O,OAAO,CAAC5O,MAA5B,EAAoC;AAClC,aAAO,KAAP;AACD;;AAED,QAAM4Q,EAAE,GAAG9T,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAX;AACA,QAAMoM,EAAE,GAAGjC,OAAO,CAACnK,KAAR,CAAc,CAAd,EAAiB,CAAC,CAAlB,CAAX;AACA,QAAM4M,EAAE,GAAGvU,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAf;AACA,QAAMsR,EAAE,GAAG1C,OAAO,CAACA,OAAO,CAAC5O,MAAR,GAAiB,CAAlB,CAAlB;AACA,WAAOqR,EAAE,KAAKC,EAAP,IAAanU,IAAI,CAAC4D,MAAL,CAAY6P,EAAZ,EAAgBC,EAAhB,CAApB;AACD,GA1MgC;;AA4MjC;;;;;;;AAQAzP,EAAAA,MAAM,CACJtE,IADI;QAEJmL,8EAEI;AAEJ,QAAM;AAAErJ,MAAAA,OAAO,GAAG;AAAZ,QAAsBqJ,OAA5B;AACA,QAAMsJ,IAAI,GAAW,EAArB;;AAEA,SAAK,IAAI/Q,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI1D,IAAI,CAACkD,MAA1B,EAAkCQ,CAAC,EAAnC,EAAuC;AACrC+Q,MAAAA,IAAI,CAACrU,IAAL,CAAUJ,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAcjE,CAAd,CAAV;AACD;;AAED,QAAI5B,OAAJ,EAAa;AACX2S,MAAAA,IAAI,CAAC3S,OAAL;AACD;;AAED,WAAO2S,IAAP;AACD,GAtOgC;;AAwOjC;;;AAIAnP,EAAAA,IAAI,CAACtF,IAAD;AACF,QAAIA,IAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI2K,KAAJ,oDACwC7N,IADxC,sCAAN;AAGD;;AAED,QAAMkN,IAAI,GAAGlN,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAjB;AACA,WAAOlD,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,EAAkBtE,MAAlB,CAAyB6J,IAAI,GAAG,CAAhC,CAAP;AACD,GArPgC;;AAuPjC;;;;;;;AAOA5M,EAAAA,yBAAyB,CAACoU,SAAD;AACvB,YAAQA,SAAS,CAAC7T,IAAlB;AACE,WAAK,aAAL;AACA,WAAK,aAAL;AACA,WAAK,YAAL;AACA,WAAK,YAAL;AACA,WAAK,WAAL;AACE,eAAO,IAAP;;AACF;AACE,eAAO,KAAP;AARJ;AAUD,GAzQgC;;AA2QjC;;;AAIAiN,EAAAA,MAAM,CAAC9N,IAAD;AACJ,QAAIA,IAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI2K,KAAJ,wDAA0D7N,IAA1D,QAAN;AACD;;AAED,WAAOA,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAP;AACD,GArRgC;;AAuRjC;;;AAIA7C,EAAAA,QAAQ,CAAC9E,IAAD;AACN,QAAIA,IAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB,YAAM,IAAI2K,KAAJ,wDAC4C7N,IAD5C,0CAAN;AAGD;;AAED,QAAMkN,IAAI,GAAGlN,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAjB;;AAEA,QAAIgK,IAAI,IAAI,CAAZ,EAAe;AACb,YAAM,IAAIW,KAAJ,+DACmD7N,IADnD,oDAAN;AAGD;;AAED,WAAOA,IAAI,CAAC2H,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,EAAkBtE,MAAlB,CAAyB6J,IAAI,GAAG,CAAhC,CAAP;AACD,GA3SgC;;AA6SjC;;;AAIAyH,EAAAA,QAAQ,CAAC3U,IAAD,EAAaiF,QAAb;AACN,QAAI,CAAC5E,IAAI,CAAC+J,UAAL,CAAgBnF,QAAhB,EAA0BjF,IAA1B,CAAD,IAAoC,CAACK,IAAI,CAAC4D,MAAL,CAAYjE,IAAZ,EAAkBiF,QAAlB,CAAzC,EAAsE;AACpE,YAAM,IAAI4I,KAAJ,4CACgC7N,IADhC,gCAC0DiF,QAD1D,sDAAN;AAGD;;AAED,WAAOjF,IAAI,CAAC2H,KAAL,CAAW1C,QAAQ,CAAC/B,MAApB,CAAP;AACD,GAzTgC;;AA2TjC;;;AAIA9D,EAAAA,SAAS,CACPY,IADO,EAEP0U,SAFO;QAGPvJ,8EAAwD;AAExD,WAAOiH,OAAO,CAACpS,IAAD,EAAO2E,CAAC;AACpB,UAAM;AAAEkL,QAAAA,QAAQ,GAAG;AAAb,UAA2B1E,OAAjC;;AAGA,UAAI,CAACnL,IAAD,IAAS,CAAAA,IAAI,SAAJ,IAAAA,IAAI,WAAJ,YAAAA,IAAI,CAAEkD,MAAN,MAAiB,CAA9B,EAAiC;AAC/B;AACD;;AAED,UAAIyB,CAAC,KAAK,IAAV,EAAgB;AACd,eAAO,IAAP;AACD;;AAED,cAAQ+P,SAAS,CAAC7T,IAAlB;AACE,aAAK,aAAL;AAAoB;AAClB,gBAAM;AAAEb,cAAAA,IAAI,EAAEjB;AAAR,gBAAe2V,SAArB;;AAEA,gBACErU,IAAI,CAAC4D,MAAL,CAAYlF,EAAZ,EAAgB4F,CAAhB,KACAtE,IAAI,CAAC4T,UAAL,CAAgBlV,EAAhB,EAAoB4F,CAApB,CADA,IAEAtE,IAAI,CAAC+J,UAAL,CAAgBrL,EAAhB,EAAoB4F,CAApB,CAHF,EAIE;AACAA,cAAAA,CAAC,CAAC5F,EAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAED;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAM;AAAElD,cAAAA,IAAI,EAAEjB;AAAR,gBAAe2V,SAArB;;AAEA,gBAAIrU,IAAI,CAAC4D,MAAL,CAAYlF,GAAZ,EAAgB4F,CAAhB,KAAsBtE,IAAI,CAAC+J,UAAL,CAAgBrL,GAAhB,EAAoB4F,CAApB,CAA1B,EAAkD;AAChD,qBAAO,IAAP;AACD,aAFD,MAEO,IAAItE,IAAI,CAAC4T,UAAL,CAAgBlV,GAAhB,EAAoB4F,CAApB,CAAJ,EAA4B;AACjCA,cAAAA,CAAC,CAAC5F,GAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAED;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAM;AAAElD,cAAAA,IAAI,EAAEjB,IAAR;AAAYiU,cAAAA;AAAZ,gBAAyB0B,SAA/B;;AAEA,gBAAIrU,IAAI,CAAC4D,MAAL,CAAYlF,IAAZ,EAAgB4F,CAAhB,KAAsBtE,IAAI,CAAC4T,UAAL,CAAgBlV,IAAhB,EAAoB4F,CAApB,CAA1B,EAAkD;AAChDA,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD,aAFD,MAEO,IAAI7C,IAAI,CAAC+J,UAAL,CAAgBrL,IAAhB,EAAoB4F,CAApB,CAAJ,EAA4B;AACjCA,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACAyB,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAJ,CAAD,IAAgB8P,QAAhB;AACD;;AAED;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAM;AAAEhT,cAAAA,IAAI,EAAEjB,IAAR;AAAYiU,cAAAA,QAAQ,EAARA;AAAZ,gBAAyB0B,SAA/B;;AAEA,gBAAIrU,IAAI,CAAC4D,MAAL,CAAYlF,IAAZ,EAAgB4F,CAAhB,CAAJ,EAAwB;AACtB,kBAAIkL,QAAQ,KAAK,SAAjB,EAA4B;AAC1BlL,gBAAAA,CAAC,CAACA,CAAC,CAACzB,MAAF,GAAW,CAAZ,CAAD,IAAmB,CAAnB;AACD,eAFD,MAEO,IAAI2M,QAAQ,KAAK,UAAjB,EAA6B,CAA7B,MAEA;AACL,uBAAO,IAAP;AACD;AACF,aARD,MAQO,IAAIxP,IAAI,CAAC4T,UAAL,CAAgBlV,IAAhB,EAAoB4F,CAApB,CAAJ,EAA4B;AACjCA,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD,aAFM,MAEA,IAAI7C,IAAI,CAAC+J,UAAL,CAAgBrL,IAAhB,EAAoB4F,CAApB,KAA0B3E,IAAI,CAACjB,IAAE,CAACmE,MAAJ,CAAJ,IAAmB8P,SAAjD,EAA2D;AAChErO,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACAyB,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAJ,CAAD,IAAgB8P,SAAhB;AACD;;AAED;AACD;;AAED,aAAK,WAAL;AAAkB;AAChB,gBAAM;AAAEhT,cAAAA,IAAI,EAAEjB,IAAR;AAAYwB,cAAAA,OAAO,EAAEqU;AAArB,gBAA6BF,SAAnC,CADgB;;AAIhB,gBAAIrU,IAAI,CAAC4D,MAAL,CAAYlF,IAAZ,EAAgB6V,GAAhB,CAAJ,EAA0B;AACxB;AACD;;AAED,gBAAIvU,IAAI,CAAC+J,UAAL,CAAgBrL,IAAhB,EAAoB4F,CAApB,KAA0BtE,IAAI,CAAC4D,MAAL,CAAYlF,IAAZ,EAAgB4F,CAAhB,CAA9B,EAAkD;AAChD,kBAAMkQ,IAAI,GAAGD,GAAG,CAACjN,KAAJ,EAAb;;AAEA,kBAAItH,IAAI,CAAC4T,UAAL,CAAgBlV,IAAhB,EAAoB6V,GAApB,KAA4B7V,IAAE,CAACmE,MAAH,GAAY0R,GAAG,CAAC1R,MAAhD,EAAwD;AACtD2R,gBAAAA,IAAI,CAAC9V,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAJ,IAAuB,CAAvB;AACD;;AAED,qBAAO2R,IAAI,CAACxR,MAAL,CAAYsB,CAAC,CAACgD,KAAF,CAAQ5I,IAAE,CAACmE,MAAX,CAAZ,CAAP;AACD,aARD,MAQO,IACL7C,IAAI,CAACgT,SAAL,CAAetU,IAAf,EAAmB6V,GAAnB,MACCvU,IAAI,CAAC+J,UAAL,CAAgBwK,GAAhB,EAAqBjQ,CAArB,KAA2BtE,IAAI,CAAC4D,MAAL,CAAY2Q,GAAZ,EAAiBjQ,CAAjB,CAD5B,CADK,EAGL;AACA,kBAAItE,IAAI,CAAC4T,UAAL,CAAgBlV,IAAhB,EAAoB4F,CAApB,CAAJ,EAA4B;AAC1BA,gBAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD,eAFD,MAEO;AACLyB,gBAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;AACF,aATM,MASA,IACL7C,IAAI,CAAC4T,UAAL,CAAgBW,GAAhB,EAAqBjQ,CAArB,KACAtE,IAAI,CAAC4D,MAAL,CAAY2Q,GAAZ,EAAiBjQ,CAAjB,CADA,IAEAtE,IAAI,CAAC+J,UAAL,CAAgBwK,GAAhB,EAAqBjQ,CAArB,CAHK,EAIL;AACA,kBAAItE,IAAI,CAAC4T,UAAL,CAAgBlV,IAAhB,EAAoB4F,CAApB,CAAJ,EAA4B;AAC1BA,gBAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAEDyB,cAAAA,CAAC,CAACiQ,GAAG,CAAC1R,MAAJ,GAAa,CAAd,CAAD,IAAqB,CAArB;AACD,aAVM,MAUA,IAAI7C,IAAI,CAAC4T,UAAL,CAAgBlV,IAAhB,EAAoB4F,CAApB,CAAJ,EAA4B;AACjC,kBAAItE,IAAI,CAAC4D,MAAL,CAAY2Q,GAAZ,EAAiBjQ,CAAjB,CAAJ,EAAyB;AACvBA,gBAAAA,CAAC,CAACiQ,GAAG,CAAC1R,MAAJ,GAAa,CAAd,CAAD,IAAqB,CAArB;AACD;;AAEDyB,cAAAA,CAAC,CAAC5F,IAAE,CAACmE,MAAH,GAAY,CAAb,CAAD,IAAoB,CAApB;AACD;;AAED;AACD;AAzGH;AA2GD,KAvHa,CAAd;AAwHD;;AA5bgC;;IC7BtB/D,OAAO,GAAqB;AACvC;;;AAIAC,EAAAA,SAAS,CAACJ,GAAD,EAAeD,EAAf;AACP,QAAM;AAAE+Q,MAAAA,OAAF;AAAWD,MAAAA;AAAX,QAAwB7Q,GAA9B;;AAEA,QAAI8Q,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAM9P,IAAI,GAAGK,IAAI,CAACjB,SAAL,CAAe0Q,OAAf,EAAwB/Q,EAAxB,EAA4B;AAAE8Q,MAAAA;AAAF,KAA5B,CAAb;AACA7Q,IAAAA,GAAG,CAAC8Q,OAAJ,GAAc9P,IAAd;;AAEA,QAAIA,IAAI,IAAI,IAAZ,EAAkB;AAChBhB,MAAAA,GAAG,CAAC+Q,KAAJ;AACD;AACF;;AAlBsC;;;;;ICa5BnD,KAAK,GAAmB;AACnC;;;;AAKA2B,EAAAA,OAAO,CAAChD,KAAD,EAAeuG,OAAf;AACL,QAAMgD,MAAM,GAAGzU,IAAI,CAACkO,OAAL,CAAahD,KAAK,CAACvL,IAAnB,EAAyB8R,OAAO,CAAC9R,IAAjC,CAAf;;AAEA,QAAI8U,MAAM,KAAK,CAAf,EAAkB;AAChB,UAAIvJ,KAAK,CAAC0B,MAAN,GAAe6E,OAAO,CAAC7E,MAA3B,EAAmC,OAAO,CAAC,CAAR;AACnC,UAAI1B,KAAK,CAAC0B,MAAN,GAAe6E,OAAO,CAAC7E,MAA3B,EAAmC,OAAO,CAAP;AACnC,aAAO,CAAP;AACD;;AAED,WAAO6H,MAAP;AACD,GAhBkC;;AAkBnC;;;AAIApC,EAAAA,OAAO,CAACnH,KAAD,EAAeuG,OAAf;AACL,WAAOlF,KAAK,CAAC2B,OAAN,CAAchD,KAAd,EAAqBuG,OAArB,MAAkC,CAAzC;AACD,GAxBkC;;AA0BnC;;;AAIAZ,EAAAA,QAAQ,CAAC3F,KAAD,EAAeuG,OAAf;AACN,WAAOlF,KAAK,CAAC2B,OAAN,CAAchD,KAAd,EAAqBuG,OAArB,MAAkC,CAAC,CAA1C;AACD,GAhCkC;;AAkCnC;;;AAIA7N,EAAAA,MAAM,CAACsH,KAAD,EAAeuG,OAAf;AACJ;AACA,WACEvG,KAAK,CAAC0B,MAAN,KAAiB6E,OAAO,CAAC7E,MAAzB,IAAmC5M,IAAI,CAAC4D,MAAL,CAAYsH,KAAK,CAACvL,IAAlB,EAAwB8R,OAAO,CAAC9R,IAAhC,CADrC;AAGD,GA3CkC;;AA6CnC;;;AAIA0P,EAAAA,OAAO,CAACxO,KAAD;AACL,WACEgJ,aAAa,CAAChJ,KAAD,CAAb,IACA,OAAOA,KAAK,CAAC+L,MAAb,KAAwB,QADxB,IAEA5M,IAAI,CAACuN,MAAL,CAAY1M,KAAK,CAAClB,IAAlB,CAHF;AAKD,GAvDkC;;AAyDnC;;;AAIAZ,EAAAA,SAAS,CACPmM,KADO,EAEPxM,EAFO;QAGPoM,8EAAwD;AAExD,WAAOiH,OAAO,CAAC7G,KAAD,EAAQ5G,CAAC;AACrB,UAAIA,CAAC,KAAK,IAAV,EAAgB;AACd,eAAO,IAAP;AACD;;AACD,UAAM;AAAEkL,QAAAA,QAAQ,GAAG;AAAb,UAA2B1E,OAAjC;AACA,UAAM;AAAEnL,QAAAA,IAAF;AAAQiN,QAAAA;AAAR,UAAmBtI,CAAzB;;AAEA,cAAQ5F,EAAE,CAAC8B,IAAX;AACE,aAAK,aAAL;AACA,aAAK,WAAL;AAAkB;AAChB8D,YAAAA,CAAC,CAAC3E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBoM,OAAzB,CAAT;AACA;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAI9K,IAAI,CAAC4D,MAAL,CAAYlF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,KAA8BjB,EAAE,CAACkO,MAAH,IAAaA,MAA/C,EAAuD;AACrDtI,cAAAA,CAAC,CAACsI,MAAF,IAAYlO,EAAE,CAAC8D,IAAH,CAAQK,MAApB;AACD;;AAED;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAI7C,IAAI,CAAC4D,MAAL,CAAYlF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,CAAJ,EAAgC;AAC9B2E,cAAAA,CAAC,CAACsI,MAAF,IAAYlO,EAAE,CAACiU,QAAf;AACD;;AAEDrO,YAAAA,CAAC,CAAC3E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBoM,OAAzB,CAAT;AACA;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAI9K,IAAI,CAAC4D,MAAL,CAAYlF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,KAA8BjB,EAAE,CAACkO,MAAH,IAAaA,MAA/C,EAAuD;AACrDtI,cAAAA,CAAC,CAACsI,MAAF,IAAY2G,IAAI,CAACD,GAAL,CAAS1G,MAAM,GAAGlO,EAAE,CAACkO,MAArB,EAA6BlO,EAAE,CAAC8D,IAAH,CAAQK,MAArC,CAAZ;AACD;;AAED;AACD;;AAED,aAAK,aAAL;AAAoB;AAClB,gBAAI7C,IAAI,CAAC4D,MAAL,CAAYlF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,KAA8BK,IAAI,CAAC+J,UAAL,CAAgBrL,EAAE,CAACiB,IAAnB,EAAyBA,IAAzB,CAAlC,EAAkE;AAChE,qBAAO,IAAP;AACD;;AAED2E,YAAAA,CAAC,CAAC3E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBoM,OAAzB,CAAT;AACA;AACD;;AAED,aAAK,YAAL;AAAmB;AACjB,gBAAI9K,IAAI,CAAC4D,MAAL,CAAYlF,EAAE,CAACiB,IAAf,EAAqBA,IAArB,CAAJ,EAAgC;AAC9B,kBAAIjB,EAAE,CAACiU,QAAH,KAAgB/F,MAAhB,IAA0B4C,QAAQ,IAAI,IAA1C,EAAgD;AAC9C,uBAAO,IAAP;AACD,eAFD,MAEO,IACL9Q,EAAE,CAACiU,QAAH,GAAc/F,MAAd,IACClO,EAAE,CAACiU,QAAH,KAAgB/F,MAAhB,IAA0B4C,QAAQ,KAAK,SAFnC,EAGL;AACAlL,gBAAAA,CAAC,CAACsI,MAAF,IAAYlO,EAAE,CAACiU,QAAf;AAEArO,gBAAAA,CAAC,CAAC3E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,sCACJoM,OADI;AAEP0E,kBAAAA,QAAQ,EAAE;AAFH,mBAAT;AAID;AACF,aAdD,MAcO;AACLlL,cAAAA,CAAC,CAAC3E,IAAF,GAASK,IAAI,CAACjB,SAAL,CAAeY,IAAf,EAAqBjB,EAArB,EAAyBoM,OAAzB,CAAT;AACD;;AAED;AACD;AA7DH;AA+DD,KAtEa,CAAd;AAuED;;AAzIkC;;ICbxB7L,QAAQ,GAAsB;AACzC;;;AAIAF,EAAAA,SAAS,CAACJ,GAAD,EAAgBD,EAAhB;AACP,QAAM;AAAE+Q,MAAAA,OAAF;AAAWD,MAAAA;AAAX,QAAwB7Q,GAA9B;;AAEA,QAAI8Q,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAMvE,KAAK,GAAGqB,KAAK,CAACxN,SAAN,CAAgB0Q,OAAhB,EAAyB/Q,EAAzB,EAA6B;AAAE8Q,MAAAA;AAAF,KAA7B,CAAd;AACA7Q,IAAAA,GAAG,CAAC8Q,OAAJ,GAAcvE,KAAd;;AAEA,QAAIA,KAAK,IAAI,IAAb,EAAmB;AACjBvM,MAAAA,GAAG,CAAC+Q,KAAJ;AACD;AACF;;AAlBwC;;;;;;;IC0B9B5O,KAAK,GAAmB;AACnC;;;;AAKA6K,EAAAA,KAAK,CACHN,KADG;QAEHP,8EAEI;AAEJ,QAAM;AAAErJ,MAAAA,OAAO,GAAG;AAAZ,QAAsBqJ,OAA5B;AACA,QAAM;AAAEG,MAAAA,MAAF;AAAUG,MAAAA;AAAV,QAAoBC,KAA1B;AACA,WAAOvK,KAAK,CAAC4T,UAAN,CAAiBrJ,KAAjB,MAA4B5J,OAA5B,GACH,CAACwJ,MAAD,EAASG,KAAT,CADG,GAEH,CAACA,KAAD,EAAQH,MAAR,CAFJ;AAGD,GAjBkC;;AAmBnC;;;AAIAvD,EAAAA,GAAG,CAAC2D,KAAD;AACD,QAAM,GAAG3D,GAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAAhB;AACA,WAAO3D,GAAP;AACD,GA1BkC;;AA4BnC;;;AAIA9D,EAAAA,MAAM,CAACyH,KAAD,EAAeoG,OAAf;AACJ,WACElF,KAAK,CAAC3I,MAAN,CAAayH,KAAK,CAACJ,MAAnB,EAA2BwG,OAAO,CAACxG,MAAnC,KACAsB,KAAK,CAAC3I,MAAN,CAAayH,KAAK,CAACD,KAAnB,EAA0BqG,OAAO,CAACrG,KAAlC,CAFF;AAID,GArCkC;;AAuCnC;;;AAIAsC,EAAAA,QAAQ,CAACrC,KAAD,EAAeE,MAAf;AACN,QAAIzK,KAAK,CAACqL,OAAN,CAAcZ,MAAd,CAAJ,EAA2B;AACzB,UACEzK,KAAK,CAAC4M,QAAN,CAAerC,KAAf,EAAsBE,MAAM,CAACN,MAA7B,KACAnK,KAAK,CAAC4M,QAAN,CAAerC,KAAf,EAAsBE,MAAM,CAACH,KAA7B,CAFF,EAGE;AACA,eAAO,IAAP;AACD;;AAED,UAAM,CAACuJ,EAAD,EAAKC,EAAL,IAAW9T,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAAjB;AACA,UAAM,CAACwJ,EAAD,EAAKC,EAAL,IAAWhU,KAAK,CAAC6K,KAAN,CAAYJ,MAAZ,CAAjB;AACA,aAAOgB,KAAK,CAACsE,QAAN,CAAe8D,EAAf,EAAmBE,EAAnB,KAA0BtI,KAAK,CAAC8F,OAAN,CAAcuC,EAAd,EAAkBE,EAAlB,CAAjC;AACD;;AAED,QAAM,CAACpJ,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAArB;AACA,QAAI0J,YAAY,GAAG,KAAnB;AACA,QAAIC,WAAW,GAAG,KAAlB;;AAEA,QAAIzI,KAAK,CAAC8C,OAAN,CAAc9D,MAAd,CAAJ,EAA2B;AACzBwJ,MAAAA,YAAY,GAAGxI,KAAK,CAAC2B,OAAN,CAAc3C,MAAd,EAAsBG,KAAtB,KAAgC,CAA/C;AACAsJ,MAAAA,WAAW,GAAGzI,KAAK,CAAC2B,OAAN,CAAc3C,MAAd,EAAsB7D,GAAtB,KAA8B,CAA5C;AACD,KAHD,MAGO;AACLqN,MAAAA,YAAY,GAAG/U,IAAI,CAACkO,OAAL,CAAa3C,MAAb,EAAqBG,KAAK,CAAC/L,IAA3B,KAAoC,CAAnD;AACAqV,MAAAA,WAAW,GAAGhV,IAAI,CAACkO,OAAL,CAAa3C,MAAb,EAAqB7D,GAAG,CAAC/H,IAAzB,KAAkC,CAAhD;AACD;;AAED,WAAOoV,YAAY,IAAIC,WAAvB;AACD,GAtEkC;;AAwEnC;;;AAIAC,EAAAA,YAAY,CAAC5J,KAAD,EAAeoG,OAAf;AACV,QAA0B1E,IAA1B,4BAAmC1B,KAAnC;;AACA,QAAM,CAAC6J,EAAD,EAAKC,EAAL,IAAWrU,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAAjB;AACA,QAAM,CAAC+J,EAAD,EAAKC,EAAL,IAAWvU,KAAK,CAAC6K,KAAN,CAAY8F,OAAZ,CAAjB;AACA,QAAM/F,KAAK,GAAGa,KAAK,CAACsE,QAAN,CAAeqE,EAAf,EAAmBE,EAAnB,IAAyBA,EAAzB,GAA8BF,EAA5C;AACA,QAAMxN,GAAG,GAAG6E,KAAK,CAACsE,QAAN,CAAesE,EAAf,EAAmBE,EAAnB,IAAyBF,EAAzB,GAA8BE,EAA1C;;AAEA,QAAI9I,KAAK,CAACsE,QAAN,CAAenJ,GAAf,EAAoBgE,KAApB,CAAJ,EAAgC;AAC9B,aAAO,IAAP;AACD,KAFD,MAEO;AACL;AAAST,QAAAA,MAAM,EAAES,KAAjB;AAAwBN,QAAAA,KAAK,EAAE1D;AAA/B,SAAuCqF,IAAvC;AACD;AACF,GAxFkC;;AA0FnC;;;;AAKA2H,EAAAA,UAAU,CAACrJ,KAAD;AACR,QAAM;AAAEJ,MAAAA,MAAF;AAAUG,MAAAA;AAAV,QAAoBC,KAA1B;AACA,WAAOkB,KAAK,CAAC8F,OAAN,CAAcpH,MAAd,EAAsBG,KAAtB,CAAP;AACD,GAlGkC;;AAoGnC;;;;AAKA7J,EAAAA,WAAW,CAAC8J,KAAD;AACT,QAAM;AAAEJ,MAAAA,MAAF;AAAUG,MAAAA;AAAV,QAAoBC,KAA1B;AACA,WAAOkB,KAAK,CAAC3I,MAAN,CAAaqH,MAAb,EAAqBG,KAArB,CAAP;AACD,GA5GkC;;AA8GnC;;;;;AAMArK,EAAAA,UAAU,CAACsK,KAAD;AACR,WAAO,CAACvK,KAAK,CAACS,WAAN,CAAkB8J,KAAlB,CAAR;AACD,GAtHkC;;AAwHnC;;;;;AAMAiK,EAAAA,SAAS,CAACjK,KAAD;AACP,WAAO,CAACvK,KAAK,CAAC4T,UAAN,CAAiBrJ,KAAjB,CAAR;AACD,GAhIkC;;AAkInC;;;AAIAc,EAAAA,OAAO,CAACtL,KAAD;AACL,WACEgJ,aAAa,CAAChJ,KAAD,CAAb,IACA0L,KAAK,CAAC8C,OAAN,CAAcxO,KAAK,CAACoK,MAApB,CADA,IAEAsB,KAAK,CAAC8C,OAAN,CAAcxO,KAAK,CAACuK,KAApB,CAHF;AAKD,GA5IkC;;AA8InC;;;AAIA,GAACmK,MAAD,CAAQlK,KAAR;AACE,UAAM,CAACA,KAAK,CAACJ,MAAP,EAAe,QAAf,CAAN;AACA,UAAM,CAACI,KAAK,CAACD,KAAP,EAAc,OAAd,CAAN;AACD,GArJkC;;AAuJnC;;;AAIAM,EAAAA,KAAK,CAACL,KAAD;AACH,QAAM,CAACK,KAAD,IAAU5K,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAAhB;AACA,WAAOK,KAAP;AACD,GA9JkC;;AAgKnC;;;AAIA3M,EAAAA,SAAS,CACPsM,KADO,EAEP3M,EAFO;QAGPoM,8EAEI;AAEJ,WAAOiH,OAAO,CAAC1G,KAAD,EAAQ5B,CAAC;AACrB,UAAIA,CAAC,KAAK,IAAV,EAAgB;AACd,eAAO,IAAP;AACD;;AACD,UAAM;AAAE+F,QAAAA,QAAQ,GAAG;AAAb,UAA0B1E,OAAhC;AACA,UAAI0K,cAAJ;AACA,UAAIC,aAAJ;;AAEA,UAAIjG,QAAQ,KAAK,QAAjB,EAA2B;AACzB;AACA;AACA;AACA,YAAMjO,WAAW,GAAGT,KAAK,CAACS,WAAN,CAAkBkI,CAAlB,CAApB;;AACA,YAAI3I,KAAK,CAACwU,SAAN,CAAgB7L,CAAhB,CAAJ,EAAwB;AACtB+L,UAAAA,cAAc,GAAG,SAAjB;AACAC,UAAAA,aAAa,GAAGlU,WAAW,GAAGiU,cAAH,GAAoB,UAA/C;AACD,SAHD,MAGO;AACLA,UAAAA,cAAc,GAAG,UAAjB;AACAC,UAAAA,aAAa,GAAGlU,WAAW,GAAGiU,cAAH,GAAoB,SAA/C;AACD;AACF,OAZD,MAYO,IAAIhG,QAAQ,KAAK,SAAjB,EAA4B;AACjC,YAAI1O,KAAK,CAACwU,SAAN,CAAgB7L,CAAhB,CAAJ,EAAwB;AACtB+L,UAAAA,cAAc,GAAG,UAAjB;AACAC,UAAAA,aAAa,GAAG,SAAhB;AACD,SAHD,MAGO;AACLD,UAAAA,cAAc,GAAG,SAAjB;AACAC,UAAAA,aAAa,GAAG,UAAhB;AACD;AACF,OARM,MAQA;AACLD,QAAAA,cAAc,GAAGhG,QAAjB;AACAiG,QAAAA,aAAa,GAAGjG,QAAhB;AACD;;AACD,UAAMvE,MAAM,GAAGsB,KAAK,CAACxN,SAAN,CAAgB0K,CAAC,CAACwB,MAAlB,EAA0BvM,EAA1B,EAA8B;AAAE8Q,QAAAA,QAAQ,EAAEgG;AAAZ,OAA9B,CAAf;AACA,UAAMpK,KAAK,GAAGmB,KAAK,CAACxN,SAAN,CAAgB0K,CAAC,CAAC2B,KAAlB,EAAyB1M,EAAzB,EAA6B;AAAE8Q,QAAAA,QAAQ,EAAEiG;AAAZ,OAA7B,CAAd;;AAEA,UAAI,CAACxK,MAAD,IAAW,CAACG,KAAhB,EAAuB;AACrB,eAAO,IAAP;AACD;;AAED3B,MAAAA,CAAC,CAACwB,MAAF,GAAWA,MAAX;AACAxB,MAAAA,CAAC,CAAC2B,KAAF,GAAUA,KAAV;AACD,KAzCa,CAAd;AA0CD;;AArNkC;;IC1BxBjM,QAAQ,GAAsB;AACzC;;;AAIAJ,EAAAA,SAAS,CAACJ,GAAD,EAAgBD,EAAhB;AACP,QAAM;AAAE+Q,MAAAA,OAAF;AAAWD,MAAAA;AAAX,QAAwB7Q,GAA9B;;AAEA,QAAI8Q,OAAO,IAAI,IAAf,EAAqB;AACnB;AACD;;AAED,QAAM9P,IAAI,GAAGmB,KAAK,CAAC/B,SAAN,CAAgB0Q,OAAhB,EAAyB/Q,EAAzB,EAA6B;AAAE8Q,MAAAA;AAAF,KAA7B,CAAb;AACA7Q,IAAAA,GAAG,CAAC8Q,OAAJ,GAAc9P,IAAd;;AAEA,QAAIA,IAAI,IAAI,IAAZ,EAAkB;AAChBhB,MAAAA,GAAG,CAAC+Q,KAAJ;AACD;AACF;;AAlBwC;;AChB3C;;;;;;;;;;;AAUO,IAAMgG,WAAW,GAAG,CACzBrT,IADyB,EAEzBoP,OAFyB;AAIzB,OAAK,IAAM7R,GAAX,IAAkByC,IAAlB,EAAwB;AACtB,QAAMsT,CAAC,GAAGtT,IAAI,CAACzC,GAAD,CAAd;AACA,QAAMgW,CAAC,GAAGnE,OAAO,CAAC7R,GAAD,CAAjB;;AACA,QAAIiK,aAAa,CAAC8L,CAAD,CAAb,IAAoB9L,aAAa,CAAC+L,CAAD,CAArC,EAA0C;AACxC,UAAI,CAACF,WAAW,CAACC,CAAD,EAAIC,CAAJ,CAAhB,EAAwB,OAAO,KAAP;AACzB,KAFD,MAEO,IAAIzR,KAAK,CAAC8F,OAAN,CAAc0L,CAAd,KAAoBxR,KAAK,CAAC8F,OAAN,CAAc2L,CAAd,CAAxB,EAA0C;AAC/C,UAAID,CAAC,CAAC9S,MAAF,KAAa+S,CAAC,CAAC/S,MAAnB,EAA2B,OAAO,KAAP;;AAC3B,WAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsS,CAAC,CAAC9S,MAAtB,EAA8BQ,CAAC,EAA/B,EAAmC;AACjC,YAAIsS,CAAC,CAACtS,CAAD,CAAD,KAASuS,CAAC,CAACvS,CAAD,CAAd,EAAmB,OAAO,KAAP;AACpB;AACF,KALM,MAKA,IAAIsS,CAAC,KAAKC,CAAV,EAAa;AAClB,aAAO,KAAP;AACD;AACF;AAED;;;;;;;AAMA,OAAK,IAAMhW,IAAX,IAAkB6R,OAAlB,EAA2B;AACzB,QAAIpP,IAAI,CAACzC,IAAD,CAAJ,KAAc0K,SAAd,IAA2BmH,OAAO,CAAC7R,IAAD,CAAP,KAAiB0K,SAAhD,EAA2D;AACzD,aAAO,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD,CAhCM;;;;;;;;ICcMpJ,IAAI,GAAkB;AACjC;;;;;;AAMA0C,EAAAA,MAAM,CACJpB,IADI,EAEJiP,OAFI;QAGJ3G,8EAA+B;AAE/B,QAAM;AAAEjH,MAAAA,KAAK,GAAG;AAAV,QAAoBiH,OAA1B;;AAEA,aAAS+K,QAAT,CAAkBC,GAAlB;AACE,UAAiB/I,IAAjB,4BAA0B+I,GAA1B;;AAEA,aAAO/I,IAAP;AACD;;AAED,WAAO2I,WAAW,CAChB7R,KAAK,GAAGgS,QAAQ,CAACrT,IAAD,CAAX,GAAoBA,IADT,EAEhBqB,KAAK,GAAGgS,QAAQ,CAACpE,OAAD,CAAX,GAAuBA,OAFZ,CAAlB;AAID,GAxBgC;;AA0BjC;;;AAIAtQ,EAAAA,MAAM,CAACN,KAAD;AACJ,WAAOgJ,aAAa,CAAChJ,KAAD,CAAb,IAAwB,OAAOA,KAAK,CAAC2B,IAAb,KAAsB,QAArD;AACD,GAhCgC;;AAkCjC;;;AAIAuT,EAAAA,UAAU,CAAClV,KAAD;AACR,WAAOsD,KAAK,CAAC8F,OAAN,CAAcpJ,KAAd,KAAwBA,KAAK,CAACqJ,KAAN,CAAYC,GAAG,IAAIjJ,IAAI,CAACC,MAAL,CAAYgJ,GAAZ,CAAnB,CAA/B;AACD,GAxCgC;;AA0CjC;;;AAIAgI,EAAAA,WAAW,CAAC9H,KAAD;AACT,WAAQA,KAAuB,CAAC7H,IAAxB,KAAiC8H,SAAzC;AACD,GAhDgC;;AAkDjC;;;;;;AAOAI,EAAAA,OAAO,CAAClI,IAAD,EAAa6H,KAAb;AACL,SAAK,IAAMzK,GAAX,IAAkByK,KAAlB,EAAyB;AACvB,UAAIzK,GAAG,KAAK,MAAZ,EAAoB;AAClB;AACD;;AAED,UAAI,CAAC4C,IAAI,CAACwT,cAAL,CAAoBpW,GAApB,CAAD,IAA6B4C,IAAI,CAAC5C,GAAD,CAAJ,KAAcyK,KAAK,CAACzK,GAAD,CAApD,EAA2D;AACzD,eAAO,KAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD,GArEgC;;AAuEjC;;;AAIAqW,EAAAA,WAAW,CAAC5T,IAAD,EAAa4T,WAAb;AACT,QAAIC,MAAM,GAAW,qBAAM7T,IAAN,EAArB;;AAEA,SAAK,IAAM8T,GAAX,IAAkBF,WAAlB,EAA+B;AAC7B,UAA0BlJ,IAA1B,4BAAmCoJ,GAAnC;;AACA,UAAM,CAACzK,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAYwK,GAAZ,CAArB;AACA,UAAMlR,IAAI,GAAG,EAAb;AACA,UAAImR,CAAC,GAAG,CAAR;;AAEA,WAAK,IAAMtJ,IAAX,IAAmBoJ,MAAnB,EAA2B;AACzB,YAAM;AAAErT,UAAAA;AAAF,YAAaiK,IAAI,CAACtK,IAAxB;AACA,YAAMoK,MAAM,GAAGwJ,CAAf;AACAA,QAAAA,CAAC,IAAIvT,MAAL,CAHyB;;AAMzB,YAAI6I,KAAK,CAACkB,MAAN,IAAgBA,MAAhB,IAA0BlF,GAAG,CAACkF,MAAJ,IAAcwJ,CAA5C,EAA+C;AAC7CC,UAAAA,MAAM,CAACC,MAAP,CAAcxJ,IAAd,EAAoBC,IAApB;AACA9H,UAAAA,IAAI,CAAClF,IAAL,CAAU+M,IAAV;AACA;AACD,SAVwB;;;AAazB,YACGpB,KAAK,CAACkB,MAAN,KAAiBlF,GAAG,CAACkF,MAArB,KACElB,KAAK,CAACkB,MAAN,KAAiBwJ,CAAjB,IAAsB1O,GAAG,CAACkF,MAAJ,KAAeA,MADvC,CAAD,IAEAlB,KAAK,CAACkB,MAAN,GAAewJ,CAFf,IAGA1O,GAAG,CAACkF,MAAJ,GAAaA,MAHb,IAIClF,GAAG,CAACkF,MAAJ,KAAeA,MAAf,IAAyBA,MAAM,KAAK,CALvC,EAME;AACA3H,UAAAA,IAAI,CAAClF,IAAL,CAAU+M,IAAV;AACA;AACD,SAtBwB;AAyBzB;AACA;;;AACA,YAAIyJ,MAAM,GAAGzJ,IAAb;AACA,YAAIrB,MAAM,SAAV;AACA,YAAIT,KAAK,SAAT;;AAEA,YAAItD,GAAG,CAACkF,MAAJ,GAAawJ,CAAjB,EAAoB;AAClB,cAAMI,GAAG,GAAG9O,GAAG,CAACkF,MAAJ,GAAaA,MAAzB;AACA5B,UAAAA,KAAK,uCAAQuL,MAAR;AAAgB/T,YAAAA,IAAI,EAAE+T,MAAM,CAAC/T,IAAP,CAAY8E,KAAZ,CAAkBkP,GAAlB;AAAtB,YAAL;AACAD,UAAAA,MAAM,uCAAQA,MAAR;AAAgB/T,YAAAA,IAAI,EAAE+T,MAAM,CAAC/T,IAAP,CAAY8E,KAAZ,CAAkB,CAAlB,EAAqBkP,GAArB;AAAtB,YAAN;AACD;;AAED,YAAI9K,KAAK,CAACkB,MAAN,GAAeA,MAAnB,EAA2B;AACzB,cAAM4J,IAAG,GAAG9K,KAAK,CAACkB,MAAN,GAAeA,MAA3B;;AACAnB,UAAAA,MAAM,uCAAQ8K,MAAR;AAAgB/T,YAAAA,IAAI,EAAE+T,MAAM,CAAC/T,IAAP,CAAY8E,KAAZ,CAAkB,CAAlB,EAAqBkP,IAArB;AAAtB,YAAN;AACAD,UAAAA,MAAM,uCAAQA,MAAR;AAAgB/T,YAAAA,IAAI,EAAE+T,MAAM,CAAC/T,IAAP,CAAY8E,KAAZ,CAAkBkP,IAAlB;AAAtB,YAAN;AACD;;AAEDH,QAAAA,MAAM,CAACC,MAAP,CAAcC,MAAd,EAAsBxJ,IAAtB;;AAEA,YAAItB,MAAJ,EAAY;AACVxG,UAAAA,IAAI,CAAClF,IAAL,CAAU0L,MAAV;AACD;;AAEDxG,QAAAA,IAAI,CAAClF,IAAL,CAAUwW,MAAV;;AAEA,YAAIvL,KAAJ,EAAW;AACT/F,UAAAA,IAAI,CAAClF,IAAL,CAAUiL,KAAV;AACD;AACF;;AAEDkL,MAAAA,MAAM,GAAGjR,IAAT;AACD;;AAED,WAAOiR,MAAP;AACD;;AAhJgC;;;;;;ACNnC,IAAMO,YAAY,GAAG,CAACxY,MAAD,EAAiBG,SAAjB,EAAuCM,EAAvC;AACnB,UAAQA,EAAE,CAAC8B,IAAX;AACE,SAAK,aAAL;AAAoB;AAClB,YAAM;AAAEb,UAAAA,IAAF;AAAQ0C,UAAAA;AAAR,YAAiB3D,EAAvB;AACA,YAAM+O,MAAM,GAAG3L,IAAI,CAAC2L,MAAL,CAAYxP,MAAZ,EAAoB0B,IAApB,CAAf;AACA,YAAMyR,KAAK,GAAGzR,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB;;AAEA,YAAIuO,KAAK,GAAG3D,MAAM,CAACvP,QAAP,CAAgB2E,MAA5B,EAAoC;AAClC,gBAAM,IAAI2K,KAAJ,8DACgD7N,IADhD,4DAAN;AAGD;;AAED8N,QAAAA,MAAM,CAACvP,QAAP,CAAgB8T,MAAhB,CAAuBZ,KAAvB,EAA8B,CAA9B,EAAiC/O,IAAjC;;AAEA,YAAIjE,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,KAAD,EAAQtL,GAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClDA,YAAAA,SAAS,CAACwB,GAAD,CAAT,GAAiB2M,KAAK,CAACxN,SAAN,CAAgBmM,KAAhB,EAAuBxM,EAAvB,CAAjB;AACD;AACF;;AAED;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAM;AAAEiB,UAAAA,IAAI,EAAJA,KAAF;AAAQiN,UAAAA,MAAR;AAAgBpK,UAAAA;AAAhB,YAAyB9D,EAA/B;AACA,YAAI8D,IAAI,CAACK,MAAL,KAAgB,CAApB,EAAuB;;AACvB,YAAMR,KAAI,GAAGP,IAAI,CAACgL,IAAL,CAAU7O,MAAV,EAAkB0B,KAAlB,CAAb;;AACA,YAAM8L,MAAM,GAAGpJ,KAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgB,CAAhB,EAAmBsF,MAAnB,CAAf;;AACA,YAAM5B,KAAK,GAAG3I,KAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgBsF,MAAhB,CAAd;;AACAvK,QAAAA,KAAI,CAACG,IAAL,GAAYiJ,MAAM,GAAGjJ,IAAT,GAAgBwI,KAA5B;;AAEA,YAAI5M,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,MAAD,EAAQtL,IAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClDA,YAAAA,SAAS,CAACwB,IAAD,CAAT,GAAiB2M,KAAK,CAACxN,SAAN,CAAgBmM,MAAhB,EAAuBxM,EAAvB,CAAjB;AACD;AACF;;AAED;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAM;AAAEiB,UAAAA,IAAI,EAAJA;AAAF,YAAWjB,EAAjB;;AACA,YAAM2D,MAAI,GAAGP,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AACA,YAAMuN,QAAQ,GAAGlN,IAAI,CAACyE,QAAL,CAAc9E,MAAd,CAAjB;AACA,YAAM4D,IAAI,GAAGzB,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiBiP,QAAjB,CAAb;;AACA,YAAMO,OAAM,GAAG3L,IAAI,CAAC2L,MAAL,CAAYxP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA,YAAMyR,MAAK,GAAGzR,MAAI,CAACA,MAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB;;AAEA,YAAI3B,IAAI,CAACC,MAAL,CAAYkB,MAAZ,KAAqBnB,IAAI,CAACC,MAAL,CAAYoC,IAAZ,CAAzB,EAA4C;AAC1CA,UAAAA,IAAI,CAACf,IAAL,IAAaH,MAAI,CAACG,IAAlB;AACD,SAFD,MAEO,IAAI,CAACtB,IAAI,CAACC,MAAL,CAAYkB,MAAZ,CAAD,IAAsB,CAACnB,IAAI,CAACC,MAAL,CAAYoC,IAAZ,CAA3B,EAA8C;AACnDA,UAAAA,IAAI,CAACrF,QAAL,CAAc6B,IAAd,CAAmB,GAAGsC,MAAI,CAACnE,QAA3B;AACD,SAFM,MAEA;AACL,gBAAM,IAAIsP,KAAJ,4DAC8C7N,MAD9C,iDACyF0C,MADzF,cACiGkB,IADjG,EAAN;AAGD;;AAEDkK,QAAAA,OAAM,CAACvP,QAAP,CAAgB8T,MAAhB,CAAuBZ,MAAvB,EAA8B,CAA9B;;AAEA,YAAIhT,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,OAAD,EAAQtL,KAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClDA,YAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiB2M,KAAK,CAACxN,SAAN,CAAgBmM,OAAhB,EAAuBxM,EAAvB,CAAjB;AACD;AACF;;AAED;AACD;;AAED,SAAK,WAAL;AAAkB;AAChB,YAAM;AAAEiB,UAAAA,IAAI,EAAJA,MAAF;AAAQO,UAAAA;AAAR,YAAoBxB,EAA1B;;AAEA,YAAIsB,IAAI,CAAC+J,UAAL,CAAgBpK,MAAhB,EAAsBO,OAAtB,CAAJ,EAAoC;AAClC,gBAAM,IAAIsN,KAAJ,+BACmB7N,MADnB,4BACyCO,OADzC,iDAAN;AAGD;;AAED,YAAMmC,MAAI,GAAGP,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AACA,YAAM8N,QAAM,GAAG3L,IAAI,CAAC2L,MAAL,CAAYxP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA,YAAMyR,OAAK,GAAGzR,MAAI,CAACA,MAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB,CAXgB;AAchB;AACA;AACA;AACA;AACA;;AACA4K,QAAAA,QAAM,CAACvP,QAAP,CAAgB8T,MAAhB,CAAuBZ,OAAvB,EAA8B,CAA9B;;AACA,YAAMsF,QAAQ,GAAG1W,IAAI,CAACjB,SAAL,CAAeY,MAAf,EAAqBjB,EAArB,CAAjB;AACA,YAAMmG,SAAS,GAAG/C,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB+B,IAAI,CAACyN,MAAL,CAAYiJ,QAAZ,CAAjB,CAAlB;AACA,YAAM5R,QAAQ,GAAG4R,QAAQ,CAACA,QAAQ,CAAC7T,MAAT,GAAkB,CAAnB,CAAzB;AAEAgC,QAAAA,SAAS,CAAC3G,QAAV,CAAmB8T,MAAnB,CAA0BlN,QAA1B,EAAoC,CAApC,EAAuCzC,MAAvC;;AAEA,YAAIjE,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,OAAD,EAAQtL,KAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClDA,YAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiB2M,KAAK,CAACxN,SAAN,CAAgBmM,OAAhB,EAAuBxM,EAAvB,CAAjB;AACD;AACF;;AAED;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAM;AAAEiB,UAAAA,IAAI,EAAJA;AAAF,YAAWjB,EAAjB;AACA,YAAM0S,OAAK,GAAGzR,MAAI,CAACA,MAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB;;AACA,YAAM4K,QAAM,GAAG3L,IAAI,CAAC2L,MAAL,CAAYxP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA8N,QAAAA,QAAM,CAACvP,QAAP,CAAgB8T,MAAhB,CAAuBZ,OAAvB,EAA8B,CAA9B,EAJkB;AAOlB;;;AACA,YAAIhT,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,OAAD,EAAQtL,KAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClD,gBAAMqW,MAAM,GAAGlI,KAAK,CAACxN,SAAN,CAAgBmM,OAAhB,EAAuBxM,EAAvB,CAAf;;AAEA,gBAAIN,SAAS,IAAI,IAAb,IAAqBqW,MAAM,IAAI,IAAnC,EAAyC;AACvCrW,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiB6U,MAAjB;AACD,aAFD,MAEO;AACL,kBAAIlR,KAAiC,SAArC;;AACA,kBAAI0B,IAAiC,SAArC;;AAEA,mBAAK,IAAM,CAAC7B,CAAD,EAAIkB,CAAJ,CAAX,IAAqBxC,IAAI,CAACyQ,KAAL,CAAWtU,MAAX,CAArB,EAAyC;AACvC,oBAAI+B,IAAI,CAACkO,OAAL,CAAa5J,CAAb,EAAgB3E,MAAhB,MAA0B,CAAC,CAA/B,EAAkC;AAChC4D,kBAAAA,KAAI,GAAG,CAACH,CAAD,EAAIkB,CAAJ,CAAP;AACD,iBAFD,MAEO;AACLW,kBAAAA,IAAI,GAAG,CAAC7B,CAAD,EAAIkB,CAAJ,CAAP;AACA;AACD;AACF;;AAED,kBAAIqS,UAAU,GAAG,KAAjB;;AACA,kBAAIpT,KAAI,IAAI0B,IAAZ,EAAkB;AAChB,oBAAIjF,IAAI,CAAC4D,MAAL,CAAYqB,IAAI,CAAC,CAAD,CAAhB,EAAqBtF,MAArB,CAAJ,EAAgC;AAC9BgX,kBAAAA,UAAU,GAAG,CAAC3W,IAAI,CAAC6T,WAAL,CAAiB5O,IAAI,CAAC,CAAD,CAArB,CAAd;AACD,iBAFD,MAEO;AACL0R,kBAAAA,UAAU,GACR3W,IAAI,CAACoP,MAAL,CAAY7L,KAAI,CAAC,CAAD,CAAhB,EAAqB5D,MAArB,EAA2BkD,MAA3B,GACA7C,IAAI,CAACoP,MAAL,CAAYnK,IAAI,CAAC,CAAD,CAAhB,EAAqBtF,MAArB,EAA2BkD,MAF7B;AAGD;AACF;;AAED,kBAAIU,KAAI,IAAI,CAACoT,UAAb,EAAyB;AACvBzL,gBAAAA,OAAK,CAACvL,IAAN,GAAa4D,KAAI,CAAC,CAAD,CAAjB;AACA2H,gBAAAA,OAAK,CAAC0B,MAAN,GAAerJ,KAAI,CAAC,CAAD,CAAJ,CAAQf,IAAR,CAAaK,MAA5B;AACD,eAHD,MAGO,IAAIoC,IAAJ,EAAU;AACfiG,gBAAAA,OAAK,CAACvL,IAAN,GAAasF,IAAI,CAAC,CAAD,CAAjB;AACAiG,gBAAAA,OAAK,CAAC0B,MAAN,GAAe,CAAf;AACD,eAHM,MAGA;AACLxO,gBAAAA,SAAS,GAAG,IAAZ;AACD;AACF;AACF;AACF;;AAED;AACD;;AAED,SAAK,aAAL;AAAoB;AAClB,YAAM;AAAEuB,UAAAA,IAAI,EAAJA,MAAF;AAAQiN,UAAAA,MAAM,EAANA,OAAR;AAAgBpK,UAAAA,IAAI,EAAJA;AAAhB,YAAyB9D,EAA/B;AACA,YAAI8D,KAAI,CAACK,MAAL,KAAgB,CAApB,EAAuB;;AACvB,YAAMR,MAAI,GAAGP,IAAI,CAACgL,IAAL,CAAU7O,MAAV,EAAkB0B,MAAlB,CAAb;;AACA,YAAM8L,OAAM,GAAGpJ,MAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgB,CAAhB,EAAmBsF,OAAnB,CAAf;;AACA,YAAM5B,MAAK,GAAG3I,MAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgBsF,OAAM,GAAGpK,KAAI,CAACK,MAA9B,CAAd;;AACAR,QAAAA,MAAI,CAACG,IAAL,GAAYiJ,OAAM,GAAGT,MAArB;;AAEA,YAAI5M,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,OAAD,EAAQtL,KAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClDA,YAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiB2M,KAAK,CAACxN,SAAN,CAAgBmM,OAAhB,EAAuBxM,EAAvB,CAAjB;AACD;AACF;;AAED;AACD;;AAED,SAAK,UAAL;AAAiB;AACf,YAAM;AAAEiB,UAAAA,IAAI,EAAJA,MAAF;AAAQkS,UAAAA,UAAR;AAAoBe,UAAAA;AAApB,YAAsClU,EAA5C;;AAEA,YAAIiB,MAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB,gBAAM,IAAI2K,KAAJ,2CAAN;AACD;;AAED,YAAMnL,MAAI,GAAGP,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AAEA,aAAK,IAAMC,KAAX,IAAkBgT,aAAlB,EAAiC;AAC/B,cAAIhT,KAAG,KAAK,UAAR,IAAsBA,KAAG,KAAK,MAAlC,EAA0C;AACxC,kBAAM,IAAI4N,KAAJ,4BAA6B5N,KAA7B,2BAAN;AACD;;AAED,cAAMiB,KAAK,GAAG+R,aAAa,CAAChT,KAAD,CAA3B;;AAEA,cAAIiB,KAAK,IAAI,IAAb,EAAmB;AACjB,mBAAOwB,MAAI,CAACzC,KAAD,CAAX;AACD,WAFD,MAEO;AACLyC,YAAAA,MAAI,CAACzC,KAAD,CAAJ,GAAYiB,KAAZ;AACD;AACF,SArBc;;;AAwBf,aAAK,IAAMjB,KAAX,IAAkBiS,UAAlB,EAA8B;AAC5B,cAAI,CAACe,aAAa,CAACoD,cAAd,CAA6BpW,KAA7B,CAAL,EAAwC;AACtC,mBAAOyC,MAAI,CAACzC,KAAD,CAAX;AACD;AACF;;AAED;AACD;;AAED,SAAK,eAAL;AAAsB;AACpB,YAAM;AAAEgT,UAAAA,aAAa,EAAbA;AAAF,YAAoBlU,EAA1B;;AAEA,YAAIkU,cAAa,IAAI,IAArB,EAA2B;AACzBxU,UAAAA,SAAS,GAAGwU,cAAZ;AACD,SAFD,MAEO;AACL,cAAIxU,SAAS,IAAI,IAAjB,EAAuB;AACrB,gBAAI,CAAC0C,KAAK,CAACqL,OAAN,CAAcyG,cAAd,CAAL,EAAmC;AACjC,oBAAM,IAAIpF,KAAJ,6EAC+D6D,IAAI,CAACC,SAAL,CACjEsB,cADiE,CAD/D,0CAAN;AAKD;;AAEDxU,YAAAA,SAAS,uBAAQwU,cAAR,CAAT;AACD;;AAED,eAAK,IAAMhT,KAAX,IAAkBgT,cAAlB,EAAiC;AAC/B,gBAAM/R,MAAK,GAAG+R,cAAa,CAAChT,KAAD,CAA3B;;AAEA,gBAAIiB,MAAK,IAAI,IAAb,EAAmB;AACjB,kBAAIjB,KAAG,KAAK,QAAR,IAAoBA,KAAG,KAAK,OAAhC,EAAyC;AACvC,sBAAM,IAAI4N,KAAJ,+BAAgC5N,KAAhC,2BAAN;AACD;;AAED,qBAAOxB,SAAS,CAACwB,KAAD,CAAhB;AACD,aAND,MAMO;AACLxB,cAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiBiB,MAAjB;AACD;AACF;AACF;;AAED;AACD;;AAED,SAAK,YAAL;AAAmB;AACjB,YAAM;AAAElB,UAAAA,IAAI,EAAJA,MAAF;AAAQgT,UAAAA,QAAR;AAAkBd,UAAAA,UAAU,EAAVA;AAAlB,YAAiCnT,EAAvC;;AAEA,YAAIiB,MAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB,gBAAM,IAAI2K,KAAJ,4DAC8C7N,MAD9C,8CAAN;AAGD;;AAED,YAAM0C,MAAI,GAAGP,IAAI,CAACzC,GAAL,CAASpB,MAAT,EAAiB0B,MAAjB,CAAb;;AACA,YAAM8N,QAAM,GAAG3L,IAAI,CAAC2L,MAAL,CAAYxP,MAAZ,EAAoB0B,MAApB,CAAf;;AACA,YAAMyR,OAAK,GAAGzR,MAAI,CAACA,MAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB;AACA,YAAI+T,OAAJ;;AAEA,YAAI1V,IAAI,CAACC,MAAL,CAAYkB,MAAZ,CAAJ,EAAuB;AACrB,cAAMoJ,QAAM,GAAGpJ,MAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgB,CAAhB,EAAmBqL,QAAnB,CAAf;;AACA,cAAM3H,OAAK,GAAG3I,MAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgBqL,QAAhB,CAAd;;AACAtQ,UAAAA,MAAI,CAACG,IAAL,GAAYiJ,QAAZ;AACAmL,UAAAA,OAAO,uCACD/E,WADC;AAELrP,YAAAA,IAAI,EAAEwI;AAFD,YAAP;AAID,SARD,MAQO;AACL,cAAMS,QAAM,GAAGpJ,MAAI,CAACnE,QAAL,CAAcoJ,KAAd,CAAoB,CAApB,EAAuBqL,QAAvB,CAAf;;AACA,cAAM3H,OAAK,GAAG3I,MAAI,CAACnE,QAAL,CAAcoJ,KAAd,CAAoBqL,QAApB,CAAd;;AACAtQ,UAAAA,MAAI,CAACnE,QAAL,GAAgBuN,QAAhB;AAEAmL,UAAAA,OAAO,uCACD/E,WADC;AAEL3T,YAAAA,QAAQ,EAAE8M;AAFL,YAAP;AAID;;AAEDyC,QAAAA,QAAM,CAACvP,QAAP,CAAgB8T,MAAhB,CAAuBZ,OAAK,GAAG,CAA/B,EAAkC,CAAlC,EAAqCwF,OAArC;;AAEA,YAAIxY,SAAJ,EAAe;AACb,eAAK,IAAM,CAAC8M,OAAD,EAAQtL,KAAR,CAAX,IAA2BkB,KAAK,CAACyU,MAAN,CAAanX,SAAb,CAA3B,EAAoD;AAClDA,YAAAA,SAAS,CAACwB,KAAD,CAAT,GAAiB2M,KAAK,CAACxN,SAAN,CAAgBmM,OAAhB,EAAuBxM,EAAvB,CAAjB;AACD;AACF;;AAED;AACD;AA9RH;;AAgSA,SAAON,SAAP;AACD,CAlSD;;AAoSO,IAAMyY,iBAAiB,GAAsB;AAClD;;;AAIA9X,EAAAA,SAAS,CAACd,MAAD,EAAiBS,EAAjB;AACPT,IAAAA,MAAM,CAACC,QAAP,GAAkB4Y,WAAW,CAAC7Y,MAAM,CAACC,QAAR,CAA7B;AACA,QAAIE,SAAS,GAAGH,MAAM,CAACG,SAAP,IAAoB0Y,WAAW,CAAC7Y,MAAM,CAACG,SAAR,CAA/C;;AAEA,QAAI;AACFA,MAAAA,SAAS,GAAGqY,YAAY,CAACxY,MAAD,EAASG,SAAT,EAAoBM,EAApB,CAAxB;AACD,KAFD,SAEU;AACRT,MAAAA,MAAM,CAACC,QAAP,GAAkB6Y,WAAW,CAAC9Y,MAAM,CAACC,QAAR,CAA7B;;AAEA,UAAIE,SAAJ,EAAe;AACbH,QAAAA,MAAM,CAACG,SAAP,GAAmB4Y,OAAO,CAAC5Y,SAAD,CAAP,GACd2Y,WAAW,CAAC3Y,SAAD,CADG,GAEfA,SAFJ;AAGD,OAJD,MAIO;AACLH,QAAAA,MAAM,CAACG,SAAP,GAAmB,IAAnB;AACD;AACF;AACF;;AAtBiD,CAA7C;;;;;;;;AC5LA,IAAM6Y,cAAc,GAAmB;AAC5C;;;AAIA3U,EAAAA,WAAW,CACTrE,MADS,EAEToG,KAFS;QAGTyG,8EAOI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAEiZ,QAAAA,OAAO,GAAG,KAAZ;AAAmBjU,QAAAA,KAAK,GAAG,KAA3B;AAAkC8H,QAAAA,IAAI,GAAG;AAAzC,UAAsDD,OAA5D;AACA,UAAI;AAAE/H,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAakW,QAAAA;AAAb,UAAwBrM,OAA5B;;AAEA,UAAIhJ,IAAI,CAACmQ,MAAL,CAAY5N,KAAZ,CAAJ,EAAwB;AACtBA,QAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;AACD;;AAED,UAAIA,KAAK,CAACxB,MAAN,KAAiB,CAArB,EAAwB;AACtB;AACD;;AAED,UAAM,CAACR,IAAD,IAASgC,KAAf;AAGA;AACA;;AACA,UAAI,CAACtB,EAAL,EAAS;AACP,YAAI9E,MAAM,CAACG,SAAX,EAAsB;AACpB2E,UAAAA,EAAE,GAAG9E,MAAM,CAACG,SAAZ;AACD,SAFD,MAEO,IAAIH,MAAM,CAACC,QAAP,CAAgB2E,MAAhB,GAAyB,CAA7B,EAAgC;AACrCE,UAAAA,EAAE,GAAGnE,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB,EAAnB,CAAL;AACD,SAFM,MAEA;AACL8E,UAAAA,EAAE,GAAG,CAAC,CAAD,CAAL;AACD;;AAEDoU,QAAAA,MAAM,GAAG,IAAT;AACD;;AAED,UAAIA,MAAM,IAAI,IAAd,EAAoB;AAClBA,QAAAA,MAAM,GAAG,KAAT;AACD;;AAED,UAAIrW,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AACrB,YAAI,CAACmU,OAAL,EAAc;AACZnU,UAAAA,EAAE,GAAGnE,MAAM,CAAC8R,WAAP,CAAmBzS,MAAnB,EAA2B8E,EAA3B,CAAL;AACD;;AAED,YAAIjC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACkI,MAAR;AACD,SAFD,MAEO;AACL,cAAM,GAAGvD,GAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAAhB;AACA,cAAM6M,QAAQ,GAAGhR,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByJ,GAAxB,CAAjB;AACApH,UAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAE8E,YAAAA;AAAF,WAA1B;AACAA,UAAAA,EAAE,GAAG6M,QAAQ,CAACF,KAAT,EAAL;AACD;AACF;;AAED,UAAInD,KAAK,CAAC8C,OAAN,CAActM,EAAd,CAAJ,EAAuB;AACrB,YAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjB,cAAIC,IAAI,CAACC,MAAL,CAAYkB,IAAZ,CAAJ,EAAuB;AACrBpB,YAAAA,KAAK,GAAGmC,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,CAAb;AACD,WAFD,MAEO,IAAInF,MAAM,CAACK,QAAP,CAAgB+D,IAAhB,CAAJ,EAA2B;AAChCpB,YAAAA,KAAK,GAAGmC,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CAA/B;AACD,WAFM,MAEA;AACLnC,YAAAA,KAAK,GAAGmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAAb;AACD;AACF;;AAED,YAAM,CAACV,KAAD,IAAU9D,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AACnC8E,UAAAA,EAAE,EAAEA,EAAE,CAACpD,IAD4B;AAEnCsB,UAAAA,KAFmC;AAGnC8J,UAAAA,IAHmC;AAInC9H,UAAAA;AAJmC,SAArB,CAAhB;;AAOA,YAAIP,KAAJ,EAAW;AACT,cAAM,GAAG0U,UAAH,IAAgB1U,KAAtB;AACA,cAAM6M,OAAO,GAAG3Q,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBmZ,UAAvB,CAAhB;AACA,cAAMC,OAAO,GAAGzY,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqB8E,EAArB,EAAyBqU,UAAzB,CAAhB;AACA9W,UAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAAE8E,YAAAA,EAAF;AAAM9B,YAAAA,KAAN;AAAa8J,YAAAA,IAAb;AAAmB9H,YAAAA;AAAnB,WAA9B;AACA,cAAMtD,IAAI,GAAG4P,OAAO,CAACG,KAAR,EAAb;AACA3M,UAAAA,EAAE,GAAGsU,OAAO,GAAGrX,IAAI,CAACiF,IAAL,CAAUtF,IAAV,CAAH,GAAqBA,IAAjC;AACD,SAPD,MAOO;AACL;AACD;AACF;;AAED,UAAMqP,UAAU,GAAGhP,IAAI,CAACyN,MAAL,CAAY1K,EAAZ,CAAnB;AACA,UAAIqO,KAAK,GAAGrO,EAAE,CAACA,EAAE,CAACF,MAAH,GAAY,CAAb,CAAd;;AAEA,UAAI,CAACI,KAAD,IAAUrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,QAAAA,EAAE,EAAEiM;AAAN,OAApB,CAAd,EAAuD;AACrD;AACD;;AAED,WAAK,IAAM3M,KAAX,IAAmBgC,KAAnB,EAA0B;AACxB,YAAM1E,KAAI,GAAGqP,UAAU,CAAChM,MAAX,CAAkBoO,KAAlB,CAAb;;AACAA,QAAAA,KAAK;AACLnT,QAAAA,MAAM,CAACQ,KAAP,CAAa;AAAE+B,UAAAA,IAAI,EAAE,aAAR;AAAuBb,UAAAA,IAAI,EAAJA,KAAvB;AAA6B0C,UAAAA,IAAI,EAAJA;AAA7B,SAAb;AACAU,QAAAA,EAAE,GAAG/C,IAAI,CAACiF,IAAL,CAAUlC,EAAV,CAAL;AACD;;AACDA,MAAAA,EAAE,GAAG/C,IAAI,CAACyE,QAAL,CAAc1B,EAAd,CAAL;;AAEA,UAAIoU,MAAJ,EAAY;AACV,YAAMjM,KAAK,GAAGtM,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB8E,EAAnB,CAAd;;AAEA,YAAImI,KAAJ,EAAW;AACT5K,UAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0BiN,KAA1B;AACD;AACF;AACF,KApGD;AAqGD,GAtH2C;;AAwH5C;;;;AAKAoM,EAAAA,SAAS,CACPrZ,MADO;QAEP6M,8EAKI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAE8E,QAAAA,EAAE,GAAG9E,MAAM,CAACG,SAAd;AAAyB2M,QAAAA,IAAI,GAAG,QAAhC;AAA0C9H,QAAAA,KAAK,GAAG;AAAlD,UAA4D6H,OAAlE;AACA,UAAI;AAAE7J,QAAAA;AAAF,UAAY6J,OAAhB;;AAEA,UAAI7J,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,IACJqU,SAAS,CAACnZ,MAAD,EAAS8E,EAAT,CADL,GAEJK,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAFT;AAGD;;AAED,UAAI,CAACL,EAAL,EAAS;AACP;AACD;;AAED,UAAM2H,OAAO,GAAG9L,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAa8J,QAAAA,IAAb;AAAmB9H,QAAAA;AAAnB,OAArB,CAAhB;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAWsG,OAAX,EAAoB;AAAA,YAAC,GAAGpG,CAAH,CAAD;AAAA,eAAW1F,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBqG,CAAvB,CAAX;AAAA,OAApB,CAAjB;;AAEA,WAAK,IAAMiL,OAAX,IAAsB1Q,QAAtB,EAAgC;AAC9B,YAAMc,IAAI,GAAG4P,OAAO,CAACG,KAAR,EAAb;;AAEA,YAAI/P,IAAI,CAACkD,MAAL,GAAc,CAAlB,EAAqB;AACnB,gBAAM,IAAI2K,KAAJ,uCAC2B7N,IAD3B,gDAAN;AAGD;;AAED,YAAM4X,eAAe,GAAG3Y,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB+B,IAAI,CAACyN,MAAL,CAAY9N,IAAZ,CAApB,CAAxB;AACA,YAAM,CAAC8N,MAAD,EAASuB,UAAT,IAAuBuI,eAA7B;AACA,YAAMnG,KAAK,GAAGzR,IAAI,CAACA,IAAI,CAACkD,MAAL,GAAc,CAAf,CAAlB;AACA,YAAM;AAAEA,UAAAA;AAAF,YAAa4K,MAAM,CAACvP,QAA1B;;AAEA,YAAI2E,MAAM,KAAK,CAAf,EAAkB;AAChB,cAAM2U,MAAM,GAAGxX,IAAI,CAACiF,IAAL,CAAU+J,UAAV,CAAf;AACA1O,UAAAA,UAAU,CAACmX,SAAX,CAAqBxZ,MAArB,EAA6B;AAAE8E,YAAAA,EAAE,EAAEpD,IAAN;AAAY0N,YAAAA,EAAE,EAAEmK,MAAhB;AAAwBvU,YAAAA;AAAxB,WAA7B;AACA3C,UAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAAE8E,YAAAA,EAAE,EAAEiM,UAAN;AAAkB/L,YAAAA;AAAlB,WAA/B;AACD,SAJD,MAIO,IAAImO,KAAK,KAAK,CAAd,EAAiB;AACtB9Q,UAAAA,UAAU,CAACmX,SAAX,CAAqBxZ,MAArB,EAA6B;AAAE8E,YAAAA,EAAE,EAAEpD,IAAN;AAAY0N,YAAAA,EAAE,EAAE2B,UAAhB;AAA4B/L,YAAAA;AAA5B,WAA7B;AACD,SAFM,MAEA,IAAImO,KAAK,KAAKvO,MAAM,GAAG,CAAvB,EAA0B;AAC/B,cAAM2U,OAAM,GAAGxX,IAAI,CAACiF,IAAL,CAAU+J,UAAV,CAAf;;AACA1O,UAAAA,UAAU,CAACmX,SAAX,CAAqBxZ,MAArB,EAA6B;AAAE8E,YAAAA,EAAE,EAAEpD,IAAN;AAAY0N,YAAAA,EAAE,EAAEmK,OAAhB;AAAwBvU,YAAAA;AAAxB,WAA7B;AACD,SAHM,MAGA;AACL,cAAMyU,SAAS,GAAG1X,IAAI,CAACiF,IAAL,CAAUtF,IAAV,CAAlB;;AACA,cAAM6X,QAAM,GAAGxX,IAAI,CAACiF,IAAL,CAAU+J,UAAV,CAAf;;AACA1O,UAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAAE8E,YAAAA,EAAE,EAAE2U,SAAN;AAAiBzU,YAAAA;AAAjB,WAA9B;AACA3C,UAAAA,UAAU,CAACmX,SAAX,CAAqBxZ,MAArB,EAA6B;AAAE8E,YAAAA,EAAE,EAAEpD,IAAN;AAAY0N,YAAAA,EAAE,EAAEmK,QAAhB;AAAwBvU,YAAAA;AAAxB,WAA7B;AACD;AACF;AACF,KA/CD;AAgDD,GAtL2C;;AAwL5C;;;;AAKAa,EAAAA,UAAU,CACR7F,MADQ;QAER6M,8EAMI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAI;AAAEgD,QAAAA,KAAF;AAAS8B,QAAAA,EAAE,GAAG9E,MAAM,CAACG;AAArB,UAAmC0M,OAAvC;AACA,UAAM;AAAEoM,QAAAA,OAAO,GAAG,KAAZ;AAAmBjU,QAAAA,KAAK,GAAG,KAA3B;AAAkC8H,QAAAA,IAAI,GAAG;AAAzC,UAAsDD,OAA5D;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAIjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB,cAAM,CAAC0K,MAAD,IAAW7O,MAAM,CAAC6O,MAAP,CAAcxP,MAAd,EAAsB8E,EAAtB,CAAjB;;AACA9B,UAAAA,KAAK,GAAGmC,CAAC,IAAIqK,MAAM,CAACvP,QAAP,CAAgBwP,QAAhB,CAAyBtK,CAAzB,CAAb;AACD,SAHD,MAGO;AACLnC,UAAAA,KAAK,GAAGmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAAb;AACD;AACF;;AAED,UAAI,CAAC8T,OAAD,IAAYpW,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAhB,EAAmC;AACjCA,QAAAA,EAAE,GAAGnE,MAAM,CAAC8R,WAAP,CAAmBzS,MAAnB,EAA2B8E,EAA3B,CAAL;AACD;;AAED,UAAIjC,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AACrB,YAAIjC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACkI,MAAR;AACD,SAFD,MAEO;AACL,cAAM,GAAGvD,GAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAAhB;AACA,cAAM6M,QAAQ,GAAGhR,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByJ,GAAxB,CAAjB;AACApH,UAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAE8E,YAAAA;AAAF,WAA1B;AACAA,UAAAA,EAAE,GAAG6M,QAAQ,CAACF,KAAT,EAAL;;AAEA,cAAI5E,OAAO,CAAC/H,EAAR,IAAc,IAAlB,EAAwB;AACtBzC,YAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0B8E,EAA1B;AACD;AACF;AACF;;AAED,UAAM,CAAC0M,OAAD,IAAY7Q,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAagC,QAAAA,KAAb;AAAoB8H,QAAAA;AAApB,OAArB,CAAlB;AACA,UAAMxH,IAAI,GAAG3E,MAAM,CAAC6F,QAAP,CAAgBxG,MAAhB,EAAwB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAagC,QAAAA,KAAb;AAAoB8H,QAAAA;AAApB,OAAxB,CAAb;;AAEA,UAAI,CAAC0E,OAAD,IAAY,CAAClM,IAAjB,EAAuB;AACrB;AACD;;AAED,UAAM,CAAClB,IAAD,EAAO1C,IAAP,IAAe8P,OAArB;AACA,UAAM,CAACxC,QAAD,EAAWC,QAAX,IAAuB3J,IAA7B;;AAEA,UAAI5D,IAAI,CAACkD,MAAL,KAAgB,CAAhB,IAAqBqK,QAAQ,CAACrK,MAAT,KAAoB,CAA7C,EAAgD;AAC9C;AACD;;AAED,UAAM3C,OAAO,GAAGF,IAAI,CAACiF,IAAL,CAAUiI,QAAV,CAAhB;AACA,UAAMyK,UAAU,GAAG3X,IAAI,CAACoP,MAAL,CAAYzP,IAAZ,EAAkBuN,QAAlB,CAAnB;AACA,UAAM0K,iBAAiB,GAAG5X,IAAI,CAACgT,SAAL,CAAerT,IAAf,EAAqBuN,QAArB,CAA1B;AACA,UAAMjJ,MAAM,GAAGE,KAAK,CAACC,IAAN,CAAWxF,MAAM,CAACqF,MAAP,CAAchG,MAAd,EAAsB;AAAE8E,QAAAA,EAAE,EAAEpD;AAAN,OAAtB,CAAX,EAAgD;AAAA,YAAC,CAACyD,CAAD,CAAD;AAAA,eAASA,CAAT;AAAA,OAAhD,EACZkE,KADY,CACNqQ,UAAU,CAAC9U,MADL,EAEZyE,KAFY,CAEN,CAFM,EAEH,CAAC,CAFE,CAAf;AAKA;;AACA,UAAMuQ,aAAa,GAAGjZ,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AACzC8E,QAAAA,EAAE,EAAEpD,IADqC;AAEzCoL,QAAAA,IAAI,EAAE,SAFmC;AAGzC9J,QAAAA,KAAK,EAAEmC,CAAC,IAAIa,MAAM,CAACyJ,QAAP,CAAgBtK,CAAhB,KAAsB0U,kBAAkB,CAAC7Z,MAAD,EAASmF,CAAT;AAHX,OAArB,CAAtB;AAMA,UAAM2U,QAAQ,GAAGF,aAAa,IAAIjZ,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuB4Z,aAAa,CAAC,CAAD,CAApC,CAAlC;AACA,UAAIhG,UAAJ;AACA,UAAIc,QAAJ;AAGA;;AACA,UAAIzR,IAAI,CAACC,MAAL,CAAYkB,IAAZ,KAAqBnB,IAAI,CAACC,MAAL,CAAY8L,QAAZ,CAAzB,EAAgD;AAC9C,YAAiBF,IAAjB,4BAA0B1K,IAA1B;;AACAsQ,QAAAA,QAAQ,GAAG1F,QAAQ,CAACzK,IAAT,CAAcK,MAAzB;AACAgP,QAAAA,UAAU,GAAG9E,IAAb;AACD,OAJD,MAIO,IAAIpK,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2BM,OAAO,CAACC,SAAR,CAAkBqK,QAAlB,CAA/B,EAA4D;AACjE,YAAqBF,IAArB,4BAA8B1K,IAA9B;;AACAsQ,QAAAA,QAAQ,GAAG1F,QAAQ,CAAC/O,QAAT,CAAkB2E,MAA7B;AACAgP,QAAAA,UAAU,GAAG9E,IAAb;AACD,OAJM,MAIA;AACL,cAAM,IAAIS,KAAJ,0CAC8B7N,IAD9B,0EACkG0R,IAAI,CAACC,SAAL,CACpGjP,IADoG,CADlG,cAGCgP,IAAI,CAACC,SAAL,CAAerE,QAAf,CAHD,EAAN;AAKD;AAGD;;;AACA,UAAI,CAAC2K,iBAAL,EAAwB;AACtBtX,QAAAA,UAAU,CAACmX,SAAX,CAAqBxZ,MAArB,EAA6B;AAAE8E,UAAAA,EAAE,EAAEpD,IAAN;AAAY0N,UAAAA,EAAE,EAAEnN,OAAhB;AAAyB+C,UAAAA;AAAzB,SAA7B;AACD;AAGD;;;AACA,UAAI8U,QAAJ,EAAc;AACZzX,QAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAAE8E,UAAAA,EAAE,EAAEgV,QAAQ,CAACtI,OAAf;AAAyBxM,UAAAA;AAAzB,SAA/B;AACD;AAGD;AACA;AACA;AACA;;;AACA,UACGN,OAAO,CAACC,SAAR,CAAkBqK,QAAlB,KAA+BrO,MAAM,CAAC8N,OAAP,CAAezO,MAAf,EAAuBgP,QAAvB,CAAhC,IACC/L,IAAI,CAACC,MAAL,CAAY8L,QAAZ,KACCA,QAAQ,CAACzK,IAAT,KAAkB,EADnB,IAEC0K,QAAQ,CAACA,QAAQ,CAACrK,MAAT,GAAkB,CAAnB,CAAR,KAAkC,CAJtC,EAKE;AACAvC,QAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAAE8E,UAAAA,EAAE,EAAEmK,QAAN;AAAgBjK,UAAAA;AAAhB,SAA/B;AACD,OAPD,MAOO;AACLhF,QAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,UAAAA,IAAI,EAAE,YADK;AAEXb,UAAAA,IAAI,EAAEO,OAFK;AAGXyS,UAAAA,QAHW;AAIXd,UAAAA;AAJW,SAAb;AAMD;;AAED,UAAIkG,QAAJ,EAAc;AACZA,QAAAA,QAAQ,CAACrI,KAAT;AACD;AACF,KA3HD;AA4HD,GAnU2C;;AAqU5C;;;AAIA+H,EAAAA,SAAS,CACPxZ,MADO,EAEP6M,OAFO;AAUPlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AACJoP,QAAAA,EADI;AAEJtK,QAAAA,EAAE,GAAG9E,MAAM,CAACG,SAFR;AAGJ2M,QAAAA,IAAI,GAAG,QAHH;AAIJ9H,QAAAA,KAAK,GAAG;AAJJ,UAKF6H,OALJ;AAMA,UAAI;AAAE7J,QAAAA;AAAF,UAAY6J,OAAhB;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,IACJqU,SAAS,CAACnZ,MAAD,EAAS8E,EAAT,CADL,GAEJK,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAFT;AAGD;;AAED,UAAM4U,KAAK,GAAGpZ,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBoP,EAAvB,CAAd;AACA,UAAM4K,OAAO,GAAGrZ,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAa8J,QAAAA,IAAb;AAAmB9H,QAAAA;AAAnB,OAArB,CAAhB;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAW6T,OAAX,EAAoB;AAAA,YAAC,GAAG3T,CAAH,CAAD;AAAA,eAAW1F,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBqG,CAAvB,CAAX;AAAA,OAApB,CAAjB;;AAEA,WAAK,IAAMiL,OAAX,IAAsB1Q,QAAtB,EAAgC;AAC9B,YAAMc,IAAI,GAAG4P,OAAO,CAACG,KAAR,EAAb;AACA,YAAMxP,OAAO,GAAG8X,KAAK,CAACvI,OAAtB;;AAEA,YAAI9P,IAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB5E,UAAAA,MAAM,CAACQ,KAAP,CAAa;AAAE+B,YAAAA,IAAI,EAAE,WAAR;AAAqBb,YAAAA,IAArB;AAA2BO,YAAAA;AAA3B,WAAb;AACD;;AAED,YACE8X,KAAK,CAACvI,OAAN,IACAzP,IAAI,CAACgT,SAAL,CAAe9S,OAAf,EAAwBP,IAAxB,CADA,IAEAK,IAAI,CAACqS,OAAL,CAAanS,OAAb,EAAsBP,IAAtB,CAHF,EAIE;AACA;AACA;AACA;AACAqY,UAAAA,KAAK,CAACvI,OAAN,GAAgBzP,IAAI,CAACiF,IAAL,CAAU+S,KAAK,CAACvI,OAAhB,CAAhB;AACD;AACF;;AAEDuI,MAAAA,KAAK,CAACtI,KAAN;AACD,KA5CD;AA6CD,GAhY2C;;AAkY5C;;;AAIAhM,EAAAA,WAAW,CACTzF,MADS;QAET6M,8EAMI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAEiZ,QAAAA,OAAO,GAAG,KAAZ;AAAmBjU,QAAAA,KAAK,GAAG,KAA3B;AAAkC8H,QAAAA,IAAI,GAAG;AAAzC,UAAsDD,OAA5D;AACA,UAAI;AAAE/H,QAAAA,EAAE,GAAG9E,MAAM,CAACG,SAAd;AAAyB6C,QAAAA;AAAzB,UAAmC6J,OAAvC;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,IACJqU,SAAS,CAACnZ,MAAD,EAAS8E,EAAT,CADL,GAEJK,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAFT;AAGD;;AAED,UAAI,CAAC8T,OAAD,IAAYpW,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAhB,EAAmC;AACjCA,QAAAA,EAAE,GAAGnE,MAAM,CAAC8R,WAAP,CAAmBzS,MAAnB,EAA2B8E,EAA3B,CAAL;AACD;;AAED,UAAMmV,MAAM,GAAGtZ,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAa8J,QAAAA,IAAb;AAAmB9H,QAAAA;AAAnB,OAArB,CAAf;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAW8T,MAAX,EAAmB;AAAA,YAAC,GAAG5T,CAAH,CAAD;AAAA,eAAW1F,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBqG,CAAvB,CAAX;AAAA,OAAnB,CAAjB;;AAEA,WAAK,IAAMiL,OAAX,IAAsB1Q,QAAtB,EAAgC;AAC9B,YAAMc,IAAI,GAAG4P,OAAO,CAACG,KAAR,EAAb;;AAEA,YAAI/P,IAAJ,EAAU;AACR,cAAM,CAAC0C,IAAD,IAASzD,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB0B,IAApB,CAAf;AACA1B,UAAAA,MAAM,CAACQ,KAAP,CAAa;AAAE+B,YAAAA,IAAI,EAAE,aAAR;AAAuBb,YAAAA,IAAvB;AAA6B0C,YAAAA;AAA7B,WAAb;AACD;AACF;AACF,KA7BD;AA8BD,GA9a2C;;AAgb5C;;;AAIArB,EAAAA,QAAQ,CACN/C,MADM,EAENoM,KAFM;QAGNS,8EAOI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAI;AAAEgD,QAAAA,KAAF;AAAS8B,QAAAA,EAAE,GAAG9E,MAAM,CAACG;AAArB,UAAmC0M,OAAvC;AACA,UAAM;AACJoM,QAAAA,OAAO,GAAG,KADN;AAEJnM,QAAAA,IAAI,GAAG,QAFH;AAGJ3J,QAAAA,KAAK,GAAG,KAHJ;AAIJ6B,QAAAA,KAAK,GAAG;AAJJ,UAKF6H,OALJ;;AAOA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,IACJqU,SAAS,CAACnZ,MAAD,EAAS8E,EAAT,CADL,GAEJK,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAFT;AAGD;;AAED,UAAI,CAAC8T,OAAD,IAAYpW,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAhB,EAAmC;AACjCA,QAAAA,EAAE,GAAGnE,MAAM,CAAC8R,WAAP,CAAmBzS,MAAnB,EAA2B8E,EAA3B,CAAL;AACD;;AAED,UAAI3B,KAAK,IAAIN,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAb,EAAgC;AAC9B,YACEjC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,KACAnE,MAAM,CAACkO,IAAP,CAAY7O,MAAZ,EAAoB8E,EAAE,CAACkI,MAAvB,EAA+B,CAA/B,EAAkCzI,IAAlC,CAAuCK,MAAvC,GAAgD,CAFlD,EAGE;AACA;AACA;AACA;AACD;;AACD,YAAM0N,QAAQ,GAAG3R,MAAM,CAAC2R,QAAP,CAAgBtS,MAAhB,EAAwB8E,EAAxB,EAA4B;AAAEyM,UAAAA,QAAQ,EAAE;AAAZ,SAA5B,CAAjB;AACA,YAAM,CAAC9D,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAArB;AACA,YAAMoV,SAAS,GAAGpN,IAAI,KAAK,QAAT,GAAoB,QAApB,GAA+B,SAAjD;AACA,YAAMqN,cAAc,GAAGxZ,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqByJ,GAArB,EAA0BA,GAAG,CAAC/H,IAA9B,CAAvB;AACAW,QAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAC5B8E,UAAAA,EAAE,EAAE2E,GADwB;AAE5BzG,UAAAA,KAF4B;AAG5B8J,UAAAA,IAAI,EAAEoN,SAHsB;AAI5BlV,UAAAA,KAJ4B;AAK5Bf,UAAAA,MAAM,EAAE,CAACkW;AALmB,SAA9B;AAOA,YAAMC,kBAAkB,GAAGzZ,MAAM,CAAC6N,OAAP,CAAexO,MAAf,EAAuByN,KAAvB,EAA8BA,KAAK,CAAC/L,IAApC,CAA3B;AACAW,QAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAC5B8E,UAAAA,EAAE,EAAE2I,KADwB;AAE5BzK,UAAAA,KAF4B;AAG5B8J,UAAAA,IAAI,EAAEoN,SAHsB;AAI5BlV,UAAAA,KAJ4B;AAK5Bf,UAAAA,MAAM,EAAE,CAACmW;AALmB,SAA9B;AAOAtV,QAAAA,EAAE,GAAGwN,QAAQ,CAACb,KAAT,EAAL;;AAEA,YAAI5E,OAAO,CAAC/H,EAAR,IAAc,IAAlB,EAAwB;AACtBzC,UAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0B8E,EAA1B;AACD;AACF;;AAED,WAAK,IAAM,CAACV,IAAD,EAAO1C,IAAP,CAAX,IAA2Bf,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAC9C8E,QAAAA,EAD8C;AAE9C9B,QAAAA,KAF8C;AAG9C8J,QAAAA,IAH8C;AAI9C9H,QAAAA;AAJ8C,OAArB,CAA3B,EAKI;AACF,YAAM4O,UAAU,GAAkB,EAAlC;AACA,YAAMe,aAAa,GAAkB,EAArC,CAFE;;AAKF,YAAIjT,IAAI,CAACkD,MAAL,KAAgB,CAApB,EAAuB;AACrB;AACD;;AAED,YAAIyV,UAAU,GAAG,KAAjB;;AAEA,aAAK,IAAMC,CAAX,IAAgBlO,KAAhB,EAAuB;AACrB,cAAIkO,CAAC,KAAK,UAAN,IAAoBA,CAAC,KAAK,MAA9B,EAAsC;AACpC;AACD;;AAED,cAAIlO,KAAK,CAACkO,CAAD,CAAL,KAAalW,IAAI,CAACkW,CAAD,CAArB,EAA0B;AACxBD,YAAAA,UAAU,GAAG,IAAb,CADwB;;AAGxB,gBAAIjW,IAAI,CAAC2T,cAAL,CAAoBuC,CAApB,CAAJ,EAA4B1G,UAAU,CAAC0G,CAAD,CAAV,GAAgBlW,IAAI,CAACkW,CAAD,CAApB,CAHJ;;AAKxB,gBAAIlO,KAAK,CAACkO,CAAD,CAAL,IAAY,IAAhB,EAAsB3F,aAAa,CAAC2F,CAAD,CAAb,GAAmBlO,KAAK,CAACkO,CAAD,CAAxB;AACvB;AACF;;AAED,YAAID,UAAJ,EAAgB;AACdra,UAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,YAAAA,IAAI,EAAE,UADK;AAEXb,YAAAA,IAFW;AAGXkS,YAAAA,UAHW;AAIXe,YAAAA;AAJW,WAAb;AAMD;AACF;AACF,KAjGD;AAkGD,GAliB2C;;AAoiB5C;;;AAIA3Q,EAAAA,UAAU,CACRhE,MADQ;QAER6M,8EAOI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAE8M,QAAAA,IAAI,GAAG,QAAT;AAAmB9H,QAAAA,KAAK,GAAG;AAA3B,UAAqC6H,OAA3C;AACA,UAAI;AAAE7J,QAAAA,KAAF;AAAS8B,QAAAA,EAAE,GAAG9E,MAAM,CAACG,SAArB;AAAgCoa,QAAAA,MAAM,GAAG,CAAzC;AAA4CtW,QAAAA,MAAM,GAAG;AAArD,UAA+D4I,OAAnE;;AAEA,UAAI7J,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAAb;AACD;;AAED,UAAItC,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,GAAG0V,WAAW,CAACxa,MAAD,EAAS8E,EAAT,CAAhB;AACD;AAGD;;;AACA,UAAI/C,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB,YAAMpD,IAAI,GAAGoD,EAAb;AACA,YAAMmI,KAAK,GAAGtM,MAAM,CAACsM,KAAP,CAAajN,MAAb,EAAqB0B,IAArB,CAAd;AACA,YAAM,CAAC8N,MAAD,IAAW7O,MAAM,CAAC6O,MAAP,CAAcxP,MAAd,EAAsB0B,IAAtB,CAAjB;;AACAsB,QAAAA,KAAK,GAAGmC,CAAC,IAAIA,CAAC,KAAKqK,MAAnB;;AACA+K,QAAAA,MAAM,GAAGtN,KAAK,CAACvL,IAAN,CAAWkD,MAAX,GAAoBlD,IAAI,CAACkD,MAAzB,GAAkC,CAA3C;AACAE,QAAAA,EAAE,GAAGmI,KAAL;AACAhJ,QAAAA,MAAM,GAAG,IAAT;AACD;;AAED,UAAI,CAACa,EAAL,EAAS;AACP;AACD;;AAED,UAAM2V,SAAS,GAAG9Z,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwB8E,EAAxB,EAA4B;AAC5CyM,QAAAA,QAAQ,EAAE;AADkC,OAA5B,CAAlB;AAGA,UAAM,CAACmJ,OAAD,IAAY/Z,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAa8J,QAAAA,IAAb;AAAmB9H,QAAAA;AAAnB,OAArB,CAAlB;;AAEA,UAAI,CAAC0V,OAAL,EAAc;AACZ;AACD;;AAED,UAAMC,SAAS,GAAGha,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,QAAAA,EAAF;AAAMgI,QAAAA,IAAI,EAAE;AAAZ,OAApB,CAAlB;AACA,UAAM8N,KAAK,GAAG,CAAd;;AAEA,UAAI,CAAC5V,KAAD,IAAU2V,SAAd,EAAyB;AACvB,YAAM,CAACE,QAAD,EAAWC,QAAX,IAAuBH,SAA7B;;AAEA,YAAIjW,OAAO,CAACC,SAAR,CAAkBkW,QAAlB,KAA+B7a,MAAM,CAACK,QAAP,CAAgBwa,QAAhB,CAAnC,EAA8D;AAC5D,cAAI9N,KAAK,GAAGpM,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqB8a,QAArB,CAAZ;;AAEA,cAAI,CAAC/N,KAAL,EAAY;AACV,gBAAMxI,IAAI,GAAG;AAAEA,cAAAA,IAAI,EAAE;AAAR,aAAb;AACA,gBAAMwW,SAAS,GAAGhZ,IAAI,CAACiF,IAAL,CAAU8T,QAAV,CAAlB;AACAzY,YAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+BuE,IAA/B,EAAqC;AAAEO,cAAAA,EAAE,EAAEiW,SAAN;AAAiB/V,cAAAA;AAAjB,aAArC;AACA+H,YAAAA,KAAK,GAAGpM,MAAM,CAACsM,KAAP,CAAajN,MAAb,EAAqB+a,SAArB,CAAR;AACD;;AAEDjW,UAAAA,EAAE,GAAGiI,KAAL;AACA9I,UAAAA,MAAM,GAAG,IAAT;AACD;;AAED,YAAM+W,aAAa,GAAGlW,EAAE,CAACpD,IAAH,CAAQkD,MAAR,GAAiBkW,QAAQ,CAAClW,MAAhD;AACA2V,QAAAA,MAAM,GAAGS,aAAa,GAAG,CAAzB;AACA/W,QAAAA,MAAM,GAAG,IAAT;AACD;;AAED,UAAMgX,QAAQ,GAAGta,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwB8E,EAAxB,CAAjB;AACA,UAAMkM,KAAK,GAAGlM,EAAE,CAACpD,IAAH,CAAQkD,MAAR,GAAiB2V,MAA/B;AACA,UAAM,GAAGW,WAAH,IAAkBR,OAAxB;AACA,UAAMS,UAAU,GAAGrW,EAAE,CAACpD,IAAH,CAAQ2H,KAAR,CAAc,CAAd,EAAiB2H,KAAjB,CAAnB;AACA,UAAI0D,QAAQ,GAAG6F,MAAM,KAAK,CAAX,GAAezV,EAAE,CAAC6J,MAAlB,GAA2B7J,EAAE,CAACpD,IAAH,CAAQsP,KAAR,IAAiB4J,KAA3D;;AAEA,WAAK,IAAM,CAACxW,IAAD,EAAO1C,MAAP,CAAX,IAA2Bf,MAAM,CAACqF,MAAP,CAAchG,MAAd,EAAsB;AAC/C8E,QAAAA,EAAE,EAAEqW,UAD2C;AAE/C3X,QAAAA,OAAO,EAAE,IAFsC;AAG/CwB,QAAAA;AAH+C,OAAtB,CAA3B,EAII;AACF,YAAI7B,KAAK,GAAG,KAAZ;;AAEA,YACEzB,MAAI,CAACkD,MAAL,GAAcsW,WAAW,CAACtW,MAA1B,IACAlD,MAAI,CAACkD,MAAL,KAAgB,CADhB,IAEC,CAACI,KAAD,IAAUrE,MAAM,CAACL,MAAP,CAAcN,MAAd,EAAsBoE,IAAtB,CAHb,EAIE;AACA;AACD;;AAED,YAAM6I,MAAK,GAAGwN,SAAS,CAACjJ,OAAxB;AACA,YAAMnD,KAAK,GAAG1N,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqBiN,MAArB,EAA4BvL,MAA5B,CAAd;;AAEA,YAAIuC,MAAM,IAAI,CAACwW,SAAX,IAAwB,CAAC9Z,MAAM,CAAC4N,MAAP,CAAcvO,MAAd,EAAsBiN,MAAtB,EAA6BvL,MAA7B,CAA7B,EAAiE;AAC/DyB,UAAAA,KAAK,GAAG,IAAR;AACA,cAAMyQ,UAAU,GAAG/P,IAAI,CAAC8P,YAAL,CAAkBvP,IAAlB,CAAnB;AACApE,UAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,YAAAA,IAAI,EAAE,YADK;AAEXb,YAAAA,IAAI,EAAJA,MAFW;AAGXgT,YAAAA,QAHW;AAIXd,YAAAA;AAJW,WAAb;AAMD;;AAEDc,QAAAA,QAAQ,GAAGhT,MAAI,CAACA,MAAI,CAACkD,MAAL,GAAc,CAAf,CAAJ,IAAyBzB,KAAK,IAAIkL,KAAT,GAAiB,CAAjB,GAAqB,CAA9C,CAAX;AACD;;AAED,UAAIxB,OAAO,CAAC/H,EAAR,IAAc,IAAlB,EAAwB;AACtB,YAAMmI,OAAK,GAAGgO,QAAQ,CAACzJ,OAAT,IAAoB7Q,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB,EAAnB,CAAlC;;AACAqC,QAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0BiN,OAA1B;AACD;;AAEDwN,MAAAA,SAAS,CAAChJ,KAAV;AACAwJ,MAAAA,QAAQ,CAACxJ,KAAT;AACD,KA3GD;AA4GD,GA/pB2C;;AAiqB5C;;;AAIA1L,EAAAA,UAAU,CACR/F,MADQ,EAERoM,KAFQ;QAGRS,8EAMI;;AAEJ,QAAI,CAAC3G,KAAK,CAAC8F,OAAN,CAAcI,KAAd,CAAL,EAA2B;AACzBA,MAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;AACD;;AAED,QAAMyL,GAAG,GAAG,EAAZ;;AAEA,SAAK,IAAMlW,GAAX,IAAkByK,KAAlB,EAAyB;AACvByL,MAAAA,GAAG,CAAClW,GAAD,CAAH,GAAW,IAAX;AACD;;AAEDU,IAAAA,UAAU,CAACU,QAAX,CAAoB/C,MAApB,EAA4B6X,GAA5B,EAAiChL,OAAjC;AACD,GA3rB2C;;AA6rB5C;;;;AAKAuO,EAAAA,WAAW,CACTpb,MADS;QAET6M,8EAMI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAE8M,QAAAA,IAAI,GAAG,QAAT;AAAmB3J,QAAAA,KAAK,GAAG,KAA3B;AAAkC6B,QAAAA,KAAK,GAAG;AAA1C,UAAoD6H,OAA1D;AACA,UAAI;AAAE/H,QAAAA,EAAE,GAAG9E,MAAM,CAACG,SAAd;AAAyB6C,QAAAA;AAAzB,UAAmC6J,OAAvC;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjBA,QAAAA,KAAK,GAAGjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,IACJqU,SAAS,CAACnZ,MAAD,EAAS8E,EAAT,CADL,GAEJK,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAFT;AAGD;;AAED,UAAIpD,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnBA,QAAAA,EAAE,GAAGnE,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqB8E,EAArB,CAAL;AACD;;AAED,UAAMwN,QAAQ,GAAGzP,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,IAAoBnE,MAAM,CAAC2R,QAAP,CAAgBtS,MAAhB,EAAwB8E,EAAxB,CAApB,GAAkD,IAAnE;AACA,UAAM2H,OAAO,GAAG9L,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAM9B,QAAAA,KAAN;AAAa8J,QAAAA,IAAb;AAAmB9H,QAAAA;AAAnB,OAArB,CAAhB;AACA,UAAMpE,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CACfsG,OADe,EAEf;AAAA,YAAC,GAAGpG,CAAH,CAAD;AAAA,eAAW1F,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBqG,CAAvB,CAAX;AAAA,OAFe;AAIf;AACA;AALe,QAMf7C,OANe,EAAjB;;iCAQW8N;AACT,YAAM5P,IAAI,GAAG4P,OAAO,CAACG,KAAR,EAAb;AACA,YAAM,CAACrN,IAAD,IAASzD,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB0B,IAApB,CAAf;AACA,YAAI0L,KAAK,GAAGzM,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqB0B,IAArB,CAAZ;;AAEA,YAAIyB,KAAK,IAAImP,QAAb,EAAuB;AACrBlF,UAAAA,KAAK,GAAGvK,KAAK,CAACmU,YAAN,CAAmB1E,QAAQ,CAACd,OAA5B,EAAsCpE,KAAtC,CAAR;AACD;;AAED/K,QAAAA,UAAU,CAACgX,SAAX,CAAqBrZ,MAArB,EAA6B;AAC3B8E,UAAAA,EAAE,EAAEsI,KADuB;AAE3BpK,UAAAA,KAAK,EAAEmC,CAAC,IAAIT,OAAO,CAACoH,UAAR,CAAmB1H,IAAnB,KAA4BA,IAAI,CAACnE,QAAL,CAAcwP,QAAd,CAAuBtK,CAAvB,CAFb;AAG3BH,UAAAA;AAH2B,SAA7B;;;AATF,WAAK,IAAMsM,OAAX,IAAsB1Q,QAAtB,EAAgC;AAAA,cAArB0Q,OAAqB;AAc/B;;AAED,UAAIgB,QAAJ,EAAc;AACZA,QAAAA,QAAQ,CAACb,KAAT;AACD;AACF,KA/CD;AAgDD,GA5vB2C;;AA8vB5C;;;;AAKA4J,EAAAA,SAAS,CACPrb,MADO,EAEP0M,OAFO;QAGPG,8EAMI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAE8M,QAAAA,IAAI,GAAG,QAAT;AAAmB3J,QAAAA,KAAK,GAAG,KAA3B;AAAkC6B,QAAAA,KAAK,GAAG;AAA1C,UAAoD6H,OAA1D;AACA,UAAI;AAAE7J,QAAAA,KAAF;AAAS8B,QAAAA,EAAE,GAAG9E,MAAM,CAACG;AAArB,UAAmC0M,OAAvC;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI9B,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAIjB,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnB9B,UAAAA,KAAK,GAAGmW,SAAS,CAACnZ,MAAD,EAAS8E,EAAT,CAAjB;AACD,SAFD,MAEO,IAAI9E,MAAM,CAACK,QAAP,CAAgBqM,OAAhB,CAAJ,EAA8B;AACnC1J,UAAAA,KAAK,GAAGmC,CAAC,IAAIxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,KAA8BlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,CAA3C;AACD,SAFM,MAEA;AACLnC,UAAAA,KAAK,GAAGmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAAb;AACD;AACF;;AAED,UAAIhC,KAAK,IAAIN,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAb,EAAgC;AAC9B,YAAM,CAAC2I,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAArB;AACA,YAAMwN,QAAQ,GAAG3R,MAAM,CAAC2R,QAAP,CAAgBtS,MAAhB,EAAwB8E,EAAxB,EAA4B;AAC3CyM,UAAAA,QAAQ,EAAE;AADiC,SAA5B,CAAjB;AAGAlP,QAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAAE8E,UAAAA,EAAE,EAAE2E,GAAN;AAAWzG,UAAAA,KAAX;AAAkBgC,UAAAA;AAAlB,SAA9B;AACA3C,QAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAAE8E,UAAAA,EAAE,EAAE2I,KAAN;AAAazK,UAAAA,KAAb;AAAoBgC,UAAAA;AAApB,SAA9B;AACAF,QAAAA,EAAE,GAAGwN,QAAQ,CAACb,KAAT,EAAL;;AAEA,YAAI5E,OAAO,CAAC/H,EAAR,IAAc,IAAlB,EAAwB;AACtBzC,UAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0B8E,EAA1B;AACD;AACF;;AAED,UAAMwW,KAAK,GAAGpV,KAAK,CAACC,IAAN,CACZxF,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AACnB8E,QAAAA,EADmB;AAEnB9B,QAAAA,KAAK,EAAEhD,MAAM,CAACK,QAAP,CAAgBqM,OAAhB,IACHvH,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CADF,GAEHA,CAAC,IAAIxE,MAAM,CAACuE,QAAP,CAAgBC,CAAhB,CAJU;AAKnB2H,QAAAA,IAAI,EAAE,QALa;AAMnB9H,QAAAA;AANmB,OAArB,CADY,CAAd;;AAWA,WAAK,IAAM,GAAGuW,QAAH,CAAX,IAA2BD,KAA3B,EAAkC;AAChC,YAAM5D,CAAC,GAAG7U,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,IACNjC,KAAK,CAACmU,YAAN,CAAmBlS,EAAnB,EAAuBnE,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqBub,QAArB,CAAvB,CADM,GAENzW,EAFJ;;AAIA,YAAI,CAAC4S,CAAL,EAAQ;AACN;AACD;;AAED,YAAMjL,OAAO,GAAGvG,KAAK,CAACC,IAAN,CACdxF,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,UAAAA,EAAE,EAAE4S,CAAN;AAAS1U,UAAAA,KAAT;AAAgB8J,UAAAA,IAAhB;AAAsB9H,UAAAA;AAAtB,SAArB,CADc,CAAhB;;AAIA,YAAIyH,OAAO,CAAC7H,MAAR,GAAiB,CAArB,EAAwB;AAAA;AACtB,gBAAM,CAAC+I,KAAD,IAAUlB,OAAhB;AACA,gBAAMmC,IAAI,GAAGnC,OAAO,CAACA,OAAO,CAAC7H,MAAR,GAAiB,CAAlB,CAApB;AACA,gBAAM,GAAGqM,SAAH,IAAgBtD,KAAtB;AACA,gBAAM,GAAGuD,QAAH,IAAetC,IAArB;;AAEA,gBAAIqC,SAAS,CAACrM,MAAV,KAAqB,CAArB,IAA0BsM,QAAQ,CAACtM,MAAT,KAAoB,CAAlD,EAAqD;AACnD;AACA;AACD;;AAED,gBAAM8U,UAAU,GAAG3X,IAAI,CAAC4D,MAAL,CAAYsL,SAAZ,EAAuBC,QAAvB,IACfnP,IAAI,CAACyN,MAAL,CAAYyB,SAAZ,CADe,GAEflP,IAAI,CAACoP,MAAL,CAAYF,SAAZ,EAAuBC,QAAvB,CAFJ;AAIA,gBAAM9D,KAAK,GAAGzM,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqBiR,SAArB,EAAgCC,QAAhC,CAAd;AACA,gBAAMsK,eAAe,GAAG7a,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB0Z,UAApB,CAAxB;AACA,gBAAM,CAAC+B,UAAD,IAAeD,eAArB;AACA,gBAAMxK,KAAK,GAAG0I,UAAU,CAAC9U,MAAX,GAAoB,CAAlC;AACA,gBAAM8W,WAAW,GAAG3Z,IAAI,CAACiF,IAAL,CAAUkK,QAAQ,CAAC7H,KAAT,CAAe,CAAf,EAAkB2H,KAAlB,CAAV,CAApB;;AACA,gBAAM2K,OAAO,uCAAQjP,OAAR;AAAiBzM,cAAAA,QAAQ,EAAE;AAA3B,cAAb;;AACAoC,YAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B2b,OAA/B,EAAwC;AAAE7W,cAAAA,EAAE,EAAE4W,WAAN;AAAmB1W,cAAAA;AAAnB,aAAxC;AAEA3C,YAAAA,UAAU,CAACmX,SAAX,CAAqBxZ,MAArB,EAA6B;AAC3B8E,cAAAA,EAAE,EAAEsI,KADuB;AAE3BpK,cAAAA,KAAK,EAAEmC,CAAC,IACNT,OAAO,CAACoH,UAAR,CAAmB2P,UAAnB,KAAkCA,UAAU,CAACxb,QAAX,CAAoBwP,QAApB,CAA6BtK,CAA7B,CAHT;AAI3BiK,cAAAA,EAAE,EAAEsM,WAAW,CAAC3W,MAAZ,CAAmB,CAAnB,CAJuB;AAK3BC,cAAAA;AAL2B,aAA7B;AAvBsB;;AAAA,mCAQpB;AAsBH;AACF;AACF,KAxFD;AAyFD;;AAv2B2C,CAAvC;;AA02BP,IAAM6U,kBAAkB,GAAG,CAAC7Z,MAAD,EAAiBoE,IAAjB;AACzB,MAAIM,OAAO,CAACC,SAAR,CAAkBP,IAAlB,CAAJ,EAA6B;AAC3B,QAAMsI,OAAO,GAAGtI,IAAhB;;AACA,QAAIzD,MAAM,CAACL,MAAP,CAAcN,MAAd,EAAsBoE,IAAtB,CAAJ,EAAiC;AAC/B,aAAO,IAAP;AACD,KAFD,MAEO,IAAIsI,OAAO,CAACzM,QAAR,CAAiB2E,MAAjB,KAA4B,CAAhC,EAAmC;AACxC,aAAOiV,kBAAkB,CAAC7Z,MAAD,EAAS0M,OAAO,CAACzM,QAAR,CAAiB,CAAjB,CAAT,CAAzB;AACD,KAFM,MAEA;AACL,aAAO,KAAP;AACD;AACF,GATD,MASO,IAAIU,MAAM,CAACuE,QAAP,CAAgBd,IAAhB,CAAJ,EAA2B;AAChC,WAAO,KAAP;AACD,GAFM,MAEA;AACL,WAAO,IAAP;AACD;AACF,CAfD;AAiBA;;;;;AAIA,IAAMoW,WAAW,GAAG,CAACxa,MAAD,EAAiBoN,KAAjB;AAClB,MAAIvK,KAAK,CAACS,WAAN,CAAkB8J,KAAlB,CAAJ,EAA8B;AAC5B,WAAOA,KAAK,CAACJ,MAAb;AACD,GAFD,MAEO;AACL,QAAM,GAAGvD,GAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAYN,KAAZ,CAAhB;AACA,QAAMuE,QAAQ,GAAGhR,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByJ,GAAxB,CAAjB;AACApH,IAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAE8E,MAAAA,EAAE,EAAEsI;AAAN,KAA1B;AACA,WAAOuE,QAAQ,CAACF,KAAT,EAAP;AACD;AACF,CATD;;AAWA,IAAM0H,SAAS,GAAG,CAACnZ,MAAD,EAAiB0B,IAAjB;AAChB,MAAM,CAAC0C,IAAD,IAASzD,MAAM,CAACyD,IAAP,CAAYpE,MAAZ,EAAoB0B,IAApB,CAAf;AACA,SAAOyD,CAAC,IAAIA,CAAC,KAAKf,IAAlB;AACD,CAHD;;;;;ACx+BO,IAAMwX,mBAAmB,GAAwB;AACtD;;;AAIAC,EAAAA,QAAQ,CACN7b,MADM;QAEN6M,8EAEI;AAEJ,QAAM;AAAEK,MAAAA,IAAI,GAAG;AAAT,QAAsBL,OAA5B;AACA,QAAM;AAAE1M,MAAAA;AAAF,QAAgBH,MAAtB;;AAEA,QAAI,CAACG,SAAL,EAAgB;AACd;AACD,KAFD,MAEO,IAAI+M,IAAI,KAAK,QAAb,EAAuB;AAC5B7K,MAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0BG,SAAS,CAAC6M,MAApC;AACD,KAFM,MAEA,IAAIE,IAAI,KAAK,OAAb,EAAsB;AAC3B7K,MAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0BG,SAAS,CAACgN,KAApC;AACD,KAFM,MAEA,IAAID,IAAI,KAAK,OAAb,EAAsB;AAC3B,UAAM,CAACO,KAAD,IAAU5K,KAAK,CAAC6K,KAAN,CAAYvN,SAAZ,CAAhB;AACAkC,MAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0ByN,KAA1B;AACD,KAHM,MAGA,IAAIP,IAAI,KAAK,KAAb,EAAoB;AACzB,UAAM,GAAGzD,GAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAYvN,SAAZ,CAAhB;AACAkC,MAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0ByJ,GAA1B;AACD;AACF,GA3BqD;;AA6BtD;;;AAIAqS,EAAAA,QAAQ,CAAC9b,MAAD;AACN,QAAM;AAAEG,MAAAA;AAAF,QAAgBH,MAAtB;;AAEA,QAAIG,SAAJ,EAAe;AACbH,MAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,QAAAA,IAAI,EAAE,eADK;AAEXqR,QAAAA,UAAU,EAAEzT,SAFD;AAGXwU,QAAAA,aAAa,EAAE;AAHJ,OAAb;AAKD;AACF,GA3CqD;;AA6CtD;;;AAIAoH,EAAAA,IAAI,CACF/b,MADE;QAEF6M,8EAKI;AAEJ,QAAM;AAAE1M,MAAAA;AAAF,QAAgBH,MAAtB;AACA,QAAM;AAAE4H,MAAAA,QAAQ,GAAG,CAAb;AAAgBvE,MAAAA,IAAI,GAAG,WAAvB;AAAoCG,MAAAA,OAAO,GAAG;AAA9C,QAAwDqJ,OAA9D;AACA,QAAI;AAAEK,MAAAA,IAAI,GAAG;AAAT,QAAkBL,OAAtB;;AAEA,QAAI,CAAC1M,SAAL,EAAgB;AACd;AACD;;AAED,QAAI+M,IAAI,KAAK,OAAb,EAAsB;AACpBA,MAAAA,IAAI,GAAGrK,KAAK,CAAC4T,UAAN,CAAiBtW,SAAjB,IAA8B,OAA9B,GAAwC,QAA/C;AACD;;AAED,QAAI+M,IAAI,KAAK,KAAb,EAAoB;AAClBA,MAAAA,IAAI,GAAGrK,KAAK,CAAC4T,UAAN,CAAiBtW,SAAjB,IAA8B,QAA9B,GAAyC,OAAhD;AACD;;AAED,QAAM;AAAE6M,MAAAA,MAAF;AAAUG,MAAAA;AAAV,QAAoBhN,SAA1B;AACA,QAAM6b,IAAI,GAAG;AAAEpU,MAAAA,QAAF;AAAYvE,MAAAA;AAAZ,KAAb;AACA,QAAM+I,KAAK,GAAmB,EAA9B;;AAEA,QAAIc,IAAI,IAAI,IAAR,IAAgBA,IAAI,KAAK,QAA7B,EAAuC;AACrC,UAAMD,KAAK,GAAGzJ,OAAO,GACjB7C,MAAM,CAAC6M,MAAP,CAAcxN,MAAd,EAAsBgN,MAAtB,EAA8BgP,IAA9B,CADiB,GAEjBrb,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqBgN,MAArB,EAA6BgP,IAA7B,CAFJ;;AAIA,UAAI/O,KAAJ,EAAW;AACTb,QAAAA,KAAK,CAACY,MAAN,GAAeC,KAAf;AACD;AACF;;AAED,QAAIC,IAAI,IAAI,IAAR,IAAgBA,IAAI,KAAK,OAA7B,EAAsC;AACpC,UAAMD,MAAK,GAAGzJ,OAAO,GACjB7C,MAAM,CAAC6M,MAAP,CAAcxN,MAAd,EAAsBmN,KAAtB,EAA6B6O,IAA7B,CADiB,GAEjBrb,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqBmN,KAArB,EAA4B6O,IAA5B,CAFJ;;AAIA,UAAI/O,MAAJ,EAAW;AACTb,QAAAA,KAAK,CAACe,KAAN,GAAcF,MAAd;AACD;AACF;;AAED5K,IAAAA,UAAU,CAAC4Z,YAAX,CAAwBjc,MAAxB,EAAgCoM,KAAhC;AACD,GAnGqD;;AAqGtD;;;AAIA8M,EAAAA,MAAM,CAAClZ,MAAD,EAAiBsN,MAAjB;AACJ,QAAM;AAAEnN,MAAAA;AAAF,QAAgBH,MAAtB;AACAsN,IAAAA,MAAM,GAAG3M,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqBsN,MAArB,CAAT;;AAEA,QAAInN,SAAJ,EAAe;AACbkC,MAAAA,UAAU,CAAC4Z,YAAX,CAAwBjc,MAAxB,EAAgCsN,MAAhC;AACA;AACD;;AAED,QAAI,CAACzK,KAAK,CAACqL,OAAN,CAAcZ,MAAd,CAAL,EAA4B;AAC1B,YAAM,IAAIiC,KAAJ,6IACuI6D,IAAI,CAACC,SAAL,CACzI/F,MADyI,CADvI,EAAN;AAKD;;AAEDtN,IAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,MAAAA,IAAI,EAAE,eADK;AAEXqR,MAAAA,UAAU,EAAEzT,SAFD;AAGXwU,MAAAA,aAAa,EAAErH;AAHJ,KAAb;AAKD,GA/HqD;;AAiItD;;;AAIA4O,EAAAA,QAAQ,CACNlc,MADM,EAENoM,KAFM;QAGNS,8EAEI;AAEJ,QAAM;AAAE1M,MAAAA;AAAF,QAAgBH,MAAtB;AACA,QAAI;AAAEkN,MAAAA,IAAI,GAAG;AAAT,QAAoBL,OAAxB;;AAEA,QAAI,CAAC1M,SAAL,EAAgB;AACd;AACD;;AAED,QAAI+M,IAAI,KAAK,OAAb,EAAsB;AACpBA,MAAAA,IAAI,GAAGrK,KAAK,CAAC4T,UAAN,CAAiBtW,SAAjB,IAA8B,OAA9B,GAAwC,QAA/C;AACD;;AAED,QAAI+M,IAAI,KAAK,KAAb,EAAoB;AAClBA,MAAAA,IAAI,GAAGrK,KAAK,CAAC4T,UAAN,CAAiBtW,SAAjB,IAA8B,QAA9B,GAAyC,OAAhD;AACD;;AAED,QAAM;AAAE6M,MAAAA,MAAF;AAAUG,MAAAA;AAAV,QAAoBhN,SAA1B;AACA,QAAM8M,KAAK,GAAGC,IAAI,KAAK,QAAT,GAAoBF,MAApB,GAA6BG,KAA3C;AAEA9K,IAAAA,UAAU,CAAC4Z,YAAX,CAAwBjc,MAAxB,EAAgC;AAC9B,OAACkN,IAAI,KAAK,QAAT,GAAoB,QAApB,GAA+B,OAAhC,uCAA+CD,KAA/C,GAAyDb,KAAzD;AAD8B,KAAhC;AAGD,GAjKqD;;AAmKtD;;;AAIA6P,EAAAA,YAAY,CAACjc,MAAD,EAAiBoM,KAAjB;AACV,QAAM;AAAEjM,MAAAA;AAAF,QAAgBH,MAAtB;AACA,QAAMmc,QAAQ,GAA0B,EAAxC;AACA,QAAMC,QAAQ,GAAmB,EAAjC;;AAEA,QAAI,CAACjc,SAAL,EAAgB;AACd;AACD;;AAED,SAAK,IAAMma,CAAX,IAAgBlO,KAAhB,EAAuB;AACrB,UACGkO,CAAC,KAAK,QAAN,IACClO,KAAK,CAACY,MAAN,IAAgB,IADjB,IAEC,CAACsB,KAAK,CAAC3I,MAAN,CAAayG,KAAK,CAACY,MAAnB,EAA2B7M,SAAS,CAAC6M,MAArC,CAFH,IAGCsN,CAAC,KAAK,OAAN,IACClO,KAAK,CAACe,KAAN,IAAe,IADhB,IAEC,CAACmB,KAAK,CAAC3I,MAAN,CAAayG,KAAK,CAACe,KAAnB,EAA0BhN,SAAS,CAACgN,KAApC,CALH,IAMCmN,CAAC,KAAK,QAAN,IAAkBA,CAAC,KAAK,OAAxB,IAAmClO,KAAK,CAACkO,CAAD,CAAL,KAAana,SAAS,CAACma,CAAD,CAP5D,EAQE;AACA6B,QAAAA,QAAQ,CAAC7B,CAAD,CAAR,GAAcna,SAAS,CAACma,CAAD,CAAvB;AACA8B,QAAAA,QAAQ,CAAC9B,CAAD,CAAR,GAAclO,KAAK,CAACkO,CAAD,CAAnB;AACD;AACF;;AAED,QAAIlC,MAAM,CAACiE,IAAP,CAAYF,QAAZ,EAAsBvX,MAAtB,GAA+B,CAAnC,EAAsC;AACpC5E,MAAAA,MAAM,CAACQ,KAAP,CAAa;AACX+B,QAAAA,IAAI,EAAE,eADK;AAEXqR,QAAAA,UAAU,EAAEuI,QAFD;AAGXxH,QAAAA,aAAa,EAAEyH;AAHJ,OAAb;AAKD;AACF;;AAtMqD,CAAjD;;ACcA,IAAME,cAAc,GAAmB;AAC5C;;;AAIA/Y,EAAAA,MAAM,CACJvD,MADI;QAEJ6M,8EAOI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AACJwD,QAAAA,OAAO,GAAG,KADN;AAEJH,QAAAA,IAAI,GAAG,WAFH;AAGJuE,QAAAA,QAAQ,GAAG,CAHP;AAIJ5C,QAAAA,KAAK,GAAG;AAJJ,UAKF6H,OALJ;AAMA,UAAI;AAAE/H,QAAAA,EAAE,GAAG9E,MAAM,CAACG,SAAd;AAAyB8Y,QAAAA,OAAO,GAAG;AAAnC,UAA6CpM,OAAjD;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAIjC,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,KAAqBjC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,CAAzB,EAAgD;AAC9CA,QAAAA,EAAE,GAAGA,EAAE,CAACkI,MAAR;AACD;;AAED,UAAIsB,KAAK,CAAC8C,OAAN,CAActM,EAAd,CAAJ,EAAuB;AACrB,YAAMyX,YAAY,GAAG5b,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,UAAAA,EAAF;AAAMgI,UAAAA,IAAI,EAAE;AAAZ,SAApB,CAArB;;AAEA,YAAI,CAAC9H,KAAD,IAAUuX,YAAd,EAA4B;AAC1B,cAAM,GAAGzB,QAAH,IAAeyB,YAArB;AACAzX,UAAAA,EAAE,GAAGgW,QAAL;AACD,SAHD,MAGO;AACL,cAAMkB,IAAI,GAAG;AAAE3Y,YAAAA,IAAF;AAAQuE,YAAAA;AAAR,WAAb;AACA,cAAM0F,MAAM,GAAG9J,OAAO,GAClB7C,MAAM,CAAC6M,MAAP,CAAcxN,MAAd,EAAsB8E,EAAtB,EAA0BkX,IAA1B,KAAmCrb,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB,EAArB,CADjB,GAElBW,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqB8E,EAArB,EAAyBkX,IAAzB,KAAkCrb,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB,EAAnB,CAFtC;AAGA8E,UAAAA,EAAE,GAAG;AAAEkI,YAAAA,MAAM,EAAElI,EAAV;AAAcqI,YAAAA,KAAK,EAAEG;AAArB,WAAL;AACA2L,UAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,UAAIlX,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnBzC,QAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAAE8E,UAAAA,EAAF;AAAME,UAAAA;AAAN,SAA/B;AACA;AACD;;AAED,UAAInC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,CAAJ,EAA2B;AACzB;AACD;;AAED,UAAI,CAACmU,OAAL,EAAc;AACZ,YAAM,GAAGxP,IAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAAhB;AACA,YAAM0X,QAAQ,GAAG7b,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB,EAAnB,CAAjB;;AAEA,YAAI,CAACsO,KAAK,CAAC3I,MAAN,CAAa8D,IAAb,EAAkB+S,QAAlB,CAAL,EAAkC;AAChC1X,UAAAA,EAAE,GAAGnE,MAAM,CAAC8R,WAAP,CAAmBzS,MAAnB,EAA2B8E,EAA3B,EAA+B;AAAEE,YAAAA;AAAF,WAA/B,CAAL;AACD;AACF;;AAED,UAAI,CAACyI,KAAD,EAAQhE,GAAR,IAAe5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAAnB;AACA,UAAM2X,UAAU,GAAG9b,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AACtCgD,QAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAD0B;AAEtCL,QAAAA,EAAE,EAAE2I,KAFkC;AAGtCzI,QAAAA;AAHsC,OAArB,CAAnB;AAKA,UAAM0N,QAAQ,GAAG/R,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AACpCgD,QAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CADwB;AAEpCL,QAAAA,EAAE,EAAE2E,GAFgC;AAGpCzE,QAAAA;AAHoC,OAArB,CAAjB;AAKA,UAAM0X,cAAc,GAClBD,UAAU,IAAI/J,QAAd,IAA0B,CAAC3Q,IAAI,CAAC4D,MAAL,CAAY8W,UAAU,CAAC,CAAD,CAAtB,EAA2B/J,QAAQ,CAAC,CAAD,CAAnC,CAD7B;AAEA,UAAMiK,YAAY,GAAG5a,IAAI,CAAC4D,MAAL,CAAY8H,KAAK,CAAC/L,IAAlB,EAAwB+H,GAAG,CAAC/H,IAA5B,CAArB;AACA,UAAMkb,SAAS,GAAG5X,KAAK,GACnB,IADmB,GAEnBrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,QAAAA,EAAE,EAAE2I,KAAN;AAAaX,QAAAA,IAAI,EAAE;AAAnB,OAApB,CAFJ;AAGA,UAAM+P,OAAO,GAAG7X,KAAK,GACjB,IADiB,GAEjBrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,QAAAA,EAAE,EAAE2E,GAAN;AAAWqD,QAAAA,IAAI,EAAE;AAAjB,OAApB,CAFJ;;AAKA,UAAI8P,SAAJ,EAAe;AACb,YAAMpP,MAAM,GAAG7M,MAAM,CAAC6M,MAAP,CAAcxN,MAAd,EAAsByN,KAAtB,CAAf;;AAEA,YACED,MAAM,IACNiP,UADA,IAEA1a,IAAI,CAAC+J,UAAL,CAAgB2Q,UAAU,CAAC,CAAD,CAA1B,EAA+BjP,MAAM,CAAC9L,IAAtC,CAHF,EAIE;AACA+L,UAAAA,KAAK,GAAGD,MAAR;AACD;AACF;;AAED,UAAIqP,OAAJ,EAAa;AACX,YAAM9P,KAAK,GAAGpM,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqByJ,GAArB,CAAd;;AAEA,YAAIsD,KAAK,IAAI2F,QAAT,IAAqB3Q,IAAI,CAAC+J,UAAL,CAAgB4G,QAAQ,CAAC,CAAD,CAAxB,EAA6B3F,KAAK,CAACrL,IAAnC,CAAzB,EAAmE;AACjE+H,UAAAA,GAAG,GAAGsD,KAAN;AACD;AACF;AAGD;;;AACA,UAAMN,OAAO,GAAgB,EAA7B;AACA,UAAIyE,QAAJ;;AAEA,WAAK,IAAMzM,KAAX,IAAoB9D,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AAAE8E,QAAAA,EAAF;AAAME,QAAAA;AAAN,OAArB,CAApB,EAAyD;AACvD,YAAM,CAACZ,IAAD,EAAO1C,IAAP,IAAe+C,KAArB;;AAEA,YAAIyM,QAAQ,IAAInP,IAAI,CAACkO,OAAL,CAAavO,IAAb,EAAmBwP,QAAnB,MAAiC,CAAjD,EAAoD;AAClD;AACD;;AAED,YACG,CAAClM,KAAD,IAAUrE,MAAM,CAACL,MAAP,CAAcN,MAAd,EAAsBoE,IAAtB,CAAX,IACC,CAACrC,IAAI,CAAC+T,QAAL,CAAcpU,IAAd,EAAoB+L,KAAK,CAAC/L,IAA1B,CAAD,IAAoC,CAACK,IAAI,CAAC+T,QAAL,CAAcpU,IAAd,EAAoB+H,GAAG,CAAC/H,IAAxB,CAFxC,EAGE;AACA+K,UAAAA,OAAO,CAAC3K,IAAR,CAAa2C,KAAb;AACAyM,UAAAA,QAAQ,GAAGxP,IAAX;AACD;AACF;;AAED,UAAMd,QAAQ,GAAGsF,KAAK,CAACC,IAAN,CAAWsG,OAAX,EAAoB;AAAA,YAAC,GAAGpG,CAAH,CAAD;AAAA,eAAW1F,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBqG,CAAvB,CAAX;AAAA,OAApB,CAAjB;AACA,UAAMyW,QAAQ,GAAGnc,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByN,KAAxB,CAAjB;AACA,UAAMsP,MAAM,GAAGpc,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByJ,GAAxB,CAAf;;AAEA,UAAI,CAACkT,YAAD,IAAiB,CAACC,SAAtB,EAAiC;AAC/B,YAAM3P,MAAK,GAAG6P,QAAQ,CAACtL,OAAvB;AACA,YAAM,CAACpN,KAAD,IAASzD,MAAM,CAACkO,IAAP,CAAY7O,MAAZ,EAAoBiN,MAApB,CAAf;AACA,YAAM;AAAEvL,UAAAA,IAAI,EAAJA;AAAF,YAAWuL,MAAjB;AACA,YAAM;AAAE0B,UAAAA;AAAF,YAAalB,KAAnB;;AACA,YAAMlJ,IAAI,GAAGH,KAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgBsF,MAAhB,CAAb;;AACA,YAAIpK,IAAI,CAACK,MAAL,GAAc,CAAlB,EACE5E,MAAM,CAACQ,KAAP,CAAa;AAAE+B,UAAAA,IAAI,EAAE,aAAR;AAAuBb,UAAAA,IAAI,EAAJA,KAAvB;AAA6BiN,UAAAA,MAA7B;AAAqCpK,UAAAA;AAArC,SAAb;AACH;;AAED,WAAK,IAAM+M,OAAX,IAAsB1Q,QAAtB,EAAgC;AAC9B,YAAMc,MAAI,GAAG4P,OAAO,CAACG,KAAR,EAAb;;AACApP,QAAAA,UAAU,CAACoD,WAAX,CAAuBzF,MAAvB,EAA+B;AAAE8E,UAAAA,EAAE,EAAEpD,MAAN;AAAYsD,UAAAA;AAAZ,SAA/B;AACD;;AAED,UAAI,CAAC6X,OAAL,EAAc;AACZ,YAAM5P,OAAK,GAAG8P,MAAM,CAACvL,OAArB;AACA,YAAM,CAACpN,MAAD,IAASzD,MAAM,CAACkO,IAAP,CAAY7O,MAAZ,EAAoBiN,OAApB,CAAf;AACA,YAAM;AAAEvL,UAAAA,IAAI,EAAJA;AAAF,YAAWuL,OAAjB;;AACA,YAAM0B,OAAM,GAAGgO,YAAY,GAAGlP,KAAK,CAACkB,MAAT,GAAkB,CAA7C;;AACA,YAAMpK,KAAI,GAAGH,MAAI,CAACG,IAAL,CAAU8E,KAAV,CAAgBsF,OAAhB,EAAwBlF,GAAG,CAACkF,MAA5B,CAAb;;AACA,YAAIpK,KAAI,CAACK,MAAL,GAAc,CAAlB,EACE5E,MAAM,CAACQ,KAAP,CAAa;AAAE+B,UAAAA,IAAI,EAAE,aAAR;AAAuBb,UAAAA,IAAI,EAAJA,MAAvB;AAA6BiN,UAAAA,MAAM,EAANA,OAA7B;AAAqCpK,UAAAA,IAAI,EAAJA;AAArC,SAAb;AACH;;AAED,UACE,CAACoY,YAAD,IACAD,cADA,IAEAK,MAAM,CAACvL,OAFP,IAGAsL,QAAQ,CAACtL,OAJX,EAKE;AACAnP,QAAAA,UAAU,CAACwD,UAAX,CAAsB7F,MAAtB,EAA8B;AAC5B8E,UAAAA,EAAE,EAAEiY,MAAM,CAACvL,OADiB;AAE5ByH,UAAAA,OAAO,EAAE,IAFmB;AAG5BjU,UAAAA;AAH4B,SAA9B;AAKD;;AAED,UAAMiI,KAAK,GAAGzJ,OAAO,GACjBsZ,QAAQ,CAACrL,KAAT,MAAoBsL,MAAM,CAACtL,KAAP,EADH,GAEjBsL,MAAM,CAACtL,KAAP,MAAkBqL,QAAQ,CAACrL,KAAT,EAFtB;;AAIA,UAAI5E,OAAO,CAAC/H,EAAR,IAAc,IAAd,IAAsBmI,KAA1B,EAAiC;AAC/B5K,QAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0BiN,KAA1B;AACD;AACF,KAnKD;AAoKD,GApL2C;;AAsL5C;;;AAIA/I,EAAAA,cAAc,CACZlE,MADY,EAEZ8D,QAFY;QAGZ+I,8EAII;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAEiZ,QAAAA,OAAO,GAAG,KAAZ;AAAmBjU,QAAAA,KAAK,GAAG;AAA3B,UAAqC6H,OAA3C;AACA,UAAI;AAAE/H,QAAAA,EAAE,GAAG9E,MAAM,CAACG;AAAd,UAA4B0M,OAAhC;;AAEA,UAAI,CAAC/I,QAAQ,CAACc,MAAd,EAAsB;AACpB;AACD;;AAED,UAAI,CAACE,EAAL,EAAS;AACP;AACD,OAFD,MAEO,IAAIjC,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AAC5B,YAAI,CAACmU,OAAL,EAAc;AACZnU,UAAAA,EAAE,GAAGnE,MAAM,CAAC8R,WAAP,CAAmBzS,MAAnB,EAA2B8E,EAA3B,CAAL;AACD;;AAED,YAAIjC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACkI,MAAR;AACD,SAFD,MAEO;AACL,cAAM,GAAGvD,GAAH,IAAU5G,KAAK,CAAC6K,KAAN,CAAY5I,EAAZ,CAAhB;;AAEA,cAAI,CAACE,KAAD,IAAUrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,YAAAA,EAAE,EAAE2E;AAAN,WAApB,CAAd,EAAgD;AAC9C;AACD;;AAED,cAAMkI,QAAQ,GAAGhR,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByJ,GAAxB,CAAjB;AACApH,UAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAE8E,YAAAA;AAAF,WAA1B;AACAA,UAAAA,EAAE,GAAG6M,QAAQ,CAACF,KAAT,EAAL;AACD;AACF,OAlBM,MAkBA,IAAI1P,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AAC1BA,QAAAA,EAAE,GAAGnE,MAAM,CAAC8M,KAAP,CAAazN,MAAb,EAAqB8E,EAArB,CAAL;AACD;;AAED,UAAI,CAACE,KAAD,IAAUrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,QAAAA;AAAF,OAApB,CAAd,EAA2C;AACzC;AACD;AAGD;;;AACA,UAAMkY,kBAAkB,GAAGrc,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AAC9C8E,QAAAA,EAD8C;AAE9C9B,QAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CAFkC;AAG9C2H,QAAAA,IAAI,EAAE,SAHwC;AAI9C9H,QAAAA;AAJ8C,OAArB,CAA3B;;AAOA,UAAIgY,kBAAJ,EAAwB;AACtB,YAAM,GAAGC,WAAH,IAAiBD,kBAAvB;;AAEA,YAAIrc,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqB8E,EAArB,EAAyBmY,WAAzB,CAAJ,EAA0C;AACxC,cAAMlQ,KAAK,GAAGpM,MAAM,CAACoM,KAAP,CAAa/M,MAAb,EAAqBid,WAArB,CAAd;AACAnY,UAAAA,EAAE,GAAGiI,KAAL;AACD,SAHD,MAGO,IAAIpM,MAAM,CAAC6N,OAAP,CAAexO,MAAf,EAAuB8E,EAAvB,EAA2BmY,WAA3B,CAAJ,EAA4C;AACjD,cAAMzP,MAAM,GAAG7M,MAAM,CAAC6M,MAAP,CAAcxN,MAAd,EAAsBid,WAAtB,CAAf;AACAnY,UAAAA,EAAE,GAAG0I,MAAL;AACD;AACF;;AAED,UAAM0P,UAAU,GAAGvc,MAAM,CAACiM,KAAP,CAAa5M,MAAb,EAAqB;AACtCgD,QAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAD0B;AAEtCL,QAAAA,EAFsC;AAGtCE,QAAAA;AAHsC,OAArB,CAAnB;AAKA,UAAM,GAAGkK,SAAH,IAAgBgO,UAAtB;AACA,UAAMC,YAAY,GAAGxc,MAAM,CAAC6N,OAAP,CAAexO,MAAf,EAAuB8E,EAAvB,EAA2BoK,SAA3B,CAArB;AACA,UAAMkO,UAAU,GAAGzc,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqB8E,EAArB,EAAyBoK,SAAzB,CAAnB;AACA,UAAMmO,YAAY,GAAGF,YAAY,IAAIC,UAArC;AACA,UAAME,UAAU,GAAG,CAACH,YAAD,IAAkBA,YAAY,IAAIC,UAArD;AACA,UAAMG,QAAQ,GAAG,CAACH,UAAlB;AACA,UAAM,GAAGnM,SAAH,IAAgBpN,IAAI,CAAC8J,KAAL,CAAW;AAAE1N,QAAAA,QAAQ,EAAE6D;AAAZ,OAAX,EAAmC,EAAnC,CAAtB;AACA,UAAM,GAAGoN,QAAH,IAAerN,IAAI,CAAC+K,IAAL,CAAU;AAAE3O,QAAAA,QAAQ,EAAE6D;AAAZ,OAAV,EAAkC,EAAlC,CAArB;AAEA,UAAM2I,OAAO,GAAgB,EAA7B;;AACA,UAAM+Q,OAAO,GAAG;YAAC,CAACrY,CAAD,EAAIkB,CAAJ;AACf,YAAMoX,MAAM,GAAGpX,CAAC,CAACzB,MAAF,KAAa,CAA5B;;AACA,YAAI6Y,MAAJ,EAAY;AACV,iBAAO,KAAP;AACD;;AAED,YAAIJ,YAAJ,EAAkB;AAChB,iBAAO,IAAP;AACD;;AAED,YACEC,UAAU,IACVvb,IAAI,CAAC+J,UAAL,CAAgBzF,CAAhB,EAAmB4K,SAAnB,CADA,IAEAvM,OAAO,CAACC,SAAR,CAAkBQ,CAAlB,CAFA,IAGA,CAACnF,MAAM,CAACM,MAAP,CAAc6E,CAAd,CAHD,IAIA,CAACnF,MAAM,CAACK,QAAP,CAAgB8E,CAAhB,CALH,EAME;AACA,iBAAO,KAAP;AACD;;AAED,YACEoY,QAAQ,IACRxb,IAAI,CAAC+J,UAAL,CAAgBzF,CAAhB,EAAmB6K,QAAnB,CADA,IAEAxM,OAAO,CAACC,SAAR,CAAkBQ,CAAlB,CAFA,IAGA,CAACnF,MAAM,CAACM,MAAP,CAAc6E,CAAd,CAHD,IAIA,CAACnF,MAAM,CAACK,QAAP,CAAgB8E,CAAhB,CALH,EAME;AACA,iBAAO,KAAP;AACD;;AAED,eAAO,IAAP;AACD,OA/BD;;AAiCA,WAAK,IAAMV,KAAX,IAAoBZ,IAAI,CAACuC,KAAL,CAClB;AAAEnG,QAAAA,QAAQ,EAAE6D;AAAZ,OADkB,EAElB;AAAEgM,QAAAA,IAAI,EAAE0N;AAAR,OAFkB,CAApB,EAGG;AACD,YAAIA,OAAO,CAAC/Y,KAAD,CAAX,EAAoB;AAClBgI,UAAAA,OAAO,CAAC3K,IAAR,CAAa2C,KAAb;AACD;AACF;;AAED,UAAMiZ,MAAM,GAAG,EAAf;AACA,UAAMC,OAAO,GAAG,EAAhB;AACA,UAAMC,IAAI,GAAG,EAAb;AACA,UAAIC,QAAQ,GAAG,IAAf;AACA,UAAIjQ,SAAS,GAAG,KAAhB;;AAEA,WAAK,IAAM,CAACxJ,IAAD,CAAX,IAAqBqI,OAArB,EAA8B;AAC5B,YAAI/H,OAAO,CAACC,SAAR,CAAkBP,IAAlB,KAA2B,CAACpE,MAAM,CAACK,QAAP,CAAgB+D,IAAhB,CAAhC,EAAuD;AACrDyZ,UAAAA,QAAQ,GAAG,KAAX;AACAjQ,UAAAA,SAAS,GAAG,IAAZ;AACA+P,UAAAA,OAAO,CAAC7b,IAAR,CAAasC,IAAb;AACD,SAJD,MAIO,IAAIyZ,QAAJ,EAAc;AACnBH,UAAAA,MAAM,CAAC5b,IAAP,CAAYsC,IAAZ;AACD,SAFM,MAEA;AACLwZ,UAAAA,IAAI,CAAC9b,IAAL,CAAUsC,IAAV;AACD;AACF;;AAED,UAAM,CAAC0Z,WAAD,IAAgBnd,MAAM,CAACyF,KAAP,CAAapG,MAAb,EAAqB;AACzC8E,QAAAA,EADyC;AAEzC9B,QAAAA,KAAK,EAAEmC,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CAFW;AAGzC2H,QAAAA,IAAI,EAAE,SAHmC;AAIzC9H,QAAAA;AAJyC,OAArB,CAAtB;AAOA,UAAM,GAAGiY,UAAH,IAAiBa,WAAvB;AACA,UAAMC,aAAa,GAAGpd,MAAM,CAAC6N,OAAP,CAAexO,MAAf,EAAuB8E,EAAvB,EAA2BmY,UAA3B,CAAtB;AACA,UAAMe,WAAW,GAAGrd,MAAM,CAAC0N,KAAP,CAAarO,MAAb,EAAqB8E,EAArB,EAAyBmY,UAAzB,CAApB;AAEA,UAAMgB,SAAS,GAAGtd,MAAM,CAAC2Q,OAAP,CAChBtR,MADgB,EAEhBod,UAAU,GAAGrb,IAAI,CAACiF,IAAL,CAAUkI,SAAV,CAAH,GAA0BA,SAFpB,CAAlB;AAKA,UAAM6N,MAAM,GAAGpc,MAAM,CAAC2Q,OAAP,CACbtR,MADa,EAEbge,WAAW,GAAGjc,IAAI,CAACiF,IAAL,CAAUiW,UAAV,CAAH,GAA2BA,UAFzB,CAAf;AAKA,UAAMiB,YAAY,GAAGvd,MAAM,CAAC2Q,OAAP,CAAetR,MAAf,EAAuBkP,SAAvB,CAArB;AAEA7M,MAAAA,UAAU,CAAC2B,UAAX,CAAsBhE,MAAtB,EAA8B;AAC5B8E,QAAAA,EAD4B;AAE5B9B,QAAAA,KAAK,EAAEmC,CAAC,IACNyI,SAAS,GACLjN,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CADK,GAELlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CALI;AAM5B2H,QAAAA,IAAI,EAAEc,SAAS,GAAG,QAAH,GAAc,SAND;AAO5B5I,QAAAA;AAP4B,OAA9B;AAUA,UAAM8X,QAAQ,GAAGnc,MAAM,CAAC2Q,OAAP,CACftR,MADe,EAEf,CAAC+d,aAAD,IAAmBA,aAAa,IAAIC,WAApC,GACIjc,IAAI,CAACiF,IAAL,CAAUiW,UAAV,CADJ,GAEIA,UAJW,CAAjB;AAOA5a,MAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B0d,MAA/B,EAAuC;AACrC5Y,QAAAA,EAAE,EAAEgY,QAAQ,CAACtL,OADwB;AAErCxO,QAAAA,KAAK,EAAEmC,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CAFO;AAGrC2H,QAAAA,IAAI,EAAE,SAH+B;AAIrC9H,QAAAA;AAJqC,OAAvC;;AAOA,UAAIqY,YAAY,IAAIM,OAAO,CAAC/Y,MAA5B,EAAoC;AAClCvC,QAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAE8E,UAAAA,EAAE,EAAEoZ,YAAY,CAACzM,KAAb,EAAN;AAA6BzM,UAAAA;AAA7B,SAA1B;AACD;;AAED3C,MAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B2d,OAA/B,EAAwC;AACtC7Y,QAAAA,EAAE,EAAEmZ,SAAS,CAACzM,OADwB;AAEtCxO,QAAAA,KAAK,EAAEmC,CAAC,IAAIxE,MAAM,CAACmN,OAAP,CAAe9N,MAAf,EAAuBmF,CAAvB,CAF0B;AAGtC2H,QAAAA,IAAI,EAAE,QAHgC;AAItC9H,QAAAA;AAJsC,OAAxC;AAOA3C,MAAAA,UAAU,CAACgC,WAAX,CAAuBrE,MAAvB,EAA+B4d,IAA/B,EAAqC;AACnC9Y,QAAAA,EAAE,EAAEiY,MAAM,CAACvL,OADwB;AAEnCxO,QAAAA,KAAK,EAAEmC,CAAC,IAAIlC,IAAI,CAACC,MAAL,CAAYiC,CAAZ,KAAkBxE,MAAM,CAACN,QAAP,CAAgBL,MAAhB,EAAwBmF,CAAxB,CAFK;AAGnC2H,QAAAA,IAAI,EAAE,SAH6B;AAInC9H,QAAAA;AAJmC,OAArC;;AAOA,UAAI,CAAC6H,OAAO,CAAC/H,EAAb,EAAiB;AACf,YAAIpD,IAAJ;;AAEA,YAAIkc,IAAI,CAAChZ,MAAL,GAAc,CAAlB,EAAqB;AACnBlD,UAAAA,IAAI,GAAGK,IAAI,CAACyE,QAAL,CAAcuW,MAAM,CAACvL,OAArB,CAAP;AACD,SAFD,MAEO,IAAImM,OAAO,CAAC/Y,MAAR,GAAiB,CAArB,EAAwB;AAC7BlD,UAAAA,IAAI,GAAGK,IAAI,CAACyE,QAAL,CAAcyX,SAAS,CAACzM,OAAxB,CAAP;AACD,SAFM,MAEA;AACL9P,UAAAA,IAAI,GAAGK,IAAI,CAACyE,QAAL,CAAcsW,QAAQ,CAACtL,OAAvB,CAAP;AACD;;AAED,YAAM/H,KAAG,GAAG9I,MAAM,CAAC8I,GAAP,CAAWzJ,MAAX,EAAmB0B,IAAnB,CAAZ;;AACAW,QAAAA,UAAU,CAAC6W,MAAX,CAAkBlZ,MAAlB,EAA0ByJ,KAA1B;AACD;;AAEDqT,MAAAA,QAAQ,CAACrL,KAAT;AACAwM,MAAAA,SAAS,CAACxM,KAAV;AACAsL,MAAAA,MAAM,CAACtL,KAAP;AACD,KAvND;AAwND,GA3Z2C;;AA6Z5C;;;AAIAnN,EAAAA,UAAU,CACRtE,MADQ,EAERuE,IAFQ;QAGRsI,8EAGI;AAEJlM,IAAAA,MAAM,CAAC+P,kBAAP,CAA0B1Q,MAA1B,EAAkC;AAChC,UAAM;AAAEgF,QAAAA,KAAK,GAAG;AAAV,UAAoB6H,OAA1B;AACA,UAAI;AAAE/H,QAAAA,EAAE,GAAG9E,MAAM,CAACG;AAAd,UAA4B0M,OAAhC;;AAEA,UAAI,CAAC/H,EAAL,EAAS;AACP;AACD;;AAED,UAAI/C,IAAI,CAACuN,MAAL,CAAYxK,EAAZ,CAAJ,EAAqB;AACnBA,QAAAA,EAAE,GAAGnE,MAAM,CAACyM,KAAP,CAAapN,MAAb,EAAqB8E,EAArB,CAAL;AACD;;AAED,UAAIjC,KAAK,CAACqL,OAAN,CAAcpJ,EAAd,CAAJ,EAAuB;AACrB,YAAIjC,KAAK,CAACS,WAAN,CAAkBwB,EAAlB,CAAJ,EAA2B;AACzBA,UAAAA,EAAE,GAAGA,EAAE,CAACkI,MAAR;AACD,SAFD,MAEO;AACL,cAAMvD,GAAG,GAAG5G,KAAK,CAAC4G,GAAN,CAAU3E,EAAV,CAAZ;;AACA,cAAI,CAACE,KAAD,IAAUrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,YAAAA,EAAE,EAAE2E;AAAN,WAApB,CAAd,EAAgD;AAC9C;AACD;;AACD,cAAMgE,KAAK,GAAG5K,KAAK,CAAC4K,KAAN,CAAY3I,EAAZ,CAAd;AACA,cAAM6M,QAAQ,GAAGhR,MAAM,CAACgR,QAAP,CAAgB3R,MAAhB,EAAwByN,KAAxB,CAAjB;AACApL,UAAAA,UAAU,CAACkB,MAAX,CAAkBvD,MAAlB,EAA0B;AAAE8E,YAAAA,EAAF;AAAME,YAAAA;AAAN,WAA1B;AACAF,UAAAA,EAAE,GAAG6M,QAAQ,CAACF,KAAT,EAAL;AACApP,UAAAA,UAAU,CAAC4Z,YAAX,CAAwBjc,MAAxB,EAAgC;AAAEgN,YAAAA,MAAM,EAAElI,EAAV;AAAcqI,YAAAA,KAAK,EAAErI;AAArB,WAAhC;AACD;AACF;;AAED,UAAI,CAACE,KAAD,IAAUrE,MAAM,CAACkS,IAAP,CAAY7S,MAAZ,EAAoB;AAAE8E,QAAAA;AAAF,OAApB,CAAd,EAA2C;AACzC;AACD;;AAED,UAAM;AAAEpD,QAAAA,IAAF;AAAQiN,QAAAA;AAAR,UAAmB7J,EAAzB;AACA,UAAIP,IAAI,CAACK,MAAL,GAAc,CAAlB,EACE5E,MAAM,CAACQ,KAAP,CAAa;AAAE+B,QAAAA,IAAI,EAAE,aAAR;AAAuBb,QAAAA,IAAvB;AAA6BiN,QAAAA,MAA7B;AAAqCpK,QAAAA;AAArC,OAAb;AACH,KAnCD;AAoCD;;AA7c2C,CAAvC;;;;;ICvCMlC,UAAU,+DAIlBuW,iBAJkB,GAKlBI,cALkB,GAMlB4C,mBANkB,GAOlBU,cAPkB;;;;"}