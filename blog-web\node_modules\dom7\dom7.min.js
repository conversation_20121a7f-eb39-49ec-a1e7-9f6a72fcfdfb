/**
 * Dom7 3.0.0
 * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API
 * https://framework7.io/docs/dom7.html
 *
 * Copyright 2020, <PERSON>
 *
 * Licensed under MIT
 *
 * Released on: November 9, 2020
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Dom7=e()}(this,(function(){"use strict";function t(t){return null!==t&&"object"==typeof t&&"constructor"in t&&t.constructor===Object}function e(n,r){void 0===n&&(n={}),void 0===r&&(r={}),Object.keys(r).forEach((function(i){void 0===n[i]?n[i]=r[i]:t(r[i])&&t(n[i])&&Object.keys(r[i]).length>0&&e(n[i],r[i])}))}var n={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function r(){var t="undefined"!=typeof document?document:{};return e(t,n),t}var i={document:n,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(t){return"undefined"==typeof setTimeout?(t(),null):setTimeout(t,0)},cancelAnimationFrame:function(t){"undefined"!=typeof setTimeout&&clearTimeout(t)}};function o(){var t="undefined"!=typeof window?window:{};return e(t,i),t}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function a(t,e){return(a=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function l(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function u(t,e,n){return(u=l()?Reflect.construct:function(t,e,n){var r=[null];r.push.apply(r,e);var i=new(Function.bind.apply(t,r));return n&&a(i,n.prototype),i}).apply(null,arguments)}function f(t){var e="function"==typeof Map?new Map:void 0;return(f=function(t){if(null===t||(n=t,-1===Function.toString.call(n).indexOf("[native code]")))return t;var n;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return u(t,arguments,s(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),a(r,t)})(t)}var h=function(t){var e,n;function r(e){var n,r,i;return n=t.call.apply(t,[this].concat(e))||this,r=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(n),i=r.__proto__,Object.defineProperty(r,"__proto__",{get:function(){return i},set:function(t){i.__proto__=t}}),n}return n=t,(e=r).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n,r}(f(Array));function c(t){void 0===t&&(t=[]);var e=[];return t.forEach((function(t){Array.isArray(t)?e.push.apply(e,c(t)):e.push(t)})),e}function p(t,e){return Array.prototype.filter.call(t,e)}function d(t,e){var n=o(),i=r(),s=[];if(!e&&t instanceof h)return t;if(!t)return new h(s);if("string"==typeof t){var a=t.trim();if(a.indexOf("<")>=0&&a.indexOf(">")>=0){var l="div";0===a.indexOf("<li")&&(l="ul"),0===a.indexOf("<tr")&&(l="tbody"),0!==a.indexOf("<td")&&0!==a.indexOf("<th")||(l="tr"),0===a.indexOf("<tbody")&&(l="table"),0===a.indexOf("<option")&&(l="select");var u=i.createElement(l);u.innerHTML=a;for(var f=0;f<u.childNodes.length;f+=1)s.push(u.childNodes[f])}else s=function(t,e){if("string"!=typeof t)return[t];for(var n=[],r=e.querySelectorAll(t),i=0;i<r.length;i+=1)n.push(r[i]);return n}(t.trim(),e||i)}else if(t.nodeType||t===n||t===i)s.push(t);else if(Array.isArray(t)){if(t instanceof h)return t;s=t}return new h(function(t){for(var e=[],n=0;n<t.length;n+=1)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(s))}d.fn=h.prototype;var v=Object.freeze({__proto__:null,addClass:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=c(e.map((function(t){return t.split(" ")})));return this.forEach((function(t){var e;(e=t.classList).add.apply(e,r)})),this},removeClass:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=c(e.map((function(t){return t.split(" ")})));return this.forEach((function(t){var e;(e=t.classList).remove.apply(e,r)})),this},toggleClass:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=c(e.map((function(t){return t.split(" ")})));this.forEach((function(t){r.forEach((function(e){t.classList.toggle(e)}))}))},hasClass:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=c(e.map((function(t){return t.split(" ")})));return p(this,(function(t){return r.filter((function(e){return t.classList.contains(e)})).length>0})).length>0},attr:function(t,e){if(1===arguments.length&&"string"==typeof t)return this[0]?this[0].getAttribute(t):void 0;for(var n=0;n<this.length;n+=1)if(2===arguments.length)this[n].setAttribute(t,e);else for(var r in t)this[n][r]=t[r],this[n].setAttribute(r,t[r]);return this},removeAttr:function(t){for(var e=0;e<this.length;e+=1)this[e].removeAttribute(t);return this},prop:function(t,e){if(1!==arguments.length||"string"!=typeof t){for(var n=0;n<this.length;n+=1)if(2===arguments.length)this[n][t]=e;else for(var r in t)this[n][r]=t[r];return this}return this[0]?this[0][t]:this},data:function(t,e){var n;if(void 0===e){if(!(n=this[0]))return;if(n.dom7ElementDataStorage&&t in n.dom7ElementDataStorage)return n.dom7ElementDataStorage[t];var r=n.getAttribute("data-"+t);return r||void 0}for(var i=0;i<this.length;i+=1)(n=this[i]).dom7ElementDataStorage||(n.dom7ElementDataStorage={}),n.dom7ElementDataStorage[t]=e;return this},removeData:function(t){for(var e=0;e<this.length;e+=1){var n=this[e];n.dom7ElementDataStorage&&n.dom7ElementDataStorage[t]&&(n.dom7ElementDataStorage[t]=null,delete n.dom7ElementDataStorage[t])}},dataset:function(){var t=this[0];if(t){var e,n={};if(t.dataset)for(var r in t.dataset)n[r]=t.dataset[r];else for(var i=0;i<t.attributes.length;i+=1){var o=t.attributes[i];o.name.indexOf("data-")>=0&&(n[(e=o.name.split("data-")[1],e.toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})))]=o.value)}for(var s in n)"false"===n[s]?n[s]=!1:"true"===n[s]?n[s]=!0:parseFloat(n[s])===1*n[s]&&(n[s]*=1);return n}},val:function(t){if(void 0===t){var e=this[0];if(!e)return;if(e.multiple&&"select"===e.nodeName.toLowerCase()){for(var n=[],r=0;r<e.selectedOptions.length;r+=1)n.push(e.selectedOptions[r].value);return n}return e.value}for(var i=0;i<this.length;i+=1){var o=this[i];if(Array.isArray(t)&&o.multiple&&"select"===o.nodeName.toLowerCase())for(var s=0;s<o.options.length;s+=1)o.options[s].selected=t.indexOf(o.options[s].value)>=0;else o.value=t}return this},value:function(t){return this.val(t)},transform:function(t){for(var e=0;e<this.length;e+=1)this[e].style.transform=t;return this},transition:function(t){for(var e=0;e<this.length;e+=1)this[e].style.transitionDuration="string"!=typeof t?t+"ms":t;return this},on:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],i=e[1],o=e[2],s=e[3];function a(t){var e=t.target;if(e){var n=t.target.dom7EventData||[];if(n.indexOf(t)<0&&n.unshift(t),d(e).is(i))o.apply(e,n);else for(var r=d(e).parents(),s=0;s<r.length;s+=1)d(r[s]).is(i)&&o.apply(r[s],n)}}function l(t){var e=t&&t.target&&t.target.dom7EventData||[];e.indexOf(t)<0&&e.unshift(t),o.apply(this,e)}"function"==typeof e[1]&&(r=e[0],o=e[1],s=e[2],i=void 0),s||(s=!1);for(var u,f=r.split(" "),h=0;h<this.length;h+=1){var c=this[h];if(i)for(u=0;u<f.length;u+=1){var p=f[u];c.dom7LiveListeners||(c.dom7LiveListeners={}),c.dom7LiveListeners[p]||(c.dom7LiveListeners[p]=[]),c.dom7LiveListeners[p].push({listener:o,proxyListener:a}),c.addEventListener(p,a,s)}else for(u=0;u<f.length;u+=1){var v=f[u];c.dom7Listeners||(c.dom7Listeners={}),c.dom7Listeners[v]||(c.dom7Listeners[v]=[]),c.dom7Listeners[v].push({listener:o,proxyListener:l}),c.addEventListener(v,l,s)}}return this},off:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],i=e[1],o=e[2],s=e[3];"function"==typeof e[1]&&(r=e[0],o=e[1],s=e[2],i=void 0),s||(s=!1);for(var a=r.split(" "),l=0;l<a.length;l+=1)for(var u=a[l],f=0;f<this.length;f+=1){var h=this[f],c=void 0;if(!i&&h.dom7Listeners?c=h.dom7Listeners[u]:i&&h.dom7LiveListeners&&(c=h.dom7LiveListeners[u]),c&&c.length)for(var p=c.length-1;p>=0;p-=1){var d=c[p];o&&d.listener===o||o&&d.listener&&d.listener.dom7proxy&&d.listener.dom7proxy===o?(h.removeEventListener(u,d.proxyListener,s),c.splice(p,1)):o||(h.removeEventListener(u,d.proxyListener,s),c.splice(p,1))}}return this},once:function(){for(var t=this,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=n[0],o=n[1],s=n[2],a=n[3];function l(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];s.apply(this,n),t.off(i,o,l,a),l.dom7proxy&&delete l.dom7proxy}return"function"==typeof n[1]&&(i=n[0],s=n[1],a=n[2],o=void 0),l.dom7proxy=s,t.on(i,o,l,a)},trigger:function(){for(var t=o(),e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];for(var i=n[0].split(" "),s=n[1],a=0;a<i.length;a+=1)for(var l=i[a],u=0;u<this.length;u+=1){var f=this[u];if(t.CustomEvent){var h=new t.CustomEvent(l,{detail:s,bubbles:!0,cancelable:!0});f.dom7EventData=n.filter((function(t,e){return e>0})),f.dispatchEvent(h),f.dom7EventData=[],delete f.dom7EventData}}return this},transitionEnd:function(t){var e=this;return t&&e.on("transitionend",(function n(r){r.target===this&&(t.call(this,r),e.off("transitionend",n))})),this},animationEnd:function(t){var e=this;return t&&e.on("animationend",(function n(r){r.target===this&&(t.call(this,r),e.off("animationend",n))})),this},width:function(){var t=o();return this[0]===t?t.innerWidth:this.length>0?parseFloat(this.css("width")):null},outerWidth:function(t){if(this.length>0){if(t){var e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},height:function(){var t=o();return this[0]===t?t.innerHeight:this.length>0?parseFloat(this.css("height")):null},outerHeight:function(t){if(this.length>0){if(t){var e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},offset:function(){if(this.length>0){var t=o(),e=r(),n=this[0],i=n.getBoundingClientRect(),s=e.body,a=n.clientTop||s.clientTop||0,l=n.clientLeft||s.clientLeft||0,u=n===t?t.scrollY:n.scrollTop,f=n===t?t.scrollX:n.scrollLeft;return{top:i.top+u-a,left:i.left+f-l}}return null},hide:function(){for(var t=0;t<this.length;t+=1)this[t].style.display="none";return this},show:function(){for(var t=o(),e=0;e<this.length;e+=1){var n=this[e];"none"===n.style.display&&(n.style.display=""),"none"===t.getComputedStyle(n,null).getPropertyValue("display")&&(n.style.display="block")}return this},styles:function(){var t=o();return this[0]?t.getComputedStyle(this[0],null):{}},css:function(t,e){var n,r=o();if(1===arguments.length){if("string"!=typeof t){for(n=0;n<this.length;n+=1)for(var i in t)this[n].style[i]=t[i];return this}if(this[0])return r.getComputedStyle(this[0],null).getPropertyValue(t)}if(2===arguments.length&&"string"==typeof t){for(n=0;n<this.length;n+=1)this[n].style[t]=e;return this}return this},each:function(t){return t?(this.forEach((function(e,n){t.apply(e,[e,n])})),this):this},filter:function(t){return d(p(this,t))},html:function(t){if(void 0===t)return this[0]?this[0].innerHTML:null;for(var e=0;e<this.length;e+=1)this[e].innerHTML=t;return this},text:function(t){if(void 0===t)return this[0]?this[0].textContent.trim():null;for(var e=0;e<this.length;e+=1)this[e].textContent=t;return this},is:function(t){var e,n,i=o(),s=r(),a=this[0];if(!a||void 0===t)return!1;if("string"==typeof t){if(a.matches)return a.matches(t);if(a.webkitMatchesSelector)return a.webkitMatchesSelector(t);if(a.msMatchesSelector)return a.msMatchesSelector(t);for(e=d(t),n=0;n<e.length;n+=1)if(e[n]===a)return!0;return!1}if(t===s)return a===s;if(t===i)return a===i;if(t.nodeType||t instanceof h){for(e=t.nodeType?[t]:t,n=0;n<e.length;n+=1)if(e[n]===a)return!0;return!1}return!1},index:function(){var t,e=this[0];if(e){for(t=0;null!==(e=e.previousSibling);)1===e.nodeType&&(t+=1);return t}},eq:function(t){if(void 0===t)return this;var e=this.length;if(t>e-1)return d([]);if(t<0){var n=e+t;return d(n<0?[]:[this[n]])}return d([this[t]])},append:function(){for(var t,e=r(),n=0;n<arguments.length;n+=1){t=n<0||arguments.length<=n?void 0:arguments[n];for(var i=0;i<this.length;i+=1)if("string"==typeof t){var o=e.createElement("div");for(o.innerHTML=t;o.firstChild;)this[i].appendChild(o.firstChild)}else if(t instanceof h)for(var s=0;s<t.length;s+=1)this[i].appendChild(t[s]);else this[i].appendChild(t)}return this},appendTo:function(t){return d(t).append(this),this},prepend:function(t){var e,n,i=r();for(e=0;e<this.length;e+=1)if("string"==typeof t){var o=i.createElement("div");for(o.innerHTML=t,n=o.childNodes.length-1;n>=0;n-=1)this[e].insertBefore(o.childNodes[n],this[e].childNodes[0])}else if(t instanceof h)for(n=0;n<t.length;n+=1)this[e].insertBefore(t[n],this[e].childNodes[0]);else this[e].insertBefore(t,this[e].childNodes[0]);return this},prependTo:function(t){return d(t).prepend(this),this},insertBefore:function(t){for(var e=d(t),n=0;n<this.length;n+=1)if(1===e.length)e[0].parentNode.insertBefore(this[n],e[0]);else if(e.length>1)for(var r=0;r<e.length;r+=1)e[r].parentNode.insertBefore(this[n].cloneNode(!0),e[r])},insertAfter:function(t){for(var e=d(t),n=0;n<this.length;n+=1)if(1===e.length)e[0].parentNode.insertBefore(this[n],e[0].nextSibling);else if(e.length>1)for(var r=0;r<e.length;r+=1)e[r].parentNode.insertBefore(this[n].cloneNode(!0),e[r].nextSibling)},next:function(t){return this.length>0?t?this[0].nextElementSibling&&d(this[0].nextElementSibling).is(t)?d([this[0].nextElementSibling]):d([]):this[0].nextElementSibling?d([this[0].nextElementSibling]):d([]):d([])},nextAll:function(t){var e=[],n=this[0];if(!n)return d([]);for(;n.nextElementSibling;){var r=n.nextElementSibling;t?d(r).is(t)&&e.push(r):e.push(r),n=r}return d(e)},prev:function(t){if(this.length>0){var e=this[0];return t?e.previousElementSibling&&d(e.previousElementSibling).is(t)?d([e.previousElementSibling]):d([]):e.previousElementSibling?d([e.previousElementSibling]):d([])}return d([])},prevAll:function(t){var e=[],n=this[0];if(!n)return d([]);for(;n.previousElementSibling;){var r=n.previousElementSibling;t?d(r).is(t)&&e.push(r):e.push(r),n=r}return d(e)},siblings:function(t){return this.nextAll(t).add(this.prevAll(t))},parent:function(t){for(var e=[],n=0;n<this.length;n+=1)null!==this[n].parentNode&&(t?d(this[n].parentNode).is(t)&&e.push(this[n].parentNode):e.push(this[n].parentNode));return d(e)},parents:function(t){for(var e=[],n=0;n<this.length;n+=1)for(var r=this[n].parentNode;r;)t?d(r).is(t)&&e.push(r):e.push(r),r=r.parentNode;return d(e)},closest:function(t){var e=this;return void 0===t?d([]):(e.is(t)||(e=e.parents(t).eq(0)),e)},find:function(t){for(var e=[],n=0;n<this.length;n+=1)for(var r=this[n].querySelectorAll(t),i=0;i<r.length;i+=1)e.push(r[i]);return d(e)},children:function(t){for(var e=[],n=0;n<this.length;n+=1)for(var r=this[n].children,i=0;i<r.length;i+=1)t&&!d(r[i]).is(t)||e.push(r[i]);return d(e)},remove:function(){for(var t=0;t<this.length;t+=1)this[t].parentNode&&this[t].parentNode.removeChild(this[t]);return this},detach:function(){return this.remove()},add:function(){for(var t,e,n=this,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];for(t=0;t<i.length;t+=1){var s=d(i[t]);for(e=0;e<s.length;e+=1)n.push(s[e])}return n},empty:function(){for(var t=0;t<this.length;t+=1){var e=this[t];if(1===e.nodeType){for(var n=0;n<e.childNodes.length;n+=1)e.childNodes[n].parentNode&&e.childNodes[n].parentNode.removeChild(e.childNodes[n]);e.textContent=""}}return this}});var m=Object.freeze({__proto__:null,scrollTo:function(){for(var t=o(),e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=n[0],s=n[1],a=n[2],l=n[3],u=n[4];return 4===n.length&&"function"==typeof l&&(u=l,i=n[0],s=n[1],a=n[2],u=n[3],l=n[4]),void 0===l&&(l="swing"),this.each((function(){var e,n,r,o,f,h,c,p,d=this,v=s>0||0===s,m=i>0||0===i;if(void 0===l&&(l="swing"),v&&(e=d.scrollTop,a||(d.scrollTop=s)),m&&(n=d.scrollLeft,a||(d.scrollLeft=i)),a){v&&(r=d.scrollHeight-d.offsetHeight,f=Math.max(Math.min(s,r),0)),m&&(o=d.scrollWidth-d.offsetWidth,h=Math.max(Math.min(i,o),0));var g=null;v&&f===e&&(v=!1),m&&h===n&&(m=!1),t.requestAnimationFrame((function r(i){void 0===i&&(i=(new Date).getTime()),null===g&&(g=i);var o,s=Math.max(Math.min((i-g)/a,1),0),y="linear"===l?s:.5-Math.cos(s*Math.PI)/2;v&&(c=e+y*(f-e)),m&&(p=n+y*(h-n)),v&&f>e&&c>=f&&(d.scrollTop=f,o=!0),v&&f<e&&c<=f&&(d.scrollTop=f,o=!0),m&&h>n&&p>=h&&(d.scrollLeft=h,o=!0),m&&h<n&&p<=h&&(d.scrollLeft=h,o=!0),o?u&&u():(v&&(d.scrollTop=c),m&&(d.scrollLeft=p),t.requestAnimationFrame(r))}))}}))},scrollTop:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],i=e[1],o=e[2],s=e[3];3===e.length&&"function"==typeof o&&(r=e[0],i=e[1],s=e[2],o=e[3]);var a=this;return void 0===r?a.length>0?a[0].scrollTop:null:a.scrollTo(void 0,r,i,o,s)},scrollLeft:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0],i=e[1],o=e[2],s=e[3];3===e.length&&"function"==typeof o&&(r=e[0],i=e[1],s=e[2],o=e[3]);var a=this;return void 0===r?a.length>0?a[0].scrollLeft:null:a.scrollTo(r,void 0,i,o,s)}});var g=Object.freeze({__proto__:null,animate:function(t,e){var n,r=o(),i=this,s={props:Object.assign({},t),params:Object.assign({duration:300,easing:"swing"},e),elements:i,animating:!1,que:[],easingProgress:function(t,e){return"swing"===t?.5-Math.cos(e*Math.PI)/2:"function"==typeof t?t(e):e},stop:function(){s.frameId&&r.cancelAnimationFrame(s.frameId),s.animating=!1,s.elements.each((function(t){delete t.dom7AnimateInstance})),s.que=[]},done:function(t){if(s.animating=!1,s.elements.each((function(t){delete t.dom7AnimateInstance})),t&&t(i),s.que.length>0){var e=s.que.shift();s.animate(e[0],e[1])}},animate:function(t,e){if(s.animating)return s.que.push([t,e]),s;var n=[];s.elements.each((function(e,i){var o,a,l,u,f;e.dom7AnimateInstance||(s.elements[i].dom7AnimateInstance=s),n[i]={container:e},Object.keys(t).forEach((function(s){o=r.getComputedStyle(e,null).getPropertyValue(s).replace(",","."),a=parseFloat(o),l=o.replace(a,""),u=parseFloat(t[s]),f=t[s]+l,n[i][s]={initialFullValue:o,initialValue:a,unit:l,finalValue:u,finalFullValue:f,currentValue:a}}))}));var o,a,l=null,u=0,f=0,h=!1;return s.animating=!0,s.frameId=r.requestAnimationFrame((function c(){var p,d;o=(new Date).getTime(),h||(h=!0,e.begin&&e.begin(i)),null===l&&(l=o),e.progress&&e.progress(i,Math.max(Math.min((o-l)/e.duration,1),0),l+e.duration-o<0?0:l+e.duration-o,l),n.forEach((function(r){var i=r;a||i.done||Object.keys(t).forEach((function(r){if(!a&&!i.done){p=Math.max(Math.min((o-l)/e.duration,1),0),d=s.easingProgress(e.easing,p);var h=i[r],c=h.initialValue,v=h.finalValue,m=h.unit;i[r].currentValue=c+d*(v-c);var g=i[r].currentValue;(v>c&&g>=v||v<c&&g<=v)&&(i.container.style[r]=v+m,(f+=1)===Object.keys(t).length&&(i.done=!0,u+=1),u===n.length&&(a=!0)),a?s.done(e.complete):i.container.style[r]=g+m}}))})),a||(s.frameId=r.requestAnimationFrame(c))})),s}};if(0===s.elements.length)return i;for(var a=0;a<s.elements.length;a+=1)s.elements[a].dom7AnimateInstance?n=s.elements[a].dom7AnimateInstance:s.elements[a].dom7AnimateInstance=s;return n||(n=s),"stop"===t?n.stop():n.animate(s.props,s.params),i},stop:function(){for(var t=this,e=0;e<t.length;e+=1)t[e].dom7AnimateInstance&&t[e].dom7AnimateInstance.stop()}}),y="resize scroll".split(" ");function b(t){return function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];if(void 0===n[0]){for(var i=0;i<this.length;i+=1)y.indexOf(t)<0&&(t in this[i]?this[i][t]():d(this[i]).trigger(t));return this}return this.on.apply(this,[t].concat(n))}}var E=b("click"),L=b("blur"),A=b("focus"),x=b("focusin"),w=b("focusout"),S=b("keyup"),O=b("keydown"),_=b("keypress"),T=b("submit"),N=b("change"),C=b("mousedown"),M=b("mousemove"),D=b("mouseup"),j=b("mouseenter"),F=b("mouseleave"),k=b("mouseout"),P=b("mouseover"),I=b("touchstart"),V=b("touchend"),q=b("touchmove"),B=b("resize"),H=b("scroll");return[v,m,g,Object.freeze({__proto__:null,click:E,blur:L,focus:A,focusin:x,focusout:w,keyup:S,keydown:O,keypress:_,submit:T,change:N,mousedown:C,mousemove:M,mouseup:D,mouseenter:j,mouseleave:F,mouseout:k,mouseover:P,touchstart:I,touchend:V,touchmove:q,resize:B,scroll:H})].forEach((function(t){Object.keys(t).forEach((function(e){d.fn[e]=t[e]}))})),d}));
//# sourceMappingURL=dom7.min.js.map