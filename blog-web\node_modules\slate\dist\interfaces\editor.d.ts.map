{"version": 3, "file": "editor.d.ts", "sourceRoot": "", "sources": ["../packages/slate/src/interfaces/editor.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,SAAS,EACT,SAAS,EACT,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,IAAI,EAEL,MAAM,IAAI,CAAA;AAcX,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AACnC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;AAEnC,oBAAY,aAAa,GAAG,KAAK,GAAG,IAAI,CAAA;AAExC,oBAAY,SAAS,GAAG,YAAY,CAAC,WAAW,EAAE,aAAa,CAAC,CAAA;AAEhE;;;GAGG;AAEH,MAAM,WAAW,UAAU;IACzB,QAAQ,EAAE,UAAU,EAAE,CAAA;IACtB,SAAS,EAAE,SAAS,CAAA;IACpB,UAAU,EAAE,SAAS,EAAE,CAAA;IACvB,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI,CAAA;IAGhC,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACvC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACrC,aAAa,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,IAAI,CAAA;IACzC,QAAQ,EAAE,MAAM,IAAI,CAAA;IAGpB,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI,CAAA;IAC1C,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,KAAK,IAAI,CAAA;IACrC,cAAc,EAAE,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,KAAK,IAAI,CAAA;IACvE,aAAa,EAAE,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,KAAK,IAAI,CAAA;IACtE,cAAc,EAAE,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,UAAU,KAAK,IAAI,CAAA;IAC5D,WAAW,EAAE,MAAM,UAAU,EAAE,CAAA;IAC/B,WAAW,EAAE,MAAM,IAAI,CAAA;IACvB,cAAc,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,IAAI,CAAA;IAC1C,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAA;IAChC,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAA;IAClC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAA;CAClC;AAED,oBAAY,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;AAEvD,MAAM,WAAW,eAAe;IAC9B,KAAK,EAAE,CAAC,CAAC,SAAS,QAAQ,EACxB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAA;QAC3B,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAA;IAC7B,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI,CAAA;IAC1D,KAAK,EAAE,CACL,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,IAAI,CAAC,EAAE,QAAQ,GAAG,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;QACzD,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,KAAK,GAAG,SAAS,CAAA;IACtB,MAAM,EAAE,CACN,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,IAAI,CAAC,EAAE,QAAQ,GAAG,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;QACzD,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,KAAK,GAAG,SAAS,CAAA;IACtB,cAAc,EAAE,CACd,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;KAC/C,KACE,IAAI,CAAA;IACT,aAAa,EAAE,CACb,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;KAC/C,KACE,IAAI,CAAA;IACT,cAAc,EAAE,CACd,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,SAAS,CAAC,EAAE,SAAS,GAAG,UAAU,CAAA;KACnC,KACE,IAAI,CAAA;IACT,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACvD,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,KAAK,KAAK,CAAA;IAC5C,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,KAAK,SAAS,CAAA;IAClD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,KAAK,UAAU,EAAE,CAAA;IACxD,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACxD,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACzD,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,OAAO,CAAA;IAChD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACvD,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAA;IACrC,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,IAAI,CAAA;IAC1D,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,CAAA;IAChD,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,IAAI,CAAA;IAClD,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,OAAO,CAAA;IACzD,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,MAAM,CAAA;IACzC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,KAAK,OAAO,CAAA;IAC9D,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,KAAK,OAAO,CAAA;IAC/D,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAA;IACtD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,OAAO,CAAA;IAC1D,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAA;IAC1C,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,KAAK,OAAO,CAAA;IAChE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,OAAO,CAAA;IACxD,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,KAAK,SAAS,CAAA;IACjD,IAAI,EAAE,CACJ,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAA;KACvB,KACE,SAAS,CAAC,IAAI,CAAC,CAAA;IACpB,MAAM,EAAE,CAAC,CAAC,SAAS,IAAI,EACrB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IAC7C,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI,CAAA;IACpD,IAAI,EAAE,CAAC,CAAC,SAAS,UAAU,EACzB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAA;IAC7B,IAAI,EAAE,CACJ,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAA;KACvB,KACE,SAAS,CAAA;IACd,KAAK,EAAE,CAAC,CAAC,SAAS,IAAI,EACpB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAA;QACpB,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IAC7C,SAAS,EAAE,CACT,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,IAAI,CAAA;IACT,MAAM,EAAE,CACN,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAA;KACvB,KACE,SAAS,CAAC,QAAQ,CAAC,CAAA;IACxB,IAAI,EAAE,CACJ,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAA;KACvB,KACE,IAAI,CAAA;IACT,OAAO,EAAE,CACP,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,UAAU,GAAG,SAAS,GAAG,IAAI,CAAA;KACzC,KACE,OAAO,CAAA;IACZ,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,CAAA;IAC1C,KAAK,EAAE,CACL,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAA;KACvB,KACE,KAAK,CAAA;IACV,QAAQ,EAAE,CACR,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,KAAK,EACZ,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,UAAU,GAAG,SAAS,GAAG,IAAI,CAAA;KACzC,KACE,QAAQ,CAAA;IACb,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC5C,SAAS,EAAE,CACT,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,IAAI,CAAC,EAAE,QAAQ,GAAG,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;QACzD,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;IACtC,QAAQ,EAAE,CAAC,CAAC,SAAS,IAAI,EACvB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;QACpB,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAA;QACnC,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAA;IAC7B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,QAAQ,KAAK,KAAK,CAAA;IAC7D,QAAQ,EAAE,CACR,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,KAAK,EACZ,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,UAAU,GAAG,SAAS,GAAG,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAA;KAChE,KACE,QAAQ,CAAA;IACb,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC5C,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,IAAI,CAAA;IACjD,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,KAAK,IAAI,CAAA;IAChE,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,KAAK,KAAK,CAAA;IAC9C,MAAM,EAAE,CACN,MAAM,EAAE,MAAM,EACd,EAAE,EAAE,QAAQ,EACZ,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,MAAM,CAAA;IACX,WAAW,EAAE,CACX,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,KAAK,EACZ,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,KAAK,CAAA;IACV,IAAI,EAAE,CACJ,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,QAAQ,CAAA;QACb,IAAI,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAA;QAC3B,KAAK,CAAC,EAAE,OAAO,CAAA;KAChB,KACE,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS,CAAA;IACnC,kBAAkB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,IAAI,KAAK,IAAI,CAAA;CAC7D;AAID,eAAO,MAAM,MAAM,EAAE,eAi5CpB,CAAA;AAED;;GAEG;AAEH,oBAAY,SAAS,CAAC,CAAC,SAAS,IAAI,IAChC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GACvC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC,CAAA"}