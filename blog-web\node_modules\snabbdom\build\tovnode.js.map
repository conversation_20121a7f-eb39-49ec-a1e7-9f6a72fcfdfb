{"version": 3, "file": "tovnode.js", "sourceRoot": "", "sources": ["../src/tovnode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC;AAC5B,OAAO,EAAE,KAAK,EAAS,MAAM,SAAS,CAAC;AACvC,OAAO,EAAE,UAAU,EAAU,MAAM,cAAc,CAAC;AAElD,MAAM,UAAU,OAAO,CAAC,IAAU,EAAE,MAAe;IACjD,MAAM,GAAG,GAAW,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;IAC/D,IAAI,IAAY,CAAC;IACjB,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;QACvB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACrD,MAAM,KAAK,GAAQ,EAAE,CAAC;QACtB,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,MAAM,IAAI,GAAwB,EAAE,CAAC;QAErC,MAAM,QAAQ,GAAY,EAAE,CAAC;QAC7B,IAAI,IAAY,CAAC;QACjB,IAAI,CAAS,EAAE,CAAS,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC5B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC5B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC;aACtD;iBAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,OAAO,EAAE;gBAC5C,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aACrC;SACF;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACtD,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAE5D,IACE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;YACrB,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EACtD;YACA,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;SAC5B;QACD,OAAO,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;KACpD;SAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,GAAG,GAAG,CAAC,cAAc,CAAC,IAAI,CAAW,CAAC;QAC1C,OAAO,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAC3D;SAAM,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;QAC9B,IAAI,GAAG,GAAG,CAAC,cAAc,CAAC,IAAI,CAAW,CAAC;QAC1C,OAAO,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAW,CAAC,CAAC;KAC9C;SAAM;QACL,OAAO,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,IAAW,CAAC,CAAC;KAClD;AACH,CAAC"}