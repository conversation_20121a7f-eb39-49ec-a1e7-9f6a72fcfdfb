<template>
  <div class="modern-home">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-particles"></div>
      </div>
      <div class="hero-content fade-in">
        <h1 class="hero-title">{{ siteTitle }}</h1>
        <p v-if="siteDescription" class="hero-description">{{ siteDescription }}</p>
        <div v-if="siteKeywords && siteKeywords.length" class="hero-tags">
          <span class="tags-label">探索主题</span>
          <div class="tags-container">
            <span
              v-for="(keyword, index) in siteKeywords"
              :key="keyword"
              :class="['modern-tag', 'hero-tag']"
              :style="{ animationDelay: `${index * 0.1}s` }">
              {{ keyword }}
            </span>
          </div>
        </div>
        <div class="hero-actions">
          <router-link to="/search" class="hero-btn primary">
            <el-icon><Search /></el-icon>
            <span>开始探索</span>
          </router-link>
          <router-link v-if="!isLoggedIn" to="/register" class="hero-btn secondary">
            <el-icon><EditPen /></el-icon>
            <span>加入我们</span>
          </router-link>
        </div>
      </div>
      <div class="hero-scroll-indicator">
        <div class="scroll-arrow">
          <el-icon><ArrowDown /></el-icon>
        </div>
      </div>
    </section>

    <!-- 文章列表区域 -->
    <section class="articles-section">
      <div class="section-header">
        <h2 class="section-title">最新文章</h2>
        <p class="section-subtitle">发现精彩内容，分享知识与思考</p>
      </div>

      <div class="articles-container" data-cy="article-list">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>

        <div v-else-if="articles.length === 0" class="empty-container">
          <div class="empty-illustration">
            <el-icon :size="80"><Document /></el-icon>
          </div>
          <h3>暂无文章</h3>
          <p>还没有发布任何文章，期待第一篇精彩内容！</p>
          <router-link v-if="isLoggedIn" to="/article/create" class="modern-btn">
            <el-icon><EditPen /></el-icon>
            <span>写第一篇文章</span>
          </router-link>
        </div>

        <div v-else class="articles-grid">
          <article
            v-for="(article, index) in articles"
            :key="article.id"
            class="article-card modern-card hover-lift"
            :class="{ 'slide-in-left': index % 2 === 0, 'slide-in-right': index % 2 === 1 }"
            :style="{ animationDelay: `${index * 0.1}s` }">

            <!-- 封面图片 -->
            <div v-if="article.coverImage" class="article-cover">
              <router-link :to="`/article/${article.id}`">
                <img :src="buildCoverImageUrl(article.coverImage)" :alt="article.title" />
              </router-link>
            </div>

            <div class="article-header">
              <h3 class="article-title">
                <router-link :to="`/article/${article.id}`" data-cy="article-link">
                  {{ article.title }}
                </router-link>
              </h3>
              <div class="article-meta">
                <div class="meta-item">
                  <el-icon><User /></el-icon>
                  <span>{{ article.author }}</span>
                </div>
                <div class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(article.createTime) }}</span>
                </div>
                <div v-if="article.categoryName" class="meta-item">
                  <el-icon><Folder /></el-icon>
                  <span>{{ article.categoryName }}</span>
                </div>
              </div>
            </div>

            <div class="article-content">
              <p class="article-summary">{{ article.summary || '暂无摘要' }}</p>
            </div>

            <div class="article-footer">
              <div v-if="article.tags && article.tags.length" class="article-tags">
                <span
                  v-for="tag in article.tags.slice(0, 3)"
                  :key="tag.id"
                  class="modern-tag article-tag">
                  {{ tag.name }}
                </span>
                <span v-if="article.tags.length > 3" class="more-tags">
                  +{{ article.tags.length - 3 }}
                </span>
              </div>
              <router-link :to="`/article/${article.id}`" class="read-more-btn">
                <span>阅读全文</span>
                <el-icon><ArrowRight /></el-icon>
              </router-link>
            </div>
          </article>
        </div>

        <!-- 加载更多按钮 -->
        <div v-if="articles.length > 0 && hasMore" class="load-more-container">
          <button @click="loadMore" :loading="loadingMore" class="load-more-btn modern-btn">
            <el-icon v-if="!loadingMore"><Refresh /></el-icon>
            <span>{{ loadingMore ? '加载中...' : '加载更多' }}</span>
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { getArticles } from '@/api/article'
import { siteConfig, fetchSiteConfig } from '@/utils/siteConfig'
import { getToken } from '@/utils/auth'
import { buildResourceUrl } from '@/config/settings'
import {
  Search, EditPen, ArrowDown, Document, User, Calendar,
  Folder, ArrowRight, Refresh
} from '@element-plus/icons-vue'

export default {
  name: 'ModernHome',
  components: {
    Search, EditPen, ArrowDown, Document, User, Calendar,
    Folder, ArrowRight, Refresh
  },
  setup() {
    const articles = ref([])
    const loading = ref(false)
    const loadingMore = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(6)
    const hasMore = ref(true)

    // 登录状态
    const isLoggedIn = computed(() => !!getToken())

    // 使用全局网站配置
    const siteTitle = computed(() => siteConfig.value.title || '现代博客系统')
    const siteDescription = computed(() => siteConfig.value.description || '分享知识，记录思考，连接世界')
    const siteKeywords = computed(() => {
      if (siteConfig.value.keywords) {
        return siteConfig.value.keywords.split(',').map(k => k.trim()).filter(k => k)
      }
      return ['技术', '生活', '思考', '分享']
    })

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      if (days < 365) return `${Math.floor(days / 30)}个月前`
      return `${Math.floor(days / 365)}年前`
    }

    // 构建封面图片URL
    const buildCoverImageUrl = (coverImage) => {
      if (!coverImage) return ''
      return buildResourceUrl(coverImage)
    }

    // 获取文章列表
    const fetchArticles = async (page = 1, append = false) => {
      if (page === 1) {
        loading.value = true
      } else {
        loadingMore.value = true
      }

      try {
        const res = await getArticles({
          current: page,
          size: pageSize.value,
          status: 1 // 已发布的文章
        })

        if (res.code === 200 && res.data) {
          const newArticles = res.data.records || []

          if (append) {
            articles.value = [...articles.value, ...newArticles]
          } else {
            articles.value = newArticles
          }

          // 检查是否还有更多数据
          hasMore.value = newArticles.length === pageSize.value &&
                          res.data.current < res.data.pages
          currentPage.value = page
        }
      } catch (error) {
        console.error('获取文章列表失败', error)
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }

    // 加载更多
    const loadMore = () => {
      if (!loadingMore.value && hasMore.value) {
        fetchArticles(currentPage.value + 1, true)
      }
    }

    onMounted(async () => {
      await fetchSiteConfig() // 获取完整的网站配置信息
      fetchArticles()

      // 添加滚动动画
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in')
          }
        })
      }, observerOptions)

      // 观察文章卡片
      setTimeout(() => {
        document.querySelectorAll('.article-card').forEach(card => {
          observer.observe(card)
        })
      }, 100)
    })

    return {
      articles,
      loading,
      loadingMore,
      hasMore,
      isLoggedIn,
      siteTitle,
      siteDescription,
      siteKeywords,
      formatDate,
      buildCoverImageUrl,
      loadMore
    }
  }
}
</script>

<style scoped>
/* 现代化首页样式 */
.modern-home {
  min-height: 100vh;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: var(--gradient-primary);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

.hero-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: float 20s ease-in-out infinite;
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 2;
  max-width: 800px;
  padding: var(--spacing-xl);
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(45deg, #fff, #a8edea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
  line-height: 1.6;
}

.hero-tags {
  margin-bottom: var(--spacing-2xl);
}

.tags-label {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  opacity: 0.8;
}

.tags-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.hero-tag {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  animation: slideInLeft 0.6s ease-out both;
}

.hero-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05) translateY(-2px);
}
.hero-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-xl);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.hero-btn.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.hero-btn.primary:hover {
  background: white;
  color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.hero-btn.secondary {
  background: var(--gradient-secondary);
  color: white;
  border: 2px solid transparent;
}

.hero-btn.secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(245, 108, 108, 0.3);
}

.hero-scroll-indicator {
  position: absolute;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.scroll-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: pulse 2s infinite;
  cursor: pointer;
}

.scroll-arrow:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 文章列表区域 */
.articles-section {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.articles-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-illustration {
  margin-bottom: var(--spacing-lg);
  color: var(--text-light);
}

.empty-container h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.empty-container p {
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}
/* 文章网格 */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

/* 文章卡片 */
.article-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--border-light);
  opacity: 0;
  transform: translateY(30px);
}

.article-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.article-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

/* 文章封面 */
.article-cover {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.article-cover img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition-normal);
}

.article-cover:hover img {
  transform: scale(1.05);
}

.article-header {
  margin-bottom: var(--spacing-lg);
}

.article-title {
  margin-bottom: var(--spacing-md);
}

.article-title a {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
  line-height: 1.4;
  display: block;
  transition: var(--transition-normal);
}

.article-title a:hover {
  color: var(--primary-color);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.meta-item .el-icon {
  font-size: 1rem;
  color: var(--primary-color);
}

.article-content {
  margin-bottom: var(--spacing-lg);
}

.article-summary {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  align-items: center;
}

.article-tag {
  font-size: 0.75rem;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.more-tags {
  font-size: 0.75rem;
  color: var(--text-light);
  font-weight: 500;
}

.read-more-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition-normal);
}

.read-more-btn:hover {
  color: var(--primary-dark);
  transform: translateX(3px);
}

.read-more-btn .el-icon {
  transition: var(--transition-normal);
}

.read-more-btn:hover .el-icon {
  transform: translateX(3px);
}

/* 加载更多 */
.load-more-container {
  text-align: center;
  margin-top: var(--spacing-xl);
}

.load-more-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 1s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out both;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out both;
}
/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .article-card {
    padding: var(--spacing-lg);
  }

  .article-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }

  .article-footer {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .read-more-btn {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: var(--spacing-lg);
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .articles-container {
    padding: 0 var(--spacing-md);
  }

  .article-card {
    padding: var(--spacing-md);
  }

  .tags-container {
    justify-content: center;
  }
}
</style>