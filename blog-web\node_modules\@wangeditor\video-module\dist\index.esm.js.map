{"version": 3, "file": "index.esm.js", "sources": ["../src/locale/index.ts", "../src/locale/en.ts", "../src/locale/zh-CN.ts", "../../../node_modules/core-js/internals/global.js", "../../../node_modules/core-js/internals/engine-v8-version.js", "../../../node_modules/core-js/internals/fails.js", "../../../node_modules/core-js/internals/descriptors.js", "../../../node_modules/core-js/internals/function-call.js", "../../../node_modules/core-js/internals/object-property-is-enumerable.js", "../../../node_modules/core-js/internals/create-property-descriptor.js", "../../../node_modules/core-js/internals/function-uncurry-this.js", "../../../node_modules/core-js/internals/classof-raw.js", "../../../node_modules/core-js/internals/indexed-object.js", "../../../node_modules/core-js/internals/require-object-coercible.js", "../../../node_modules/core-js/internals/to-indexed-object.js", "../../../node_modules/core-js/internals/is-callable.js", "../../../node_modules/core-js/internals/is-object.js", "../../../node_modules/core-js/internals/get-built-in.js", "../../../node_modules/core-js/internals/object-is-prototype-of.js", "../../../node_modules/core-js/internals/engine-user-agent.js", "../../../node_modules/core-js/internals/native-symbol.js", "../../../node_modules/core-js/internals/use-symbol-as-uid.js", "../../../node_modules/core-js/internals/is-symbol.js", "../../../node_modules/core-js/internals/try-to-string.js", "../../../node_modules/core-js/internals/a-callable.js", "../../../node_modules/core-js/internals/get-method.js", "../../../node_modules/core-js/internals/ordinary-to-primitive.js", "../../../node_modules/core-js/internals/set-global.js", "../../../node_modules/core-js/internals/shared-store.js", "../../../node_modules/core-js/internals/shared.js", "../../../node_modules/core-js/internals/to-object.js", "../../../node_modules/core-js/internals/has-own-property.js", "../../../node_modules/core-js/internals/uid.js", "../../../node_modules/core-js/internals/well-known-symbol.js", "../../../node_modules/core-js/internals/to-primitive.js", "../../../node_modules/core-js/internals/to-property-key.js", "../../../node_modules/core-js/internals/document-create-element.js", "../../../node_modules/core-js/internals/ie8-dom-define.js", "../../../node_modules/core-js/internals/object-get-own-property-descriptor.js", "../../../node_modules/core-js/internals/an-object.js", "../../../node_modules/core-js/internals/object-define-property.js", "../../../node_modules/core-js/internals/create-non-enumerable-property.js", "../../../node_modules/core-js/internals/inspect-source.js", "../../../node_modules/core-js/internals/internal-state.js", "../../../node_modules/core-js/internals/native-weak-map.js", "../../../node_modules/core-js/internals/shared-key.js", "../../../node_modules/core-js/internals/hidden-keys.js", "../../../node_modules/core-js/internals/function-name.js", "../../../node_modules/core-js/internals/redefine.js", "../../../node_modules/core-js/internals/to-integer-or-infinity.js", "../../../node_modules/core-js/internals/to-absolute-index.js", "../../../node_modules/core-js/internals/to-length.js", "../../../node_modules/core-js/internals/length-of-array-like.js", "../../../node_modules/core-js/internals/array-includes.js", "../../../node_modules/core-js/internals/object-keys-internal.js", "../../../node_modules/core-js/internals/enum-bug-keys.js", "../../../node_modules/core-js/internals/object-get-own-property-names.js", "../../../node_modules/core-js/internals/object-get-own-property-symbols.js", "../../../node_modules/core-js/internals/own-keys.js", "../../../node_modules/core-js/internals/copy-constructor-properties.js", "../../../node_modules/core-js/internals/is-forced.js", "../../../node_modules/core-js/internals/export.js", "../../../node_modules/core-js/internals/to-string-tag-support.js", "../../../node_modules/core-js/internals/string-trim-forced.js", "../../../node_modules/core-js/internals/classof.js", "../../../node_modules/core-js/internals/to-string.js", "../../../node_modules/core-js/internals/whitespaces.js", "../../../node_modules/core-js/internals/string-trim.js", "../../../node_modules/core-js/modules/es.string.trim.js", "../src/utils/dom.ts", "../../../node_modules/core-js/modules/es.global-this.js", "../src/module/render-elem.tsx", "../src/module/elem-to-html.ts", "../../../node_modules/core-js/internals/object-create.js", "../../../node_modules/core-js/internals/object-keys.js", "../../../node_modules/core-js/internals/object-define-properties.js", "../../../node_modules/core-js/internals/html.js", "../../../node_modules/core-js/internals/add-to-unscopables.js", "../../../node_modules/core-js/modules/es.array.includes.js", "../src/module/pre-parse-html.ts", "../../../node_modules/core-js/internals/function-bind-context.js", "../../../node_modules/core-js/internals/is-array.js", "../../../node_modules/core-js/internals/is-constructor.js", "../../../node_modules/core-js/internals/array-species-constructor.js", "../../../node_modules/core-js/internals/array-species-create.js", "../../../node_modules/core-js/internals/array-iteration.js", "../../../node_modules/core-js/modules/es.array.find.js", "../../../node_modules/core-js/internals/object-to-string.js", "../src/module/parse-elem-html.ts", "../../../node_modules/core-js/modules/es.object.to-string.js", "../../../node_modules/core-js/internals/regexp-exec.js", "../../../node_modules/core-js/internals/regexp-flags.js", "../../../node_modules/core-js/internals/regexp-sticky-helpers.js", "../../../node_modules/core-js/internals/regexp-unsupported-dot-all.js", "../../../node_modules/core-js/internals/regexp-unsupported-ncg.js", "../../../node_modules/core-js/modules/es.regexp.exec.js", "../../../node_modules/core-js/internals/function-apply.js", "../../../node_modules/core-js/internals/fix-regexp-well-known-symbol-logic.js", "../../../node_modules/core-js/internals/string-multibyte.js", "../../../node_modules/core-js/internals/advance-string-index.js", "../../../node_modules/core-js/internals/get-substitution.js", "../../../node_modules/core-js/internals/regexp-exec-abstract.js", "../../../node_modules/core-js/modules/es.string.replace.js", "../src/utils/util.ts", "../src/constants/svg.ts", "../../../node_modules/core-js/internals/native-promise-constructor.js", "../../../node_modules/core-js/internals/redefine-all.js", "../../../node_modules/core-js/internals/a-possible-prototype.js", "../../../node_modules/core-js/internals/object-set-prototype-of.js", "../../../node_modules/core-js/internals/set-to-string-tag.js", "../../../node_modules/core-js/internals/set-species.js", "../../../node_modules/core-js/internals/an-instance.js", "../../../node_modules/core-js/internals/iterators.js", "../../../node_modules/core-js/internals/is-array-iterator-method.js", "../../../node_modules/core-js/internals/get-iterator-method.js", "../../../node_modules/core-js/internals/get-iterator.js", "../../../node_modules/core-js/internals/iterator-close.js", "../../../node_modules/core-js/internals/iterate.js", "../../../node_modules/core-js/internals/check-correctness-of-iteration.js", "../../../node_modules/core-js/internals/task.js", "../../../node_modules/core-js/internals/a-constructor.js", "../../../node_modules/core-js/internals/species-constructor.js", "../../../node_modules/core-js/internals/array-slice.js", "../../../node_modules/core-js/internals/engine-is-ios.js", "../../../node_modules/core-js/internals/engine-is-node.js", "../../../node_modules/core-js/internals/microtask.js", "../../../node_modules/core-js/internals/engine-is-ios-pebble.js", "../../../node_modules/core-js/internals/engine-is-webos-webkit.js", "../../../node_modules/core-js/modules/es.promise.js", "../../../node_modules/core-js/internals/new-promise-capability.js", "../../../node_modules/core-js/internals/perform.js", "../../../node_modules/core-js/internals/engine-is-browser.js", "../../../node_modules/core-js/internals/host-report-errors.js", "../src/module/helper/insert-video.ts", "../src/module/menu/InsertVideoMenu.ts", "../../../node_modules/core-js/internals/promise-resolve.js", "../../../node_modules/core-js/modules/es.array.join.js", "../../../node_modules/core-js/internals/array-method-is-strict.js", "../../../node_modules/core-js/internals/correct-prototype-getter.js", "../../../node_modules/core-js/internals/iterators-core.js", "../../../node_modules/core-js/internals/object-get-prototype-of.js", "../../../node_modules/core-js/internals/create-iterator-constructor.js", "../../../node_modules/core-js/internals/define-iterator.js", "../../../node_modules/core-js/modules/es.array.iterator.js", "../../../node_modules/core-js/modules/es.string.iterator.js", "../../../node_modules/core-js/internals/create-property.js", "../../../node_modules/core-js/internals/array-slice-simple.js", "../../../node_modules/core-js/internals/object-get-own-property-names-external.js", "../../../node_modules/core-js/internals/array-buffer-non-extensible.js", "../../../node_modules/core-js/internals/object-is-extensible.js", "../../../node_modules/core-js/internals/freezing.js", "../../../node_modules/core-js/internals/internal-metadata.js", "../../../node_modules/core-js/internals/collection-weak.js", "../../../node_modules/core-js/modules/es.weak-map.js", "../../../node_modules/core-js/internals/collection.js", "../../../node_modules/core-js/internals/inherit-if-required.js", "../../../node_modules/core-js/internals/dom-iterables.js", "../../../node_modules/core-js/internals/dom-token-list-prototype.js", "../../../node_modules/core-js/modules/web.dom-collections.iterator.js", "../../../node_modules/core-js/modules/es.function.name.js", "../../../node_modules/core-js/internals/array-method-has-species-support.js", "../../../node_modules/core-js/modules/es.array.slice.js", "../src/module/helper/upload-videos.ts", "../src/module/menu/UploadVideoMenu.ts", "../../../node_modules/core-js/modules/es.regexp.to-string.js", "../src/module/menu/EditVideoSizeMenu.ts", "../src/module/menu/index.ts", "../src/module/index.ts", "../src/module/menu/config.ts", "../src/module/plugin.ts"], "sourcesContent": ["/**\n * @description i18n entry\n * <AUTHOR>\n */\n\nimport { i18nAddResources } from '@wangeditor/core'\nimport enResources from './en'\nimport zhResources from './zh-CN'\n\ni18nAddResources('en', enResources)\ni18nAddResources('zh-CN', zhResources)\n", "/**\n * @description i18n en\n * <AUTHOR>\n */\n\nexport default {\n  videoModule: {\n    delete: 'Delete',\n    uploadVideo: 'Upload video',\n    insertVideo: 'Insert video',\n    videoSrc: 'Video source',\n    videoSrcPlaceHolder: 'Video file url, or third-party <iframe>',\n    videoPoster: 'Video poster',\n    videoPosterPlaceHolder: 'Poster image url',\n    ok: 'Ok',\n    editSize: 'Edit size',\n    width: 'Width',\n    height: 'Height',\n  },\n}\n", "/**\n * @description i18n zh-CN\n * <AUTHOR>\n */\n\nexport default {\n  videoModule: {\n    delete: '删除视频',\n    uploadVideo: '上传视频',\n    insertVideo: '插入视频',\n    videoSrc: '视频地址',\n    videoSrcPlaceHolder: '视频文件 url 或第三方 <iframe>',\n    videoPoster: '视频封面',\n    videoPosterPlaceHolder: '封面图片 url',\n    ok: '确定',\n    editSize: '修改尺寸',\n    width: '宽度',\n    height: '高度',\n  },\n}\n", "var check = function (it) {\n  return it && it.Math == Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n", "var global = require('../internals/global');\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n", "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "var fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] != 7;\n});\n", "var call = Function.prototype.call;\n\nmodule.exports = call.bind ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n", "'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n", "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "var FunctionPrototype = Function.prototype;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\nvar callBind = bind && bind.bind(call);\n\nmodule.exports = bind ? function (fn) {\n  return fn && callBind(call, fn);\n} : function (fn) {\n  return fn && function () {\n    return call.apply(fn, arguments);\n  };\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n", "var global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar Object = global.Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split(it, '') : Object(it);\n} : Object;\n", "var global = require('../internals/global');\n\nvar TypeError = global.TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = function (argument) {\n  return typeof argument == 'function';\n};\n", "var isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(global[namespace]) : global[namespace] && global[namespace][method];\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n", "var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';\n", "/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol();\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  return !String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n", "/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n", "var global = require('../internals/global');\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Object = global.Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, Object(it));\n};\n", "var global = require('../internals/global');\n\nvar String = global.String;\n\nmodule.exports = function (argument) {\n  try {\n    return String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar TypeError = global.TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw TypeError(tryToString(argument) + ' is not a function');\n};\n", "var aCallable = require('../internals/a-callable');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return func == null ? undefined : aCallable(func);\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar TypeError = global.TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n", "var global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "var global = require('../internals/global');\nvar setGlobal = require('../internals/set-global');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\n\nmodule.exports = store;\n", "var IS_PURE = require('../internals/is-pure');\nvar store = require('../internals/shared-store');\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.19.3',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2021 <PERSON> (zloirock.ru)'\n});\n", "var global = require('../internals/global');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar Object = global.Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n", "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar symbolFor = Symbol && Symbol['for'];\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n    var description = 'Symbol.' + name;\n    if (NATIVE_SYMBOL && hasOwn(Symbol, name)) {\n      WellKnownSymbolsStore[name] = Symbol[name];\n    } else if (USE_SYMBOL_AS_UID && symbolFor) {\n      WellKnownSymbolsStore[name] = symbolFor(description);\n    } else {\n      WellKnownSymbolsStore[name] = createWellKnownSymbol(description);\n    }\n  } return WellKnownSymbolsStore[name];\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TypeError = global.TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n", "var toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n", "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- requied for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a != 7;\n});\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n", "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar String = global.String;\nvar TypeError = global.TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw TypeError(String(argument) + ' is not an object');\n};\n", "var global = require('../internals/global');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar TypeError = global.TypeError;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n", "var NATIVE_WEAK_MAP = require('../internals/native-weak-map');\nvar global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\nvar shared = require('../internals/shared-store');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\nvar TypeError = global.TypeError;\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP || shared.state) {\n  var store = shared.state || (shared.state = new WeakMap());\n  var wmget = uncurryThis(store.get);\n  var wmhas = uncurryThis(store.has);\n  var wmset = uncurryThis(store.set);\n  set = function (it, metadata) {\n    if (wmhas(store, it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    wmset(store, it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return wmget(store, it) || {};\n  };\n  has = function (it) {\n    return wmhas(store, it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    if (hasOwn(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return hasOwn(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return hasOwn(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar inspectSource = require('../internals/inspect-source');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(inspectSource(WeakMap));\n", "var shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "module.exports = {};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar setGlobal = require('../internals/set-global');\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\n\nvar getInternalState = InternalStateModule.get;\nvar enforceInternalState = InternalStateModule.enforce;\nvar TEMPLATE = String(String).split('String');\n\n(module.exports = function (O, key, value, options) {\n  var unsafe = options ? !!options.unsafe : false;\n  var simple = options ? !!options.enumerable : false;\n  var noTargetGet = options ? !!options.noTargetGet : false;\n  var name = options && options.name !== undefined ? options.name : key;\n  var state;\n  if (isCallable(value)) {\n    if (String(name).slice(0, 7) === 'Symbol(') {\n      name = '[' + String(name).replace(/^Symbol\\(([^)]*)\\)/, '$1') + ']';\n    }\n    if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n      createNonEnumerableProperty(value, 'name', name);\n    }\n    state = enforceInternalState(value);\n    if (!state.source) {\n      state.source = TEMPLATE.join(typeof name == 'string' ? name : '');\n    }\n  }\n  if (O === global) {\n    if (simple) O[key] = value;\n    else setGlobal(key, value);\n    return;\n  } else if (!unsafe) {\n    delete O[key];\n  } else if (!noTargetGet && O[key]) {\n    simple = true;\n  }\n  if (simple) O[key] = value;\n  else createNonEnumerableProperty(O, key, value);\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, 'toString', function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n});\n", "var ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- safe\n  return number !== number || number === 0 ? 0 : (number > 0 ? floor : ceil)(number);\n};\n", "var toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "var toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  return argument > 0 ? min(toIntegerOrInfinity(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "var toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n", "var toIndexedObject = require('../internals/to-indexed-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = lengthOfArrayLike(O);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar hasOwn = require('../internals/has-own-property');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar push = uncurryThis([].push);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !hasOwn(hiddenKeys, key) && hasOwn(O, key) && push(result, key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (hasOwn(O, key = names[i++])) {\n    ~indexOf(result, key) || push(result, key);\n  }\n  return result;\n};\n", "// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n", "var getBuiltIn = require('../internals/get-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar concat = uncurryThis([].concat);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? concat(keys, getOwnPropertySymbols(it)) : keys;\n};\n", "var hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n  }\n};\n", "var fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true\n    : value == NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "var global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar redefine = require('../internals/redefine');\nvar setGlobal = require('../internals/set-global');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target      - name of the target object\n  options.global      - target is the global object\n  options.stat        - export as static methods of target\n  options.proto       - export as prototype methods of target\n  options.real        - real prototype method for the `pure` version\n  options.forced      - export even if the native feature is available\n  options.bind        - bind methods to the target, required for the `pure` version\n  options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe      - use the simple assignment of property instead of delete + defineProperty\n  options.sham        - add a flag to not completely full polyfills\n  options.enumerable  - export as enumerable property\n  options.noTargetGet - prevent calling a getter on target\n  options.name        - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || setGlobal(TARGET, {});\n  } else {\n    target = (global[TARGET] || {}).prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.noTargetGet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    // extend global\n    redefine(target, key, sourceProperty, options);\n  }\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "var PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar fails = require('../internals/fails');\nvar whitespaces = require('../internals/whitespaces');\n\nvar non = '\\u200B\\u0085\\u180E';\n\n// check that a method works with the correct list\n// of whitespaces and has a correct name\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    return !!whitespaces[METHOD_NAME]()\n      || non[METHOD_NAME]() !== non\n      || (PROPER_FUNCTION_NAME && whitespaces[METHOD_NAME].name !== METHOD_NAME);\n  });\n};\n", "var global = require('../internals/global');\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar Object = global.Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) == 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "var global = require('../internals/global');\nvar classof = require('../internals/classof');\n\nvar String = global.String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw TypeError('Cannot convert a Symbol value to a string');\n  return String(argument);\n};\n", "// a string of all valid unicode whitespaces\nmodule.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' +\n  '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\nvar whitespaces = require('../internals/whitespaces');\n\nvar replace = uncurryThis(''.replace);\nvar whitespace = '[' + whitespaces + ']';\nvar ltrim = RegExp('^' + whitespace + whitespace + '*');\nvar rtrim = RegExp(whitespace + whitespace + '*$');\n\n// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\nvar createMethod = function (TYPE) {\n  return function ($this) {\n    var string = toString(requireObjectCoercible($this));\n    if (TYPE & 1) string = replace(string, ltrim, '');\n    if (TYPE & 2) string = replace(string, rtrim, '');\n    return string;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.{ trimLeft, trimStart }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n  start: createMethod(1),\n  // `String.prototype.{ trimRight, trimEnd }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimend\n  end: createMethod(2),\n  // `String.prototype.trim` method\n  // https://tc39.es/ecma262/#sec-string.prototype.trim\n  trim: createMethod(3)\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar $trim = require('../internals/string-trim').trim;\nvar forcedStringTrimMethod = require('../internals/string-trim-forced');\n\n// `String.prototype.trim` method\n// https://tc39.es/ecma262/#sec-string.prototype.trim\n$({ target: 'String', proto: true, forced: forcedStringTrimMethod('trim') }, {\n  trim: function trim() {\n    return $trim(this);\n  }\n});\n", "/**\n * @description DOM 操作\n * <AUTHOR>\n */\n\nimport $, { append, on, focus, attr, val, html, parent, hasClass, Dom7Array, empty } from 'dom7'\nexport { Dom7Array } from 'dom7'\n\nif (append) $.fn.append = append\nif (on) $.fn.on = on\nif (focus) $.fn.focus = focus\nif (attr) $.fn.attr = attr\nif (val) $.fn.val = val\nif (html) $.fn.html = html\nif (parent) $.fn.parent = parent\nif (hasClass) $.fn.hasClass = hasClass\nif (empty) $.fn.empty = empty\n\nexport default $\n\n/**\n * 获取 tagName lower-case\n * @param $elem $elem\n */\nexport function getTagName($elem: Dom7Array): string {\n  if ($elem.length) return $elem[0].tagName.toLowerCase()\n  return ''\n}\n\n/**\n * 生成带 size 样式的 iframe html\n * @param iframeHtml iframe html string\n * @param width width\n * @param height height\n * @returns iframe html string with size style\n */\nexport function genSizeStyledIframeHtml(\n  iframeHtml: string,\n  width: string = 'auto',\n  height: string = 'auto'\n): string {\n  const $iframe = $(iframeHtml)\n  $iframe.attr('width', width)\n  $iframe.attr('height', height)\n  return $iframe[0].outerHTML\n}\n\n// COMPAT: This is required to prevent TypeScript aliases from doing some very\n// weird things for Slate's types with the same name as globals. (2019/11/27)\n// https://github.com/microsoft/TypeScript/issues/35002\nimport DOMNode = globalThis.Node\nimport DOMComment = globalThis.Comment\nimport DOMElement = globalThis.Element\nimport DOMText = globalThis.Text\nimport DOMRange = globalThis.Range\nimport DOMSelection = globalThis.Selection\nimport DOMStaticRange = globalThis.StaticRange\nexport { DOMNode, DOMComment, DOMElement, DOMText, DOMRange, DOMSelection, DOMStaticRange }\n", "var $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({ global: true }, {\n  globalThis: global\n});\n", "/**\n * @description video render elem\n * <AUTHOR>\n */\n\nimport { Element } from 'slate'\nimport { h, jsx, VNode } from 'snabbdom'\nimport { IDomEditor, DomEditor } from '@wangeditor/core'\nimport { VideoElement } from './custom-types'\nimport { genSizeStyledIframeHtml } from '../utils/dom'\n\nfunction renderVideo(elemNode: Element, children: VNode[] | null, editor: IDomEditor): VNode {\n  const { src = '', poster = '', width = 'auto', height = 'auto' } = elemNode as VideoElement\n\n  // 是否选中\n  const selected = DomEditor.isNodeSelected(editor, elemNode)\n\n  let vnode: VNode\n  if (src.trim().indexOf('<iframe ') === 0) {\n    // 增加尺寸样式\n    const iframeHtml = genSizeStyledIframeHtml(src, width, height)\n\n    // iframe 形式，第三方视频\n    vnode = (\n      <div\n        className=\"w-e-textarea-video-container\"\n        data-selected={selected ? 'true' : ''} // 标记为 选中\n        innerHTML={iframeHtml} // 内嵌第三方 iframe 视频\n      ></div>\n    )\n  } else {\n    // 其他，mp4 格式\n    const videoVnode = (\n      <video poster={poster} controls>\n        <source src={src} type=\"video/mp4\" />\n        {`Sorry, your browser doesn't support embedded videos.\\n 抱歉，浏览器不支持 video 视频`}\n      </video>\n    )\n    // @ts-ignore 添加尺寸\n    if (width !== 'auto') videoVnode.data.width = width\n    // @ts-ignore\n    if (height !== 'auto') videoVnode.data.height = height\n\n    vnode = (\n      <div\n        className=\"w-e-textarea-video-container\"\n        data-selected={selected ? 'true' : ''} // 标记为 选中\n      >\n        {videoVnode}\n      </div>\n    )\n  }\n\n  // 【注意】void node 中，renderElem 不用处理 children 。core 会统一处理。\n\n  const containerVnode = h(\n    'div',\n    {\n      props: {\n        contentEditable: false,\n      },\n      on: {\n        mousedown: e => e.preventDefault(),\n      },\n    },\n    vnode\n  )\n\n  return containerVnode\n}\n\nconst renderVideoConf = {\n  type: 'video', // 和 elemNode.type 一致\n  renderElem: renderVideo,\n}\n\nexport { renderVideoConf }\n", "/**\n * @description to html\n * <AUTHOR>\n */\n\nimport { Element } from 'slate'\nimport { VideoElement } from './custom-types'\nimport { genSizeStyledIframeHtml } from '../utils/dom'\n\nfunction videoToHtml(elemNode: Element, childrenHtml?: string): string {\n  const { src = '', poster = '', width = 'auto', height = 'auto' } = elemNode as VideoElement\n  let res = '<div data-w-e-type=\"video\" data-w-e-is-void>\\n'\n\n  if (src.trim().indexOf('<iframe ') === 0) {\n    // iframe 形式\n    const iframeHtml = genSizeStyledIframeHtml(src, width, height)\n    res += iframeHtml\n  } else {\n    // 其他，mp4 等 url 格式\n    res += `<video poster=\"${poster}\" controls=\"true\" width=\"${width}\" height=\"${height}\"><source src=\"${src}\" type=\"video/mp4\"/></video>`\n  }\n  res += '\\n</div>'\n\n  return res\n}\n\nexport const videoToHtmlConf = {\n  type: 'video',\n  elemToHtml: videoToHtml,\n}\n", "/* global ActiveXObject -- old IE, WSH */\nvar anObject = require('../internals/an-object');\nvar defineProperties = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\n\nvar GT = '>';\nvar LT = '<';\nvar PROTOTYPE = 'prototype';\nvar SCRIPT = 'script';\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar EmptyConstructor = function () { /* empty */ };\n\nvar scriptTag = function (content) {\n  return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n};\n\n// Create object with fake `null` prototype: use ActiveX Object with cleared prototype\nvar NullProtoObjectViaActiveX = function (activeXDocument) {\n  activeXDocument.write(scriptTag(''));\n  activeXDocument.close();\n  var temp = activeXDocument.parentWindow.Object;\n  activeXDocument = null; // avoid memory leak\n  return temp;\n};\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar NullProtoObjectViaIFrame = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var JS = 'java' + SCRIPT + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  // https://github.com/zloirock/core-js/issues/475\n  iframe.src = String(JS);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(scriptTag('document.F=Object'));\n  iframeDocument.close();\n  return iframeDocument.F;\n};\n\n// Check for document.domain and active x support\n// No need to use active x approach when document.domain is not set\n// see https://github.com/es-shims/es5-shim/issues/150\n// variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n// avoid IE GC bug\nvar activeXDocument;\nvar NullProtoObject = function () {\n  try {\n    activeXDocument = new ActiveXObject('htmlfile');\n  } catch (error) { /* ignore */ }\n  NullProtoObject = typeof document != 'undefined'\n    ? document.domain && activeXDocument\n      ? NullProtoObjectViaActiveX(activeXDocument) // old IE\n      : NullProtoObjectViaIFrame()\n    : NullProtoObjectViaActiveX(activeXDocument); // WSH\n  var length = enumBugKeys.length;\n  while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n  return NullProtoObject();\n};\n\nhiddenKeys[IE_PROTO] = true;\n\n// `Object.create` method\n// https://tc39.es/ecma262/#sec-object.create\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    EmptyConstructor[PROTOTYPE] = anObject(O);\n    result = new EmptyConstructor();\n    EmptyConstructor[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = NullProtoObject();\n  return Properties === undefined ? result : defineProperties(result, Properties);\n};\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// `Object.keys` method\n// https://tc39.es/ecma262/#sec-object.keys\n// eslint-disable-next-line es/no-object-keys -- safe\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar objectKeys = require('../internals/object-keys');\n\n// `Object.defineProperties` method\n// https://tc39.es/ecma262/#sec-object.defineproperties\n// eslint-disable-next-line es/no-object-defineproperties -- safe\nmodule.exports = DESCRIPTORS ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var props = toIndexedObject(Properties);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], props[key]);\n  return O;\n};\n", "var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('document', 'documentElement');\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\nvar create = require('../internals/object-create');\nvar definePropertyModule = require('../internals/object-define-property');\n\nvar UNSCOPABLES = wellKnownSymbol('unscopables');\nvar ArrayPrototype = Array.prototype;\n\n// Array.prototype[@@unscopables]\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\nif (ArrayPrototype[UNSCOPABLES] == undefined) {\n  definePropertyModule.f(ArrayPrototype, UNSCOPABLES, {\n    configurable: true,\n    value: create(null)\n  });\n}\n\n// add a key to Array.prototype[@@unscopables]\nmodule.exports = function (key) {\n  ArrayPrototype[UNSCOPABLES][key] = true;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar $includes = require('../internals/array-includes').includes;\nvar addToUnscopables = require('../internals/add-to-unscopables');\n\n// `Array.prototype.includes` method\n// https://tc39.es/ecma262/#sec-array.prototype.includes\n$({ target: 'Array', proto: true }, {\n  includes: function includes(el /* , fromIndex = 0 */) {\n    return $includes(this, el, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables('includes');\n", "/**\n * @description pre parse html\n * <AUTHOR>\n */\n\nimport $, { getTagName, DOMElement } from '../utils/dom'\n\n/**\n * pre-prase video ，兼容 V4\n * @param elem elem\n */\nfunction preParse(elem: DOMElement): DOMElement {\n  const $elem = $(elem)\n  let $video = $elem\n\n  const elemTagName = getTagName($elem)\n  if (elemTagName === 'p') {\n    // v4 的 video 或 iframe 是被 p 包裹的\n    const children = $elem.children()\n    if (children.length === 1) {\n      const firstChild = children[0]\n      const firstChildTagName = firstChild.tagName.toLowerCase()\n      if (['iframe', 'video'].includes(firstChildTagName)) {\n        // p 下面包含 iframe 或 video\n        $video = $(firstChild)\n      }\n    }\n  }\n\n  const videoTagName = getTagName($video)\n  if (videoTagName !== 'iframe' && videoTagName !== 'video') return $video[0]\n\n  // 已经符合 V5 格式\n  const $parent = $video.parent()\n  if ($parent.attr('data-w-e-type') === 'video') return $video[0]\n\n  const $container = $(`<div data-w-e-type=\"video\" data-w-e-is-void></div>`)\n  $container.append($video)\n\n  return $container[0]\n}\n\nexport const preParseHtmlConf = {\n  selector: 'iframe,video,p',\n  preParseHtml: preParse,\n}\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nvar bind = uncurryThis(uncurryThis.bind);\n\n// optional / simple context binding\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : bind ? bind(fn, that) : function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n", "var classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) == 'Array';\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof');\nvar getBuiltIn = require('../internals/get-built-in');\nvar inspectSource = require('../internals/inspect-source');\n\nvar noop = function () { /* empty */ };\nvar empty = [];\nvar construct = getBuiltIn('Reflect', 'construct');\nvar constructorRegExp = /^\\s*(?:class|function)\\b/;\nvar exec = uncurryThis(constructorRegExp.exec);\nvar INCORRECT_TO_STRING = !constructorRegExp.exec(noop);\n\nvar isConstructorModern = function (argument) {\n  if (!isCallable(argument)) return false;\n  try {\n    construct(noop, empty, argument);\n    return true;\n  } catch (error) {\n    return false;\n  }\n};\n\nvar isConstructorLegacy = function (argument) {\n  if (!isCallable(argument)) return false;\n  switch (classof(argument)) {\n    case 'AsyncFunction':\n    case 'GeneratorFunction':\n    case 'AsyncGeneratorFunction': return false;\n    // we can't check .prototype since constructors produced by .bind haven't it\n  } return INCORRECT_TO_STRING || !!exec(constructorRegExp, inspectSource(argument));\n};\n\n// `IsConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-isconstructor\nmodule.exports = !construct || fails(function () {\n  var called;\n  return isConstructorModern(isConstructorModern.call)\n    || !isConstructorModern(Object)\n    || !isConstructorModern(function () { called = true; })\n    || called;\n}) ? isConstructorLegacy : isConstructorModern;\n", "var global = require('../internals/global');\nvar isArray = require('../internals/is-array');\nvar isConstructor = require('../internals/is-constructor');\nvar isObject = require('../internals/is-object');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar SPECIES = wellKnownSymbol('species');\nvar Array = global.Array;\n\n// a part of `ArraySpeciesCreate` abstract operation\n// https://tc39.es/ecma262/#sec-arrayspeciescreate\nmodule.exports = function (originalArray) {\n  var C;\n  if (isArray(originalArray)) {\n    C = originalArray.constructor;\n    // cross-realm fallback\n    if (isConstructor(C) && (C === Array || isArray(C.prototype))) C = undefined;\n    else if (isObject(C)) {\n      C = C[SPECIES];\n      if (C === null) C = undefined;\n    }\n  } return C === undefined ? Array : C;\n};\n", "var arraySpeciesConstructor = require('../internals/array-species-constructor');\n\n// `ArraySpeciesCreate` abstract operation\n// https://tc39.es/ecma262/#sec-arrayspeciescreate\nmodule.exports = function (originalArray, length) {\n  return new (arraySpeciesConstructor(originalArray))(length === 0 ? 0 : length);\n};\n", "var bind = require('../internals/function-bind-context');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar IndexedObject = require('../internals/indexed-object');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar arraySpeciesCreate = require('../internals/array-species-create');\n\nvar push = uncurryThis([].push);\n\n// `Array.prototype.{ forEach, map, filter, some, every, find, findIndex, filterReject }` methods implementation\nvar createMethod = function (TYPE) {\n  var IS_MAP = TYPE == 1;\n  var IS_FILTER = TYPE == 2;\n  var IS_SOME = TYPE == 3;\n  var IS_EVERY = TYPE == 4;\n  var IS_FIND_INDEX = TYPE == 6;\n  var IS_FILTER_REJECT = TYPE == 7;\n  var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;\n  return function ($this, callbackfn, that, specificCreate) {\n    var O = toObject($this);\n    var self = IndexedObject(O);\n    var boundFunction = bind(callbackfn, that);\n    var length = lengthOfArrayLike(self);\n    var index = 0;\n    var create = specificCreate || arraySpeciesCreate;\n    var target = IS_MAP ? create($this, length) : IS_FILTER || IS_FILTER_REJECT ? create($this, 0) : undefined;\n    var value, result;\n    for (;length > index; index++) if (NO_HOLES || index in self) {\n      value = self[index];\n      result = boundFunction(value, index, O);\n      if (TYPE) {\n        if (IS_MAP) target[index] = result; // map\n        else if (result) switch (TYPE) {\n          case 3: return true;              // some\n          case 5: return value;             // find\n          case 6: return index;             // findIndex\n          case 2: push(target, value);      // filter\n        } else switch (TYPE) {\n          case 4: return false;             // every\n          case 7: push(target, value);      // filterReject\n        }\n      }\n    }\n    return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.forEach` method\n  // https://tc39.es/ecma262/#sec-array.prototype.foreach\n  forEach: createMethod(0),\n  // `Array.prototype.map` method\n  // https://tc39.es/ecma262/#sec-array.prototype.map\n  map: createMethod(1),\n  // `Array.prototype.filter` method\n  // https://tc39.es/ecma262/#sec-array.prototype.filter\n  filter: createMethod(2),\n  // `Array.prototype.some` method\n  // https://tc39.es/ecma262/#sec-array.prototype.some\n  some: createMethod(3),\n  // `Array.prototype.every` method\n  // https://tc39.es/ecma262/#sec-array.prototype.every\n  every: createMethod(4),\n  // `Array.prototype.find` method\n  // https://tc39.es/ecma262/#sec-array.prototype.find\n  find: createMethod(5),\n  // `Array.prototype.findIndex` method\n  // https://tc39.es/ecma262/#sec-array.prototype.findIndex\n  findIndex: createMethod(6),\n  // `Array.prototype.filterReject` method\n  // https://github.com/tc39/proposal-array-filtering\n  filterReject: createMethod(7)\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar $find = require('../internals/array-iteration').find;\nvar addToUnscopables = require('../internals/add-to-unscopables');\n\nvar FIND = 'find';\nvar SKIPS_HOLES = true;\n\n// Shouldn't skip holes\nif (FIND in []) Array(1)[FIND](function () { SKIPS_HOLES = false; });\n\n// `Array.prototype.find` method\n// https://tc39.es/ecma262/#sec-array.prototype.find\n$({ target: 'Array', proto: true, forced: SKIPS_HOLES }, {\n  find: function find(callbackfn /* , that = undefined */) {\n    return $find(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables(FIND);\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar classof = require('../internals/classof');\n\n// `Object.prototype.toString` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.tostring\nmodule.exports = TO_STRING_TAG_SUPPORT ? {}.toString : function toString() {\n  return '[object ' + classof(this) + ']';\n};\n", "/**\n * @description parse html\n * <AUTHOR>\n */\n\nimport { Descendant } from 'slate'\nimport { IDomEditor } from '@wangeditor/core'\nimport { VideoElement } from './custom-types'\nimport $, { DOMElement } from '../utils/dom'\n\nfunction genVideoElem(\n  src: string,\n  poster: string = '',\n  width = 'auto',\n  height = 'auto'\n): VideoElement {\n  return {\n    type: 'video',\n    src,\n    poster,\n    width,\n    height,\n    children: [{ text: '' }], // void 元素有一个空 text\n  }\n}\n\nfunction parseHtml(elem: DOMElement, children: Descendant[], editor: IDomEditor): VideoElement {\n  const $elem = $(elem)\n  let src = ''\n  let poster = ''\n  let width = 'auto'\n  let height = 'auto'\n\n  // <iframe> 形式\n  const $iframe = $elem.find('iframe')\n  if ($iframe.length > 0) {\n    width = $iframe.attr('width') || 'auto'\n    height = $iframe.attr('height') || 'auto'\n    src = $iframe[0].outerHTML\n    return genVideoElem(src, poster, width, height)\n  }\n\n  // <video> 形式\n  const $video = $elem.find('video')\n  src = $video.attr('src') || ''\n  if (!src) {\n    if ($video.length > 0) {\n      const $source = $video.find('source')\n      src = $source.attr('src') || ''\n    }\n  }\n  width = $video.attr('width') || 'auto'\n  height = $video.attr('height') || 'auto'\n  poster = $video.attr('poster') || ''\n  return genVideoElem(src, poster, width, height)\n}\n\nexport const parseHtmlConf = {\n  selector: 'div[data-w-e-type=\"video\"]',\n  parseElemHtml: parseHtml,\n}\n", "var TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar redefine = require('../internals/redefine');\nvar toString = require('../internals/object-to-string');\n\n// `Object.prototype.toString` method\n// https://tc39.es/ecma262/#sec-object.prototype.tostring\nif (!TO_STRING_TAG_SUPPORT) {\n  redefine(Object.prototype, 'toString', toString, { unsafe: true });\n}\n", "'use strict';\n/* eslint-disable regexp/no-empty-capturing-group, regexp/no-empty-group, regexp/no-lazy-ends -- testing */\n/* eslint-disable regexp/no-useless-quantifier -- testing */\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar regexpFlags = require('../internals/regexp-flags');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar shared = require('../internals/shared');\nvar create = require('../internals/object-create');\nvar getInternalState = require('../internals/internal-state').get;\nvar UNSUPPORTED_DOT_ALL = require('../internals/regexp-unsupported-dot-all');\nvar UNSUPPORTED_NCG = require('../internals/regexp-unsupported-ncg');\n\nvar nativeReplace = shared('native-string-replace', String.prototype.replace);\nvar nativeExec = RegExp.prototype.exec;\nvar patchedExec = nativeExec;\nvar charAt = uncurryThis(''.charAt);\nvar indexOf = uncurryThis(''.indexOf);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n\nvar UPDATES_LAST_INDEX_WRONG = (function () {\n  var re1 = /a/;\n  var re2 = /b*/g;\n  call(nativeExec, re1, 'a');\n  call(nativeExec, re2, 'a');\n  return re1.lastIndex !== 0 || re2.lastIndex !== 0;\n})();\n\nvar UNSUPPORTED_Y = stickyHelpers.BROKEN_CARET;\n\n// nonparticipating capturing group, copied from es5-shim's String#split patch.\nvar NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;\n\nvar PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED || UNSUPPORTED_Y || UNSUPPORTED_DOT_ALL || UNSUPPORTED_NCG;\n\nif (PATCH) {\n  patchedExec = function exec(string) {\n    var re = this;\n    var state = getInternalState(re);\n    var str = toString(string);\n    var raw = state.raw;\n    var result, reCopy, lastIndex, match, i, object, group;\n\n    if (raw) {\n      raw.lastIndex = re.lastIndex;\n      result = call(patchedExec, raw, str);\n      re.lastIndex = raw.lastIndex;\n      return result;\n    }\n\n    var groups = state.groups;\n    var sticky = UNSUPPORTED_Y && re.sticky;\n    var flags = call(regexpFlags, re);\n    var source = re.source;\n    var charsAdded = 0;\n    var strCopy = str;\n\n    if (sticky) {\n      flags = replace(flags, 'y', '');\n      if (indexOf(flags, 'g') === -1) {\n        flags += 'g';\n      }\n\n      strCopy = stringSlice(str, re.lastIndex);\n      // Support anchored sticky behavior.\n      if (re.lastIndex > 0 && (!re.multiline || re.multiline && charAt(str, re.lastIndex - 1) !== '\\n')) {\n        source = '(?: ' + source + ')';\n        strCopy = ' ' + strCopy;\n        charsAdded++;\n      }\n      // ^(? + rx + ) is needed, in combination with some str slicing, to\n      // simulate the 'y' flag.\n      reCopy = new RegExp('^(?:' + source + ')', flags);\n    }\n\n    if (NPCG_INCLUDED) {\n      reCopy = new RegExp('^' + source + '$(?!\\\\s)', flags);\n    }\n    if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;\n\n    match = call(nativeExec, sticky ? reCopy : re, strCopy);\n\n    if (sticky) {\n      if (match) {\n        match.input = stringSlice(match.input, charsAdded);\n        match[0] = stringSlice(match[0], charsAdded);\n        match.index = re.lastIndex;\n        re.lastIndex += match[0].length;\n      } else re.lastIndex = 0;\n    } else if (UPDATES_LAST_INDEX_WRONG && match) {\n      re.lastIndex = re.global ? match.index + match[0].length : lastIndex;\n    }\n    if (NPCG_INCLUDED && match && match.length > 1) {\n      // Fix browsers whose `exec` methods don't consistently return `undefined`\n      // for NPCG, like IE8. NOTE: This doesn' work for /(.?)?/\n      call(nativeReplace, match[0], reCopy, function () {\n        for (i = 1; i < arguments.length - 2; i++) {\n          if (arguments[i] === undefined) match[i] = undefined;\n        }\n      });\n    }\n\n    if (match && groups) {\n      match.groups = object = create(null);\n      for (i = 0; i < groups.length; i++) {\n        group = groups[i];\n        object[group[0]] = match[group[1]];\n      }\n    }\n\n    return match;\n  };\n}\n\nmodule.exports = patchedExec;\n", "'use strict';\nvar anObject = require('../internals/an-object');\n\n// `RegExp.prototype.flags` getter implementation\n// https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\nmodule.exports = function () {\n  var that = anObject(this);\n  var result = '';\n  if (that.global) result += 'g';\n  if (that.ignoreCase) result += 'i';\n  if (that.multiline) result += 'm';\n  if (that.dotAll) result += 's';\n  if (that.unicode) result += 'u';\n  if (that.sticky) result += 'y';\n  return result;\n};\n", "var fails = require('../internals/fails');\nvar global = require('../internals/global');\n\n// babel-minify and Closure Compiler transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError\nvar $RegExp = global.RegExp;\n\nvar UNSUPPORTED_Y = fails(function () {\n  var re = $RegExp('a', 'y');\n  re.lastIndex = 2;\n  return re.exec('abcd') != null;\n});\n\n// UC Browser bug\n// https://github.com/zloirock/core-js/issues/1008\nvar MISSED_STICKY = UNSUPPORTED_Y || fails(function () {\n  return !$RegExp('a', 'y').sticky;\n});\n\nvar BROKEN_CARET = UNSUPPORTED_Y || fails(function () {\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=773687\n  var re = $RegExp('^r', 'gy');\n  re.lastIndex = 2;\n  return re.exec('str') != null;\n});\n\nmodule.exports = {\n  BROKEN_CARET: BROKEN_CARET,\n  MISSED_STICKY: MISSED_STICKY,\n  UNSUPPORTED_Y: UNSUPPORTED_Y\n};\n", "var fails = require('../internals/fails');\nvar global = require('../internals/global');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 's') -> /./s and it causes SyntaxError\nvar $RegExp = global.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('.', 's');\n  return !(re.dotAll && re.exec('\\n') && re.flags === 's');\n});\n", "var fails = require('../internals/fails');\nvar global = require('../internals/global');\n\n// babel-minify and Closure Compiler transpiles RegExp('(?<a>b)', 'g') -> /(?<a>b)/g and it causes SyntaxError\nvar $RegExp = global.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('(?<a>b)', 'g');\n  return re.exec('b').groups.a !== 'b' ||\n    'b'.replace(re, '$<a>c') !== 'bc';\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar exec = require('../internals/regexp-exec');\n\n// `RegExp.prototype.exec` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.exec\n$({ target: 'RegExp', proto: true, forced: /./.exec !== exec }, {\n  exec: exec\n});\n", "var FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (bind ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n", "'use strict';\n// TODO: Remove from `core-js@4` since it's moved to entry points\nrequire('../modules/es.regexp.exec');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar redefine = require('../internals/redefine');\nvar regexpExec = require('../internals/regexp-exec');\nvar fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\nvar SPECIES = wellKnownSymbol('species');\nvar RegExpPrototype = RegExp.prototype;\n\nmodule.exports = function (KEY, exec, FORCED, SHAM) {\n  var SYMBOL = wellKnownSymbol(KEY);\n\n  var DELEGATES_TO_SYMBOL = !fails(function () {\n    // String methods call symbol-named RegEp methods\n    var O = {};\n    O[SYMBOL] = function () { return 7; };\n    return ''[KEY](O) != 7;\n  });\n\n  var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {\n    // Symbol-named RegExp methods call .exec\n    var execCalled = false;\n    var re = /a/;\n\n    if (KEY === 'split') {\n      // We can't use real regex here since it causes deoptimization\n      // and serious performance degradation in V8\n      // https://github.com/zloirock/core-js/issues/306\n      re = {};\n      // RegExp[@@split] doesn't call the regex's exec method, but first creates\n      // a new one. We need to return the patched regex when creating the new one.\n      re.constructor = {};\n      re.constructor[SPECIES] = function () { return re; };\n      re.flags = '';\n      re[SYMBOL] = /./[SYMBOL];\n    }\n\n    re.exec = function () { execCalled = true; return null; };\n\n    re[SYMBOL]('');\n    return !execCalled;\n  });\n\n  if (\n    !DELEGATES_TO_SYMBOL ||\n    !DELEGATES_TO_EXEC ||\n    FORCED\n  ) {\n    var uncurriedNativeRegExpMethod = uncurryThis(/./[SYMBOL]);\n    var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {\n      var uncurriedNativeMethod = uncurryThis(nativeMethod);\n      var $exec = regexp.exec;\n      if ($exec === regexpExec || $exec === RegExpPrototype.exec) {\n        if (DELEGATES_TO_SYMBOL && !forceStringMethod) {\n          // The native String method already delegates to @@method (this\n          // polyfilled function), leasing to infinite recursion.\n          // We avoid it by directly calling the native @@method method.\n          return { done: true, value: uncurriedNativeRegExpMethod(regexp, str, arg2) };\n        }\n        return { done: true, value: uncurriedNativeMethod(str, regexp, arg2) };\n      }\n      return { done: false };\n    });\n\n    redefine(String.prototype, KEY, methods[0]);\n    redefine(RegExpPrototype, SYMBOL, methods[1]);\n  }\n\n  if (SHAM) createNonEnumerableProperty(RegExpPrototype[SYMBOL], 'sham', true);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar charAt = uncurryThis(''.charAt);\nvar charCodeAt = uncurryThis(''.charCodeAt);\nvar stringSlice = uncurryThis(''.slice);\n\nvar createMethod = function (CONVERT_TO_STRING) {\n  return function ($this, pos) {\n    var S = toString(requireObjectCoercible($this));\n    var position = toIntegerOrInfinity(pos);\n    var size = S.length;\n    var first, second;\n    if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;\n    first = charCodeAt(S, position);\n    return first < 0xD800 || first > 0xDBFF || position + 1 === size\n      || (second = charCodeAt(S, position + 1)) < 0xDC00 || second > 0xDFFF\n        ? CONVERT_TO_STRING\n          ? charAt(S, position)\n          : first\n        : CONVERT_TO_STRING\n          ? stringSlice(S, position, position + 2)\n          : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.codePointAt` method\n  // https://tc39.es/ecma262/#sec-string.prototype.codepointat\n  codeAt: createMethod(false),\n  // `String.prototype.at` method\n  // https://github.com/mathiasbynens/String.prototype.at\n  charAt: createMethod(true)\n};\n", "'use strict';\nvar charAt = require('../internals/string-multibyte').charAt;\n\n// `AdvanceStringIndex` abstract operation\n// https://tc39.es/ecma262/#sec-advancestringindex\nmodule.exports = function (S, index, unicode) {\n  return index + (unicode ? charAt(S, index).length : 1);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar floor = Math.floor;\nvar charAt = uncurryThis(''.charAt);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\nvar SUBSTITUTION_SYMBOLS = /\\$([$&'`]|\\d{1,2}|<[^>]*>)/g;\nvar SUBSTITUTION_SYMBOLS_NO_NAMED = /\\$([$&'`]|\\d{1,2})/g;\n\n// `GetSubstitution` abstract operation\n// https://tc39.es/ecma262/#sec-getsubstitution\nmodule.exports = function (matched, str, position, captures, namedCaptures, replacement) {\n  var tailPos = position + matched.length;\n  var m = captures.length;\n  var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;\n  if (namedCaptures !== undefined) {\n    namedCaptures = toObject(namedCaptures);\n    symbols = SUBSTITUTION_SYMBOLS;\n  }\n  return replace(replacement, symbols, function (match, ch) {\n    var capture;\n    switch (charAt(ch, 0)) {\n      case '$': return '$';\n      case '&': return matched;\n      case '`': return stringSlice(str, 0, position);\n      case \"'\": return stringSlice(str, tailPos);\n      case '<':\n        capture = namedCaptures[stringSlice(ch, 1, -1)];\n        break;\n      default: // \\d\\d?\n        var n = +ch;\n        if (n === 0) return match;\n        if (n > m) {\n          var f = floor(n / 10);\n          if (f === 0) return match;\n          if (f <= m) return captures[f - 1] === undefined ? charAt(ch, 1) : captures[f - 1] + charAt(ch, 1);\n          return match;\n        }\n        capture = captures[n - 1];\n    }\n    return capture === undefined ? '' : capture;\n  });\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof-raw');\nvar regexpExec = require('../internals/regexp-exec');\n\nvar TypeError = global.TypeError;\n\n// `RegExpExec` abstract operation\n// https://tc39.es/ecma262/#sec-regexpexec\nmodule.exports = function (R, S) {\n  var exec = R.exec;\n  if (isCallable(exec)) {\n    var result = call(exec, R, S);\n    if (result !== null) anObject(result);\n    return result;\n  }\n  if (classof(R) === 'RegExp') return call(regexpExec, R, S);\n  throw TypeError('RegExp#exec called on incompatible receiver');\n};\n", "'use strict';\nvar apply = require('../internals/function-apply');\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fixRegExpWellKnownSymbolLogic = require('../internals/fix-regexp-well-known-symbol-logic');\nvar fails = require('../internals/fails');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar advanceStringIndex = require('../internals/advance-string-index');\nvar getMethod = require('../internals/get-method');\nvar getSubstitution = require('../internals/get-substitution');\nvar regExpExec = require('../internals/regexp-exec-abstract');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar REPLACE = wellKnownSymbol('replace');\nvar max = Math.max;\nvar min = Math.min;\nvar concat = uncurryThis([].concat);\nvar push = uncurryThis([].push);\nvar stringIndexOf = uncurryThis(''.indexOf);\nvar stringSlice = uncurryThis(''.slice);\n\nvar maybeToString = function (it) {\n  return it === undefined ? it : String(it);\n};\n\n// IE <= 11 replaces $0 with the whole match, as if it was $&\n// https://stackoverflow.com/questions/6024666/getting-ie-to-replace-a-regex-with-the-literal-string-0\nvar REPLACE_KEEPS_$0 = (function () {\n  // eslint-disable-next-line regexp/prefer-escape-replacement-dollar-char -- required for testing\n  return 'a'.replace(/./, '$0') === '$0';\n})();\n\n// Safari <= 13.0.3(?) substitutes nth capture where n>m with an empty string\nvar REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE = (function () {\n  if (/./[REPLACE]) {\n    return /./[REPLACE]('a', '$0') === '';\n  }\n  return false;\n})();\n\nvar REPLACE_SUPPORTS_NAMED_GROUPS = !fails(function () {\n  var re = /./;\n  re.exec = function () {\n    var result = [];\n    result.groups = { a: '7' };\n    return result;\n  };\n  // eslint-disable-next-line regexp/no-useless-dollar-replacements -- false positive\n  return ''.replace(re, '$<a>') !== '7';\n});\n\n// @@replace logic\nfixRegExpWellKnownSymbolLogic('replace', function (_, nativeReplace, maybeCallNative) {\n  var UNSAFE_SUBSTITUTE = REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE ? '$' : '$0';\n\n  return [\n    // `String.prototype.replace` method\n    // https://tc39.es/ecma262/#sec-string.prototype.replace\n    function replace(searchValue, replaceValue) {\n      var O = requireObjectCoercible(this);\n      var replacer = searchValue == undefined ? undefined : getMethod(searchValue, REPLACE);\n      return replacer\n        ? call(replacer, searchValue, O, replaceValue)\n        : call(nativeReplace, toString(O), searchValue, replaceValue);\n    },\n    // `RegExp.prototype[@@replace]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@replace\n    function (string, replaceValue) {\n      var rx = anObject(this);\n      var S = toString(string);\n\n      if (\n        typeof replaceValue == 'string' &&\n        stringIndexOf(replaceValue, UNSAFE_SUBSTITUTE) === -1 &&\n        stringIndexOf(replaceValue, '$<') === -1\n      ) {\n        var res = maybeCallNative(nativeReplace, rx, S, replaceValue);\n        if (res.done) return res.value;\n      }\n\n      var functionalReplace = isCallable(replaceValue);\n      if (!functionalReplace) replaceValue = toString(replaceValue);\n\n      var global = rx.global;\n      if (global) {\n        var fullUnicode = rx.unicode;\n        rx.lastIndex = 0;\n      }\n      var results = [];\n      while (true) {\n        var result = regExpExec(rx, S);\n        if (result === null) break;\n\n        push(results, result);\n        if (!global) break;\n\n        var matchStr = toString(result[0]);\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n      }\n\n      var accumulatedResult = '';\n      var nextSourcePosition = 0;\n      for (var i = 0; i < results.length; i++) {\n        result = results[i];\n\n        var matched = toString(result[0]);\n        var position = max(min(toIntegerOrInfinity(result.index), S.length), 0);\n        var captures = [];\n        // NOTE: This is equivalent to\n        //   captures = result.slice(1).map(maybeToString)\n        // but for some reason `nativeSlice.call(result, 1, result.length)` (called in\n        // the slice polyfill when slicing native arrays) \"doesn't work\" in safari 9 and\n        // causes a crash (https://pastebin.com/N21QzeQA) when trying to debug it.\n        for (var j = 1; j < result.length; j++) push(captures, maybeToString(result[j]));\n        var namedCaptures = result.groups;\n        if (functionalReplace) {\n          var replacerArgs = concat([matched], captures, position, S);\n          if (namedCaptures !== undefined) push(replacerArgs, namedCaptures);\n          var replacement = toString(apply(replaceValue, undefined, replacerArgs));\n        } else {\n          replacement = getSubstitution(matched, S, position, captures, namedCaptures, replaceValue);\n        }\n        if (position >= nextSourcePosition) {\n          accumulatedResult += stringSlice(S, nextSourcePosition, position) + replacement;\n          nextSourcePosition = position + matched.length;\n        }\n      }\n      return accumulatedResult + stringSlice(S, nextSourcePosition);\n    }\n  ];\n}, !REPLACE_SUPPORTS_NAMED_GROUPS || !REPLACE_KEEPS_$0 || REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE);\n", "/**\n * @description 工具函数\n * <AUTHOR>\n */\n\nimport { nanoid } from 'nanoid'\n\n/**\n * 获取随机数字符串\n * @param prefix 前缀\n * @returns 随机数字符串\n */\nexport function genRandomStr(prefix: string = 'r'): string {\n  return `${prefix}-${nanoid()}`\n}\n\nexport function replaceSymbols(str: string) {\n  return str.replace(/</g, '&lt;').replace(/>/g, '&gt;')\n}\n", "/**\n * @description icon svg\n * <AUTHOR>\n */\n\n/**\n * 【注意】svg 字符串的长度 ，否则会导致代码体积过大\n * 尽量选择 https://www.iconfont.cn/collections/detail?spm=a313x.7781069.0.da5a778a4&cid=20293\n * 找不到再从 iconfont.com 搜索\n */\n\n// 视频\nexport const VIDEO_SVG =\n  '<svg viewBox=\"0 0 1024 1024\"><path d=\"M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z\"></path></svg>'\n\n// 上传视频\nexport const UPLOAD_VIDEO_SVG =\n  '<svg viewBox=\"0 0 1056 1024\"><path d=\"M805.902261 521.819882a251.441452 251.441452 0 0 0-251.011972 246.600033 251.051015 251.051015 0 1 0 502.023944 8.823877 253.237463 253.237463 0 0 0-251.011972-255.42391z m59.463561 240.001647v129.898403h-116.701631v-129.898403h-44.041298l101.279368-103.504859 101.279368 103.504859z\" p-id=\"6802\"></path><path d=\"M788.254507 0.000781H99.094092A98.663439 98.663439 0 0 0 0.001171 99.093701v590.067495a98.663439 98.663439 0 0 0 99.092921 99.092921h411.7549a266.434235 266.434235 0 0 1-2.186448-41.815807 275.843767 275.843767 0 0 1 275.180024-270.729042 270.650955 270.650955 0 0 1 103.504859 19.834201V99.093701A101.51363 101.51363 0 0 0 788.254507 0.000781zM295.054441 640.747004V147.507894l394.146189 246.600033z\"></path></svg>'\n\n// // 垃圾桶（删除）\n// export const TRASH_SVG =\n//   '<svg viewBox=\"0 0 1024 1024\"><path d=\"M826.8032 356.5312c-19.328 0-36.3776 15.6928-36.3776 35.0464v524.2624c0 19.328-16 34.56-35.328 34.56H264.9344c-19.328 0-35.5072-15.3088-35.5072-34.56V390.0416c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.6928-33.5104 35.0464V915.712c0 57.9328 44.6208 108.288 102.528 108.288H755.2c57.9328 0 108.0832-50.4576 108.0832-108.288V391.4752c-0.1024-19.2512-17.1264-34.944-36.48-34.944z\" p-id=\"9577\"></path><path d=\"M437.1712 775.7568V390.6048c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.616-33.5104 35.0464v385.152c0 19.328 14.1568 35.0464 33.5104 35.0464s33.5104-15.7184 33.5104-35.0464zM649.7024 775.7568V390.6048c0-19.328-17.0496-35.0464-36.3776-35.0464s-36.3776 15.616-36.3776 35.0464v385.152c0 19.328 17.0496 35.0464 36.3776 35.0464s36.3776-15.7184 36.3776-35.0464zM965.0432 217.0368h-174.6176V145.5104c0-57.9328-47.2064-101.76-104.6528-101.76h-350.976c-57.8304 0-105.3952 43.8528-105.3952 101.76v71.5264H54.784c-19.4304 0-35.0464 14.1568-35.0464 33.5104 0 19.328 15.616 33.5104 35.0464 33.5104h910.3616c19.328 0 35.0464-14.1568 35.0464-33.5104 0-19.3536-15.6928-33.5104-35.1488-33.5104z m-247.3728 0H297.3952V145.5104c0-19.328 18.2016-34.7648 37.4272-34.7648h350.976c19.1488 0 31.872 15.1296 31.872 34.7648v71.5264z\"></path></svg>'\n", "var global = require('../internals/global');\n\nmodule.exports = global.Promise;\n", "var redefine = require('../internals/redefine');\n\nmodule.exports = function (target, src, options) {\n  for (var key in src) redefine(target, key, src[key], options);\n  return target;\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar String = global.String;\nvar TypeError = global.TypeError;\n\nmodule.exports = function (argument) {\n  if (typeof argument == 'object' || isCallable(argument)) return argument;\n  throw TypeError(\"Can't set \" + String(argument) + ' as a prototype');\n};\n", "/* eslint-disable no-proto -- safe */\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar anObject = require('../internals/an-object');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    setter = uncurryThis(Object.getOwnPropertyDescriptor(Object.prototype, '__proto__').set);\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "var defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (it, TAG, STATIC) {\n  if (it && !hasOwn(it = STATIC ? it : it.prototype, TO_STRING_TAG)) {\n    defineProperty(it, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar definePropertyModule = require('../internals/object-define-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar DESCRIPTORS = require('../internals/descriptors');\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (CONSTRUCTOR_NAME) {\n  var Constructor = getBuiltIn(CONSTRUCTOR_NAME);\n  var defineProperty = definePropertyModule.f;\n\n  if (DESCRIPTORS && Constructor && !Constructor[SPECIES]) {\n    defineProperty(Constructor, SPECIES, {\n      configurable: true,\n      get: function () { return this; }\n    });\n  }\n};\n", "var global = require('../internals/global');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\n\nvar TypeError = global.TypeError;\n\nmodule.exports = function (it, Prototype) {\n  if (isPrototypeOf(Prototype, it)) return it;\n  throw TypeError('Incorrect invocation');\n};\n", "module.exports = {};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayPrototype = Array.prototype;\n\n// check on default Array iterator\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);\n};\n", "var classof = require('../internals/classof');\nvar getMethod = require('../internals/get-method');\nvar Iterators = require('../internals/iterators');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\n\nmodule.exports = function (it) {\n  if (it != undefined) return getMethod(it, ITERATOR)\n    || getMethod(it, '@@iterator')\n    || Iterators[classof(it)];\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar tryToString = require('../internals/try-to-string');\nvar getIteratorMethod = require('../internals/get-iterator-method');\n\nvar TypeError = global.TypeError;\n\nmodule.exports = function (argument, usingIterator) {\n  var iteratorMethod = arguments.length < 2 ? getIteratorMethod(argument) : usingIterator;\n  if (aCallable(iteratorMethod)) return anObject(call(iteratorMethod, argument));\n  throw TypeError(tryToString(argument) + ' is not iterable');\n};\n", "var call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar getMethod = require('../internals/get-method');\n\nmodule.exports = function (iterator, kind, value) {\n  var innerResult, innerError;\n  anObject(iterator);\n  try {\n    innerResult = getMethod(iterator, 'return');\n    if (!innerResult) {\n      if (kind === 'throw') throw value;\n      return value;\n    }\n    innerResult = call(innerResult, iterator);\n  } catch (error) {\n    innerError = true;\n    innerResult = error;\n  }\n  if (kind === 'throw') throw value;\n  if (innerError) throw innerResult;\n  anObject(innerResult);\n  return value;\n};\n", "var global = require('../internals/global');\nvar bind = require('../internals/function-bind-context');\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar tryToString = require('../internals/try-to-string');\nvar isArrayIteratorMethod = require('../internals/is-array-iterator-method');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar getIterator = require('../internals/get-iterator');\nvar getIteratorMethod = require('../internals/get-iterator-method');\nvar iteratorClose = require('../internals/iterator-close');\n\nvar TypeError = global.TypeError;\n\nvar Result = function (stopped, result) {\n  this.stopped = stopped;\n  this.result = result;\n};\n\nvar ResultPrototype = Result.prototype;\n\nmodule.exports = function (iterable, unboundFunction, options) {\n  var that = options && options.that;\n  var AS_ENTRIES = !!(options && options.AS_ENTRIES);\n  var IS_ITERATOR = !!(options && options.IS_ITERATOR);\n  var INTERRUPTED = !!(options && options.INTERRUPTED);\n  var fn = bind(unboundFunction, that);\n  var iterator, iterFn, index, length, result, next, step;\n\n  var stop = function (condition) {\n    if (iterator) iteratorClose(iterator, 'normal', condition);\n    return new Result(true, condition);\n  };\n\n  var callFn = function (value) {\n    if (AS_ENTRIES) {\n      anObject(value);\n      return INTERRUPTED ? fn(value[0], value[1], stop) : fn(value[0], value[1]);\n    } return INTERRUPTED ? fn(value, stop) : fn(value);\n  };\n\n  if (IS_ITERATOR) {\n    iterator = iterable;\n  } else {\n    iterFn = getIteratorMethod(iterable);\n    if (!iterFn) throw TypeError(tryToString(iterable) + ' is not iterable');\n    // optimisation for array iterators\n    if (isArrayIteratorMethod(iterFn)) {\n      for (index = 0, length = lengthOfArrayLike(iterable); length > index; index++) {\n        result = callFn(iterable[index]);\n        if (result && isPrototypeOf(ResultPrototype, result)) return result;\n      } return new Result(false);\n    }\n    iterator = getIterator(iterable, iterFn);\n  }\n\n  next = iterator.next;\n  while (!(step = call(next, iterator)).done) {\n    try {\n      result = callFn(step.value);\n    } catch (error) {\n      iteratorClose(iterator, 'throw', error);\n    }\n    if (typeof result == 'object' && result && isPrototypeOf(ResultPrototype, result)) return result;\n  } return new Result(false);\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var called = 0;\n  var iteratorWithReturn = {\n    next: function () {\n      return { done: !!called++ };\n    },\n    'return': function () {\n      SAFE_CLOSING = true;\n    }\n  };\n  iteratorWithReturn[ITERATOR] = function () {\n    return this;\n  };\n  // eslint-disable-next-line es/no-array-from, no-throw-literal -- required for testing\n  Array.from(iteratorWithReturn, function () { throw 2; });\n} catch (error) { /* empty */ }\n\nmodule.exports = function (exec, SKIP_CLOSING) {\n  if (!SKIP_CLOSING && !SAFE_CLOSING) return false;\n  var ITERATION_SUPPORT = false;\n  try {\n    var object = {};\n    object[ITERATOR] = function () {\n      return {\n        next: function () {\n          return { done: ITERATION_SUPPORT = true };\n        }\n      };\n    };\n    exec(object);\n  } catch (error) { /* empty */ }\n  return ITERATION_SUPPORT;\n};\n", "var global = require('../internals/global');\nvar apply = require('../internals/function-apply');\nvar bind = require('../internals/function-bind-context');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar fails = require('../internals/fails');\nvar html = require('../internals/html');\nvar arraySlice = require('../internals/array-slice');\nvar createElement = require('../internals/document-create-element');\nvar IS_IOS = require('../internals/engine-is-ios');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar set = global.setImmediate;\nvar clear = global.clearImmediate;\nvar process = global.process;\nvar Dispatch = global.Dispatch;\nvar Function = global.Function;\nvar MessageChannel = global.MessageChannel;\nvar String = global.String;\nvar counter = 0;\nvar queue = {};\nvar ONREADYSTATECHANGE = 'onreadystatechange';\nvar location, defer, channel, port;\n\ntry {\n  // <PERSON><PERSON> throws a ReferenceError on `location` access without `--location` flag\n  location = global.location;\n} catch (error) { /* empty */ }\n\nvar run = function (id) {\n  if (hasOwn(queue, id)) {\n    var fn = queue[id];\n    delete queue[id];\n    fn();\n  }\n};\n\nvar runner = function (id) {\n  return function () {\n    run(id);\n  };\n};\n\nvar listener = function (event) {\n  run(event.data);\n};\n\nvar post = function (id) {\n  // old engines have not location.origin\n  global.postMessage(String(id), location.protocol + '//' + location.host);\n};\n\n// Node.js 0.9+ & IE10+ has setImmediate, otherwise:\nif (!set || !clear) {\n  set = function setImmediate(fn) {\n    var args = arraySlice(arguments, 1);\n    queue[++counter] = function () {\n      apply(isCallable(fn) ? fn : Function(fn), undefined, args);\n    };\n    defer(counter);\n    return counter;\n  };\n  clear = function clearImmediate(id) {\n    delete queue[id];\n  };\n  // Node.js 0.8-\n  if (IS_NODE) {\n    defer = function (id) {\n      process.nextTick(runner(id));\n    };\n  // Sphere (JS game engine) Dispatch API\n  } else if (Dispatch && Dispatch.now) {\n    defer = function (id) {\n      Dispatch.now(runner(id));\n    };\n  // Browsers with MessageChannel, includes WebWorkers\n  // except iOS - https://github.com/zloirock/core-js/issues/624\n  } else if (MessageChannel && !IS_IOS) {\n    channel = new MessageChannel();\n    port = channel.port2;\n    channel.port1.onmessage = listener;\n    defer = bind(port.postMessage, port);\n  // Browsers with postMessage, skip WebWorkers\n  // IE8 has postMessage, but it's sync & typeof its postMessage is 'object'\n  } else if (\n    global.addEventListener &&\n    isCallable(global.postMessage) &&\n    !global.importScripts &&\n    location && location.protocol !== 'file:' &&\n    !fails(post)\n  ) {\n    defer = post;\n    global.addEventListener('message', listener, false);\n  // IE8-\n  } else if (ONREADYSTATECHANGE in createElement('script')) {\n    defer = function (id) {\n      html.appendChild(createElement('script'))[ONREADYSTATECHANGE] = function () {\n        html.removeChild(this);\n        run(id);\n      };\n    };\n  // Rest old browsers\n  } else {\n    defer = function (id) {\n      setTimeout(runner(id), 0);\n    };\n  }\n}\n\nmodule.exports = {\n  set: set,\n  clear: clear\n};\n", "var global = require('../internals/global');\nvar isConstructor = require('../internals/is-constructor');\nvar tryToString = require('../internals/try-to-string');\n\nvar TypeError = global.TypeError;\n\n// `Assert: IsConstructor(argument) is true`\nmodule.exports = function (argument) {\n  if (isConstructor(argument)) return argument;\n  throw TypeError(tryToString(argument) + ' is not a constructor');\n};\n", "var anObject = require('../internals/an-object');\nvar aConstructor = require('../internals/a-constructor');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar SPECIES = wellKnownSymbol('species');\n\n// `SpeciesConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-speciesconstructor\nmodule.exports = function (O, defaultConstructor) {\n  var C = anObject(O).constructor;\n  var S;\n  return C === undefined || (S = anObject(C)[SPECIES]) == undefined ? defaultConstructor : aConstructor(S);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis([].slice);\n", "var userAgent = require('../internals/engine-user-agent');\n\nmodule.exports = /(?:ipad|iphone|ipod).*applewebkit/i.test(userAgent);\n", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "var global = require('../internals/global');\nvar bind = require('../internals/function-bind-context');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar macrotask = require('../internals/task').set;\nvar IS_IOS = require('../internals/engine-is-ios');\nvar IS_IOS_PEBBLE = require('../internals/engine-is-ios-pebble');\nvar IS_WEBOS_WEBKIT = require('../internals/engine-is-webos-webkit');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar MutationObserver = global.MutationObserver || global.WebKitMutationObserver;\nvar document = global.document;\nvar process = global.process;\nvar Promise = global.Promise;\n// Node.js 11 shows ExperimentalWarning on getting `queueMicrotask`\nvar queueMicrotaskDescriptor = getOwnPropertyDescriptor(global, 'queueMicrotask');\nvar queueMicrotask = queueMicrotaskDescriptor && queueMicrotaskDescriptor.value;\n\nvar flush, head, last, notify, toggle, node, promise, then;\n\n// modern engines have queueMicrotask method\nif (!queueMicrotask) {\n  flush = function () {\n    var parent, fn;\n    if (IS_NODE && (parent = process.domain)) parent.exit();\n    while (head) {\n      fn = head.fn;\n      head = head.next;\n      try {\n        fn();\n      } catch (error) {\n        if (head) notify();\n        else last = undefined;\n        throw error;\n      }\n    } last = undefined;\n    if (parent) parent.enter();\n  };\n\n  // browsers with MutationObserver, except iOS - https://github.com/zloirock/core-js/issues/339\n  // also except WebOS Webkit https://github.com/zloirock/core-js/issues/898\n  if (!IS_IOS && !IS_NODE && !IS_WEBOS_WEBKIT && MutationObserver && document) {\n    toggle = true;\n    node = document.createTextNode('');\n    new MutationObserver(flush).observe(node, { characterData: true });\n    notify = function () {\n      node.data = toggle = !toggle;\n    };\n  // environments with maybe non-completely correct, but existent Promise\n  } else if (!IS_IOS_PEBBLE && Promise && Promise.resolve) {\n    // Promise.resolve without an argument throws an error in LG WebOS 2\n    promise = Promise.resolve(undefined);\n    // workaround of WebKit ~ iOS Safari 10.1 bug\n    promise.constructor = Promise;\n    then = bind(promise.then, promise);\n    notify = function () {\n      then(flush);\n    };\n  // Node.js without promises\n  } else if (IS_NODE) {\n    notify = function () {\n      process.nextTick(flush);\n    };\n  // for other environments - macrotask based on:\n  // - setImmediate\n  // - MessageChannel\n  // - window.postMessag\n  // - onreadystatechange\n  // - setTimeout\n  } else {\n    // strange IE + webpack dev server bug - use .bind(global)\n    macrotask = bind(macrotask, global);\n    notify = function () {\n      macrotask(flush);\n    };\n  }\n}\n\nmodule.exports = queueMicrotask || function (fn) {\n  var task = { fn: fn, next: undefined };\n  if (last) last.next = task;\n  if (!head) {\n    head = task;\n    notify();\n  } last = task;\n};\n", "var userAgent = require('../internals/engine-user-agent');\nvar global = require('../internals/global');\n\nmodule.exports = /ipad|iphone|ipod/i.test(userAgent) && global.Pebble !== undefined;\n", "var userAgent = require('../internals/engine-user-agent');\n\nmodule.exports = /web0s(?!.*chrome)/i.test(userAgent);\n", "'use strict';\nvar $ = require('../internals/export');\nvar IS_PURE = require('../internals/is-pure');\nvar global = require('../internals/global');\nvar getBuiltIn = require('../internals/get-built-in');\nvar call = require('../internals/function-call');\nvar NativePromise = require('../internals/native-promise-constructor');\nvar redefine = require('../internals/redefine');\nvar redefineAll = require('../internals/redefine-all');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar setSpecies = require('../internals/set-species');\nvar aCallable = require('../internals/a-callable');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar anInstance = require('../internals/an-instance');\nvar inspectSource = require('../internals/inspect-source');\nvar iterate = require('../internals/iterate');\nvar checkCorrectnessOfIteration = require('../internals/check-correctness-of-iteration');\nvar speciesConstructor = require('../internals/species-constructor');\nvar task = require('../internals/task').set;\nvar microtask = require('../internals/microtask');\nvar promiseResolve = require('../internals/promise-resolve');\nvar hostReportErrors = require('../internals/host-report-errors');\nvar newPromiseCapabilityModule = require('../internals/new-promise-capability');\nvar perform = require('../internals/perform');\nvar InternalStateModule = require('../internals/internal-state');\nvar isForced = require('../internals/is-forced');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_BROWSER = require('../internals/engine-is-browser');\nvar IS_NODE = require('../internals/engine-is-node');\nvar V8_VERSION = require('../internals/engine-v8-version');\n\nvar SPECIES = wellKnownSymbol('species');\nvar PROMISE = 'Promise';\n\nvar getInternalState = InternalStateModule.getterFor(PROMISE);\nvar setInternalState = InternalStateModule.set;\nvar getInternalPromiseState = InternalStateModule.getterFor(PROMISE);\nvar NativePromisePrototype = NativePromise && NativePromise.prototype;\nvar PromiseConstructor = NativePromise;\nvar PromisePrototype = NativePromisePrototype;\nvar TypeError = global.TypeError;\nvar document = global.document;\nvar process = global.process;\nvar newPromiseCapability = newPromiseCapabilityModule.f;\nvar newGenericPromiseCapability = newPromiseCapability;\n\nvar DISPATCH_EVENT = !!(document && document.createEvent && global.dispatchEvent);\nvar NATIVE_REJECTION_EVENT = isCallable(global.PromiseRejectionEvent);\nvar UNHANDLED_REJECTION = 'unhandledrejection';\nvar REJECTION_HANDLED = 'rejectionhandled';\nvar PENDING = 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\nvar HANDLED = 1;\nvar UNHANDLED = 2;\nvar SUBCLASSING = false;\n\nvar Internal, OwnPromiseCapability, PromiseWrapper, nativeThen;\n\nvar FORCED = isForced(PROMISE, function () {\n  var PROMISE_CONSTRUCTOR_SOURCE = inspectSource(PromiseConstructor);\n  var GLOBAL_CORE_JS_PROMISE = PROMISE_CONSTRUCTOR_SOURCE !== String(PromiseConstructor);\n  // V8 6.6 (Node 10 and Chrome 66) have a bug with resolving custom thenables\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=830565\n  // We can't detect it synchronously, so just check versions\n  if (!GLOBAL_CORE_JS_PROMISE && V8_VERSION === 66) return true;\n  // We need Promise#finally in the pure version for preventing prototype pollution\n  if (IS_PURE && !PromisePrototype['finally']) return true;\n  // We can't use @@species feature detection in V8 since it causes\n  // deoptimization and performance degradation\n  // https://github.com/zloirock/core-js/issues/679\n  if (V8_VERSION >= 51 && /native code/.test(PROMISE_CONSTRUCTOR_SOURCE)) return false;\n  // Detect correctness of subclassing with @@species support\n  var promise = new PromiseConstructor(function (resolve) { resolve(1); });\n  var FakePromise = function (exec) {\n    exec(function () { /* empty */ }, function () { /* empty */ });\n  };\n  var constructor = promise.constructor = {};\n  constructor[SPECIES] = FakePromise;\n  SUBCLASSING = promise.then(function () { /* empty */ }) instanceof FakePromise;\n  if (!SUBCLASSING) return true;\n  // Unhandled rejections tracking support, NodeJS Promise without it fails @@species test\n  return !GLOBAL_CORE_JS_PROMISE && IS_BROWSER && !NATIVE_REJECTION_EVENT;\n});\n\nvar INCORRECT_ITERATION = FORCED || !checkCorrectnessOfIteration(function (iterable) {\n  PromiseConstructor.all(iterable)['catch'](function () { /* empty */ });\n});\n\n// helpers\nvar isThenable = function (it) {\n  var then;\n  return isObject(it) && isCallable(then = it.then) ? then : false;\n};\n\nvar notify = function (state, isReject) {\n  if (state.notified) return;\n  state.notified = true;\n  var chain = state.reactions;\n  microtask(function () {\n    var value = state.value;\n    var ok = state.state == FULFILLED;\n    var index = 0;\n    // variable length - can't use forEach\n    while (chain.length > index) {\n      var reaction = chain[index++];\n      var handler = ok ? reaction.ok : reaction.fail;\n      var resolve = reaction.resolve;\n      var reject = reaction.reject;\n      var domain = reaction.domain;\n      var result, then, exited;\n      try {\n        if (handler) {\n          if (!ok) {\n            if (state.rejection === UNHANDLED) onHandleUnhandled(state);\n            state.rejection = HANDLED;\n          }\n          if (handler === true) result = value;\n          else {\n            if (domain) domain.enter();\n            result = handler(value); // can throw\n            if (domain) {\n              domain.exit();\n              exited = true;\n            }\n          }\n          if (result === reaction.promise) {\n            reject(TypeError('Promise-chain cycle'));\n          } else if (then = isThenable(result)) {\n            call(then, result, resolve, reject);\n          } else resolve(result);\n        } else reject(value);\n      } catch (error) {\n        if (domain && !exited) domain.exit();\n        reject(error);\n      }\n    }\n    state.reactions = [];\n    state.notified = false;\n    if (isReject && !state.rejection) onUnhandled(state);\n  });\n};\n\nvar dispatchEvent = function (name, promise, reason) {\n  var event, handler;\n  if (DISPATCH_EVENT) {\n    event = document.createEvent('Event');\n    event.promise = promise;\n    event.reason = reason;\n    event.initEvent(name, false, true);\n    global.dispatchEvent(event);\n  } else event = { promise: promise, reason: reason };\n  if (!NATIVE_REJECTION_EVENT && (handler = global['on' + name])) handler(event);\n  else if (name === UNHANDLED_REJECTION) hostReportErrors('Unhandled promise rejection', reason);\n};\n\nvar onUnhandled = function (state) {\n  call(task, global, function () {\n    var promise = state.facade;\n    var value = state.value;\n    var IS_UNHANDLED = isUnhandled(state);\n    var result;\n    if (IS_UNHANDLED) {\n      result = perform(function () {\n        if (IS_NODE) {\n          process.emit('unhandledRejection', value, promise);\n        } else dispatchEvent(UNHANDLED_REJECTION, promise, value);\n      });\n      // Browsers should not trigger `rejectionHandled` event if it was handled here, NodeJS - should\n      state.rejection = IS_NODE || isUnhandled(state) ? UNHANDLED : HANDLED;\n      if (result.error) throw result.value;\n    }\n  });\n};\n\nvar isUnhandled = function (state) {\n  return state.rejection !== HANDLED && !state.parent;\n};\n\nvar onHandleUnhandled = function (state) {\n  call(task, global, function () {\n    var promise = state.facade;\n    if (IS_NODE) {\n      process.emit('rejectionHandled', promise);\n    } else dispatchEvent(REJECTION_HANDLED, promise, state.value);\n  });\n};\n\nvar bind = function (fn, state, unwrap) {\n  return function (value) {\n    fn(state, value, unwrap);\n  };\n};\n\nvar internalReject = function (state, value, unwrap) {\n  if (state.done) return;\n  state.done = true;\n  if (unwrap) state = unwrap;\n  state.value = value;\n  state.state = REJECTED;\n  notify(state, true);\n};\n\nvar internalResolve = function (state, value, unwrap) {\n  if (state.done) return;\n  state.done = true;\n  if (unwrap) state = unwrap;\n  try {\n    if (state.facade === value) throw TypeError(\"Promise can't be resolved itself\");\n    var then = isThenable(value);\n    if (then) {\n      microtask(function () {\n        var wrapper = { done: false };\n        try {\n          call(then, value,\n            bind(internalResolve, wrapper, state),\n            bind(internalReject, wrapper, state)\n          );\n        } catch (error) {\n          internalReject(wrapper, error, state);\n        }\n      });\n    } else {\n      state.value = value;\n      state.state = FULFILLED;\n      notify(state, false);\n    }\n  } catch (error) {\n    internalReject({ done: false }, error, state);\n  }\n};\n\n// constructor polyfill\nif (FORCED) {\n  // 25.4.3.1 Promise(executor)\n  PromiseConstructor = function Promise(executor) {\n    anInstance(this, PromisePrototype);\n    aCallable(executor);\n    call(Internal, this);\n    var state = getInternalState(this);\n    try {\n      executor(bind(internalResolve, state), bind(internalReject, state));\n    } catch (error) {\n      internalReject(state, error);\n    }\n  };\n  PromisePrototype = PromiseConstructor.prototype;\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  Internal = function Promise(executor) {\n    setInternalState(this, {\n      type: PROMISE,\n      done: false,\n      notified: false,\n      parent: false,\n      reactions: [],\n      rejection: false,\n      state: PENDING,\n      value: undefined\n    });\n  };\n  Internal.prototype = redefineAll(PromisePrototype, {\n    // `Promise.prototype.then` method\n    // https://tc39.es/ecma262/#sec-promise.prototype.then\n    then: function then(onFulfilled, onRejected) {\n      var state = getInternalPromiseState(this);\n      var reactions = state.reactions;\n      var reaction = newPromiseCapability(speciesConstructor(this, PromiseConstructor));\n      reaction.ok = isCallable(onFulfilled) ? onFulfilled : true;\n      reaction.fail = isCallable(onRejected) && onRejected;\n      reaction.domain = IS_NODE ? process.domain : undefined;\n      state.parent = true;\n      reactions[reactions.length] = reaction;\n      if (state.state != PENDING) notify(state, false);\n      return reaction.promise;\n    },\n    // `Promise.prototype.catch` method\n    // https://tc39.es/ecma262/#sec-promise.prototype.catch\n    'catch': function (onRejected) {\n      return this.then(undefined, onRejected);\n    }\n  });\n  OwnPromiseCapability = function () {\n    var promise = new Internal();\n    var state = getInternalState(promise);\n    this.promise = promise;\n    this.resolve = bind(internalResolve, state);\n    this.reject = bind(internalReject, state);\n  };\n  newPromiseCapabilityModule.f = newPromiseCapability = function (C) {\n    return C === PromiseConstructor || C === PromiseWrapper\n      ? new OwnPromiseCapability(C)\n      : newGenericPromiseCapability(C);\n  };\n\n  if (!IS_PURE && isCallable(NativePromise) && NativePromisePrototype !== Object.prototype) {\n    nativeThen = NativePromisePrototype.then;\n\n    if (!SUBCLASSING) {\n      // make `Promise#then` return a polyfilled `Promise` for native promise-based APIs\n      redefine(NativePromisePrototype, 'then', function then(onFulfilled, onRejected) {\n        var that = this;\n        return new PromiseConstructor(function (resolve, reject) {\n          call(nativeThen, that, resolve, reject);\n        }).then(onFulfilled, onRejected);\n      // https://github.com/zloirock/core-js/issues/640\n      }, { unsafe: true });\n\n      // makes sure that native promise-based APIs `Promise#catch` properly works with patched `Promise#then`\n      redefine(NativePromisePrototype, 'catch', PromisePrototype['catch'], { unsafe: true });\n    }\n\n    // make `.constructor === Promise` work for native promise-based APIs\n    try {\n      delete NativePromisePrototype.constructor;\n    } catch (error) { /* empty */ }\n\n    // make `instanceof Promise` work for native promise-based APIs\n    if (setPrototypeOf) {\n      setPrototypeOf(NativePromisePrototype, PromisePrototype);\n    }\n  }\n}\n\n$({ global: true, wrap: true, forced: FORCED }, {\n  Promise: PromiseConstructor\n});\n\nsetToStringTag(PromiseConstructor, PROMISE, false, true);\nsetSpecies(PROMISE);\n\nPromiseWrapper = getBuiltIn(PROMISE);\n\n// statics\n$({ target: PROMISE, stat: true, forced: FORCED }, {\n  // `Promise.reject` method\n  // https://tc39.es/ecma262/#sec-promise.reject\n  reject: function reject(r) {\n    var capability = newPromiseCapability(this);\n    call(capability.reject, undefined, r);\n    return capability.promise;\n  }\n});\n\n$({ target: PROMISE, stat: true, forced: IS_PURE || FORCED }, {\n  // `Promise.resolve` method\n  // https://tc39.es/ecma262/#sec-promise.resolve\n  resolve: function resolve(x) {\n    return promiseResolve(IS_PURE && this === PromiseWrapper ? PromiseConstructor : this, x);\n  }\n});\n\n$({ target: PROMISE, stat: true, forced: INCORRECT_ITERATION }, {\n  // `Promise.all` method\n  // https://tc39.es/ecma262/#sec-promise.all\n  all: function all(iterable) {\n    var C = this;\n    var capability = newPromiseCapability(C);\n    var resolve = capability.resolve;\n    var reject = capability.reject;\n    var result = perform(function () {\n      var $promiseResolve = aCallable(C.resolve);\n      var values = [];\n      var counter = 0;\n      var remaining = 1;\n      iterate(iterable, function (promise) {\n        var index = counter++;\n        var alreadyCalled = false;\n        remaining++;\n        call($promiseResolve, C, promise).then(function (value) {\n          if (alreadyCalled) return;\n          alreadyCalled = true;\n          values[index] = value;\n          --remaining || resolve(values);\n        }, reject);\n      });\n      --remaining || resolve(values);\n    });\n    if (result.error) reject(result.value);\n    return capability.promise;\n  },\n  // `Promise.race` method\n  // https://tc39.es/ecma262/#sec-promise.race\n  race: function race(iterable) {\n    var C = this;\n    var capability = newPromiseCapability(C);\n    var reject = capability.reject;\n    var result = perform(function () {\n      var $promiseResolve = aCallable(C.resolve);\n      iterate(iterable, function (promise) {\n        call($promiseResolve, C, promise).then(capability.resolve, reject);\n      });\n    });\n    if (result.error) reject(result.value);\n    return capability.promise;\n  }\n});\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\n\nvar PromiseCapability = function (C) {\n  var resolve, reject;\n  this.promise = new C(function ($$resolve, $$reject) {\n    if (resolve !== undefined || reject !== undefined) throw TypeError('Bad Promise constructor');\n    resolve = $$resolve;\n    reject = $$reject;\n  });\n  this.resolve = aCallable(resolve);\n  this.reject = aCallable(reject);\n};\n\n// `NewPromiseCapability` abstract operation\n// https://tc39.es/ecma262/#sec-newpromisecapability\nmodule.exports.f = function (C) {\n  return new PromiseCapability(C);\n};\n", "module.exports = function (exec) {\n  try {\n    return { error: false, value: exec() };\n  } catch (error) {\n    return { error: true, value: error };\n  }\n};\n", "module.exports = typeof window == 'object';\n", "var global = require('../internals/global');\n\nmodule.exports = function (a, b) {\n  var console = global.console;\n  if (console && console.error) {\n    arguments.length == 1 ? console.error(a) : console.error(a, b);\n  }\n};\n", "/**\n * @description insert video\n * <AUTHOR>\n */\n\nimport { Transforms } from 'slate'\nimport { IDomEditor } from '@wangeditor/core'\nimport { replaceSymbols } from '../../utils/util'\nimport { VideoElement } from '../custom-types'\n\n/**\n * 插入视频\n * @param editor editor\n * @param src video src\n * @param poster video poster\n */\nexport default async function (editor: IDomEditor, src: string, poster = '') {\n  if (!src) return\n\n  // 还原选区\n  editor.restoreSelection()\n\n  // 校验\n  const { onInsertedVideo, checkVideo, parseVideoSrc } = editor.getMenuConfig('insertVideo')\n  const checkRes = await checkVideo(src, poster)\n  if (typeof checkRes === 'string') {\n    // 校验失败，给出提示\n    editor.alert(checkRes, 'error')\n    return\n  }\n  if (checkRes == null) {\n    // 校验失败，不给提示\n    return\n  }\n\n  // 转换 src\n  let parsedSrc = await parseVideoSrc(src)\n\n  if (parsedSrc.trim().indexOf('<iframe ') !== 0) {\n    parsedSrc = replaceSymbols(parsedSrc)\n  }\n\n  // 新建一个 video node\n  const video: VideoElement = {\n    type: 'video',\n    src: parsedSrc,\n    poster,\n    children: [{ text: '' }], // 【注意】void node 需要一个空 text 作为 children\n  }\n\n  // 插入视频\n  // 不使用此方式会比正常的选区选取先执行\n  Promise.resolve().then(() => {\n    Transforms.insertNodes(editor, video)\n  })\n\n  // 调用 callback\n  onInsertedVideo(video)\n}\n", "/**\n * @description insert video menu\n * <AUTHOR>\n */\n\nimport { Range, Node } from 'slate'\nimport {\n  IModalMenu,\n  IDomEditor,\n  DomEditor,\n  genModalInputElems,\n  genModalButtonElems,\n  t,\n} from '@wangeditor/core'\nimport $, { Dom7Array, DOMElement } from '../../utils/dom'\nimport { genRandomStr } from '../../utils/util'\nimport { VIDEO_SVG } from '../../constants/svg'\nimport insertVideo from '../helper/insert-video'\n\n/**\n * 生成唯一的 DOM ID\n */\nfunction genDomID(): string {\n  return genRandomStr('w-e-insert-video')\n}\n\nclass InsertVideoMenu implements IModalMenu {\n  readonly title = t('videoModule.insertVideo')\n  readonly iconSvg = VIDEO_SVG\n  readonly tag = 'button'\n  readonly showModal = true // 点击 button 时显示 modal\n  readonly modalWidth = 320\n  private $content: Dom7Array | null = null\n  private readonly srcInputId = genDomID()\n  private readonly posterInputId = genDomID()\n  private readonly buttonId = genDomID()\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 插入菜单，不需要 value\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 任何时候，都不用激活 menu\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    // 点击菜单时，弹出 modal 之前，不需要执行其他代码\n    // 此处空着即可\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true // 选区非折叠，禁用\n\n    const selectedElems = DomEditor.getSelectedElems(editor)\n    const hasVoidOrPre = selectedElems.some(elem => {\n      const type = DomEditor.getNodeType(elem)\n      if (type === 'pre') return true\n      if (type === 'list-item') return true\n      if (editor.isVoid(elem)) return true\n      return false\n    })\n    if (hasVoidOrPre) return true // void 或 pre ，禁用\n\n    return false\n  }\n\n  getModalPositionNode(editor: IDomEditor): Node | null {\n    return null // modal 依据选区定位\n  }\n\n  getModalContentElem(editor: IDomEditor): DOMElement {\n    const { srcInputId, posterInputId, buttonId } = this\n\n    // 获取 input button elem\n    const [srcContainerElem, inputSrcElem] = genModalInputElems(\n      t('videoModule.videoSrc'),\n      srcInputId,\n      t('videoModule.videoSrcPlaceHolder')\n    )\n    const [posterContainerElem, inputPosterElem] = genModalInputElems(\n      t('videoModule.videoPoster'),\n      posterInputId,\n      t('videoModule.videoPosterPlaceHolder')\n    )\n    const $inputSrc = $(inputSrcElem)\n    const $inputPoster = $(inputPosterElem)\n    const [buttonContainerElem] = genModalButtonElems(buttonId, t('videoModule.ok'))\n\n    if (this.$content == null) {\n      // 第一次渲染\n      const $content = $('<div></div>')\n\n      // 绑定事件（第一次渲染时绑定，不要重复绑定）\n      $content.on('click', `#${buttonId}`, async e => {\n        e.preventDefault()\n        const src = $content.find(`#${srcInputId}`).val().trim()\n        const poster = $content.find(`#${posterInputId}`).val().trim()\n        await insertVideo(editor, src, poster)\n        editor.hidePanelOrModal() // 隐藏 modal\n      })\n\n      // 记录属性，重要\n      this.$content = $content\n    }\n\n    const $content = this.$content\n    $content.empty() // 先清空内容\n\n    // append inputs and button\n    $content.append(srcContainerElem)\n    $content.append(posterContainerElem)\n    $content.append(buttonContainerElem)\n\n    // 设置 input val\n    $inputSrc.val('')\n    $inputPoster.val('')\n\n    // focus 一个 input（异步，此时 DOM 尚未渲染）\n    setTimeout(() => {\n      $inputSrc.focus()\n    })\n\n    return $content[0]\n  }\n}\n\nexport default InsertVideoMenu\n", "var anObject = require('../internals/an-object');\nvar isObject = require('../internals/is-object');\nvar newPromiseCapability = require('../internals/new-promise-capability');\n\nmodule.exports = function (C, x) {\n  anObject(C);\n  if (isObject(x) && x.constructor === C) return x;\n  var promiseCapability = newPromiseCapability.f(C);\n  var resolve = promiseCapability.resolve;\n  resolve(x);\n  return promiseCapability.promise;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar IndexedObject = require('../internals/indexed-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\n\nvar un$Join = uncurryThis([].join);\n\nvar ES3_STRINGS = IndexedObject != Object;\nvar STRICT_METHOD = arrayMethodIsStrict('join', ',');\n\n// `Array.prototype.join` method\n// https://tc39.es/ecma262/#sec-array.prototype.join\n$({ target: 'Array', proto: true, forced: ES3_STRINGS || !STRICT_METHOD }, {\n  join: function join(separator) {\n    return un$Join(toIndexedObject(this), separator === undefined ? ',' : separator);\n  }\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n", "'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar create = require('../internals/object-create');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar redefine = require('../internals/redefine');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\n// `%IteratorPrototype%` object\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\n/* eslint-disable es/no-array-prototype-keys -- safe */\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;\n  else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\n\nvar NEW_ITERATOR_PROTOTYPE = IteratorPrototype == undefined || fails(function () {\n  var test = {};\n  // FF44- legacy iterators case\n  return IteratorPrototype[ITERATOR].call(test) !== test;\n});\n\nif (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};\nelse if (IS_PURE) IteratorPrototype = create(IteratorPrototype);\n\n// `%IteratorPrototype%[@@iterator]()` method\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-@@iterator\nif (!isCallable(IteratorPrototype[ITERATOR])) {\n  redefine(IteratorPrototype, ITERATOR, function () {\n    return this;\n  });\n}\n\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};\n", "var global = require('../internals/global');\nvar hasOwn = require('../internals/has-own-property');\nvar isCallable = require('../internals/is-callable');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar Object = global.Object;\nvar ObjectPrototype = Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? Object.getPrototypeOf : function (O) {\n  var object = toObject(O);\n  if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];\n  var constructor = object.constructor;\n  if (isCallable(constructor) && object instanceof constructor) {\n    return constructor.prototype;\n  } return object instanceof Object ? ObjectPrototype : null;\n};\n", "'use strict';\nvar IteratorPrototype = require('../internals/iterators-core').IteratorPrototype;\nvar create = require('../internals/object-create');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar Iterators = require('../internals/iterators');\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (IteratorConstructor, NAME, next, ENUMERABLE_NEXT) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(+!ENUMERABLE_NEXT, next) });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar IS_PURE = require('../internals/is-pure');\nvar FunctionName = require('../internals/function-name');\nvar isCallable = require('../internals/is-callable');\nvar createIteratorConstructor = require('../internals/create-iterator-constructor');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar redefine = require('../internals/redefine');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\nvar IteratorsCore = require('../internals/iterators-core');\n\nvar PROPER_FUNCTION_NAME = FunctionName.PROPER;\nvar CONFIGURABLE_FUNCTION_NAME = FunctionName.CONFIGURABLE;\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND in IterablePrototype) return IterablePrototype[KIND];\n    switch (KIND) {\n      case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };\n      case VALUES: return function values() { return new IteratorConstructor(this, KIND); };\n      case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };\n    } return function () { return new IteratorConstructor(this); };\n  };\n\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR]\n    || IterablePrototype['@@iterator']\n    || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME == 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (CurrentIteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (!isCallable(CurrentIteratorPrototype[ITERATOR])) {\n          redefine(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array.prototype.{ values, @@iterator }.name in V8 / FF\n  if (PROPER_FUNCTION_NAME && DEFAULT == VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    if (!IS_PURE && CONFIGURABLE_FUNCTION_NAME) {\n      createNonEnumerableProperty(IterablePrototype, 'name', VALUES);\n    } else {\n      INCORRECT_VALUES_NAME = true;\n      defaultIterator = function values() { return call(nativeIterator, this); };\n    }\n  }\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        redefine(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    redefine(IterablePrototype, ITERATOR, defaultIterator, { name: DEFAULT });\n  }\n  Iterators[NAME] = defaultIterator;\n\n  return methods;\n};\n", "'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar addToUnscopables = require('../internals/add-to-unscopables');\nvar Iterators = require('../internals/iterators');\nvar InternalStateModule = require('../internals/internal-state');\nvar defineIterator = require('../internals/define-iterator');\n\nvar ARRAY_ITERATOR = 'Array Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);\n\n// `Array.prototype.entries` method\n// https://tc39.es/ecma262/#sec-array.prototype.entries\n// `Array.prototype.keys` method\n// https://tc39.es/ecma262/#sec-array.prototype.keys\n// `Array.prototype.values` method\n// https://tc39.es/ecma262/#sec-array.prototype.values\n// `Array.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-array.prototype-@@iterator\n// `CreateArrayIterator` internal method\n// https://tc39.es/ecma262/#sec-createarrayiterator\nmodule.exports = defineIterator(Array, 'Array', function (iterated, kind) {\n  setInternalState(this, {\n    type: ARRAY_ITERATOR,\n    target: toIndexedObject(iterated), // target\n    index: 0,                          // next index\n    kind: kind                         // kind\n  });\n// `%ArrayIteratorPrototype%.next` method\n// https://tc39.es/ecma262/#sec-%arrayiteratorprototype%.next\n}, function () {\n  var state = getInternalState(this);\n  var target = state.target;\n  var kind = state.kind;\n  var index = state.index++;\n  if (!target || index >= target.length) {\n    state.target = undefined;\n    return { value: undefined, done: true };\n  }\n  if (kind == 'keys') return { value: index, done: false };\n  if (kind == 'values') return { value: target[index], done: false };\n  return { value: [index, target[index]], done: false };\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values%\n// https://tc39.es/ecma262/#sec-createunmappedargumentsobject\n// https://tc39.es/ecma262/#sec-createmappedargumentsobject\nIterators.Arguments = Iterators.Array;\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n", "'use strict';\nvar charAt = require('../internals/string-multibyte').charAt;\nvar toString = require('../internals/to-string');\nvar InternalStateModule = require('../internals/internal-state');\nvar defineIterator = require('../internals/define-iterator');\n\nvar STRING_ITERATOR = 'String Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(STRING_ITERATOR);\n\n// `String.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-string.prototype-@@iterator\ndefineIterator(String, 'String', function (iterated) {\n  setInternalState(this, {\n    type: STRING_ITERATOR,\n    string: toString(iterated),\n    index: 0\n  });\n// `%StringIteratorPrototype%.next` method\n// https://tc39.es/ecma262/#sec-%stringiteratorprototype%.next\n}, function next() {\n  var state = getInternalState(this);\n  var string = state.string;\n  var index = state.index;\n  var point;\n  if (index >= string.length) return { value: undefined, done: true };\n  point = charAt(string, index);\n  state.index += point.length;\n  return { value: point, done: false };\n});\n", "'use strict';\nvar toPropertyKey = require('../internals/to-property-key');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = function (object, key, value) {\n  var propertyKey = toPropertyKey(key);\n  if (propertyKey in object) definePropertyModule.f(object, propertyKey, createPropertyDescriptor(0, value));\n  else object[propertyKey] = value;\n};\n", "var global = require('../internals/global');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar createProperty = require('../internals/create-property');\n\nvar Array = global.Array;\nvar max = Math.max;\n\nmodule.exports = function (O, start, end) {\n  var length = lengthOfArrayLike(O);\n  var k = toAbsoluteIndex(start, length);\n  var fin = toAbsoluteIndex(end === undefined ? length : end, length);\n  var result = Array(max(fin - k, 0));\n  for (var n = 0; k < fin; k++, n++) createProperty(result, n, O[k]);\n  result.length = n;\n  return result;\n};\n", "/* eslint-disable es/no-object-getownpropertynames -- safe */\nvar classof = require('../internals/classof-raw');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar $getOwnPropertyNames = require('../internals/object-get-own-property-names').f;\nvar arraySlice = require('../internals/array-slice-simple');\n\nvar windowNames = typeof window == 'object' && window && Object.getOwnPropertyNames\n  ? Object.getOwnPropertyNames(window) : [];\n\nvar getWindowNames = function (it) {\n  try {\n    return $getOwnPropertyNames(it);\n  } catch (error) {\n    return arraySlice(windowNames);\n  }\n};\n\n// fallback for IE11 buggy Object.getOwnPropertyNames with iframe and window\nmodule.exports.f = function getOwnPropertyNames(it) {\n  return windowNames && classof(it) == 'Window'\n    ? getWindowNames(it)\n    : $getOwnPropertyNames(toIndexedObject(it));\n};\n", "// FF26- bug: ArrayBuffers are non-extensible, but Object.isExtensible does not report it\nvar fails = require('../internals/fails');\n\nmodule.exports = fails(function () {\n  if (typeof ArrayBuffer == 'function') {\n    var buffer = new ArrayBuffer(8);\n    // eslint-disable-next-line es/no-object-isextensible, es/no-object-defineproperty -- safe\n    if (Object.isExtensible(buffer)) Object.defineProperty(buffer, 'a', { value: 8 });\n  }\n});\n", "var fails = require('../internals/fails');\nvar isObject = require('../internals/is-object');\nvar classof = require('../internals/classof-raw');\nvar ARRAY_BUFFER_NON_EXTENSIBLE = require('../internals/array-buffer-non-extensible');\n\n// eslint-disable-next-line es/no-object-isextensible -- safe\nvar $isExtensible = Object.isExtensible;\nvar FAILS_ON_PRIMITIVES = fails(function () { $isExtensible(1); });\n\n// `Object.isExtensible` method\n// https://tc39.es/ecma262/#sec-object.isextensible\nmodule.exports = (FAILS_ON_PRIMITIVES || ARRAY_BUFFER_NON_EXTENSIBLE) ? function isExtensible(it) {\n  if (!isObject(it)) return false;\n  if (ARRAY_BUFFER_NON_EXTENSIBLE && classof(it) == 'ArrayBuffer') return false;\n  return $isExtensible ? $isExtensible(it) : true;\n} : $isExtensible;\n", "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-isextensible, es/no-object-preventextensions -- required for testing\n  return Object.isExtensible(Object.preventExtensions({}));\n});\n", "var $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar isObject = require('../internals/is-object');\nvar hasOwn = require('../internals/has-own-property');\nvar defineProperty = require('../internals/object-define-property').f;\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertyNamesExternalModule = require('../internals/object-get-own-property-names-external');\nvar isExtensible = require('../internals/object-is-extensible');\nvar uid = require('../internals/uid');\nvar FREEZING = require('../internals/freezing');\n\nvar REQUIRED = false;\nvar METADATA = uid('meta');\nvar id = 0;\n\nvar setMetadata = function (it) {\n  defineProperty(it, METADATA, { value: {\n    objectID: 'O' + id++, // object ID\n    weakData: {}          // weak collections IDs\n  } });\n};\n\nvar fastKey = function (it, create) {\n  // return a primitive with prefix\n  if (!isObject(it)) return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if (!hasOwn(it, METADATA)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return 'F';\n    // not necessary to add metadata\n    if (!create) return 'E';\n    // add missing metadata\n    setMetadata(it);\n  // return object ID\n  } return it[METADATA].objectID;\n};\n\nvar getWeakData = function (it, create) {\n  if (!hasOwn(it, METADATA)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return true;\n    // not necessary to add metadata\n    if (!create) return false;\n    // add missing metadata\n    setMetadata(it);\n  // return the store of weak collections IDs\n  } return it[METADATA].weakData;\n};\n\n// add metadata on freeze-family methods calling\nvar onFreeze = function (it) {\n  if (FREEZING && REQUIRED && isExtensible(it) && !hasOwn(it, METADATA)) setMetadata(it);\n  return it;\n};\n\nvar enable = function () {\n  meta.enable = function () { /* empty */ };\n  REQUIRED = true;\n  var getOwnPropertyNames = getOwnPropertyNamesModule.f;\n  var splice = uncurryThis([].splice);\n  var test = {};\n  test[METADATA] = 1;\n\n  // prevent exposing of metadata key\n  if (getOwnPropertyNames(test).length) {\n    getOwnPropertyNamesModule.f = function (it) {\n      var result = getOwnPropertyNames(it);\n      for (var i = 0, length = result.length; i < length; i++) {\n        if (result[i] === METADATA) {\n          splice(result, i, 1);\n          break;\n        }\n      } return result;\n    };\n\n    $({ target: 'Object', stat: true, forced: true }, {\n      getOwnPropertyNames: getOwnPropertyNamesExternalModule.f\n    });\n  }\n};\n\nvar meta = module.exports = {\n  enable: enable,\n  fastKey: fastKey,\n  getWeakData: getWeakData,\n  onFreeze: onFreeze\n};\n\nhiddenKeys[METADATA] = true;\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar redefineAll = require('../internals/redefine-all');\nvar getWeakData = require('../internals/internal-metadata').getWeakData;\nvar anObject = require('../internals/an-object');\nvar isObject = require('../internals/is-object');\nvar anInstance = require('../internals/an-instance');\nvar iterate = require('../internals/iterate');\nvar ArrayIterationModule = require('../internals/array-iteration');\nvar hasOwn = require('../internals/has-own-property');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar setInternalState = InternalStateModule.set;\nvar internalStateGetterFor = InternalStateModule.getterFor;\nvar find = ArrayIterationModule.find;\nvar findIndex = ArrayIterationModule.findIndex;\nvar splice = uncurryThis([].splice);\nvar id = 0;\n\n// fallback for uncaught frozen keys\nvar uncaughtFrozenStore = function (store) {\n  return store.frozen || (store.frozen = new UncaughtFrozenStore());\n};\n\nvar UncaughtFrozenStore = function () {\n  this.entries = [];\n};\n\nvar findUncaughtFrozen = function (store, key) {\n  return find(store.entries, function (it) {\n    return it[0] === key;\n  });\n};\n\nUncaughtFrozenStore.prototype = {\n  get: function (key) {\n    var entry = findUncaughtFrozen(this, key);\n    if (entry) return entry[1];\n  },\n  has: function (key) {\n    return !!findUncaughtFrozen(this, key);\n  },\n  set: function (key, value) {\n    var entry = findUncaughtFrozen(this, key);\n    if (entry) entry[1] = value;\n    else this.entries.push([key, value]);\n  },\n  'delete': function (key) {\n    var index = findIndex(this.entries, function (it) {\n      return it[0] === key;\n    });\n    if (~index) splice(this.entries, index, 1);\n    return !!~index;\n  }\n};\n\nmodule.exports = {\n  getConstructor: function (wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER) {\n    var Constructor = wrapper(function (that, iterable) {\n      anInstance(that, Prototype);\n      setInternalState(that, {\n        type: CONSTRUCTOR_NAME,\n        id: id++,\n        frozen: undefined\n      });\n      if (iterable != undefined) iterate(iterable, that[ADDER], { that: that, AS_ENTRIES: IS_MAP });\n    });\n\n    var Prototype = Constructor.prototype;\n\n    var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);\n\n    var define = function (that, key, value) {\n      var state = getInternalState(that);\n      var data = getWeakData(anObject(key), true);\n      if (data === true) uncaughtFrozenStore(state).set(key, value);\n      else data[state.id] = value;\n      return that;\n    };\n\n    redefineAll(Prototype, {\n      // `{ WeakMap, WeakSet }.prototype.delete(key)` methods\n      // https://tc39.es/ecma262/#sec-weakmap.prototype.delete\n      // https://tc39.es/ecma262/#sec-weakset.prototype.delete\n      'delete': function (key) {\n        var state = getInternalState(this);\n        if (!isObject(key)) return false;\n        var data = getWeakData(key);\n        if (data === true) return uncaughtFrozenStore(state)['delete'](key);\n        return data && hasOwn(data, state.id) && delete data[state.id];\n      },\n      // `{ WeakMap, WeakSet }.prototype.has(key)` methods\n      // https://tc39.es/ecma262/#sec-weakmap.prototype.has\n      // https://tc39.es/ecma262/#sec-weakset.prototype.has\n      has: function has(key) {\n        var state = getInternalState(this);\n        if (!isObject(key)) return false;\n        var data = getWeakData(key);\n        if (data === true) return uncaughtFrozenStore(state).has(key);\n        return data && hasOwn(data, state.id);\n      }\n    });\n\n    redefineAll(Prototype, IS_MAP ? {\n      // `WeakMap.prototype.get(key)` method\n      // https://tc39.es/ecma262/#sec-weakmap.prototype.get\n      get: function get(key) {\n        var state = getInternalState(this);\n        if (isObject(key)) {\n          var data = getWeakData(key);\n          if (data === true) return uncaughtFrozenStore(state).get(key);\n          return data ? data[state.id] : undefined;\n        }\n      },\n      // `WeakMap.prototype.set(key, value)` method\n      // https://tc39.es/ecma262/#sec-weakmap.prototype.set\n      set: function set(key, value) {\n        return define(this, key, value);\n      }\n    } : {\n      // `WeakSet.prototype.add(value)` method\n      // https://tc39.es/ecma262/#sec-weakset.prototype.add\n      add: function add(value) {\n        return define(this, value, true);\n      }\n    });\n\n    return Constructor;\n  }\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar redefineAll = require('../internals/redefine-all');\nvar InternalMetadataModule = require('../internals/internal-metadata');\nvar collection = require('../internals/collection');\nvar collectionWeak = require('../internals/collection-weak');\nvar isObject = require('../internals/is-object');\nvar isExtensible = require('../internals/object-is-extensible');\nvar enforceIternalState = require('../internals/internal-state').enforce;\nvar NATIVE_WEAK_MAP = require('../internals/native-weak-map');\n\nvar IS_IE11 = !global.ActiveXObject && 'ActiveXObject' in global;\nvar InternalWeakMap;\n\nvar wrapper = function (init) {\n  return function WeakMap() {\n    return init(this, arguments.length ? arguments[0] : undefined);\n  };\n};\n\n// `WeakMap` constructor\n// https://tc39.es/ecma262/#sec-weakmap-constructor\nvar $WeakMap = collection('WeakMap', wrapper, collectionWeak);\n\n// IE11 WeakMap frozen keys fix\n// We can't use feature detection because it crash some old IE builds\n// https://github.com/zloirock/core-js/issues/485\nif (NATIVE_WEAK_MAP && IS_IE11) {\n  InternalWeakMap = collectionWeak.getConstructor(wrapper, 'WeakMap', true);\n  InternalMetadataModule.enable();\n  var WeakMapPrototype = $WeakMap.prototype;\n  var nativeDelete = uncurryThis(WeakMapPrototype['delete']);\n  var nativeHas = uncurryThis(WeakMapPrototype.has);\n  var nativeGet = uncurryThis(WeakMapPrototype.get);\n  var nativeSet = uncurryThis(WeakMapPrototype.set);\n  redefineAll(WeakMapPrototype, {\n    'delete': function (key) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        return nativeDelete(this, key) || state.frozen['delete'](key);\n      } return nativeDelete(this, key);\n    },\n    has: function has(key) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        return nativeHas(this, key) || state.frozen.has(key);\n      } return nativeHas(this, key);\n    },\n    get: function get(key) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        return nativeHas(this, key) ? nativeGet(this, key) : state.frozen.get(key);\n      } return nativeGet(this, key);\n    },\n    set: function set(key, value) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        nativeHas(this, key) ? nativeSet(this, key, value) : state.frozen.set(key, value);\n      } else nativeSet(this, key, value);\n      return this;\n    }\n  });\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isForced = require('../internals/is-forced');\nvar redefine = require('../internals/redefine');\nvar InternalMetadataModule = require('../internals/internal-metadata');\nvar iterate = require('../internals/iterate');\nvar anInstance = require('../internals/an-instance');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar fails = require('../internals/fails');\nvar checkCorrectnessOfIteration = require('../internals/check-correctness-of-iteration');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar inheritIfRequired = require('../internals/inherit-if-required');\n\nmodule.exports = function (CONSTRUCTOR_NAME, wrapper, common) {\n  var IS_MAP = CONSTRUCTOR_NAME.indexOf('Map') !== -1;\n  var IS_WEAK = CONSTRUCTOR_NAME.indexOf('Weak') !== -1;\n  var ADDER = IS_MAP ? 'set' : 'add';\n  var NativeConstructor = global[CONSTRUCTOR_NAME];\n  var NativePrototype = NativeConstructor && NativeConstructor.prototype;\n  var Constructor = NativeConstructor;\n  var exported = {};\n\n  var fixMethod = function (KEY) {\n    var uncurriedNativeMethod = uncurryThis(NativePrototype[KEY]);\n    redefine(NativePrototype, KEY,\n      KEY == 'add' ? function add(value) {\n        uncurriedNativeMethod(this, value === 0 ? 0 : value);\n        return this;\n      } : KEY == 'delete' ? function (key) {\n        return IS_WEAK && !isObject(key) ? false : uncurriedNativeMethod(this, key === 0 ? 0 : key);\n      } : KEY == 'get' ? function get(key) {\n        return IS_WEAK && !isObject(key) ? undefined : uncurriedNativeMethod(this, key === 0 ? 0 : key);\n      } : KEY == 'has' ? function has(key) {\n        return IS_WEAK && !isObject(key) ? false : uncurriedNativeMethod(this, key === 0 ? 0 : key);\n      } : function set(key, value) {\n        uncurriedNativeMethod(this, key === 0 ? 0 : key, value);\n        return this;\n      }\n    );\n  };\n\n  var REPLACE = isForced(\n    CONSTRUCTOR_NAME,\n    !isCallable(NativeConstructor) || !(IS_WEAK || NativePrototype.forEach && !fails(function () {\n      new NativeConstructor().entries().next();\n    }))\n  );\n\n  if (REPLACE) {\n    // create collection constructor\n    Constructor = common.getConstructor(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER);\n    InternalMetadataModule.enable();\n  } else if (isForced(CONSTRUCTOR_NAME, true)) {\n    var instance = new Constructor();\n    // early implementations not supports chaining\n    var HASNT_CHAINING = instance[ADDER](IS_WEAK ? {} : -0, 1) != instance;\n    // V8 ~ Chromium 40- weak-collections throws on primitives, but should return false\n    var THROWS_ON_PRIMITIVES = fails(function () { instance.has(1); });\n    // most early implementations doesn't supports iterables, most modern - not close it correctly\n    // eslint-disable-next-line no-new -- required for testing\n    var ACCEPT_ITERABLES = checkCorrectnessOfIteration(function (iterable) { new NativeConstructor(iterable); });\n    // for early implementations -0 and +0 not the same\n    var BUGGY_ZERO = !IS_WEAK && fails(function () {\n      // V8 ~ Chromium 42- fails only with 5+ elements\n      var $instance = new NativeConstructor();\n      var index = 5;\n      while (index--) $instance[ADDER](index, index);\n      return !$instance.has(-0);\n    });\n\n    if (!ACCEPT_ITERABLES) {\n      Constructor = wrapper(function (dummy, iterable) {\n        anInstance(dummy, NativePrototype);\n        var that = inheritIfRequired(new NativeConstructor(), dummy, Constructor);\n        if (iterable != undefined) iterate(iterable, that[ADDER], { that: that, AS_ENTRIES: IS_MAP });\n        return that;\n      });\n      Constructor.prototype = NativePrototype;\n      NativePrototype.constructor = Constructor;\n    }\n\n    if (THROWS_ON_PRIMITIVES || BUGGY_ZERO) {\n      fixMethod('delete');\n      fixMethod('has');\n      IS_MAP && fixMethod('get');\n    }\n\n    if (BUGGY_ZERO || HASNT_CHAINING) fixMethod(ADDER);\n\n    // weak collections should not contains .clear method\n    if (IS_WEAK && NativePrototype.clear) delete NativePrototype.clear;\n  }\n\n  exported[CONSTRUCTOR_NAME] = Constructor;\n  $({ global: true, forced: Constructor != NativeConstructor }, exported);\n\n  setToStringTag(Constructor, CONSTRUCTOR_NAME);\n\n  if (!IS_WEAK) common.setStrong(Constructor, CONSTRUCTOR_NAME, IS_MAP);\n\n  return Constructor;\n};\n", "var isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    isCallable(NewTarget = dummy.constructor) &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "// iterable DOM collections\n// flag - `iterable` interface - 'entries', 'keys', 'values', 'forEach' methods\nmodule.exports = {\n  CSSRuleList: 0,\n  CSSStyleDeclaration: 0,\n  CSSValueList: 0,\n  ClientRectList: 0,\n  DOMRectList: 0,\n  DOMStringList: 0,\n  DOMTokenList: 1,\n  DataTransferItemList: 0,\n  FileList: 0,\n  HTMLAllCollection: 0,\n  HTMLCollection: 0,\n  HTMLFormElement: 0,\n  HTMLSelectElement: 0,\n  MediaList: 0,\n  MimeTypeArray: 0,\n  NamedNodeMap: 0,\n  NodeList: 1,\n  PaintRequestList: 0,\n  Plugin: 0,\n  PluginArray: 0,\n  SVGLengthList: 0,\n  SVGNumberList: 0,\n  SVGPathSegList: 0,\n  SVGPointList: 0,\n  SVGStringList: 0,\n  SVGTransformList: 0,\n  SourceBufferList: 0,\n  StyleSheetList: 0,\n  TextTrackCueList: 0,\n  TextTrackList: 0,\n  TouchList: 0\n};\n", "// in old WebKit versions, `element.classList` is not an instance of global `DOMTokenList`\nvar documentCreateElement = require('../internals/document-create-element');\n\nvar classList = documentCreateElement('span').classList;\nvar DOMTokenListPrototype = classList && classList.constructor && classList.constructor.prototype;\n\nmodule.exports = DOMTokenListPrototype === Object.prototype ? undefined : DOMTokenListPrototype;\n", "var global = require('../internals/global');\nvar DOMIterables = require('../internals/dom-iterables');\nvar DOMTokenListPrototype = require('../internals/dom-token-list-prototype');\nvar ArrayIteratorMethods = require('../modules/es.array.iterator');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar ArrayValues = ArrayIteratorMethods.values;\n\nvar handlePrototype = function (CollectionPrototype, COLLECTION_NAME) {\n  if (CollectionPrototype) {\n    // some Chrome versions have non-configurable methods on DOMTokenList\n    if (CollectionPrototype[ITERATOR] !== ArrayValues) try {\n      createNonEnumerableProperty(CollectionPrototype, ITERATOR, ArrayValues);\n    } catch (error) {\n      CollectionPrototype[ITERATOR] = ArrayValues;\n    }\n    if (!CollectionPrototype[TO_STRING_TAG]) {\n      createNonEnumerableProperty(CollectionPrototype, TO_STRING_TAG, COLLECTION_NAME);\n    }\n    if (DOMIterables[COLLECTION_NAME]) for (var METHOD_NAME in ArrayIteratorMethods) {\n      // some Chrome versions have non-configurable methods on DOMTokenList\n      if (CollectionPrototype[METHOD_NAME] !== ArrayIteratorMethods[METHOD_NAME]) try {\n        createNonEnumerableProperty(CollectionPrototype, METHOD_NAME, ArrayIteratorMethods[METHOD_NAME]);\n      } catch (error) {\n        CollectionPrototype[METHOD_NAME] = ArrayIteratorMethods[METHOD_NAME];\n      }\n    }\n  }\n};\n\nfor (var COLLECTION_NAME in DOMIterables) {\n  handlePrototype(global[COLLECTION_NAME] && global[COLLECTION_NAME].prototype, COLLECTION_NAME);\n}\n\nhandlePrototype(DOMTokenListPrototype, 'DOMTokenList');\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar FUNCTION_NAME_EXISTS = require('../internals/function-name').EXISTS;\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar defineProperty = require('../internals/object-define-property').f;\n\nvar FunctionPrototype = Function.prototype;\nvar functionToString = uncurryThis(FunctionPrototype.toString);\nvar nameRE = /function\\b(?:\\s|\\/\\*[\\S\\s]*?\\*\\/|\\/\\/[^\\n\\r]*[\\n\\r]+)*([^\\s(/]*)/;\nvar regExpExec = uncurryThis(nameRE.exec);\nvar NAME = 'name';\n\n// Function instances `.name` property\n// https://tc39.es/ecma262/#sec-function-instances-name\nif (DESCRIPTORS && !FUNCTION_NAME_EXISTS) {\n  defineProperty(FunctionPrototype, NAME, {\n    configurable: true,\n    get: function () {\n      try {\n        return regExpExec(nameRE, functionToString(this))[1];\n      } catch (error) {\n        return '';\n      }\n    }\n  });\n}\n", "var fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar V8_VERSION = require('../internals/engine-v8-version');\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (METHOD_NAME) {\n  // We can't use this feature detection in V8 since it causes\n  // deoptimization and serious performance degradation\n  // https://github.com/zloirock/core-js/issues/677\n  return V8_VERSION >= 51 || !fails(function () {\n    var array = [];\n    var constructor = array.constructor = {};\n    constructor[SPECIES] = function () {\n      return { foo: 1 };\n    };\n    return array[METHOD_NAME](Boolean).foo !== 1;\n  });\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar isArray = require('../internals/is-array');\nvar isConstructor = require('../internals/is-constructor');\nvar isObject = require('../internals/is-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar createProperty = require('../internals/create-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar arrayMethodHasSpeciesSupport = require('../internals/array-method-has-species-support');\nvar un$Slice = require('../internals/array-slice');\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('slice');\n\nvar SPECIES = wellKnownSymbol('species');\nvar Array = global.Array;\nvar max = Math.max;\n\n// `Array.prototype.slice` method\n// https://tc39.es/ecma262/#sec-array.prototype.slice\n// fallback for not array-like ES3 strings and DOM objects\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  slice: function slice(start, end) {\n    var O = toIndexedObject(this);\n    var length = lengthOfArrayLike(O);\n    var k = toAbsoluteIndex(start, length);\n    var fin = toAbsoluteIndex(end === undefined ? length : end, length);\n    // inline `ArraySpeciesCreate` for usage native `Array#slice` where it's possible\n    var Constructor, result, n;\n    if (isArray(O)) {\n      Constructor = O.constructor;\n      // cross-realm fallback\n      if (isConstructor(Constructor) && (Constructor === Array || isArray(Constructor.prototype))) {\n        Constructor = undefined;\n      } else if (isObject(Constructor)) {\n        Constructor = Constructor[SPECIES];\n        if (Constructor === null) Constructor = undefined;\n      }\n      if (Constructor === Array || Constructor === undefined) {\n        return un$Slice(O, k, fin);\n      }\n    }\n    result = new (Constructor === undefined ? Array : Constructor)(max(fin - k, 0));\n    for (n = 0; k < fin; k++, n++) if (k in O) createProperty(result, n, O[k]);\n    result.length = n;\n    return result;\n  }\n});\n", "/**\n * @description upload video\n * <AUTHOR>\n */\n\nimport Uppy, { UppyFile } from '@uppy/core'\nimport { IDomEditor, createUploader } from '@wangeditor/core'\nimport insertVideo from './insert-video'\nimport { IUploadConfigForVideo } from '../menu/config'\n\nfunction getMenuConfig(editor: IDomEditor): IUploadConfigForVideo {\n  // 获取配置，见 `./config.js`\n  return editor.getMenuConfig('uploadVideo') as IUploadConfigForVideo\n}\n\n// 存储 editor uppy 的关系 - 缓存 uppy ，不重复创建\nconst EDITOR_TO_UPPY_MAP = new WeakMap<IDomEditor, Uppy>()\n\n/**\n * 获取 uppy 实例（并通过 editor 缓存）\n * @param editor editor\n */\nfunction getUppy(editor: IDomEditor): Uppy {\n  // 从缓存中获取\n  let uppy = EDITOR_TO_UPPY_MAP.get(editor)\n  if (uppy != null) return uppy\n\n  const menuConfig = getMenuConfig(editor)\n  const { onSuccess, onProgress, onFailed, customInsert, onError } = menuConfig\n\n  // 上传完成之后\n  const successHandler = (file: UppyFile, res: any) => {\n    // 预期 res 格式：\n    // 成功：{ errno: 0, data: { url, poster } }\n    // 失败：{ errno: !0, message: '失败信息' }\n\n    if (customInsert) {\n      // 用户自定义插入视频，此时 res 格式可能不符合预期\n      customInsert(res, (src, poster) => insertVideo(editor, src, poster))\n      // success 回调\n      onSuccess(file, res)\n      return\n    }\n\n    let { errno = 1, data = {} } = res\n    if (errno !== 0) {\n      // failed 回调\n      onFailed(file, res)\n      return\n    }\n\n    const { url = '', poster = '' } = data\n    insertVideo(editor, url, poster)\n\n    // success 回调\n    onSuccess(file, res)\n  }\n\n  // progress 显示进度条\n  const progressHandler = (progress: number) => {\n    editor.showProgressBar(progress)\n\n    // 回调函数\n    onProgress && onProgress(progress)\n  }\n\n  // onError 提示错误\n  const errorHandler = (file: any, err: any, res: any) => {\n    onError(file, err, res)\n  }\n\n  // 创建 uppy\n  uppy = createUploader({\n    ...menuConfig,\n    onProgress: progressHandler,\n    onSuccess: successHandler,\n    onError: errorHandler,\n  })\n  // 缓存 uppy\n  EDITOR_TO_UPPY_MAP.set(editor, uppy)\n\n  return uppy\n}\n\n/**\n * 上传视频文件\n * @param editor editor\n * @param file file\n */\nasync function uploadFile(editor: IDomEditor, file: File) {\n  const uppy = getUppy(editor)\n\n  const { name, type, size } = file\n  uppy.addFile({\n    name,\n    type,\n    size,\n    data: file,\n  })\n  await uppy.upload()\n}\n\nexport default async function (editor: IDomEditor, files: FileList | null) {\n  if (files == null) return\n  const fileList = Array.prototype.slice.call(files)\n\n  // 获取菜单配置\n  const { customUpload } = getMenuConfig(editor)\n\n  // 按顺序上传\n  for await (const file of fileList) {\n    // 上传\n    if (customUpload) {\n      // 自定义上传\n      await customUpload(file, (src, poster) => insertVideo(editor, src, poster))\n    } else {\n      // 默认上传\n      await uploadFile(editor, file)\n    }\n  }\n}\n", "/**\n * @description upload video menu\n * <AUTHOR>\n */\n\nimport { Range } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport $ from '../../utils/dom'\nimport { UPLOAD_VIDEO_SVG } from '../../constants/svg'\nimport { IUploadConfigForVideo } from './config'\nimport insertVideo from '../helper/insert-video'\nimport uploadVideos from '../helper/upload-videos'\n\nclass UploadVideoMenu implements IButtonMenu {\n  readonly title = t('videoModule.uploadVideo')\n  readonly iconSvg = UPLOAD_VIDEO_SVG\n  readonly tag = 'button'\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 无需获取 val\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 任何时候，都不用激活 menu\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    const { allowedFileTypes = [], customBrowseAndUpload } = this.getMenuConfig(editor)\n\n    // 自定义选择图片，并上传，如图床\n    if (customBrowseAndUpload) {\n      customBrowseAndUpload((src, poster) => insertVideo(editor, src, poster))\n      return\n    }\n\n    // 设置选择文件的类型\n    let acceptAttr = ''\n    if (allowedFileTypes.length > 0) {\n      acceptAttr = `accept=\"${allowedFileTypes.join(', ')}\"`\n    }\n\n    // 添加 file input（每次重新创建 input）\n    const $body = $('body')\n    const $inputFile = $(`<input type=\"file\" ${acceptAttr} multiple/>`)\n    $inputFile.hide()\n    $body.append($inputFile)\n    $inputFile.click()\n    // 选中文件\n    $inputFile.on('change', () => {\n      const files = ($inputFile[0] as HTMLInputElement).files\n      uploadVideos(editor, files) // 上传文件\n    })\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true // 选区非折叠，禁用\n\n    const selectedElems = DomEditor.getSelectedElems(editor)\n    const hasVoidOrPre = selectedElems.some(elem => {\n      const type = DomEditor.getNodeType(elem)\n      if (type === 'pre') return true\n      if (type === 'list-item') return true\n      if (editor.isVoid(elem)) return true\n      return false\n    })\n    if (hasVoidOrPre) return true // void 或 pre ，禁用\n\n    return false\n  }\n\n  private getMenuConfig(editor: IDomEditor): IUploadConfigForVideo {\n    // 获取配置，见 `./config.js`\n    return editor.getMenuConfig('uploadVideo') as IUploadConfigForVideo\n  }\n}\n\nexport default UploadVideoMenu\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar redefine = require('../internals/redefine');\nvar anObject = require('../internals/an-object');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar $toString = require('../internals/to-string');\nvar fails = require('../internals/fails');\nvar regExpFlags = require('../internals/regexp-flags');\n\nvar TO_STRING = 'toString';\nvar RegExpPrototype = RegExp.prototype;\nvar n$ToString = RegExpPrototype[TO_STRING];\nvar getFlags = uncurryThis(regExpFlags);\n\nvar NOT_GENERIC = fails(function () { return n$ToString.call({ source: 'a', flags: 'b' }) != '/a/b'; });\n// FF44- RegExp#toString has a wrong name\nvar INCORRECT_NAME = PROPER_FUNCTION_NAME && n$ToString.name != TO_STRING;\n\n// `RegExp.prototype.toString` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.tostring\nif (NOT_GENERIC || INCORRECT_NAME) {\n  redefine(RegExp.prototype, TO_STRING, function toString() {\n    var R = anObject(this);\n    var p = $toString(R.source);\n    var rf = R.flags;\n    var f = $toString(rf === undefined && isPrototypeOf(RegExpPrototype, R) && !('flags' in RegExpPrototype) ? getFlags(R) : rf);\n    return '/' + p + '/' + f;\n  }, { unsafe: true });\n}\n", "/**\n * @description 修改视频尺寸\n * <AUTHOR>\n */\n\nimport { Node as SlateNode, Transforms } from 'slate'\nimport {\n  IModalMenu,\n  IDomEditor,\n  DomEditor,\n  genModalInputElems,\n  genModalButtonElems,\n  t,\n} from '@wangeditor/core'\nimport $, { Dom7Array, DOMElement } from '../../utils/dom'\nimport { genRandomStr } from '../../utils/util'\nimport { VideoElement } from '../custom-types'\n\n/**\n * 生成唯一的 DOM ID\n */\nfunction genDomID(): string {\n  return genRandomStr('w-e-insert-video')\n}\n\nclass EditorVideoSizeMenu implements IModalMenu {\n  readonly title = t('videoModule.editSize')\n  readonly tag = 'button'\n  readonly showModal = true // 点击 button 时显示 modal\n  readonly modalWidth = 320\n  private $content: Dom7Array | null = null\n  private readonly widthInputId = genDomID()\n  private readonly heightInputId = genDomID()\n  private readonly buttonId = genDomID()\n\n  private getSelectedVideoNode(editor: IDomEditor): SlateNode | null {\n    return DomEditor.getSelectedNodeByType(editor, 'video')\n  }\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 插入菜单，不需要 value\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 任何时候，都不用激活 menu\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    // 点击菜单时，弹出 modal 之前，不需要执行其他代码\n    // 此处空着即可\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    if (editor.selection == null) return true\n\n    const videoNode = this.getSelectedVideoNode(editor)\n    if (videoNode == null) {\n      // 选区未处于 video node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  getModalPositionNode(editor: IDomEditor): SlateNode | null {\n    return this.getSelectedVideoNode(editor)\n  }\n\n  getModalContentElem(editor: IDomEditor): DOMElement {\n    // return $('<div><p>修改尺寸</p><p>修改尺寸</p><p>修改尺寸</p><p>修改尺寸</p></div>')[0]\n\n    const { widthInputId, heightInputId, buttonId } = this\n\n    const [widthContainerElem, inputWidthElem] = genModalInputElems(\n      t('videoModule.width'),\n      widthInputId,\n      'auto'\n    )\n    const $inputWidth = $(inputWidthElem)\n    const [heightContainerElem, inputHeightElem] = genModalInputElems(\n      t('videoModule.height'),\n      heightInputId,\n      'auto'\n    )\n    const $inputHeight = $(inputHeightElem)\n    const [buttonContainerElem] = genModalButtonElems(buttonId, t('videoModule.ok'))\n\n    if (this.$content == null) {\n      // 第一次渲染\n      const $content = $('<div></div>')\n\n      // 绑定事件（第一次渲染时绑定，不要重复绑定）\n      $content.on('click', `#${buttonId}`, e => {\n        e.preventDefault()\n\n        const rawWidth = $content.find(`#${widthInputId}`).val().trim()\n        const rawHeight = $content.find(`#${heightInputId}`).val().trim()\n        const numberWidth = parseInt(rawWidth)\n        const numberHeight = parseInt(rawHeight)\n        const width = numberWidth ? numberWidth.toString() : 'auto'\n        const height = numberHeight ? numberHeight.toString() : 'auto'\n\n        editor.restoreSelection()\n\n        // 修改尺寸\n        Transforms.setNodes(\n          editor,\n          { width, height },\n          {\n            match: n => DomEditor.checkNodeType(n, 'video'),\n          }\n        )\n\n        editor.hidePanelOrModal() // 隐藏 modal\n      })\n\n      this.$content = $content\n    }\n\n    const $content = this.$content\n\n    // 先清空，再重新添加 DOM 内容\n    $content.empty()\n    $content.append(widthContainerElem)\n    $content.append(heightContainerElem)\n    $content.append(buttonContainerElem)\n\n    const videoNode = this.getSelectedVideoNode(editor) as VideoElement\n    if (videoNode == null) return $content[0]\n\n    // 初始化 input 值\n    const { width = 'auto', height = 'auto' } = videoNode\n    $inputWidth.val(width)\n    $inputHeight.val(height)\n    setTimeout(() => {\n      $inputWidth.focus()\n    })\n\n    return $content[0]\n  }\n}\n\nexport default EditorVideoSizeMenu\n", "/**\n * @description video menu\n * <AUTHOR>\n */\n\nimport InsertVideoMenu from './InsertVideoMenu'\n// import DeleteVideoMenu from './DeleteVideoMenu'\nimport UploadVideoMenu from './UploadVideoMenu'\nimport EditorVideoSizeMenu from './EditVideoSizeMenu'\nimport { genInsertVideoMenuConfig, genUploadVideoMenuConfig } from './config'\n\nexport const insertVideoMenuConf = {\n  key: 'insertVideo',\n  factory() {\n    return new InsertVideoMenu()\n  },\n\n  // 默认的菜单菜单配置，将存储在 editorConfig.MENU_CONF[key] 中\n  // 创建编辑器时，可通过 editorConfig.MENU_CONF[key] = {...} 来修改\n  config: genInsertVideoMenuConfig(),\n}\n\nexport const uploadVideoMenuConf = {\n  key: 'uploadVideo',\n  factory() {\n    return new UploadVideoMenu()\n  },\n\n  // 默认的菜单菜单配置，将存储在 editorConfig.MENU_CONF[key] 中\n  // 创建编辑器时，可通过 editorConfig.MENU_CONF[key] = {...} 来修改\n  config: genUploadVideoMenuConfig(),\n}\n\nexport const editorVideSizeMenuConf = {\n  key: 'editVideoSize',\n  factory() {\n    return new EditorVideoSizeMenu()\n  },\n}\n\n// export const deleteVideoMenuConf = {\n//   key: 'deleteVideo',\n//   factory() {\n//     return new DeleteVideoMenu()\n//   },\n// }\n// 键盘能删除 video 了，注释掉这个菜单 wangfupeng 22.02.23\n", "/**\n * @description video module\n * <AUTHOR>\n */\n\nimport { IModuleConf } from '@wangeditor/core'\nimport withVideo from './plugin'\nimport { renderVideoConf } from './render-elem'\nimport { videoToHtmlConf } from './elem-to-html'\nimport { preParseHtmlConf } from './pre-parse-html'\nimport { parseHtmlConf } from './parse-elem-html'\nimport { insertVideoMenuConf, uploadVideoMenuConf, editorVideSizeMenuConf } from './menu/index'\n\nconst video: Partial<IModuleConf> = {\n  renderElems: [renderVideoConf],\n  elemsToHtml: [videoToHtmlConf],\n  preParseHtml: [preParseHtmlConf],\n  parseElemsHtml: [parseHtmlConf],\n  menus: [insertVideoMenuConf, uploadVideoMenuConf, editorVideSizeMenuConf],\n  editorPlugin: withVideo,\n}\n\nexport default video\n", "/**\n * @description video menu config\n * <AUTHOR>\n */\n\nimport { IUploadConfig } from '@wangeditor/core'\nimport { VideoElement } from '../custom-types'\n\ntype InsertFn = (src: string, poster: string) => void\n\n// 在通用 uploadConfig 上，扩展 video 相关配置\nexport type IUploadConfigForVideo = IUploadConfig & {\n  allowedFileTypes?: string[]\n  // 用户自定义插入视频\n  customInsert?: (res: any, insertFn: InsertFn) => void\n  // 用户自定义上传视频\n  customUpload?: (files: File, insertFn: InsertFn) => void\n  // 自定义选择视频，如图床\n  customBrowseAndUpload?: (insertFn: InsertFn) => void\n}\n\nexport function genUploadVideoMenuConfig(): IUploadConfigForVideo {\n  return {\n    server: '', // server API 地址，需用户配置\n\n    fieldName: 'wangeditor-uploaded-video', // formData 中，文件的 key\n    maxFileSize: 10 * 1024 * 1024, // 10M\n    maxNumberOfFiles: 5, // 最多上传 xx 个视频\n    allowedFileTypes: ['video/*'],\n    meta: {\n      // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。\n      // 例如：token: 'xxxxx', x: 100\n    },\n    metaWithUrl: false,\n    // headers: {\n    //   // 自定义 http headers\n    //   // 例如：Accept: 'text/x-json', a: 100,\n    // },\n    withCredentials: false,\n    timeout: 30 * 1000, // 30s\n\n    onBeforeUpload: (files: any) => files, // 返回 false 则终止上传\n    onProgress: (progress: number) => {\n      /* on progress */\n    },\n    onSuccess: (file: any, res: any) => {\n      /* on success */\n    },\n    onFailed: (file: any, res: any) => {\n      /* on failed */\n      console.error(`'${file.name}' upload failed`, res)\n    },\n    onError: (file: any, err: any, res: any) => {\n      /* on error */\n      /* on timeout */\n      console.error(`'${file.name} upload error`, err, res)\n    },\n\n    // 自定义插入视频，用户配置\n    // customInsert: (res, insertFn) => {},\n\n    // 自定义上传视频，用户配置\n    // customUpload: (file, insertFn) => {},\n\n    // 自定义选择，并上传视频，如：图床 （用户配置）\n    // customBrowseAndUpload: insertFn => {},\n  }\n}\n\n/**\n * 生成插入网络视频的配置\n */\nexport function genInsertVideoMenuConfig() {\n  return {\n    onInsertedVideo(node: VideoElement) {\n      // 插入视频之后的 callback\n    },\n\n    /**\n     * 检查 video ，支持 async\n     * @param src src\n     * @param poster poster\n     */\n    checkVideo(src: string, poster: string): boolean | string | undefined {\n      // 1. 返回 true ，说明检查通过\n      // 2. 返回一个字符串，说明检查未通过，编辑器会阻止插入。会 alert 出错误信息（即返回的字符串）\n      // 3. 返回 undefined（即没有任何返回），说明检查未通过，编辑器会阻止插入\n      return true\n    },\n\n    /**\n     * 转换 video src\n     * @param src src\n     * @returns new src\n     */\n    parseVideoSrc(src: string): string {\n      return src\n    },\n  }\n}\n", "/**\n * @description editor 插件，重写 editor API\n * <AUTHOR>\n */\n\nimport { Transforms } from 'slate'\nimport { IDomEditor, DomEditor } from '@wangeditor/core'\nimport { CustomElement } from '../../../custom-types'\n\nfunction withVideo<T extends IDomEditor>(editor: T): T {\n  const { isVoid, normalizeNode } = editor\n  const newEditor = editor\n\n  // 重写 isVoid\n  newEditor.isVoid = (elem: CustomElement) => {\n    const { type } = elem\n\n    if (type === 'video') {\n      return true\n    }\n\n    return isVoid(elem)\n  }\n\n  // 重写 normalizeNode\n  newEditor.normalizeNode = ([node, path]) => {\n    const type = DomEditor.getNodeType(node)\n\n    // ----------------- video 后面必须跟一个 p header blockquote -----------------\n    if (type === 'video') {\n      // -------------- video 是 editor 最后一个节点，需要后面插入 p --------------\n      const isLast = DomEditor.isLastNode(newEditor, node)\n      if (isLast) {\n        Transforms.insertNodes(newEditor, DomEditor.genEmptyParagraph(), { at: [path[0] + 1] })\n      }\n    }\n\n    // 执行默认的 normalizeNode ，重要！！！\n    return normalizeNode([node, path])\n  }\n\n  // 返回 editor ，重要！\n  return newEditor\n}\n\nexport default withVideo\n"], "names": ["i18nAddResources", "videoModule", "delete", "uploadVideo", "insertVideo", "videoSrc", "videoSrcPlaceHolder", "videoPoster", "videoPosterPlaceHolder", "ok", "editSize", "width", "height", "match", "version", "check", "it", "Math", "globalThis", "window", "self", "global", "this", "Function", "exec", "error", "fails", "Object", "defineProperty", "get", "call", "prototype", "bind", "apply", "arguments", "$propertyIsEnumerable", "propertyIsEnumerable", "getOwnPropertyDescriptor", "V", "descriptor", "enumerable", "bitmap", "value", "configurable", "writable", "FunctionPrototype", "callBind", "fn", "toString", "uncurryThis", "stringSlice", "slice", "split", "classof", "TypeError", "undefined", "IndexedObject", "requireObjectCoercible", "argument", "isCallable", "aFunction", "namespace", "method", "length", "isPrototypeOf", "getBuiltIn", "process", "<PERSON><PERSON>", "versions", "v8", "userAgent", "getOwnPropertySymbols", "symbol", "Symbol", "String", "sham", "V8_VERSION", "NATIVE_SYMBOL", "iterator", "USE_SYMBOL_AS_UID", "$Symbol", "tryToString", "P", "func", "aCallable", "key", "setGlobal", "module", "store", "push", "mode", "copyright", "hasOwnProperty", "hasOwn", "toObject", "id", "postfix", "random", "WellKnownSymbolsStore", "shared", "symbolFor", "createWellKnownSymbol", "withoutSetter", "uid", "name", "description", "TO_PRIMITIVE", "wellKnownSymbol", "input", "pref", "isObject", "isSymbol", "result", "exoticToPrim", "getMethod", "val", "valueOf", "ordinaryToPrimitive", "toPrimitive", "document", "EXISTS", "createElement", "DESCRIPTORS", "a", "$getOwnPropertyDescriptor", "O", "toIndexedObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IE8_DOM_DEFINE", "createPropertyDescriptor", "propertyIsEnumerableModule", "f", "$defineProperty", "Attributes", "anObject", "object", "definePropertyModule", "functionToString", "inspectSource", "set", "has", "WeakMap", "test", "keys", "NATIVE_WEAK_MAP", "state", "wmget", "wmhas", "wmset", "metadata", "facade", "STATE", "sharedKey", "hiddenKeys", "createNonEnumerableProperty", "enforce", "getter<PERSON>or", "TYPE", "type", "getDescriptor", "PROPER", "CONFIGURABLE", "CONFIGURABLE_FUNCTION_NAME", "require$$0", "getInternalState", "InternalStateModule", "enforceInternalState", "TEMPLATE", "options", "unsafe", "simple", "noTargetGet", "replace", "source", "join", "ceil", "floor", "number", "max", "min", "index", "integer", "toIntegerOrInfinity", "obj", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_INCLUDES", "$this", "el", "fromIndex", "lengthOfArrayLike", "toAbsoluteIndex", "includes", "indexOf", "names", "i", "enumBugKeys", "concat", "getOwnPropertyNames", "internalObjectKeys", "getOwnPropertyNamesModule", "getOwnPropertySymbolsModule", "target", "ownKeys", "getOwnPropertyDescriptorModule", "replacement", "isForced", "feature", "detection", "data", "normalize", "POLYFILL", "NATIVE", "string", "toLowerCase", "targetProperty", "sourceProperty", "TARGET", "GLOBAL", "STATIC", "stat", "forced", "copyConstructorProperties", "redefine", "METHOD_NAME", "TO_STRING_TAG", "CORRECT_ARGUMENTS", "classofRaw", "TO_STRING_TAG_SUPPORT", "tag", "tryGet", "callee", "whitespace", "whitespaces", "ltrim", "RegExp", "rtrim", "start", "end", "trim", "PROPER_FUNCTION_NAME", "$trim", "getTagName", "$elem", "tagName", "genSizeStyledIframeHtml", "iframeHtml", "$iframe", "$", "attr", "outerHTML", "proto", "append", "on", "focus", "html", "parent", "hasClass", "empty", "renderVideoConf", "renderElem", "elemNode", "children", "editor", "vnode", "_a", "_b", "src", "_c", "poster", "_d", "_e", "selected", "Dom<PERSON><PERSON>or", "isNodeSelected", "jsx", "className", "innerHTML", "videoVnode", "controls", "h", "props", "contentEditable", "mousedown", "e", "preventDefault", "activeXDocument", "videoToHtmlConf", "elemToHtml", "childrenHtml", "res", "defineProperties", "Properties", "objectKeys", "IE_PROTO", "EmptyConstructor", "scriptTag", "content", "LT", "NullProtoObjectViaActiveX", "write", "close", "temp", "parentWindow", "NullProtoObject", "ActiveXObject", "iframeDocument", "iframe", "domain", "documentCreateElement", "style", "display", "append<PERSON><PERSON><PERSON>", "contentWindow", "open", "F", "create", "UNSCOPABLES", "ArrayPrototype", "Array", "$includes", "addToUnscopables", "preParseHtmlConf", "selector", "preParseHtml", "elem", "$video", "<PERSON><PERSON><PERSON><PERSON>", "firstChildTagName", "videoTagName", "$container", "that", "isArray", "noop", "construct", "constructorRegExp", "INCORRECT_TO_STRING", "isConstructorModern", "called", "SPECIES", "originalArray", "C", "constructor", "isConstructor", "IS_MAP", "IS_FILTER", "IS_SOME", "IS_EVERY", "IS_FIND_INDEX", "IS_FILTER_REJECT", "NO_HOLES", "callbackfn", "specificCreate", "boundFunction", "arraySpeciesCreate", "for<PERSON>ach", "map", "filter", "some", "every", "find", "findIndex", "filterReject", "$find", "SKIPS_HOLES", "genVideoElem", "text", "re1", "re2", "parseHtmlConf", "parseElemHtml", "ignoreCase", "multiline", "dotAll", "unicode", "sticky", "$RegExp", "UNSUPPORTED_Y", "re", "lastIndex", "MISSED_STICKY", "BROKEN_CARET", "flags", "groups", "nativeReplace", "nativeExec", "patchedExec", "char<PERSON>t", "UPDATES_LAST_INDEX_WRONG", "stickyHelpers", "NPCG_INCLUDED", "UNSUPPORTED_DOT_ALL", "UNSUPPORTED_NCG", "reCopy", "group", "str", "raw", "regexpFlags", "charsAdded", "strCopy", "Reflect", "RegExpPrototype", "charCodeAt", "CONVERT_TO_STRING", "pos", "first", "second", "S", "position", "size", "codeAt", "SUBSTITUTION_SYMBOLS", "SUBSTITUTION_SYMBOLS_NO_NAMED", "matched", "captures", "namedCaptures", "tailPos", "m", "symbols", "ch", "capture", "n", "R", "regexpExec", "REPLACE", "stringIndexOf", "REPLACE_KEEPS_$0", "REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE", "genRandomStr", "prefix", "nanoid", "KEY", "FORCED", "SHAM", "SYMBOL", "DELEGATES_TO_SYMBOL", "DELEGATES_TO_EXEC", "execCalled", "uncurriedNativeRegExpMethod", "methods", "nativeMethod", "regexp", "arg2", "forceStringMethod", "uncurriedNativeMethod", "$exec", "done", "fixRegExpWellKnownSymbolLogic", "_", "maybeCallNative", "UNSAFE_SUBSTITUTE", "searchValue", "replaceValue", "replacer", "rx", "functionalReplace", "fullUnicode", "results", "regExpExec", "advanceStringIndex", "accumulatedResult", "nextSourcePosition", "j", "replacer<PERSON><PERSON><PERSON>", "getSubstitution", "Promise", "setPrototypeOf", "setter", "CORRECT_SETTER", "aPossiblePrototype", "__proto__", "TAG", "Prototype", "ITERATOR", "Iterators", "kind", "innerResult", "innerError", "Result", "stopped", "ResultPrototype", "iterable", "unboundFunction", "iterFn", "next", "step", "AS_ENTRIES", "IS_ITERATOR", "INTERRUPTED", "stop", "condition", "iteratorClose", "callFn", "getIteratorMethod", "usingIterator", "iteratorMethod", "getIterator", "SAFE_CLOSING", "iteratorWithReturn", "return", "from", "location", "defer", "channel", "port", "SKIP_CLOSING", "ITERATION_SUPPORT", "defaultConstructor", "aConstructor", "setImmediate", "clear", "clearImmediate", "Dispatch", "MessageChannel", "counter", "queue", "run", "runner", "listener", "event", "post", "postMessage", "protocol", "host", "args", "arraySlice", "IS_NODE", "nextTick", "now", "IS_IOS", "port2", "port1", "onmessage", "addEventListener", "importScripts", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "flush", "head", "last", "notify", "toggle", "node", "promise", "then", "Pebble", "macrotask", "require$$1", "MutationObserver", "WebKitMutationObserver", "queueMicrotaskDescriptor", "queueMicrotask", "exit", "enter", "IS_WEBOS_WEBKIT", "IS_IOS_PEBBLE", "resolve", "createTextNode", "observe", "characterData", "Internal", "OwnPromiseCapability", "PromiseWrapper", "nativeThen", "task", "PromiseCapability", "reject", "$$resolve", "$$reject", "PROMISE", "setInternalState", "getInternalPromiseState", "NativePromisePrototype", "NativePromise", "PromiseConstructor", "PromisePrototype", "newPromiseCapability", "newPromiseCapabilityModule", "newGenericPromiseCapability", "DISPATCH_EVENT", "createEvent", "dispatchEvent", "NATIVE_REJECTION_EVENT", "PromiseRejectionEvent", "SUBCLASSING", "PROMISE_CONSTRUCTOR_SOURCE", "GLOBAL_CORE_JS_PROMISE", "FakePromise", "IS_BROWSER", "INCORRECT_ITERATION", "checkCorrectnessOfIteration", "all", "isThenable", "isReject", "notified", "chain", "reactions", "microtask", "exited", "reaction", "handler", "fail", "rejection", "onHandleUnhandled", "onUnhandled", "reason", "initEvent", "b", "console", "hostReportErrors", "isUnhandled", "perform", "emit", "unwrap", "internalReject", "internalResolve", "wrapper", "executor", "anInstance", "redefineAll", "onFulfilled", "onRejected", "speciesConstructor", "catch", "restoreSelection", "getMenuConfig", "onInsertedVideo", "checkVideo", "parseVideoSrc", "checkRes", "alert", "parsedSrc", "video", "Transforms", "insertNodes", "genDomID", "wrap", "setToStringTag", "CONSTRUCTOR_NAME", "<PERSON><PERSON><PERSON><PERSON>", "setSpecies", "r", "capability", "x", "promiseCapability", "promiseResolve", "$promiseResolve", "values", "remaining", "iterate", "alreadyCalled", "race", "t", "InsertVideoMenu", "selection", "Range", "isCollapsed", "getSelectedElems", "getNodeType", "isVoid", "srcInputId", "posterInputId", "buttonId", "__read", "genModalInputElems", "srcContainerElem", "inputSrcElem", "posterContainerElem", "inputPosterElem", "$inputSrc", "$inputPoster", "buttonContainerElem", "genModalButtonElems", "$content", "$content_1", "hidePanelOrModal", "un$Join", "ES3_STRINGS", "STRICT_METHOD", "arrayMethodIsStrict", "separator", "IteratorPrototype", "PrototypeOfArrayIteratorPrototype", "arrayIterator", "getPrototypeOf", "ObjectPrototype", "CORRECT_PROTOTYPE_GETTER", "BUGGY_SAFARI_ITERATORS", "NEW_ITERATOR_PROTOTYPE", "returnThis", "FunctionName", "IteratorsCore", "Iterable", "NAME", "IteratorConstructor", "DEFAULT", "IS_SET", "ENUMERABLE_NEXT", "createIteratorConstructor", "CurrentIteratorPrototype", "getIterationMethod", "KIND", "defaultIterator", "IterablePrototype", "INCORRECT_VALUES_NAME", "nativeIterator", "anyNativeIterator", "entries", "defineIterator", "iterated", "Arguments", "point", "propertyKey", "$getOwnPropertyNames", "windowNames", "getWindowNames", "k", "fin", "createProperty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "isExtensible", "$isExtensible", "ARRAY_BUFFER_NON_EXTENSIBLE", "preventExtensions", "REQUIRED", "METADATA", "setMetadata", "objectID", "weakData", "meta", "enable", "splice", "getOwnPropertyNamesExternalModule", "<PERSON><PERSON><PERSON>", "getWeakData", "onFreeze", "FREEZING", "internalStateGetterFor", "ArrayIterationModule", "uncaughtFrozenStore", "frozen", "UncaughtFrozenStore", "find<PERSON><PERSON><PERSON>tF<PERSON>zen", "entry", "InternalWeakMap", "getConstructor", "ADDER", "define", "add", "enforceIternalState", "IS_IE11", "init", "$WeakMap", "common", "IS_WEAK", "NativeConstructor", "NativePrototype", "exported", "fixMethod", "InternalMetadataModule", "instance", "HASNT_CHAINING", "THROWS_ON_PRIMITIVES", "ACCEPT_ITERABLES", "BUGGY_ZERO", "$instance", "dummy", "Wrapper", "<PERSON><PERSON><PERSON><PERSON>", "NewTargetPrototype", "inheritIfRequired", "setStrong", "collection", "collectionWeak", "WeakMapPrototype", "nativeDelete", "nativeHas", "nativeGet", "nativeSet", "CSSRuleList", "CSSStyleDeclaration", "CSSValueList", "ClientRectList", "DOMRectList", "DOMStringList", "DOMTokenList", "DataTransferItemList", "FileList", "HTMLAllCollection", "HTMLCollection", "HTMLFormElement", "HTMLSelectElement", "MediaList", "MimeTypeArray", "NamedNodeMap", "NodeList", "PaintRequestList", "Plugin", "PluginArray", "SVGLengthList", "SVGNumberList", "SVGPathSegList", "SVGPointList", "SVGStringList", "SVGTransformList", "SourceBufferList", "StyleSheetList", "TextTrackCueList", "TextTrackList", "TouchList", "classList", "DOMTokenListPrototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArrayIteratorMethods", "handlePrototype", "CollectionPrototype", "COLLECTION_NAME", "DOMIterables", "FUNCTION_NAME_EXISTS", "nameRE", "HAS_SPECIES_SUPPORT", "array", "foo", "Boolean", "arrayMethodHasSpeciesSupport", "un$Slice", "EDITOR_TO_UPPY_MAP", "uploadFile", "file", "uppy", "menuConfig", "onSuccess", "onProgress", "onFailed", "customInsert", "onError", "createUploader", "progress", "showProgressBar", "errno", "url", "err", "getUppy", "addFile", "upload", "UploadVideoMenu", "allowedFileTypes", "customBrowseAndUpload", "acceptAttr", "$body", "$inputFile", "hide", "click", "files", "fileList", "customUpload", "fileList_1", "__asyncValues", "uploadVideos", "n$ToString", "getFlags", "regExpFlags", "NOT_GENERIC", "INCORRECT_NAME", "p", "$toString", "rf", "EditorVideoSizeMenu", "getSelectedNodeByType", "getSelectedVideoNode", "widthInputId", "heightInputId", "widthContainerElem", "inputWidthElem", "$inputWidth", "heightContainerElem", "inputHeightElem", "$inputHeight", "<PERSON><PERSON><PERSON><PERSON>", "rawHeight", "numberWidth", "parseInt", "numberHeight", "setNodes", "checkNodeType", "videoNode", "_f", "renderElems", "elemsToHtml", "parseElemsHtml", "menus", "factory", "config", "server", "fieldName", "maxFileSize", "maxNumberOfFiles", "metaWithUrl", "withCredentials", "timeout", "onBeforeUpload", "editor<PERSON><PERSON><PERSON>", "normalizeNode", "newEditor", "path", "isLastNode", "genEmptyParagraph", "at"], "mappings": "yXASAA,EAAiB,KCJF,CACbC,YAAa,CACXC,OAAQ,SACRC,YAAa,eACbC,YAAa,eACbC,SAAU,eACVC,oBAAqB,0CACrBC,YAAa,eACbC,uBAAwB,mBACxBC,GAAI,KACJC,SAAU,YACVC,MAAO,QACPC,OAAQ,YDPZZ,EAAiB,QELF,CACbC,YAAa,CACXC,OAAQ,OACRC,YAAa,OACbC,YAAa,OACbC,SAAU,OACVC,oBAAqB,yBACrBC,YAAa,OACbC,uBAAwB,WACxBC,GAAI,KACJC,SAAU,OACVC,MAAO,KACPC,OAAQ;;;;;;;;;;;;;;;6uFCjBZ,ICOIC,EAAOC,EDPPC,EAAQ,SAAUC,GACpB,OAAOA,GAAMA,EAAGC,MAAQA,MAAQD,KAMhCD,EAA2B,iBAAdG,YAA0BA,aACvCH,EAAuB,iBAAVI,QAAsBA,SAEnCJ,EAAqB,iBAARK,MAAoBA,OACjCL,EAAuB,iBAAVM,GAAsBA,IAEnC,WAAe,OAAOC,KAAtB,IAAoCC,SAAS,cAATA,KEbrB,SAAUC,GACzB,IACE,QAASA,IACT,MAAOC,GACP,OAAO,OCDOC,GAAM,WAEtB,OAA8E,GAAvEC,OAAOC,eAAe,GAAI,EAAG,CAAEC,IAAK,WAAc,OAAO,KAAQ,MCLtEC,EAAOP,SAASQ,UAAUD,OAEbA,EAAKE,KAAOF,EAAKE,KAAKF,GAAQ,WAC7C,OAAOA,EAAKG,MAAMH,EAAMI,YCFtBC,EAAwB,GAAGC,qBAE3BC,EAA2BV,OAAOU,8BAGpBA,IAA6BF,EAAsBL,KAAK,CAAE,EAAG,GAAK,GAI1D,SAA8BQ,GACtD,IAAIC,EAAaF,EAAyBf,KAAMgB,GAChD,QAASC,GAAcA,EAAWC,YAChCL,KCba,SAAUM,EAAQC,GACjC,MAAO,CACLF,aAAuB,EAATC,GACdE,eAAyB,EAATF,GAChBG,WAAqB,EAATH,GACZC,MAAOA,ICLPG,EAAoBtB,SAASQ,UAC7BC,EAAOa,EAAkBb,KACzBF,EAAOe,EAAkBf,KACzBgB,EAAWd,GAAQA,EAAKA,KAAKF,KAEhBE,EAAO,SAAUe,GAChC,OAAOA,GAAMD,EAAShB,EAAMiB,IAC1B,SAAUA,GACZ,OAAOA,GAAM,WACX,OAAOjB,EAAKG,MAAMc,EAAIb,aCPtBc,EAAWC,EAAY,GAAGD,UAC1BE,EAAcD,EAAY,GAAGE,SAEhB,SAAUnC,GACzB,OAAOkC,EAAYF,EAAShC,GAAK,GAAI,ICDnCW,EAASN,EAAOM,OAChByB,EAAQH,EAAY,GAAGG,SAGV1B,GAAM,WAGrB,OAAQC,EAAO,KAAKS,qBAAqB,MACtC,SAAUpB,GACb,MAAsB,UAAfqC,EAAQrC,GAAkBoC,EAAMpC,EAAI,IAAMW,EAAOX,IACtDW,ECbA2B,EAAYjC,EAAOiC,YAIN,SAAUtC,GACzB,GAAUuC,MAANvC,EAAiB,MAAMsC,EAAU,wBAA0BtC,GAC/D,OAAOA,MCJQ,SAAUA,GACzB,OAAOwC,EAAcC,EAAuBzC,QCH7B,SAAU0C,GACzB,MAA0B,mBAAZA,MCDC,SAAU1C,GACzB,MAAoB,iBAANA,EAAwB,OAAPA,EAAc2C,GAAW3C,ICAtD4C,GAAY,SAAUF,GACxB,OAAOC,GAAWD,GAAYA,OAAWH,MAG1B,SAAUM,EAAWC,GACpC,OAAO5B,UAAU6B,OAAS,EAAIH,GAAUvC,EAAOwC,IAAcxC,EAAOwC,IAAcxC,EAAOwC,GAAWC,OCNrFb,EAAY,GAAGe,kBCAfC,GAAW,YAAa,cAAgB,GfCrDC,GAAU7C,EAAO6C,QACjBC,GAAO9C,EAAO8C,KACdC,GAAWF,IAAWA,GAAQE,UAAYD,IAAQA,GAAKrD,QACvDuD,GAAKD,IAAYA,GAASC,GAG1BA,KAIFvD,GAHAD,EAAQwD,GAAGjB,MAAM,MAGD,GAAK,GAAKvC,EAAM,GAAK,EAAI,IAAMA,EAAM,GAAKA,EAAM,MAK7DC,GAAWwD,OACdzD,EAAQyD,GAAUzD,MAAM,iBACVA,EAAM,IAAM,MACxBA,EAAQyD,GAAUzD,MAAM,oBACbC,GAAWD,EAAM,IAIhC,OAAiBC,OgBrBEa,OAAO4C,wBAA0B7C,GAAM,WACxD,IAAI8C,EAASC,SAGb,OAAQC,OAAOF,MAAa7C,OAAO6C,aAAmBC,UAEnDA,OAAOE,MAAQC,IAAcA,GAAa,SCR9BC,KACXJ,OAAOE,MACkB,iBAAnBF,OAAOK,SCCfnD,GAASN,EAAOM,UAEHoD,GAAoB,SAAU/D,GAC7C,MAAoB,iBAANA,GACZ,SAAUA,GACZ,IAAIgE,EAAUf,GAAW,UACzB,OAAON,GAAWqB,IAAYhB,GAAcgB,EAAQjD,UAAWJ,GAAOX,KCVpE0D,GAASrD,EAAOqD,UAEH,SAAUhB,GACzB,IACE,OAAOgB,GAAOhB,GACd,MAAOjC,GACP,MAAO,WCJP6B,GAAYjC,EAAOiC,aAGN,SAAUI,GACzB,GAAIC,GAAWD,GAAW,OAAOA,EACjC,MAAMJ,GAAU2B,GAAYvB,GAAY,0BCLzB,SAAUpB,EAAG4C,GAC5B,IAAIC,EAAO7C,EAAE4C,GACb,OAAe,MAARC,OAAe5B,EAAY6B,GAAUD,ICD1C7B,GAAYjC,EAAOiC,UCFnB1B,GAAiBD,OAAOC,kBAEX,SAAUyD,EAAK3C,GAC9B,IACEd,GAAeP,EAAQgE,EAAK,CAAE3C,MAAOA,EAAOC,cAAc,EAAMC,UAAU,IAC1E,MAAOnB,GACPJ,EAAOgE,GAAO3C,EACd,OAAOA,MCNCrB,EADC,uBACiBiE,GADjB,qBACmC,uBCD/CC,UAAiB,SAAUF,EAAK3C,GAC/B,OAAO8C,GAAMH,KAASG,GAAMH,QAAiB9B,IAAVb,EAAsBA,EAAQ,MAChE,WAAY,IAAI+C,KAAK,CACtB3E,QAAS,SACT4E,KAAyB,SACzBC,UAAW,4CCLThE,GAASN,EAAOM,UAIH,SAAU+B,GACzB,OAAO/B,GAAO8B,EAAuBC,KCLnCkC,GAAiB3C,EAAY,GAAG2C,mBAInBjE,OAAOkE,QAAU,SAAgB7E,EAAIqE,GACpD,OAAOO,GAAeE,GAAS9E,GAAKqE,ICNlCU,GAAK,EACLC,GAAU/E,KAAKgF,SACfjD,GAAWC,EAAY,GAAID,aAEd,SAAUqC,GACzB,MAAO,gBAAqB9B,IAAR8B,EAAoB,GAAKA,GAAO,KAAOrC,KAAW+C,GAAKC,GAAS,KCAlFE,GAAwBC,GAAO,OAC/B1B,GAASpD,EAAOoD,OAChB2B,GAAY3B,IAAUA,GAAY,IAClC4B,GAAwBtB,GAAoBN,GAASA,IAAUA,GAAO6B,eAAiBC,MAE1E,SAAUC,GACzB,IAAKX,GAAOK,GAAuBM,KAAW3B,IAAuD,iBAA/BqB,GAAsBM,GAAoB,CAC9G,IAAIC,EAAc,UAAYD,EAC1B3B,IAAiBgB,GAAOpB,GAAQ+B,GAClCN,GAAsBM,GAAQ/B,GAAO+B,GAErCN,GAAsBM,GADbzB,IAAqBqB,GACAA,GAAUK,GAEVJ,GAAsBI,GAEtD,OAAOP,GAAsBM,ICd7BlD,GAAYjC,EAAOiC,UACnBoD,GAAeC,GAAgB,kBAIlB,SAAUC,EAAOC,GAChC,IAAKC,GAASF,IAAUG,GAASH,GAAQ,OAAOA,EAChD,IACII,EADAC,EAAeC,GAAUN,EAAOF,IAEpC,GAAIO,EAAc,CAGhB,QAFa1D,IAATsD,IAAoBA,EAAO,WAC/BG,EAASlF,EAAKmF,EAAcL,EAAOC,IAC9BC,GAASE,IAAWD,GAASC,GAAS,OAAOA,EAClD,MAAM1D,GAAU,2CAGlB,YADaC,IAATsD,IAAoBA,EAAO,URdhB,SAAUD,EAAOC,GAChC,IAAI9D,EAAIoE,EACR,GAAa,WAATN,GAAqBlD,GAAWZ,EAAK6D,EAAM5D,YAAc8D,GAASK,EAAMrF,EAAKiB,EAAI6D,IAAS,OAAOO,EACrG,GAAIxD,GAAWZ,EAAK6D,EAAMQ,WAAaN,GAASK,EAAMrF,EAAKiB,EAAI6D,IAAS,OAAOO,EAC/E,GAAa,WAATN,GAAqBlD,GAAWZ,EAAK6D,EAAM5D,YAAc8D,GAASK,EAAMrF,EAAKiB,EAAI6D,IAAS,OAAOO,EACrG,MAAM7D,GAAU,2CQUT+D,CAAoBT,EAAOC,OCnBnB,SAAUnD,GACzB,IAAI2B,EAAMiC,GAAY5D,EAAU,UAChC,OAAOqD,GAAS1B,GAAOA,EAAMA,EAAM,ICJjCkC,GAAWlG,EAAOkG,SAElBC,GAASV,GAASS,KAAaT,GAASS,GAASE,kBAEpC,SAAUzG,GACzB,OAAOwG,GAASD,GAASE,cAAczG,GAAM,QCH7B0G,IAAgBhG,GAAM,WAEtC,OAEQ,GAFDC,OAAOC,eAAe6F,GAAc,OAAQ,IAAK,CACtD5F,IAAK,WAAc,OAAO,KACzB8F,KCCDC,GAA4BjG,OAAOU,+BAI3BqF,EAAcE,GAA4B,SAAkCC,EAAG3C,GAGzF,GAFA2C,EAAIC,GAAgBD,GACpB3C,EAAI6C,GAAc7C,GACd8C,GAAgB,IAClB,OAAOJ,GAA0BC,EAAG3C,GACpC,MAAOzD,IACT,GAAIoE,GAAOgC,EAAG3C,GAAI,OAAO+C,GAA0BnG,EAAKoG,EAA2BC,EAAGN,EAAG3C,GAAI2C,EAAE3C,MCjB7FR,GAASrD,EAAOqD,OAChBpB,GAAYjC,EAAOiC,aAGN,SAAUI,GACzB,GAAIoD,GAASpD,GAAW,OAAOA,EAC/B,MAAMJ,GAAUoB,GAAOhB,GAAY,sBCHjCJ,GAAYjC,EAAOiC,UAEnB8E,GAAkBzG,OAAOC,qBAIjB8F,EAAcU,GAAkB,SAAwBP,EAAG3C,EAAGmD,GAIxE,GAHAC,GAAST,GACT3C,EAAI6C,GAAc7C,GAClBoD,GAASD,GACLL,GAAgB,IAClB,OAAOI,GAAgBP,EAAG3C,EAAGmD,GAC7B,MAAO5G,IACT,GAAI,QAAS4G,GAAc,QAASA,EAAY,MAAM/E,GAAU,2BAEhE,MADI,UAAW+E,IAAYR,EAAE3C,GAAKmD,EAAW3F,OACtCmF,OCjBQH,EAAc,SAAUa,EAAQlD,EAAK3C,GACpD,OAAO8F,GAAqBL,EAAEI,EAAQlD,EAAK4C,EAAyB,EAAGvF,KACrE,SAAU6F,EAAQlD,EAAK3C,GAEzB,OADA6F,EAAOlD,GAAO3C,EACP6F,GCJLE,GAAmBxF,EAAY1B,SAASyB,UAGvCW,GAAW6B,GAAMkD,iBACpBlD,GAAMkD,cAAgB,SAAU1H,GAC9B,OAAOyH,GAAiBzH,KAI5B,ICAI2H,GAAK9G,GAAK+G,MDAGpD,GAAMkD,cETnBG,GAAUxH,EAAOwH,WAEJlF,GAAWkF,KAAY,cAAcC,KAAKJ,GAAcG,KCHrEE,GAAO5C,GAAO,WAED,SAAUd,GACzB,OAAO0D,GAAK1D,KAAS0D,GAAK1D,GAAOkB,GAAIlB,QCNtB,GHWb/B,GAAYjC,EAAOiC,UACnBuF,GAAUxH,EAAOwH,QAgBrB,GAAIG,IAAmB7C,GAAO8C,MAAO,CACnC,IAAIzD,GAAQW,GAAO8C,QAAU9C,GAAO8C,MAAQ,IAAIJ,IAC5CK,GAAQjG,EAAYuC,GAAM3D,KAC1BsH,GAAQlG,EAAYuC,GAAMoD,KAC1BQ,GAAQnG,EAAYuC,GAAMmD,KAC9BA,GAAM,SAAU3H,EAAIqI,GAClB,GAAIF,GAAM3D,GAAOxE,GAAK,MAAM,IAAIsC,GAxBH,8BA2B7B,OAFA+F,EAASC,OAAStI,EAClBoI,GAAM5D,GAAOxE,EAAIqI,GACVA,GAETxH,GAAM,SAAUb,GACd,OAAOkI,GAAM1D,GAAOxE,IAAO,IAE7B4H,GAAM,SAAU5H,GACd,OAAOmI,GAAM3D,GAAOxE,QAEjB,CACL,IAAIuI,GAAQC,GAAU,SACtBC,GAAWF,KAAS,EACpBZ,GAAM,SAAU3H,EAAIqI,GAClB,GAAIxD,GAAO7E,EAAIuI,IAAQ,MAAM,IAAIjG,GAvCJ,8BA0C7B,OAFA+F,EAASC,OAAStI,EAClB0I,GAA4B1I,EAAIuI,GAAOF,GAChCA,GAETxH,GAAM,SAAUb,GACd,OAAO6E,GAAO7E,EAAIuI,IAASvI,EAAGuI,IAAS,IAEzCX,GAAM,SAAU5H,GACd,OAAO6E,GAAO7E,EAAIuI,KAItB,OAAiB,CACfZ,IAAKA,GACL9G,IAAKA,GACL+G,IAAKA,GACLe,QAnDY,SAAU3I,GACtB,OAAO4H,GAAI5H,GAAMa,GAAIb,GAAM2H,GAAI3H,EAAI,KAmDnC4I,UAhDc,SAAUC,GACxB,OAAO,SAAU7I,GACf,IAAIiI,EACJ,IAAKnC,GAAS9F,KAAQiI,EAAQpH,GAAIb,IAAK8I,OAASD,EAC9C,MAAMvG,GAAU,0BAA4BuG,EAAO,aACnD,OAAOZ,KIrBTpG,GAAoBtB,SAASQ,UAE7BgI,GAAgBrC,GAAe/F,OAAOU,yBAEtCmF,GAAS3B,GAAOhD,GAAmB,WAKtB,CACf2E,OAAQA,GACRwC,OALWxC,IAA0D,cAAhD,aAAuChB,KAM5DyD,aALiBzC,MAAYE,GAAgBA,GAAeqC,GAAclH,GAAmB,QAAQF,iCCHvG,IAAIuH,EAA6BC,GAAsCF,aAEnEG,EAAmBC,GAAoBxI,IACvCyI,EAAuBD,GAAoBV,QAC3CY,EAAW7F,OAAOA,QAAQtB,MAAM,WAEnCmC,UAAiB,SAAUsC,EAAGxC,EAAK3C,EAAO8H,GACzC,IAIIvB,EAJAwB,IAASD,KAAYA,EAAQC,OAC7BC,IAASF,KAAYA,EAAQhI,WAC7BmI,IAAcH,KAAYA,EAAQG,YAClCnE,EAAOgE,QAA4BjH,IAAjBiH,EAAQhE,KAAqBgE,EAAQhE,KAAOnB,EAE9D1B,GAAWjB,KACoB,YAA7BgC,OAAO8B,GAAMrD,MAAM,EAAG,KACxBqD,EAAO,IAAM9B,OAAO8B,GAAMoE,QAAQ,qBAAsB,MAAQ,OAE7D/E,GAAOnD,EAAO,SAAYwH,GAA8BxH,EAAM8D,OAASA,IAC1EkD,GAA4BhH,EAAO,OAAQ8D,IAE7CyC,EAAQqB,EAAqB5H,IAClBmI,SACT5B,EAAM4B,OAASN,EAASO,KAAoB,iBAARtE,EAAmBA,EAAO,MAG9DqB,IAAMxG,GAIEoJ,GAEAE,GAAe9C,EAAExC,KAC3BqF,GAAS,UAFF7C,EAAExC,GAIPqF,EAAQ7C,EAAExC,GAAO3C,EAChBgH,GAA4B7B,EAAGxC,EAAK3C,IATnCgI,EAAQ7C,EAAExC,GAAO3C,EAChB4C,GAAUD,EAAK3C,KAUrBnB,SAASQ,UAAW,YAAY,WACjC,OAAO4B,GAAWrC,OAAS8I,EAAiB9I,MAAMuJ,QAAUnC,GAAcpH,YC5CxEyJ,GAAO9J,KAAK8J,KACZC,GAAQ/J,KAAK+J,SAIA,SAAUtH,GACzB,IAAIuH,GAAUvH,EAEd,OAAOuH,GAAWA,GAAqB,IAAXA,EAAe,GAAKA,EAAS,EAAID,GAAQD,IAAME,ICNzEC,GAAMjK,KAAKiK,IACXC,GAAMlK,KAAKkK,OAKE,SAAUC,EAAOrH,GAChC,IAAIsH,EAAUC,GAAoBF,GAClC,OAAOC,EAAU,EAAIH,GAAIG,EAAUtH,EAAQ,GAAKoH,GAAIE,EAAStH,ICR3DoH,GAAMlK,KAAKkK,OAIE,SAAUzH,GACzB,OAAOA,EAAW,EAAIyH,GAAIG,GAAoB5H,GAAW,kBAAoB,MCH9D,SAAU6H,GACzB,OAAOC,GAASD,EAAIxH,SCAlB0H,GAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAOC,EAAIC,GAC1B,IAGInJ,EAHAmF,EAAIC,GAAgB6D,GACpB5H,EAAS+H,GAAkBjE,GAC3BuD,EAAQW,GAAgBF,EAAW9H,GAIvC,GAAI2H,GAAeE,GAAMA,GAAI,KAAO7H,EAASqH,GAG3C,IAFA1I,EAAQmF,EAAEuD,OAEG1I,EAAO,OAAO,OAEtB,KAAMqB,EAASqH,EAAOA,IAC3B,IAAKM,GAAeN,KAASvD,IAAMA,EAAEuD,KAAWQ,EAAI,OAAOF,GAAeN,GAAS,EACnF,OAAQM,IAAgB,OAIb,CAGfM,SAAUP,IAAa,GAGvBQ,QAASR,IAAa,IC3BpBQ,GAAU9B,GAAuC8B,QAGjDxG,GAAOxC,EAAY,GAAGwC,SAET,SAAU8C,EAAQ2D,GACjC,IAGI7G,EAHAwC,EAAIC,GAAgBS,GACpB4D,EAAI,EACJnF,EAAS,GAEb,IAAK3B,KAAOwC,GAAIhC,GAAO4D,GAAYpE,IAAQQ,GAAOgC,EAAGxC,IAAQI,GAAKuB,EAAQ3B,GAE1E,KAAO6G,EAAMnI,OAASoI,GAAOtG,GAAOgC,EAAGxC,EAAM6G,EAAMC,SAChDF,GAAQjF,EAAQ3B,IAAQI,GAAKuB,EAAQ3B,IAExC,OAAO2B,MCjBQ,CACf,cACA,iBACA,gBACA,uBACA,iBACA,WACA,WCLEyC,GAAa2C,GAAYC,OAAO,SAAU,mBAKlC1K,OAAO2K,qBAAuB,SAA6BzE,GACrE,OAAO0E,GAAmB1E,EAAG4B,YCRnB9H,OAAO4C,uBCKf8H,GAASpJ,EAAY,GAAGoJ,WAGXpI,GAAW,UAAW,YAAc,SAAiBjD,GACpE,IAAI+H,EAAOyD,GAA0BrE,EAAEG,GAAStH,IAC5CuD,EAAwBkI,GAA4BtE,EACxD,OAAO5D,EAAwB8H,GAAOtD,EAAMxE,EAAsBvD,IAAO+H,MCP1D,SAAU2D,EAAQ7B,GAIjC,IAHA,IAAI9B,EAAO4D,GAAQ9B,GACfjJ,EAAiB4G,GAAqBL,EACtC9F,EAA2BuK,GAA+BzE,EACrDgE,EAAI,EAAGA,EAAIpD,EAAKhF,OAAQoI,IAAK,CACpC,IAAI9G,EAAM0D,EAAKoD,GACVtG,GAAO6G,EAAQrH,IAAMzD,EAAe8K,EAAQrH,EAAKhD,EAAyBwI,EAAQxF,MCRvFwH,GAAc,kBAEdC,GAAW,SAAUC,EAASC,GAChC,IAAItK,EAAQuK,GAAKC,GAAUH,IAC3B,OAAOrK,GAASyK,IACZzK,GAAS0K,KACTzJ,GAAWqJ,GAAatL,EAAMsL,KAC5BA,IAGJE,GAAYJ,GAASI,UAAY,SAAUG,GAC7C,OAAO3I,OAAO2I,GAAQzC,QAAQiC,GAAa,KAAKS,eAG9CL,GAAOH,GAASG,KAAO,GACvBG,GAASN,GAASM,OAAS,IAC3BD,GAAWL,GAASK,SAAW,OAElBL,GCpBbzK,GAA2B8H,GAA2DhC,KAsBzE,SAAUqC,EAASK,GAClC,IAGY6B,EAAQrH,EAAKkI,EAAgBC,EAAgBjL,EAHrDkL,EAASjD,EAAQkC,OACjBgB,EAASlD,EAAQnJ,OACjBsM,EAASnD,EAAQoD,KASrB,GANElB,EADEgB,EACOrM,EACAsM,EACAtM,EAAOoM,IAAWnI,GAAUmI,EAAQ,KAEnCpM,EAAOoM,IAAW,IAAI1L,UAEtB,IAAKsD,KAAOwF,EAAQ,CAQ9B,GAPA2C,EAAiB3C,EAAOxF,GAGtBkI,EAFE/C,EAAQG,aACVpI,EAAaF,GAAyBqK,EAAQrH,KACf9C,EAAWG,MACpBgK,EAAOrH,IACtByH,GAASY,EAASrI,EAAMoI,GAAUE,EAAS,IAAM,KAAOtI,EAAKmF,EAAQqD,cAE5CtK,IAAnBgK,EAA8B,CAC3C,UAAWC,UAAyBD,EAAgB,SACpDO,GAA0BN,EAAgBD,IAGxC/C,EAAQ7F,MAAS4I,GAAkBA,EAAe5I,OACpD+E,GAA4B8D,EAAgB,QAAQ,GAGtDO,GAASrB,EAAQrH,EAAKmI,EAAgBhD,KCjDtC1B,GAAO,GAEXA,GAHoBnC,GAAgB,gBAGd,IAEtB,ICC2BqH,MDDO,eAAjBtJ,OAAOoE,IEDpBmF,GAAgBtH,GAAgB,eAChChF,GAASN,EAAOM,OAGhBuM,GAAuE,aAAnDC,EAAW,WAAc,OAAOjM,UAArB,OAUlBkM,GAAwBD,EAAa,SAAUnN,GAC9D,IAAI6G,EAAGwG,EAAKrH,EACZ,YAAczD,IAAPvC,EAAmB,YAAqB,OAAPA,EAAc,OAEM,iBAAhDqN,EAXD,SAAUrN,EAAIqE,GACzB,IACE,OAAOrE,EAAGqE,GACV,MAAO5D,KAQS6M,CAAOzG,EAAIlG,GAAOX,GAAKiN,KAA8BI,EAEnEH,GAAoBC,EAAWtG,GAEH,WAA3Bb,EAASmH,EAAWtG,KAAmBlE,GAAWkE,EAAE0G,QAAU,YAAcvH,GCzB/EtC,GAASrD,EAAOqD,UAEH,SAAUhB,GACzB,GAA0B,WAAtBL,GAAQK,GAAwB,MAAMJ,UAAU,6CACpD,OAAOoB,GAAOhB,OCNC,gDCIbkH,GAAU3H,EAAY,GAAG2H,SACzB4D,GAAa,IAAMC,GAAc,IACjCC,GAAQC,OAAO,IAAMH,GAAaA,GAAa,KAC/CI,GAAQD,OAAOH,GAAaA,GAAa,MAGzC/C,GAAe,SAAU5B,GAC3B,OAAO,SAAU8B,GACf,IAAI0B,EAASrK,GAASS,EAAuBkI,IAG7C,OAFW,EAAP9B,IAAUwD,EAASzC,GAAQyC,EAAQqB,GAAO,KACnC,EAAP7E,IAAUwD,EAASzC,GAAQyC,EAAQuB,GAAO,KACvCvB,OAIM,CAGfwB,MAAOpD,GAAa,GAGpBqD,IAAKrD,GAAa,GAGlBsD,KAAMtD,GAAa,IJ7BjBuD,GAAuB7E,GAAsCH,OKE7DiF,GAAQ9E,GAAoC4E,cCsBhCG,GAAWC,UACrBA,EAAMpL,OAAeoL,EAAM,GAAGC,QAAQ9B,cACnC,YAUO+B,GACdC,EACA3O,EACAC,gBADAD,uBACAC,cAEM2O,EAAUC,EAAEF,UAClBC,EAAQE,KAAK,QAAS9O,GACtB4O,EAAQE,KAAK,SAAU7O,GAChB2O,EAAQ,GAAGG,UDrCpBF,GAAE,CAAE9C,OAAQ,SAAUiD,OAAO,EAAM9B,QLCRG,GKDuC,OLEzDtM,GAAM,WACX,QAAS+M,GAAYT,OANf,QAAA,MAOGA,OACHgB,IAAwBP,GAAYT,IAAaxH,OAASwH,QKLS,CAC3Ee,KAAM,WACJ,OAAOE,GAAM3N,SEJjBkO,GAAE,CAAEnO,QAAQ,GAAQ,CAClBH,WAAYG,IDEVuO,IAAQJ,EAAEzM,GAAG6M,OAASA,GACtBC,IAAIL,EAAEzM,GAAG8M,GAAKA,GACdC,IAAON,EAAEzM,GAAG+M,MAAQA,GACpBL,IAAMD,EAAEzM,GAAG0M,KAAOA,GAClBtI,IAAKqI,EAAEzM,GAAGoE,IAAMA,GAChB4I,IAAMP,EAAEzM,GAAGgN,KAAOA,GAClBC,IAAQR,EAAEzM,GAAGiN,OAASA,GACtBC,IAAUT,EAAEzM,GAAGkN,SAAWA,GAC1BC,IAAOV,EAAEzM,GAAGmN,MAAQA,GEuDxB,IAAMC,GAAkB,CACtBrG,KAAM,QACNsG,WA9DF,SAAqBC,EAAmBC,EAA0BC,OAM5DC,EALEC,EAA6DJ,EAA3DK,QAAAC,aAAM,KAAIC,WAAAC,aAAS,KAAIC,UAAAnQ,aAAQ,SAAQoQ,WAAAnQ,aAAS,SAGlDoQ,EAAWC,EAAUC,eAAeX,EAAQF,MAGX,IAAnCM,EAAI5B,OAAO9C,QAAQ,YAAmB,KAElCqD,EAAaD,GAAwBsB,EAAKhQ,EAAOC,GAGvD4P,EACEW,SACEC,UAAU,+CACKJ,EAAW,OAAS,GACnCK,UAAW/B,QAGV,KAECgC,EACJH,WAAON,OAAQA,EAAQU,aACrBJ,YAAQR,IAAKA,EAAK7G,KAAK,cACtB,6EAIS,SAAVnJ,IAAkB2Q,EAAWrE,KAAKtM,MAAQA,GAE/B,SAAXC,IAAmB0Q,EAAWrE,KAAKrM,OAASA,GAEhD4P,EACEW,SACEC,UAAU,+CACKJ,EAAW,OAAS,IAElCM,UAOgBE,EACrB,MACA,CACEC,MAAO,CACLC,iBAAiB,GAEnB7B,GAAI,CACF8B,UAAW,SAAAC,UAAKA,EAAEC,oBAGtBrB,KCvCG,IC0BHsB,GD1BSC,GAAkB,CAC7BjI,KAAM,QACNkI,WAnBF,SAAqB3B,EAAmB4B,OAChCxB,EAA6DJ,EAA3DK,QAAAC,aAAM,KAAIC,WAAAC,aAAS,KAAIC,UAAAnQ,aAAQ,SAAQoQ,WAAAnQ,aAAS,SACpDsR,EAAM,wDAE6B,IAAnCvB,EAAI5B,OAAO9C,QAAQ,YAGrBiG,GADmB7C,GAAwBsB,EAAKhQ,EAAOC,GAIvDsR,GAAO,kBAAkBrB,8BAAkClQ,eAAkBC,oBAAwB+P,iCAEvGuB,GAAO,gBEfQvQ,OAAOoH,MAAQ,SAAclB,GAC5C,OAAO0E,GAAmB1E,EAAGuE,QCEd1E,EAAc/F,OAAOwQ,iBAAmB,SAA0BtK,EAAGuK,GACpF9J,GAAST,GAMT,IALA,IAIIxC,EAJAoM,EAAQ3J,GAAgBsK,GACxBrJ,EAAOsJ,GAAWD,GAClBrO,EAASgF,EAAKhF,OACdqH,EAAQ,EAELrH,EAASqH,GAAO5C,GAAqBL,EAAEN,EAAGxC,EAAM0D,EAAKqC,KAAUqG,EAAMpM,IAC5E,OAAOwC,MCfQ5D,GAAW,WAAY,mBHWpCqO,GAAW9I,GAAU,YAErB+I,GAAmB,aAEnBC,GAAY,SAAUC,GACxB,MAAOC,WAAmBD,EAAnBC,cAILC,GAA4B,SAAUb,GACxCA,EAAgBc,MAAMJ,GAAU,KAChCV,EAAgBe,QAChB,IAAIC,EAAOhB,EAAgBiB,aAAapR,OAExC,OADAmQ,EAAkB,KACXgB,GA0BLE,GAAkB,WACpB,IACElB,GAAkB,IAAImB,cAAc,YACpC,MAAOxR,IAzBoB,IAIzByR,EAFAC,EAwBJH,GAAqC,oBAAZzL,SACrBA,SAAS6L,QAAUtB,GACjBa,GAA0Bb,MA1B5BqB,EAASE,GAAsB,WAG5BC,MAAMC,QAAU,OACvBxD,GAAKyD,YAAYL,GAEjBA,EAAOxC,IAAMjM,OALJ,gBAMTwO,EAAiBC,EAAOM,cAAclM,UACvBmM,OACfR,EAAeN,MAAMJ,GAAU,sBAC/BU,EAAeL,QACRK,EAAeS,GAiBlBhB,GAA0Bb,IAE9B,IADA,IAAI/N,EAASqI,GAAYrI,OAClBA,YAAiBiP,GAAyB,UAAE5G,GAAYrI,IAC/D,OAAOiP,MAGTvJ,GAAW6I,KAAY,EAIvB,OAAiB3Q,OAAOiS,QAAU,SAAgB/L,EAAGuK,GACnD,IAAIpL,EAQJ,OAPU,OAANa,GACF0K,GAA0B,UAAIjK,GAAST,GACvCb,EAAS,IAAIuL,GACbA,GAA0B,UAAI,KAE9BvL,EAAOsL,IAAYzK,GACdb,EAASgM,UACMzP,IAAf6O,EAA2BpL,EAASmL,GAAiBnL,EAAQoL,II5ElEyB,GAAclN,GAAgB,eAC9BmN,GAAiBC,MAAMhS,UAIQwB,MAA/BuQ,GAAeD,KACjBrL,GAAqBL,EAAE2L,GAAgBD,GAAa,CAClDlR,cAAc,EACdD,MAAOkR,GAAO,QAKlB,OAAiB,SAAUvO,GACzByO,GAAeD,IAAaxO,IAAO,GChBjC2O,GAAY7J,GAAuC6B,SAKvDwD,GAAE,CAAE9C,OAAQ,QAASiD,OAAO,GAAQ,CAClC3D,SAAU,SAAkBJ,GAC1B,OAAOoI,GAAU1S,KAAMsK,EAAI1J,UAAU6B,OAAS,EAAI7B,UAAU,QAAKqB,MAKrE0Q,GAAiB,YC4BV,IAAMC,GAAmB,CAC9BC,SAAU,iBACVC,aAjCF,SAAkBC,OACVlF,EAAQK,EAAE6E,GACZC,EAASnF,KAGO,MADAD,GAAWC,GACN,KAEjBmB,EAAWnB,EAAMmB,cACC,IAApBA,EAASvM,OAAc,KACnBwQ,EAAajE,EAAS,GACtBkE,EAAoBD,EAAWnF,QAAQ9B,cACzC,CAAC,SAAU,SAAStB,SAASwI,KAE/BF,EAAS9E,EAAE+E,SAKXE,EAAevF,GAAWoF,MACX,WAAjBG,GAA8C,UAAjBA,EAA0B,OAAOH,EAAO,MAInC,UADtBA,EAAOtE,SACXP,KAAK,iBAA8B,OAAO6E,EAAO,OAEvDI,EAAalF,EAAE,6DACrBkF,EAAW9E,OAAO0E,GAEXI,EAAW,KCpChB1S,GAAOiB,EAAYA,EAAYjB,SAGlB,SAAUe,EAAI4R,GAE7B,OADAvP,GAAUrC,QACMQ,IAAToR,EAAqB5R,EAAKf,GAAOA,GAAKe,EAAI4R,GAAQ,WACvD,OAAO5R,EAAGd,MAAM0S,EAAMzS,gBCJT6R,MAAMa,SAAW,SAAiBlR,GACjD,MAA4B,SAArBL,EAAQK,ICCbmR,GAAO,aACP3E,GAAQ,GACR4E,GAAY7Q,GAAW,UAAW,aAClC8Q,GAAoB,2BACpBvT,GAAOyB,EAAY8R,GAAkBvT,MACrCwT,IAAuBD,GAAkBvT,KAAKqT,IAE9CI,GAAsB,SAAUvR,GAClC,IAAKC,GAAWD,GAAW,OAAO,EAClC,IAEE,OADAoR,GAAUD,GAAM3E,GAAOxM,IAChB,EACP,MAAOjC,GACP,OAAO,QAgBOqT,IAAapT,GAAM,WACnC,IAAIwT,EACJ,OAAOD,GAAoBA,GAAoBnT,QACzCmT,GAAoBtT,UACpBsT,IAAoB,WAAcC,GAAS,MAC5CA,KAjBmB,SAAUxR,GAClC,IAAKC,GAAWD,GAAW,OAAO,EAClC,OAAQL,GAAQK,IACd,IAAK,gBACL,IAAK,oBACL,IAAK,yBAA0B,OAAO,EAEtC,OAAOsR,MAAyBxT,GAAKuT,GAAmBrM,GAAchF,KAW/CuR,GCpCvBE,GAAUxO,GAAgB,WAC1BoN,GAAQ1S,EAAO0S,SCHF,SAAUqB,EAAerR,GACxC,OAAO,IDMQ,SAAUqR,GACzB,IAAIC,EASF,OARET,GAAQQ,KACVC,EAAID,EAAcE,aAEdC,GAAcF,KAAOA,IAAMtB,IAASa,GAAQS,EAAEtT,aACzC+E,GAASuO,IAEN,QADVA,EAAIA,EAAEF,QAFuDE,OAAI9R,SAKtDA,IAAN8R,EAAkBtB,GAAQsB,GChBCD,GAA7B,CAAwD,IAAXrR,EAAe,EAAIA,ICErE0B,GAAOxC,EAAY,GAAGwC,MAGtBgG,GAAe,SAAU5B,GAC3B,IAAI2L,EAAiB,GAAR3L,EACT4L,EAAoB,GAAR5L,EACZ6L,EAAkB,GAAR7L,EACV8L,EAAmB,GAAR9L,EACX+L,EAAwB,GAAR/L,EAChBgM,EAA2B,GAARhM,EACnBiM,EAAmB,GAARjM,GAAa+L,EAC5B,OAAO,SAAUjK,EAAOoK,EAAYpB,EAAMqB,GASxC,IARA,IAOItT,EAAOsE,EAPPa,EAAI/B,GAAS6F,GACbvK,EAAOoC,EAAcqE,GACrBoO,EAAgBjU,GAAK+T,EAAYpB,GACjC5Q,EAAS+H,GAAkB1K,GAC3BgK,EAAQ,EACRwI,EAASoC,GAAkBE,GAC3BxJ,EAAS8I,EAAS5B,EAAOjI,EAAO5H,GAAU0R,GAAaI,EAAmBjC,EAAOjI,EAAO,QAAKpI,EAE3FQ,EAASqH,EAAOA,IAAS,IAAI0K,GAAY1K,KAAShK,KAEtD4F,EAASiP,EADTvT,EAAQtB,EAAKgK,GACiBA,EAAOvD,GACjCgC,GACF,GAAI2L,EAAQ9I,EAAOtB,GAASpE,OACvB,GAAIA,EAAQ,OAAQ6C,GACvB,KAAK,EAAG,OAAO,EACf,KAAK,EAAG,OAAOnH,EACf,KAAK,EAAG,OAAO0I,EACf,KAAK,EAAG3F,GAAKiH,EAAQhK,QAChB,OAAQmH,GACb,KAAK,EAAG,OAAO,EACf,KAAK,EAAGpE,GAAKiH,EAAQhK,GAI3B,OAAOkT,GAAiB,EAAIF,GAAWC,EAAWA,EAAWjJ,OAIhD,CAGfyJ,QAAS1K,GAAa,GAGtB2K,IAAK3K,GAAa,GAGlB4K,OAAQ5K,GAAa,GAGrB6K,KAAM7K,GAAa,GAGnB8K,MAAO9K,GAAa,GAGpB+K,KAAM/K,GAAa,GAGnBgL,UAAWhL,GAAa,GAGxBiL,aAAcjL,GAAa,ICrEzBkL,GAAQxM,GAAwCqM,KAIhDI,IAAc,EADP,QAIC,IAAI7C,MAAM,GAAO,MAAE,WAAc6C,IAAc,KAI3DpH,GAAE,CAAE9C,OAAQ,QAASiD,OAAO,EAAM9B,OAAQ+I,IAAe,CACvDJ,KAAM,SAAcT,GAClB,OAAOY,GAAMrV,KAAMyU,EAAY7T,UAAU6B,OAAS,EAAI7B,UAAU,QAAKqB,MAKzE0Q,GAfW,QCCX,OAAiB7F,GAAwB,GAAGpL,SAAW,WACrD,MAAO,WAAaK,GAAQ/B,MAAQ,KCGtC,SAASuV,GACPlG,EACAE,EACAlQ,EACAC,uBAFAiQ,mBACAlQ,uBACAC,UAEO,CACLkJ,KAAM,QACN6G,MACAE,SACAlQ,QACAC,SACA0P,SAAU,CAAC,CAAEwG,KAAM,MChBlB1I,IACHL,GAASpM,OAAOI,UAAW,WAAYiB,GAAU,CAAEyH,QAAQ,IDkDtD,IElCDsM,GACAC,GFiCOC,GAAgB,CAC3B9C,SAAU,6BACV+C,cAjCF,SAAmB7C,EAAkB/D,EAAwBC,OACrDpB,EAAQK,EAAE6E,GACZ1D,EAAM,GACNE,EAAS,GACTlQ,EAAQ,OACRC,EAAS,OAGP2O,EAAUJ,EAAMqH,KAAK,aACvBjH,EAAQxL,OAAS,SACnBpD,EAAQ4O,EAAQE,KAAK,UAAY,OACjC7O,EAAS2O,EAAQE,KAAK,WAAa,OAE5BoH,GADPlG,EAAMpB,EAAQ,GAAGG,UACQmB,EAAQlQ,EAAOC,OAIpC0T,EAASnF,EAAMqH,KAAK,gBAC1B7F,EAAM2D,EAAO7E,KAAK,QAAU,KAEtB6E,EAAOvQ,OAAS,IAElB4M,EADgB2D,EAAOkC,KAAK,UACd/G,KAAK,QAAU,IAGjC9O,EAAQ2T,EAAO7E,KAAK,UAAY,OAChC7O,EAAS0T,EAAO7E,KAAK,WAAa,OAE3BoH,GAAalG,EADpBE,EAASyD,EAAO7E,KAAK,WAAa,GACD9O,EAAOC,QGjDzB,WACf,IAAI+T,EAAOrM,GAAShH,MAChB0F,EAAS,GAOb,OANI2N,EAAKtT,SAAQ2F,GAAU,KACvB2N,EAAKwC,aAAYnQ,GAAU,KAC3B2N,EAAKyC,YAAWpQ,GAAU,KAC1B2N,EAAK0C,SAAQrQ,GAAU,KACvB2N,EAAK2C,UAAStQ,GAAU,KACxB2N,EAAK4C,SAAQvQ,GAAU,KACpBA,GCVLwQ,GAAUnW,EAAOsN,OAEjB8I,GAAgB/V,GAAM,WACxB,IAAIgW,EAAKF,GAAQ,IAAK,KAEtB,OADAE,EAAGC,UAAY,EACW,MAAnBD,EAAGlW,KAAK,WAKboW,GAAgBH,IAAiB/V,GAAM,WACzC,OAAQ8V,GAAQ,IAAK,KAAKD,aAUX,CACfM,aARiBJ,IAAiB/V,GAAM,WAExC,IAAIgW,EAAKF,GAAQ,KAAM,MAEvB,OADAE,EAAGC,UAAY,EACU,MAAlBD,EAAGlW,KAAK,UAKfoW,cAAeA,GACfH,cAAeA,ICxBbD,GAAUnW,EAAOsN,UAEJjN,GAAM,WACrB,IAAIgW,EAAKF,GAAQ,IAAK,KACtB,QAASE,EAAGL,QAAUK,EAAGlW,KAAK,OAAsB,MAAbkW,EAAGI,UCJxCN,GAAUnW,EAAOsN,UAEJjN,GAAM,WACrB,IAAIgW,EAAKF,GAAQ,UAAW,KAC5B,MAAiC,MAA1BE,EAAGlW,KAAK,KAAKuW,OAAOpQ,GACI,OAA7B,IAAIiD,QAAQ8M,EAAI,YJChBtN,GAAmBD,GAAuCtI,IAI1DmW,GAAgB7R,GAAO,wBAAyBzB,OAAO3C,UAAU6I,SACjEqN,GAAatJ,OAAO5M,UAAUP,KAC9B0W,GAAcD,GACdE,GAASlV,EAAY,GAAGkV,QACxBlM,GAAUhJ,EAAY,GAAGgJ,SACzBrB,GAAU3H,EAAY,GAAG2H,SACzB1H,GAAcD,EAAY,GAAGE,OAE7BiV,IAEEpB,GAAM,MACVlV,EAAKmW,GAFDlB,GAAM,IAEY,KACtBjV,EAAKmW,GAAYjB,GAAK,KACG,IAAlBD,GAAIY,WAAqC,IAAlBX,GAAIW,WAGhCF,GAAgBY,GAAcR,aAG9BS,QAAuC/U,IAAvB,OAAO/B,KAAK,IAAI,IAExB4W,IAA4BE,IAAiBb,IAAiBc,IAAuBC,MAG/FN,GAAc,SAAc7K,GAC1B,IAIIrG,EAAQyR,EAAQd,EAAW9W,EAAOsL,EAAG5D,EAAQmQ,EAJ7ChB,EAAKpW,KACL2H,EAAQmB,GAAiBsN,GACzBiB,EAAM3V,GAASqK,GACfuL,EAAM3P,EAAM2P,IAGhB,GAAIA,EAIF,OAHAA,EAAIjB,UAAYD,EAAGC,UACnB3Q,EAASlF,EAAKoW,GAAaU,EAAKD,GAChCjB,EAAGC,UAAYiB,EAAIjB,UACZ3Q,EAGT,IAAI+Q,EAAS9O,EAAM8O,OACfR,EAASE,IAAiBC,EAAGH,OAC7BO,EAAQhW,EAAK+W,GAAanB,GAC1B7M,EAAS6M,EAAG7M,OACZiO,EAAa,EACbC,EAAUJ,EA+Cd,GA7CIpB,IACFO,EAAQlN,GAAQkN,EAAO,IAAK,KACC,IAAzB7L,GAAQ6L,EAAO,OACjBA,GAAS,KAGXiB,EAAU7V,GAAYyV,EAAKjB,EAAGC,WAE1BD,EAAGC,UAAY,KAAOD,EAAGN,WAAaM,EAAGN,WAA+C,OAAlCe,GAAOQ,EAAKjB,EAAGC,UAAY,MACnF9M,EAAS,OAASA,EAAS,IAC3BkO,EAAU,IAAMA,EAChBD,KAIFL,EAAS,IAAI9J,OAAO,OAAS9D,EAAS,IAAKiN,IAGzCQ,KACFG,EAAS,IAAI9J,OAAO,IAAM9D,EAAS,WAAYiN,IAE7CM,KAA0BT,EAAYD,EAAGC,WAE7C9W,EAAQiB,EAAKmW,GAAYV,EAASkB,EAASf,EAAIqB,GAE3CxB,EACE1W,GACFA,EAAM+F,MAAQ1D,GAAYrC,EAAM+F,MAAOkS,GACvCjY,EAAM,GAAKqC,GAAYrC,EAAM,GAAIiY,GACjCjY,EAAMuK,MAAQsM,EAAGC,UACjBD,EAAGC,WAAa9W,EAAM,GAAGkD,QACpB2T,EAAGC,UAAY,EACbS,IAA4BvX,IACrC6W,EAAGC,UAAYD,EAAGrW,OAASR,EAAMuK,MAAQvK,EAAM,GAAGkD,OAAS4T,GAEzDW,IAAiBzX,GAASA,EAAMkD,OAAS,GAG3CjC,EAAKkW,GAAenX,EAAM,GAAI4X,GAAQ,WACpC,IAAKtM,EAAI,EAAGA,EAAIjK,UAAU6B,OAAS,EAAGoI,SACf5I,IAAjBrB,UAAUiK,KAAkBtL,EAAMsL,QAAK5I,MAK7C1C,GAASkX,EAEX,IADAlX,EAAMkX,OAASxP,EAASqL,GAAO,MAC1BzH,EAAI,EAAGA,EAAI4L,EAAOhU,OAAQoI,IAE7B5D,GADAmQ,EAAQX,EAAO5L,IACF,IAAMtL,EAAM6X,EAAM,IAInC,OAAO7X,IAIX,OAAiBqX,GK9GjB1I,GAAE,CAAE9C,OAAQ,SAAUiD,OAAO,EAAM9B,OAAQ,IAAIrM,OAASA,IAAQ,CAC9DA,KAAMA,KCPR,IAAIqB,GAAoBtB,SAASQ,UAC7BE,GAAQY,GAAkBZ,MAC1BD,GAAOa,GAAkBb,KACzBF,GAAOe,GAAkBf,QAGM,iBAAXkX,SAAuBA,QAAQ/W,QAAUD,GAAOF,GAAKE,KAAKC,IAAS,WACzF,OAAOH,GAAKG,MAAMA,GAAOC,aCGvBiT,GAAUxO,GAAgB,WAC1BsS,GAAkBtK,OAAO5M,UCNzBoW,GAASlV,EAAY,GAAGkV,QACxBe,GAAajW,EAAY,GAAGiW,YAC5BhW,GAAcD,EAAY,GAAGE,OAE7BsI,GAAe,SAAU0N,GAC3B,OAAO,SAAUxN,EAAOyN,GACtB,IAGIC,EAAOC,EAHPC,EAAIvW,GAASS,EAAuBkI,IACpC6N,EAAWlO,GAAoB8N,GAC/BK,EAAOF,EAAExV,OAEb,OAAIyV,EAAW,GAAKA,GAAYC,EAAaN,EAAoB,QAAK5V,GACtE8V,EAAQH,GAAWK,EAAGC,IACP,OAAUH,EAAQ,OAAUG,EAAW,IAAMC,IACtDH,EAASJ,GAAWK,EAAGC,EAAW,IAAM,OAAUF,EAAS,MAC3DH,EACEhB,GAAOoB,EAAGC,GACVH,EACFF,EACEjW,GAAYqW,EAAGC,EAAUA,EAAW,GACVF,EAAS,OAAlCD,EAAQ,OAAU,IAA0B,WAIxC,CAGfK,OAAQjO,IAAa,GAGrB0M,OAAQ1M,IAAa,ICjCnB0M,GAAShO,GAAyCgO,UAIrC,SAAUoB,EAAGnO,EAAOkM,GACnC,OAAOlM,GAASkM,EAAUa,GAAOoB,EAAGnO,GAAOrH,OAAS,ICHlDiH,GAAQ/J,KAAK+J,MACbmN,GAASlV,EAAY,GAAGkV,QACxBvN,GAAU3H,EAAY,GAAG2H,SACzB1H,GAAcD,EAAY,GAAGE,OAC7BwW,GAAuB,8BACvBC,GAAgC,yBAInB,SAAUC,EAASlB,EAAKa,EAAUM,EAAUC,EAAelN,GAC1E,IAAImN,EAAUR,EAAWK,EAAQ9V,OAC7BkW,EAAIH,EAAS/V,OACbmW,EAAUN,GAKd,YAJsBrW,IAAlBwW,IACFA,EAAgBjU,GAASiU,GACzBG,EAAUP,IAEL/O,GAAQiC,EAAaqN,GAAS,SAAUrZ,EAAOsZ,GACpD,IAAIC,EACJ,OAAQjC,GAAOgC,EAAI,IACjB,IAAK,IAAK,MAAO,IACjB,IAAK,IAAK,OAAON,EACjB,IAAK,IAAK,OAAO3W,GAAYyV,EAAK,EAAGa,GACrC,IAAK,IAAK,OAAOtW,GAAYyV,EAAKqB,GAClC,IAAK,IACHI,EAAUL,EAAc7W,GAAYiX,EAAI,GAAI,IAC5C,MACF,QACE,IAAIE,GAAKF,EACT,GAAU,IAANE,EAAS,OAAOxZ,EACpB,GAAIwZ,EAAIJ,EAAG,CACT,IAAI9R,EAAI6C,GAAMqP,EAAI,IAClB,OAAU,IAANlS,EAAgBtH,EAChBsH,GAAK8R,OAA8B1W,IAApBuW,EAAS3R,EAAI,GAAmBgQ,GAAOgC,EAAI,GAAKL,EAAS3R,EAAI,GAAKgQ,GAAOgC,EAAI,GACzFtZ,EAETuZ,EAAUN,EAASO,EAAI,GAE3B,YAAmB9W,IAAZ6W,EAAwB,GAAKA,MClCpC9W,GAAYjC,EAAOiC,aAIN,SAAUgX,EAAGf,GAC5B,IAAI/X,EAAO8Y,EAAE9Y,KACb,GAAImC,GAAWnC,GAAO,CACpB,IAAIwF,EAASlF,EAAKN,EAAM8Y,EAAGf,GAE3B,OADe,OAAXvS,GAAiBsB,GAAStB,GACvBA,EAET,GAAmB,WAAf3D,EAAQiX,GAAiB,OAAOxY,EAAKyY,GAAYD,EAAGf,GACxD,MAAMjW,GAAU,gDCDdkX,GAAU7T,GAAgB,WAC1BuE,GAAMjK,KAAKiK,IACXC,GAAMlK,KAAKkK,IACXkB,GAASpJ,EAAY,GAAGoJ,QACxB5G,GAAOxC,EAAY,GAAGwC,MACtBgV,GAAgBxX,EAAY,GAAGgJ,SAC/B/I,GAAcD,EAAY,GAAGE,OAQ7BuX,GAEgC,OAA3B,IAAI9P,QAAQ,IAAK,MAItB+P,KACE,IAAIH,KAC6B,KAA5B,IAAIA,IAAS,IAAK,eC5BbI,GAAaC,uBAAAA,OACjBA,MAAUC,KNAL,SAAUC,EAAKvZ,EAAMwZ,EAAQC,GAC5C,IAAIC,EAASvU,GAAgBoU,GAEzBI,GAAuBzZ,GAAM,WAE/B,IAAImG,EAAI,GAER,OADAA,EAAEqT,GAAU,WAAc,OAAO,GACZ,GAAd,GAAGH,GAAKlT,MAGbuT,EAAoBD,IAAwBzZ,GAAM,WAEpD,IAAI2Z,GAAa,EACb3D,EAAK,IAkBT,MAhBY,UAARqD,KAIFrD,EAAK,IAGFpC,YAAc,GACjBoC,EAAGpC,YAAYH,IAAW,WAAc,OAAOuC,GAC/CA,EAAGI,MAAQ,GACXJ,EAAGwD,GAAU,IAAIA,IAGnBxD,EAAGlW,KAAO,WAAiC,OAAnB6Z,GAAa,EAAa,MAElD3D,EAAGwD,GAAQ,KACHG,KAGV,IACGF,IACAC,GACDJ,EACA,CACA,IAAIM,EAA8BrY,EAAY,IAAIiY,IAC9CK,EAAU/Z,EAAK0Z,EAAQ,GAAGH,IAAM,SAAUS,EAAcC,EAAQ9C,EAAK+C,EAAMC,GAC7E,IAAIC,EAAwB3Y,EAAYuY,GACpCK,EAAQJ,EAAOja,KACnB,OAAIqa,IAAUtB,IAAcsB,IAAU5C,GAAgBzX,KAChD2Z,IAAwBQ,EAInB,CAAEG,MAAM,EAAMpZ,MAAO4Y,EAA4BG,EAAQ9C,EAAK+C,IAEhE,CAAEI,MAAM,EAAMpZ,MAAOkZ,EAAsBjD,EAAK8C,EAAQC,IAE1D,CAAEI,MAAM,MAGjB/N,GAASrJ,OAAO3C,UAAWgZ,EAAKQ,EAAQ,IACxCxN,GAASkL,GAAiBiC,EAAQK,EAAQ,IAGxCN,GAAMvR,GAA4BuP,GAAgBiC,GAAS,QAAQ,GKfzEa,CAA8B,WAAW,SAAUC,EAAGhE,EAAeiE,GACnE,IAAIC,EAAoBvB,GAA+C,IAAM,KAE7E,MAAO,CAGL,SAAiBwB,EAAaC,GAC5B,IAAIvU,EAAIpE,EAAuBnC,MAC3B+a,EAA0B9Y,MAAf4Y,OAA2B5Y,EAAY2D,GAAUiV,EAAa3B,IAC7E,OAAO6B,EACHva,EAAKua,EAAUF,EAAatU,EAAGuU,GAC/Bta,EAAKkW,EAAehV,GAAS6E,GAAIsU,EAAaC,IAIpD,SAAU/O,EAAQ+O,GAChB,IAAIE,EAAKhU,GAAShH,MACdiY,EAAIvW,GAASqK,GAEjB,GACyB,iBAAhB+O,IAC6C,IAApD3B,GAAc2B,EAAcF,KACW,IAAvCzB,GAAc2B,EAAc,MAC5B,CACA,IAAIlK,EAAM+J,EAAgBjE,EAAesE,EAAI/C,EAAG6C,GAChD,GAAIlK,EAAI4J,KAAM,OAAO5J,EAAIxP,MAG3B,IAAI6Z,EAAoB5Y,GAAWyY,GAC9BG,IAAmBH,EAAepZ,GAASoZ,IAEhD,IAAI/a,EAASib,EAAGjb,OAChB,GAAIA,EAAQ,CACV,IAAImb,EAAcF,EAAGhF,QACrBgF,EAAG3E,UAAY,EAGjB,IADA,IAAI8E,EAAU,KACD,CACX,IAAIzV,EAAS0V,GAAWJ,EAAI/C,GAC5B,GAAe,OAAXvS,EAAiB,MAGrB,GADAvB,GAAKgX,EAASzV,IACT3F,EAAQ,MAGI,KADF2B,GAASgE,EAAO,MACVsV,EAAG3E,UAAYgF,GAAmBpD,EAAG/N,GAAS8Q,EAAG3E,WAAY6E,IAKpF,IAFA,IA/EwBxb,EA+EpB4b,EAAoB,GACpBC,EAAqB,EAChB1Q,EAAI,EAAGA,EAAIsQ,EAAQ1Y,OAAQoI,IAAK,CAWvC,IARA,IAAI0N,EAAU7W,IAFdgE,EAASyV,EAAQtQ,IAEa,IAC1BqN,EAAWtO,GAAIC,GAAIG,GAAoBtE,EAAOoE,OAAQmO,EAAExV,QAAS,GACjE+V,EAAW,GAMNgD,EAAI,EAAGA,EAAI9V,EAAOjD,OAAQ+Y,IAAKrX,GAAKqU,OA3FrCvW,KADcvC,EA4F+CgG,EAAO8V,IA3FxD9b,EAAK0D,OAAO1D,IA4FhC,IAAI+Y,EAAgB/S,EAAO+Q,OAC3B,GAAIwE,EAAmB,CACrB,IAAIQ,EAAe1Q,GAAO,CAACwN,GAAUC,EAAUN,EAAUD,QACnChW,IAAlBwW,GAA6BtU,GAAKsX,EAAchD,GACpD,IAAIlN,EAAc7J,GAASf,GAAMma,OAAc7Y,EAAWwZ,SAE1DlQ,EAAcmQ,GAAgBnD,EAASN,EAAGC,EAAUM,EAAUC,EAAeqC,GAE3E5C,GAAYqD,IACdD,GAAqB1Z,GAAYqW,EAAGsD,EAAoBrD,GAAY3M,EACpEgQ,EAAqBrD,EAAWK,EAAQ9V,QAG5C,OAAO6Y,EAAoB1Z,GAAYqW,EAAGsD,SAvFXnb,GAAM,WACzC,IAAIgW,EAAK,IAOT,OANAA,EAAGlW,KAAO,WACR,IAAIwF,EAAS,GAEb,OADAA,EAAO+Q,OAAS,CAAEpQ,EAAG,KACdX,GAGyB,MAA3B,GAAG4D,QAAQ8M,EAAI,aAkFcgD,IAAoBC,IE3HnD,OCVUtZ,EAAO4b,WCAP,SAAUvQ,EAAQiE,EAAKnG,GACtC,IAAK,IAAInF,KAAOsL,EAAK5C,GAASrB,EAAQrH,EAAKsL,EAAItL,GAAMmF,GACrD,OAAOkC,GCDLhI,GAASrD,EAAOqD,OAChBpB,GAAYjC,EAAOiC,aCKN3B,OAAOub,iBAAmB,aAAe,GAAK,WAC7D,IAEIC,EAFAC,GAAiB,EACjBtU,EAAO,GAEX,KAEEqU,EAASla,EAAYtB,OAAOU,yBAAyBV,OAAOI,UAAW,aAAa4G,MAC7EG,EAAM,IACbsU,EAAiBtU,aAAgBiL,MACjC,MAAOtS,IACT,OAAO,SAAwBoG,EAAG8H,GAKhC,OAJArH,GAAST,GDdI,SAAUnE,GACzB,GAAuB,iBAAZA,GAAwBC,GAAWD,GAAW,OAAOA,EAChE,MAAMJ,GAAU,aAAeoB,GAAOhB,GAAY,mBCahD2Z,CAAmB1N,GACfyN,EAAgBD,EAAOtV,EAAG8H,GACzB9H,EAAEyV,UAAY3N,EACZ9H,GAfoD,QAiBzDtE,GC1BF3B,GAAiBuI,GAA+ChC,EAIhE8F,GAAgBtH,GAAgB,kBAEnB,SAAU3F,EAAIuc,EAAK5P,GAC9B3M,IAAO6E,GAAO7E,EAAK2M,EAAS3M,EAAKA,EAAGe,UAAWkM,KACjDrM,GAAeZ,EAAIiN,GAAe,CAAEtL,cAAc,EAAMD,MAAO6a,KCF/DpI,GAAUxO,GAAgB,WCH1BrD,GAAYjC,EAAOiC,aAEN,SAAUtC,EAAIwc,GAC7B,GAAIxZ,GAAcwZ,EAAWxc,GAAK,OAAOA,EACzC,MAAMsC,GAAU,4BCPD,GCGbma,GAAW9W,GAAgB,YAC3BmN,GAAiBC,MAAMhS,UCCvB0b,GAAW9W,GAAgB,eAEd,SAAU3F,GACzB,GAAUuC,MAANvC,EAAiB,OAAOkG,GAAUlG,EAAIyc,KACrCvW,GAAUlG,EAAI,eACd0c,GAAUra,GAAQrC,KCHrBsC,GAAYjC,EAAOiC,aCHN,SAAUwB,EAAU6Y,EAAMjb,GACzC,IAAIkb,EAAaC,EACjBvV,GAASxD,GACT,IAEE,KADA8Y,EAAc1W,GAAUpC,EAAU,WAChB,CAChB,GAAa,UAAT6Y,EAAkB,MAAMjb,EAC5B,OAAOA,EAETkb,EAAc9b,EAAK8b,EAAa9Y,GAChC,MAAOrD,GACPoc,GAAa,EACbD,EAAcnc,EAEhB,GAAa,UAATkc,EAAkB,MAAMjb,EAC5B,GAAImb,EAAY,MAAMD,EAEtB,OADAtV,GAASsV,GACFlb,GCTLY,GAAYjC,EAAOiC,UAEnBwa,GAAS,SAAUC,EAAS/W,GAC9B1F,KAAKyc,QAAUA,EACfzc,KAAK0F,OAASA,GAGZgX,GAAkBF,GAAO/b,aAEZ,SAAUkc,EAAUC,EAAiB1T,GACpD,IAKI1F,EAAUqZ,EAAQ/S,EAAOrH,EAAQiD,EAAQoX,EAAMC,EJpB1Brd,EIerB2T,EAAOnK,GAAWA,EAAQmK,KAC1B2J,KAAgB9T,IAAWA,EAAQ8T,YACnCC,KAAiB/T,IAAWA,EAAQ+T,aACpCC,KAAiBhU,IAAWA,EAAQgU,aACpCzb,EAAKf,GAAKkc,EAAiBvJ,GAG3B8J,EAAO,SAAUC,GAEnB,OADI5Z,GAAU6Z,GAAc7Z,EAAU,SAAU4Z,GACzC,IAAIZ,IAAO,EAAMY,IAGtBE,EAAS,SAAUlc,GACrB,OAAI4b,GACFhW,GAAS5F,GACF8b,EAAczb,EAAGL,EAAM,GAAIA,EAAM,GAAI+b,GAAQ1b,EAAGL,EAAM,GAAIA,EAAM,KAChE8b,EAAczb,EAAGL,EAAO+b,GAAQ1b,EAAGL,IAG9C,GAAI6b,EACFzZ,EAAWmZ,MACN,CAEL,KADAE,EAASU,GAAkBZ,IACd,MAAM3a,GAAU2B,GAAYgZ,GAAY,oBAErD,QJvCY1a,KADWvC,EIwCGmd,KJvCAT,GAAU3J,QAAU/S,GAAM8S,GAAe2J,MAAczc,GIuC9C,CACjC,IAAKoK,EAAQ,EAAGrH,EAAS+H,GAAkBmS,GAAWla,EAASqH,EAAOA,IAEpE,IADApE,EAAS4X,EAAOX,EAAS7S,MACXpH,GAAcga,GAAiBhX,GAAS,OAAOA,EAC7D,OAAO,IAAI8W,IAAO,GAEtBhZ,EF5Ca,SAAUpB,EAAUob,GACnC,IAAIC,EAAiB7c,UAAU6B,OAAS,EAAI8a,GAAkBnb,GAAYob,EAC1E,GAAI1Z,GAAU2Z,GAAiB,OAAOzW,GAASxG,EAAKid,EAAgBrb,IACpE,MAAMJ,GAAU2B,GAAYvB,GAAY,oBEyC3Bsb,CAAYf,EAAUE,GAInC,IADAC,EAAOtZ,EAASsZ,OACPC,EAAOvc,EAAKsc,EAAMtZ,IAAWgX,MAAM,CAC1C,IACE9U,EAAS4X,EAAOP,EAAK3b,OACrB,MAAOjB,GACPkd,GAAc7Z,EAAU,QAASrD,GAEnC,GAAqB,iBAAVuF,GAAsBA,GAAUhD,GAAcga,GAAiBhX,GAAS,OAAOA,EAC1F,OAAO,IAAI8W,IAAO,IC9DlBL,GAAW9W,GAAgB,YAC3BsY,IAAe,EAEnB,IACE,IAAI/J,GAAS,EACTgK,GAAqB,CACvBd,KAAM,WACJ,MAAO,CAAEtC,OAAQ5G,OAEnBiK,OAAU,WACRF,IAAe,IAGnBC,GAAmBzB,IAAY,WAC7B,OAAOnc,MAGTyS,MAAMqL,KAAKF,IAAoB,WAAc,MAAM,KACnD,MAAOzd,IAET,ICAI4d,GAAUC,GAAOC,GAASC,MDAb,SAAUhe,EAAMie,GAC/B,IAAKA,IAAiBR,GAAc,OAAO,EAC3C,IAAIS,GAAoB,EACxB,IACE,IAAInX,EAAS,GACbA,EAAOkV,IAAY,WACjB,MAAO,CACLW,KAAM,WACJ,MAAO,CAAEtC,KAAM4D,GAAoB,MAIzCle,EAAK+G,GACL,MAAO9G,IACT,OAAOie,GEhCLpc,GAAYjC,EAAOiC,UCAnB6R,GAAUxO,GAAgB,cAIb,SAAUkB,EAAG8X,GAC5B,IACIpG,EADAlE,EAAI/M,GAAST,GAAGyN,YAEpB,YAAa/R,IAAN8R,GAAiD9R,OAA7BgW,EAAIjR,GAAS+M,GAAGF,KAAyBwK,EDJrD,SAAUjc,GACzB,GAAI6R,GAAc7R,GAAW,OAAOA,EACpC,MAAMJ,GAAU2B,GAAYvB,GAAY,yBCEiDkc,CAAarG,OCTvFtW,EAAY,GAAGE,UCAf,qCAAqC2F,KAAKxE,OCCf,WAA3BjB,EAAQhC,EAAO6C,SLS5ByE,GAAMtH,EAAOwe,aACbC,GAAQze,EAAO0e,eACf7b,GAAU7C,EAAO6C,QACjB8b,GAAW3e,EAAO2e,SAClBze,GAAWF,EAAOE,SAClB0e,GAAiB5e,EAAO4e,eACxBvb,GAASrD,EAAOqD,OAChBwb,GAAU,EACVC,GAAQ,GAIZ,IAEEd,GAAWhe,EAAOge,SAClB,MAAO5d,IAET,IAAI2e,GAAM,SAAUra,GAClB,GAAIF,GAAOsa,GAAOpa,GAAK,CACrB,IAAIhD,EAAKod,GAAMpa,UACRoa,GAAMpa,GACbhD,MAIAsd,GAAS,SAAUta,GACrB,OAAO,WACLqa,GAAIra,KAIJua,GAAW,SAAUC,GACvBH,GAAIG,EAAMtT,OAGRuT,GAAO,SAAUza,GAEnB1E,EAAOof,YAAY/b,GAAOqB,GAAKsZ,GAASqB,SAAW,KAAOrB,GAASsB,OAIhEhY,IAAQmX,KACXnX,GAAM,SAAsB5F,GAC1B,IAAI6d,EAAOC,GAAW3e,UAAW,GAKjC,OAJAie,KAAQD,IAAW,WACjBje,GAAM0B,GAAWZ,GAAMA,EAAKxB,GAASwB,QAAKQ,EAAWqd,IAEvDtB,GAAMY,IACCA,IAETJ,GAAQ,SAAwB/Z,UACvBoa,GAAMpa,IAGX+a,GACFxB,GAAQ,SAAUvZ,GAChB7B,GAAQ6c,SAASV,GAAOta,KAGjBia,IAAYA,GAASgB,IAC9B1B,GAAQ,SAAUvZ,GAChBia,GAASgB,IAAIX,GAAOta,KAIbka,KAAmBgB,IAE5BzB,IADAD,GAAU,IAAIU,IACCiB,MACf3B,GAAQ4B,MAAMC,UAAYd,GAC1BhB,GAAQtd,GAAKwd,GAAKiB,YAAajB,KAI/Bne,EAAOggB,kBACP1d,GAAWtC,EAAOof,eACjBpf,EAAOigB,eACRjC,IAAkC,UAAtBA,GAASqB,WACpBhf,EAAM8e,KAEPlB,GAAQkB,GACRnf,EAAOggB,iBAAiB,UAAWf,IAAU,IAG7ChB,GA1EqB,uBAyEU7X,GAAc,UACrC,SAAU1B,GAChBgK,GAAKyD,YAAY/L,GAAc,WAA6B,mBAAI,WAC9DsI,GAAKwR,YAAYjgB,MACjB8e,GAAIra,KAKA,SAAUA,GAChByb,WAAWnB,GAAOta,GAAK,KAK7B,IM5FI0b,GAAOC,GAAMC,GAAMC,GAAQC,GAAQC,GAAMC,GAASC,MN4FrC,CACfrZ,IAAKA,GACLmX,MAAOA,OO5GQ,oBAAoBhX,KAAKxE,UAAgCf,IAAlBlC,EAAO4gB,UCD9C,qBAAqBnZ,KAAKxE,IFAvCjC,GAA2B8H,GAA2DhC,EACtF+Z,GAAYC,GAA6BxZ,IAMzCyZ,GAAmB/gB,EAAO+gB,kBAAoB/gB,EAAOghB,uBACrD9a,GAAWlG,EAAOkG,SAClBrD,GAAU7C,EAAO6C,QACjB+Y,GAAU5b,EAAO4b,QAEjBqF,GAA2BjgB,GAAyBhB,EAAQ,kBAC5DkhB,GAAiBD,IAA4BA,GAAyB5f,MAKrE6f,KACHd,GAAQ,WACN,IAAIzR,EAAQjN,EAEZ,IADI+d,KAAY9Q,EAAS9L,GAAQkP,SAASpD,EAAOwS,OAC1Cd,IAAM,CACX3e,EAAK2e,GAAK3e,GACV2e,GAAOA,GAAKtD,KACZ,IACErb,IACA,MAAOtB,GAGP,MAFIigB,GAAME,KACLD,QAAOpe,EACN9B,GAERkgB,QAAOpe,EACLyM,GAAQA,EAAOyS,SAKhBxB,IAAWH,IAAY4B,KAAmBN,KAAoB7a,IAQvDob,IAAiB1F,IAAWA,GAAQ2F,UAE9Cb,GAAU9E,GAAQ2F,aAAQrf,IAElB+R,YAAc2H,GACtB+E,GAAOhgB,GAAK+f,GAAQC,KAAMD,IAC1BH,GAAS,WACPI,GAAKP,MAGEX,GACTc,GAAS,WACP1d,GAAQ6c,SAASU,MAUnBS,GAAYlgB,GAAKkgB,GAAW7gB,GAC5BugB,GAAS,WACPM,GAAUT,OA/BZI,IAAS,EACTC,GAAOva,GAASsb,eAAe,IAC/B,IAAIT,GAAiBX,IAAOqB,QAAQhB,GAAM,CAAEiB,eAAe,IAC3DnB,GAAS,WACPE,GAAK7U,KAAO4U,IAAUA,MAgC5B,IGlBImB,GAAUC,GAAsBC,GAAgBC,MHkBnCZ,IAAkB,SAAUxf,GAC3C,IAAIqgB,EAAO,CAAErgB,GAAIA,EAAIqb,UAAM7a,GACvBoe,KAAMA,GAAKvD,KAAOgF,GACjB1B,KACHA,GAAO0B,EACPxB,MACAD,GAAOyB,GIhFPC,GAAoB,SAAUhO,GAChC,IAAIuN,EAASU,EACbhiB,KAAKygB,QAAU,IAAI1M,GAAE,SAAUkO,EAAWC,GACxC,QAAgBjgB,IAAZqf,QAAoCrf,IAAX+f,EAAsB,MAAMhgB,UAAU,2BACnEsf,EAAUW,EACVD,EAASE,KAEXliB,KAAKshB,QAAUxd,GAAUwd,GACzBthB,KAAKgiB,OAASle,GAAUke,UAKP,SAAUjO,GAC3B,OAAO,IAAIgO,GAAkBhO,QCjBd,SAAU7T,GACzB,IACE,MAAO,CAAEC,OAAO,EAAOiB,MAAOlB,KAC9B,MAAOC,GACP,MAAO,CAAEA,OAAO,EAAMiB,MAAOjB,QCJC,iBAAVN,OHoBpBiiB,GAAOjZ,GAA6BxB,IAapCwM,GAAUxO,GAAgB,WAC1B8c,GAAU,UAEVrZ,GAAmBC,GAAoBT,UAAU6Z,IACjDC,GAAmBrZ,GAAoB1B,IACvCgb,GAA0BtZ,GAAoBT,UAAU6Z,IACxDG,GAAyBC,IAAiBA,GAAc9hB,UACxD+hB,GAAqBD,GACrBE,GAAmBH,GACnBtgB,GAAYjC,EAAOiC,UACnBiE,GAAWlG,EAAOkG,SAClBrD,GAAU7C,EAAO6C,QACjB8f,GAAuBC,GAA2B9b,EAClD+b,GAA8BF,GAE9BG,MAAoB5c,IAAYA,GAAS6c,aAAe/iB,EAAOgjB,eAC/DC,GAAyB3gB,GAAWtC,EAAOkjB,uBAQ3CC,IAAc,EAIdxJ,GAASlO,GAAS2W,IAAS,WAC7B,IAAIgB,EAA6B/b,GAAcob,IAC3CY,EAAyBD,IAA+B/f,OAAOof,IAInE,IAAKY,GAAyC,KAAf9f,GAAmB,OAAO,EAMzD,GAAIA,IAAc,IAAM,cAAckE,KAAK2b,GAA6B,OAAO,EAE/E,IAAI1C,EAAU,IAAI+B,IAAmB,SAAUlB,GAAWA,EAAQ,MAC9D+B,EAAc,SAAUnjB,GAC1BA,GAAK,eAA6B,gBAKpC,OAHkBugB,EAAQzM,YAAc,IAC5BH,IAAWwP,IACvBH,GAAczC,EAAQC,MAAK,yBAAwC2C,KAG3DD,GAA0BE,KAAeN,MAG/CO,GAAsB7J,KAAW8J,IAA4B,SAAU7G,GACzE6F,GAAmBiB,IAAI9G,GAAiB,OAAE,kBAIxC+G,GAAa,SAAUhkB,GACzB,IAAIghB,EACJ,SAAOlb,GAAS9F,KAAO2C,GAAWqe,EAAOhhB,EAAGghB,QAAQA,GAGlDJ,GAAS,SAAU3Y,EAAOgc,GAC5B,IAAIhc,EAAMic,SAAV,CACAjc,EAAMic,UAAW,EACjB,IAAIC,EAAQlc,EAAMmc,UAClBC,IAAU,WAKR,IAJA,IAAI3iB,EAAQuG,EAAMvG,MACdjC,EAlDQ,GAkDHwI,EAAMA,MACXmC,EAAQ,EAEL+Z,EAAMphB,OAASqH,GAAO,CAC3B,IAKIpE,EAAQgb,EAAMsD,EALdC,EAAWJ,EAAM/Z,KACjBoa,EAAU/kB,EAAK8kB,EAAS9kB,GAAK8kB,EAASE,KACtC7C,EAAU2C,EAAS3C,QACnBU,EAASiC,EAASjC,OAClBlQ,EAASmS,EAASnS,OAEtB,IACMoS,GACG/kB,IA3DC,IA4DAwI,EAAMyc,WAAyBC,GAAkB1c,GACrDA,EAAMyc,UA9DJ,IAgEY,IAAZF,EAAkBxe,EAAStE,GAEzB0Q,GAAQA,EAAOqP,QACnBzb,EAASwe,EAAQ9iB,GACb0Q,IACFA,EAAOoP,OACP8C,GAAS,IAGTte,IAAWue,EAASxD,QACtBuB,EAAOhgB,GAAU,yBACR0e,EAAOgD,GAAWhe,IAC3BlF,EAAKkgB,EAAMhb,EAAQ4b,EAASU,GACvBV,EAAQ5b,IACVsc,EAAO5gB,GACd,MAAOjB,GACH2R,IAAWkS,GAAQlS,EAAOoP,OAC9Bc,EAAO7hB,IAGXwH,EAAMmc,UAAY,GAClBnc,EAAMic,UAAW,EACbD,IAAahc,EAAMyc,WAAWE,GAAY3c,QAI9Cob,GAAgB,SAAU7d,EAAMub,EAAS8D,GAC3C,IAAItF,EAAOiF,EACPrB,KACF5D,EAAQhZ,GAAS6c,YAAY,UACvBrC,QAAUA,EAChBxB,EAAMsF,OAASA,EACftF,EAAMuF,UAAUtf,GAAM,GAAO,GAC7BnF,EAAOgjB,cAAc9D,IAChBA,EAAQ,CAAEwB,QAASA,EAAS8D,OAAQA,IACtCvB,KAA2BkB,EAAUnkB,EAAO,KAAOmF,IAAQgf,EAAQjF,GAxGhD,uBAyGf/Z,GIzJM,SAAUmB,EAAGoe,GAC5B,IAAIC,EAAU3kB,EAAO2kB,QACjBA,GAAWA,EAAQvkB,QACD,GAApBS,UAAU6B,OAAciiB,EAAQvkB,MAAMkG,GAAKqe,EAAQvkB,MAAMkG,EAAGoe,IJsJvBE,CAAiB,8BAA+BJ,IAGrFD,GAAc,SAAU3c,GAC1BnH,EAAKshB,GAAM/hB,GAAQ,WACjB,IAGI2F,EAHA+a,EAAU9Y,EAAMK,OAChB5G,EAAQuG,EAAMvG,MAGlB,GAFmBwjB,GAAYjd,KAG7BjC,EAASmf,IAAQ,WACXrF,GACF5c,GAAQkiB,KAAK,qBAAsB1jB,EAAOqf,GACrCsC,GAtHW,qBAsHwBtC,EAASrf,MAGrDuG,EAAMyc,UAAY5E,IAAWoF,GAAYjd,GAnH/B,EADF,EAqHJjC,EAAOvF,OAAO,MAAMuF,EAAOtE,UAKjCwjB,GAAc,SAAUjd,GAC1B,OA3HY,IA2HLA,EAAMyc,YAA0Bzc,EAAM+G,QAG3C2V,GAAoB,SAAU1c,GAChCnH,EAAKshB,GAAM/hB,GAAQ,WACjB,IAAI0gB,EAAU9Y,EAAMK,OAChBwX,GACF5c,GAAQkiB,KAAK,mBAAoBrE,GAC5BsC,GAvIa,mBAuIoBtC,EAAS9Y,EAAMvG,WAIvDV,GAAO,SAAUe,EAAIkG,EAAOod,GAC9B,OAAO,SAAU3jB,GACfK,EAAGkG,EAAOvG,EAAO2jB,KAIjBC,GAAiB,SAAUrd,EAAOvG,EAAO2jB,GACvCpd,EAAM6S,OACV7S,EAAM6S,MAAO,EACTuK,IAAQpd,EAAQod,GACpBpd,EAAMvG,MAAQA,EACduG,EAAMA,MAnJO,EAoJb2Y,GAAO3Y,GAAO,KAGZsd,GAAkB,SAAUtd,EAAOvG,EAAO2jB,GAC5C,IAAIpd,EAAM6S,KAAV,CACA7S,EAAM6S,MAAO,EACTuK,IAAQpd,EAAQod,GACpB,IACE,GAAIpd,EAAMK,SAAW5G,EAAO,MAAMY,GAAU,oCAC5C,IAAI0e,EAAOgD,GAAWtiB,GAClBsf,EACFqD,IAAU,WACR,IAAImB,EAAU,CAAE1K,MAAM,GACtB,IACEha,EAAKkgB,EAAMtf,EACTV,GAAKukB,GAAiBC,EAASvd,GAC/BjH,GAAKskB,GAAgBE,EAASvd,IAEhC,MAAOxH,GACP6kB,GAAeE,EAAS/kB,EAAOwH,QAInCA,EAAMvG,MAAQA,EACduG,EAAMA,MA7KI,EA8KV2Y,GAAO3Y,GAAO,IAEhB,MAAOxH,GACP6kB,GAAe,CAAExK,MAAM,GAASra,EAAOwH,MAK3C,GAAI+R,KAaF+I,IAXAD,GAAqB,SAAiB2C,GACpCC,GAAWplB,KAAMyiB,IACjB3e,GAAUqhB,GACV3kB,EAAKkhB,GAAU1hB,MACf,IAAI2H,EAAQmB,GAAiB9I,MAC7B,IACEmlB,EAASzkB,GAAKukB,GAAiBtd,GAAQjH,GAAKskB,GAAgBrd,IAC5D,MAAOxH,GACP6kB,GAAerd,EAAOxH,MAGYM,WAEtCihB,GAAW,SAAiByD,GAC1B/C,GAAiBpiB,KAAM,CACrBwI,KAAM2Z,GACN3H,MAAM,EACNoJ,UAAU,EACVlV,QAAQ,EACRoV,UAAW,GACXM,WAAW,EACXzc,MA9MQ,EA+MRvG,WAAOa,MAGFxB,UAAY4kB,GAAY5C,GAAkB,CAGjD/B,KAAM,SAAc4E,EAAaC,GAC/B,IAAI5d,EAAQ0a,GAAwBriB,MAChC8jB,EAAYnc,EAAMmc,UAClBG,EAAWvB,GAAqB8C,GAAmBxlB,KAAMwiB,KAO7D,OANAyB,EAAS9kB,IAAKkD,GAAWijB,IAAeA,EACxCrB,EAASE,KAAO9hB,GAAWkjB,IAAeA,EAC1CtB,EAASnS,OAAS0N,GAAU5c,GAAQkP,YAAS7P,EAC7C0F,EAAM+G,QAAS,EACfoV,EAAUA,EAAUrhB,QAAUwhB,EA7NtB,GA8NJtc,EAAMA,OAAkB2Y,GAAO3Y,GAAO,GACnCsc,EAASxD,SAIlBgF,MAAS,SAAUF,GACjB,OAAOvlB,KAAK0gB,UAAKze,EAAWsjB,MAGhC5D,GAAuB,WACrB,IAAIlB,EAAU,IAAIiB,GACd/Z,EAAQmB,GAAiB2X,GAC7BzgB,KAAKygB,QAAUA,EACfzgB,KAAKshB,QAAU5gB,GAAKukB,GAAiBtd,GACrC3H,KAAKgiB,OAASthB,GAAKskB,GAAgBrd,IAErCgb,GAA2B9b,EAAI6b,GAAuB,SAAU3O,GAC9D,OAAOA,IAAMyO,IAAsBzO,IAAM6N,GACrC,IAAID,GAAqB5N,GACzB6O,GAA4B7O,IAGlB1R,GAAWkgB,KAAkBD,KAA2BjiB,OAAOI,WAAW,CACxFohB,GAAaS,GAAuB5B,KAE/BwC,KAEHzW,GAAS6V,GAAwB,QAAQ,SAAcgD,EAAaC,GAClE,IAAIlS,EAAOrT,KACX,OAAO,IAAIwiB,IAAmB,SAAUlB,EAASU,GAC/CxhB,EAAKqhB,GAAYxO,EAAMiO,EAASU,MAC/BtB,KAAK4E,EAAaC,KAEpB,CAAEpc,QAAQ,IAGbsD,GAAS6V,GAAwB,QAASG,GAAwB,MAAG,CAAEtZ,QAAQ,KAIjF,WACSmZ,GAAuBtO,YAC9B,MAAO7T,IAGLyb,IACFA,GAAe0G,GAAwBG,gBKhTdxT,EAAoBI,EAAaE,uBAAAA,kHACzDF,GAGLJ,EAAOyW,mBAGDvW,EAAiDF,EAAO0W,cAAc,eAApEC,oBAAiBC,eAAYC,qBACdD,EAAWxW,EAAKE,sBACf,iBADlBwW,EAAW3W,WAGfH,EAAO+W,MAAMD,EAAU,cAGT,MAAZA,SAMkBD,EAAczW,kBAES,KAFzC4W,EAAY7W,UAEF3B,OAAO9C,QAAQ,cAC3Bsb,EAA2BA,E9BtBlB3c,QAAQ,KAAM,QAAQA,QAAQ,KAAM,S8B0BzC4c,EAAsB,CAC1B1d,KAAM,QACN6G,IAAK4W,EACL1W,SACAP,SAAU,CAAC,CAAEwG,KAAM,MAKrBmG,QAAQ2F,UAAUZ,MAAK,WACrByF,EAAWC,YAAYnX,EAAQiX,MAIjCN,EAAgBM,cCnClB,SAASG,YACA/M,GAAa,oBN8StBpL,GAAE,CAAEnO,QAAQ,EAAMumB,MAAM,EAAM/Z,OAAQmN,IAAU,CAC9CiC,QAAS6G,KAGX+D,GAAe/D,GAAoBL,IAAS,GlBjU3B,SAAUqE,GACzB,IAAIC,EAAc9jB,GAAW6jB,GACzBlmB,EAAiB4G,GAAqBL,EAEtCT,GAAeqgB,IAAgBA,EAAY5S,KAC7CvT,EAAemmB,EAAa5S,GAAS,CACnCxS,cAAc,EACdd,IAAK,WAAc,OAAOP,QkB2ThC0mB,CAAWvE,IAEXP,GAAiBjf,GAAWwf,IAG5BjU,GAAE,CAAE9C,OAAQ+W,GAAS7V,MAAM,EAAMC,OAAQmN,IAAU,CAGjDsI,OAAQ,SAAgB2E,GACtB,IAAIC,EAAalE,GAAqB1iB,MAEtC,OADAQ,EAAKomB,EAAW5E,YAAQ/f,EAAW0kB,GAC5BC,EAAWnG,WAItBvS,GAAE,CAAE9C,OAAQ+W,GAAS7V,MAAM,EAAMC,OAAmBmN,IAAU,CAG5D4H,QAAS,SAAiBuF,GACxB,OOzVa,SAAU9S,EAAG8S,GAE5B,GADA7f,GAAS+M,GACLvO,GAASqhB,IAAMA,EAAE7S,cAAgBD,EAAG,OAAO8S,EAC/C,IAAIC,EAAoBpE,GAAqB7b,EAAEkN,GAG/C,OADAuN,EADcwF,EAAkBxF,SACxBuF,GACDC,EAAkBrG,QPmVhBsG,CAAyE/mB,KAAM6mB,MAI1F3Y,GAAE,CAAE9C,OAAQ+W,GAAS7V,MAAM,EAAMC,OAAQgX,IAAuB,CAG9DE,IAAK,SAAa9G,GAChB,IAAI5I,EAAI/T,KACJ4mB,EAAalE,GAAqB3O,GAClCuN,EAAUsF,EAAWtF,QACrBU,EAAS4E,EAAW5E,OACpBtc,EAASmf,IAAQ,WACnB,IAAImC,EAAkBljB,GAAUiQ,EAAEuN,SAC9B2F,EAAS,GACTrI,EAAU,EACVsI,EAAY,EAChBC,GAAQxK,GAAU,SAAU8D,GAC1B,IAAI3W,EAAQ8U,IACRwI,GAAgB,EACpBF,IACA1mB,EAAKwmB,EAAiBjT,EAAG0M,GAASC,MAAK,SAAUtf,GAC3CgmB,IACJA,GAAgB,EAChBH,EAAOnd,GAAS1I,IACd8lB,GAAa5F,EAAQ2F,MACtBjF,QAEHkF,GAAa5F,EAAQ2F,MAGzB,OADIvhB,EAAOvF,OAAO6hB,EAAOtc,EAAOtE,OACzBwlB,EAAWnG,SAIpB4G,KAAM,SAAc1K,GAClB,IAAI5I,EAAI/T,KACJ4mB,EAAalE,GAAqB3O,GAClCiO,EAAS4E,EAAW5E,OACpBtc,EAASmf,IAAQ,WACnB,IAAImC,EAAkBljB,GAAUiQ,EAAEuN,SAClC6F,GAAQxK,GAAU,SAAU8D,GAC1BjgB,EAAKwmB,EAAiBjT,EAAG0M,GAASC,KAAKkG,EAAWtF,QAASU,SAI/D,OADItc,EAAOvF,OAAO6hB,EAAOtc,EAAOtE,OACzBwlB,EAAWnG,WMjXtB,0CACmB6G,EAAE,wC9BdnB,qX8BgBe,yBACM,kBACC,kBACe,qBACPjB,wBACGA,mBACLA,YAE5BkB,qBAAA,SAAStY,SAEA,IAGTsY,qBAAA,SAAStY,UAEA,GAGTsY,iBAAA,SAAKtY,EAAoB7N,KAKzBmmB,uBAAA,SAAWtY,OACDuY,EAAcvY,mBACL,MAAbuY,KACCC,EAAMC,YAAYF,MAED7X,EAAUgY,iBAAiB1Y,GACd+F,MAAK,SAAAjC,OAChCvK,EAAOmH,EAAUiY,YAAY7U,SACtB,QAATvK,IACS,cAATA,KACAyG,EAAO4Y,OAAO9U,SAQtBwU,iCAAA,SAAqBtY,UACZ,MAGTsY,gCAAA,SAAoBtY,cACZE,EAA0CnP,KAAxC8nB,eAAYC,kBAAeC,aAG7B5Y,EAAA6Y,EAAmCC,EACvCZ,EAAE,wBACFQ,EACAR,EAAE,uCAHGa,OAAkBC,OAKnB9Y,EAAA2Y,EAAyCC,EAC7CZ,EAAE,2BACFS,EACAT,EAAE,0CAHGe,OAAqBC,OAKtBC,EAAYra,EAAEka,GACdI,EAAeta,EAAEoa,GAChBG,EAADR,EAAwBS,EAAoBV,EAAUV,EAAE,4BAEzC,MAAjBtnB,KAAK2oB,SAAkB,KAEnBC,EAAW1a,EAAE,eAGnB0a,EAASra,GAAG,QAAS,IAAIyZ,GAAY,SAAM1X,yGACzCA,EAAEC,iBACIlB,EAAMuZ,EAAS1T,KAAK,IAAI4S,GAAcjiB,MAAM4H,OAC5C8B,EAASqZ,EAAS1T,KAAK,IAAI6S,GAAiBliB,MAAM4H,UAClD3O,GAAYmQ,EAAQI,EAAKE,kBAA/BJ,SACAF,EAAO4Z,sCAIJF,SAAWC,MAGZD,EAAW3oB,KAAK2oB,gBACtBA,EAAS/Z,QAGT+Z,EAASra,OAAO6Z,GAChBQ,EAASra,OAAO+Z,GAChBM,EAASra,OAAOma,GAGhBF,EAAU1iB,IAAI,IACd2iB,EAAa3iB,IAAI,IAGjBqa,YAAW,WACTqI,EAAU/Z,WAGLma,EAAS,SEvHhBG,GAAUnnB,EAAY,GAAG6H,MAEzBuf,GAAc7mB,GAAiB7B,OAC/B2oB,GCPa,SAAUtc,EAAatK,GACtC,IAAII,EAAS,GAAGkK,GAChB,QAASlK,GAAUpC,GAAM,WAEvBoC,EAAOhC,KAAK,KAAM4B,GAAY,WAAc,MAAM,GAAM,MDGxC6mB,CAAoB,OAAQ,KAIhD/a,GAAE,CAAE9C,OAAQ,QAASiD,OAAO,EAAM9B,OAAQwc,KAAgBC,IAAiB,CACzExf,KAAM,SAAc0f,GAClB,OAAOJ,GAAQtiB,GAAgBxG,WAAqBiC,IAAdinB,EAA0B,IAAMA,MEd1E,ICYIC,GAAmBC,GAAmCC,ODZxCjpB,GAAM,WACtB,SAASiS,KAGT,OAFAA,EAAE5R,UAAUuT,YAAc,KAEnB3T,OAAOipB,eAAe,IAAIjX,KAASA,EAAE5R,aEC1CuQ,GAAW9I,GAAU,YACrB7H,GAASN,EAAOM,OAChBkpB,GAAkBlpB,GAAOI,aAIZ+oB,GAA2BnpB,GAAOipB,eAAiB,SAAU/iB,GAC5E,IAAIU,EAASzC,GAAS+B,GACtB,GAAIhC,GAAO0C,EAAQ+J,IAAW,OAAO/J,EAAO+J,IAC5C,IAAIgD,EAAc/M,EAAO+M,YACzB,OAAI3R,GAAW2R,IAAgB/M,aAAkB+M,EACxCA,EAAYvT,UACZwG,aAAkB5G,GAASkpB,GAAkB,MDVpDpN,GAAW9W,GAAgB,YAC3BokB,IAAyB,EAOzB,GAAGhiB,OAGC,SAFN4hB,GAAgB,GAAG5hB,SAIjB2hB,GAAoCE,GAAeA,GAAeD,QACxBhpB,OAAOI,YAAW0oB,GAAoBC,IAHlDK,IAAyB,GAO3D,IAAIC,GAA8CznB,MAArBknB,IAAkC/oB,GAAM,WACnE,IAAIoH,EAAO,GAEX,OAAO2hB,GAAkBhN,IAAU3b,KAAKgH,KAAUA,KAGhDkiB,KAAwBP,GAAoB,IAK3C9mB,GAAW8mB,GAAkBhN,MAChC1P,GAAS0c,GAAmBhN,IAAU,WACpC,OAAOnc,QAIX,OAAiB,CACfmpB,kBAAmBA,GACnBM,uBAAwBA,IE7CtBN,GAAoBtgB,GAAuCsgB,kBAM3DQ,GAAa,WAAc,OAAO3pB,MCSlC0N,GAAuBkc,GAAalhB,OACpCE,GAA6BghB,GAAajhB,aAC1CwgB,GAAoBU,GAAcV,kBAClCM,GAAyBI,GAAcJ,uBACvCtN,GAAW9W,GAAgB,YAK3BskB,GAAa,WAAc,OAAO3pB,SAErB,SAAU8pB,EAAUC,EAAMC,EAAqBlN,EAAMmN,EAASC,EAAQxQ,IDlBtE,SAAUsQ,EAAqBD,EAAMjN,EAAMqN,GAC1D,IAAIxd,EAAgBod,EAAO,YAC3BC,EAAoBvpB,UAAY6R,GAAO6W,GAAmB,CAAErM,KAAMnW,IAA2BwjB,EAAiBrN,KAC9GyJ,GAAeyD,EAAqBrd,GAAe,GACnDyP,GAAUzP,GAAiBgd,GCe3BS,CAA0BJ,EAAqBD,EAAMjN,GAErD,IAkBIuN,EAA0BpQ,EAASR,EAlBnC6Q,EAAqB,SAAUC,GACjC,GAAIA,IAASN,GAAWO,EAAiB,OAAOA,EAChD,IAAKf,IAA0Bc,KAAQE,EAAmB,OAAOA,EAAkBF,GACnF,OAAQA,GACN,IAbK,OAcL,IAbO,SAcP,IAbQ,UAaM,OAAO,WAAqB,OAAO,IAAIP,EAAoBhqB,KAAMuqB,IAC/E,OAAO,WAAc,OAAO,IAAIP,EAAoBhqB,QAGpD2M,EAAgBod,EAAO,YACvBW,GAAwB,EACxBD,EAAoBX,EAASrpB,UAC7BkqB,EAAiBF,EAAkBtO,KAClCsO,EAAkB,eAClBR,GAAWQ,EAAkBR,GAC9BO,GAAmBf,IAA0BkB,GAAkBL,EAAmBL,GAClFW,EAA4B,SAARb,GAAkBU,EAAkBI,SAA4BF,EA+BxF,GA3BIC,IACFP,EAA2Bf,GAAesB,EAAkBpqB,KAAK,IAAIspB,OACpCzpB,OAAOI,WAAa4pB,EAAyBvN,OAC5DwM,GAAee,KAA8BlB,KACvDvN,GACFA,GAAeyO,EAA0BlB,IAC/B9mB,GAAWgoB,EAAyBlO,MAC9C1P,GAAS4d,EAA0BlO,GAAUwN,KAIjDpD,GAAe8D,EAA0B1d,GAAe,IAMxDe,IA9CO,UA8CiBuc,GAAqBU,GA9CtC,WA8CwDA,EAAezlB,OAChE0D,GACdR,GAA4BqiB,EAAmB,OAhDxC,WAkDPC,GAAwB,EACxBF,EAAkB,WAAoB,OAAOhqB,EAAKmqB,EAAgB3qB,SAKlEiqB,EAMF,GALAhQ,EAAU,CACRgN,OAAQqD,EA1DD,UA2DP7iB,KAAMyiB,EAASM,EAAkBF,EA5D5B,QA6DLO,QAASP,EA3DD,YA6DN5Q,EAAQ,IAAKD,KAAOQ,GAClBwP,IAA0BiB,KAA2BjR,KAAOgR,KAC9Dhe,GAASge,EAAmBhR,EAAKQ,EAAQR,SAEtCvL,GAAE,CAAE9C,OAAQ2e,EAAM1b,OAAO,EAAM9B,OAAQkd,IAA0BiB,GAAyBzQ,GASnG,OAL4BwQ,EAAkBtO,MAAcqO,GAC1D/d,GAASge,EAAmBtO,GAAUqO,EAAiB,CAAEtlB,KAAM+kB,IAEjE7N,GAAU2N,GAAQS,EAEXvQ,GCzFLmI,GAAmBrZ,GAAoB1B,IACvCyB,GAAmBC,GAAoBT,UAFtB,qBAcJwiB,GAAerY,MAAO,SAAS,SAAUsY,EAAU1O,GAClE+F,GAAiBpiB,KAAM,CACrBwI,KAhBiB,iBAiBjB4C,OAAQ5E,GAAgBukB,GACxBjhB,MAAO,EACPuS,KAAMA,OAIP,WACD,IAAI1U,EAAQmB,GAAiB9I,MACzBoL,EAASzD,EAAMyD,OACfiR,EAAO1U,EAAM0U,KACbvS,EAAQnC,EAAMmC,QAClB,OAAKsB,GAAUtB,GAASsB,EAAO3I,QAC7BkF,EAAMyD,YAASnJ,EACR,CAAEb,WAAOa,EAAWuY,MAAM,IAEvB,QAAR6B,EAAuB,CAAEjb,MAAO0I,EAAO0Q,MAAM,GACrC,UAAR6B,EAAyB,CAAEjb,MAAOgK,EAAOtB,GAAQ0Q,MAAM,GACpD,CAAEpZ,MAAO,CAAC0I,EAAOsB,EAAOtB,IAAS0Q,MAAM,KAC7C,UAKH4B,GAAU4O,UAAY5O,GAAU3J,MAGhCE,GAAiB,QACjBA,GAAiB,UACjBA,GAAiB,WCnDjB,IAAIkE,GAAShO,GAAyCgO,OAMlDuL,GAAmBrZ,GAAoB1B,IACvCyB,GAAmBC,GAAoBT,UAFrB,mBAMtBwiB,GAAe1nB,OAAQ,UAAU,SAAU2nB,GACzC3I,GAAiBpiB,KAAM,CACrBwI,KARkB,kBASlBuD,OAAQrK,GAASqpB,GACjBjhB,MAAO,OAIR,WACD,IAGImhB,EAHAtjB,EAAQmB,GAAiB9I,MACzB+L,EAASpE,EAAMoE,OACfjC,EAAQnC,EAAMmC,MAElB,OAAIA,GAASiC,EAAOtJ,OAAe,CAAErB,WAAOa,EAAWuY,MAAM,IAC7DyQ,EAAQpU,GAAO9K,EAAQjC,GACvBnC,EAAMmC,OAASmhB,EAAMxoB,OACd,CAAErB,MAAO6pB,EAAOzQ,MAAM,OCvB/B,OAAiB,SAAUvT,EAAQlD,EAAK3C,GACtC,IAAI8pB,EAAczkB,GAAc1C,GAC5BmnB,KAAejkB,EAAQC,GAAqBL,EAAEI,EAAQikB,EAAavkB,EAAyB,EAAGvF,IAC9F6F,EAAOikB,GAAe9pB,GCHzBqR,GAAQ1S,EAAO0S,MACf7I,GAAMjK,KAAKiK,ICHXuhB,GAAuBtiB,GAAsDhC,EAG7EukB,GAA+B,iBAAVvrB,QAAsBA,QAAUQ,OAAO2K,oBAC5D3K,OAAO2K,oBAAoBnL,QAAU,GAErCwrB,GAAiB,SAAU3rB,GAC7B,IACE,OAAOyrB,GAAqBzrB,GAC5B,MAAOS,GACP,ODLa,SAAUoG,EAAGgH,EAAOC,GAKnC,IAJA,IAAI/K,EAAS+H,GAAkBjE,GAC3B+kB,EAAI7gB,GAAgB8C,EAAO9K,GAC3B8oB,EAAM9gB,QAAwBxI,IAARuL,EAAoB/K,EAAS+K,EAAK/K,GACxDiD,EAAS+M,GAAM7I,GAAI2hB,EAAMD,EAAG,IACvBvS,EAAI,EAAGuS,EAAIC,EAAKD,IAAKvS,IAAKyS,GAAe9lB,EAAQqT,EAAGxS,EAAE+kB,IAE/D,OADA5lB,EAAOjD,OAASsW,EACTrT,ECFE6Z,CAAW6L,YAKH,SAA6B1rB,GAC9C,OAAO0rB,IAA8B,UAAfrpB,EAAQrC,GAC1B2rB,GAAe3rB,GACfyrB,GAAqB3kB,GAAgB9G,SClB1BU,GAAM,WACrB,GAA0B,mBAAfqrB,YAA2B,CACpC,IAAIC,EAAS,IAAID,YAAY,GAEzBprB,OAAOsrB,aAAaD,IAASrrB,OAAOC,eAAeorB,EAAQ,IAAK,CAAEtqB,MAAO,QCD7EwqB,GAAgBvrB,OAAOsrB,gBACDvrB,GAAM,WAAcwrB,GAAc,OAInBC,GAA+B,SAAsBnsB,GAC5F,QAAK8F,GAAS9F,OACVmsB,IAA8C,eAAf9pB,EAAQrC,OACpCksB,IAAgBA,GAAclsB,MACnCksB,OCbcxrB,GAAM,WAEtB,OAAOC,OAAOsrB,aAAatrB,OAAOyrB,kBAAkB,0BCCtD,IAAIxrB,EAAiBuI,GAA+ChC,EAOhEklB,GAAW,EACXC,EAAW/mB,GAAI,QACfR,EAAK,EAELwnB,EAAc,SAAUvsB,GAC1BY,EAAeZ,EAAIssB,EAAU,CAAE5qB,MAAO,CACpC8qB,SAAU,IAAMznB,IAChB0nB,SAAU,OA8DVC,EAAOnoB,UAAiB,CAC1BooB,OA3BW,WACXD,EAAKC,OAAS,aACdN,GAAW,EACX,IAAI/gB,EAAsBE,GAA0BrE,EAChDylB,EAAS3qB,EAAY,GAAG2qB,QACxB9kB,EAAO,GACXA,EAAKwkB,GAAY,EAGbhhB,EAAoBxD,GAAM/E,SAC5ByI,GAA0BrE,EAAI,SAAUnH,GAEtC,IADA,IAAIgG,EAASsF,EAAoBtL,GACxBmL,EAAI,EAAGpI,EAASiD,EAAOjD,OAAQoI,EAAIpI,EAAQoI,IAClD,GAAInF,EAAOmF,KAAOmhB,EAAU,CAC1BM,EAAO5mB,EAAQmF,EAAG,GAClB,MAEF,OAAOnF,GAGXwI,GAAE,CAAE9C,OAAQ,SAAUkB,MAAM,EAAMC,QAAQ,GAAQ,CAChDvB,oBAAqBuhB,GAAkC1lB,MAO3D2lB,QA5DY,SAAU9sB,EAAI4S,GAE1B,IAAK9M,GAAS9F,GAAK,MAAoB,iBAANA,EAAiBA,GAAmB,iBAANA,EAAiB,IAAM,KAAOA,EAC7F,IAAK6E,GAAO7E,EAAIssB,GAAW,CAEzB,IAAKL,GAAajsB,GAAK,MAAO,IAE9B,IAAK4S,EAAQ,MAAO,IAEpB2Z,EAAYvsB,GAEZ,OAAOA,EAAGssB,GAAUE,UAkDtBO,YA/CgB,SAAU/sB,EAAI4S,GAC9B,IAAK/N,GAAO7E,EAAIssB,GAAW,CAEzB,IAAKL,GAAajsB,GAAK,OAAO,EAE9B,IAAK4S,EAAQ,OAAO,EAEpB2Z,EAAYvsB,GAEZ,OAAOA,EAAGssB,GAAUG,UAuCtBO,SAnCa,SAAUhtB,GAEvB,OADIitB,IAAYZ,GAAYJ,GAAajsB,KAAQ6E,GAAO7E,EAAIssB,IAAWC,EAAYvsB,GAC5EA,IAoCTyI,GAAW6jB,IAAY,KCrFnBS,GAAc5jB,GAA0C4jB,YASxDrK,GAAmBrZ,GAAoB1B,IACvCulB,GAAyB7jB,GAAoBT,UAC7C4M,GAAO2X,GAAqB3X,KAC5BC,GAAY0X,GAAqB1X,UACjCmX,GAAS3qB,EAAY,GAAG2qB,QACxB7nB,GAAK,EAGLqoB,GAAsB,SAAU5oB,GAClC,OAAOA,EAAM6oB,SAAW7oB,EAAM6oB,OAAS,IAAIC,KAGzCA,GAAsB,WACxBhtB,KAAK6qB,QAAU,IAGboC,GAAqB,SAAU/oB,EAAOH,GACxC,OAAOmR,GAAKhR,EAAM2mB,SAAS,SAAUnrB,GACnC,OAAOA,EAAG,KAAOqE,MAIrBipB,GAAoBvsB,UAAY,CAC9BF,IAAK,SAAUwD,GACb,IAAImpB,EAAQD,GAAmBjtB,KAAM+D,GACrC,GAAImpB,EAAO,OAAOA,EAAM,IAE1B5lB,IAAK,SAAUvD,GACb,QAASkpB,GAAmBjtB,KAAM+D,IAEpCsD,IAAK,SAAUtD,EAAK3C,GAClB,IAAI8rB,EAAQD,GAAmBjtB,KAAM+D,GACjCmpB,EAAOA,EAAM,GAAK9rB,EACjBpB,KAAK6qB,QAAQ1mB,KAAK,CAACJ,EAAK3C,KAE/BxC,OAAU,SAAUmF,GAClB,IAAI+F,EAAQqL,GAAUnV,KAAK6qB,SAAS,SAAUnrB,GAC5C,OAAOA,EAAG,KAAOqE,KAGnB,OADK+F,GAAOwiB,GAAOtsB,KAAK6qB,QAAS/gB,EAAO,MAC9BA,IAId,IC3CIqjB,MD2Ca,CACfC,eAAgB,SAAUlI,EAASsB,EAAkBtS,EAAQmZ,GAC3D,IAAI5G,EAAcvB,GAAQ,SAAU7R,EAAMsJ,GACxCyI,GAAW/R,EAAM6I,GACjBkG,GAAiB/O,EAAM,CACrB7K,KAAMge,EACN/hB,GAAIA,KACJsoB,YAAQ9qB,IAEMA,MAAZ0a,GAAuBwK,GAAQxK,EAAUtJ,EAAKga,GAAQ,CAAEha,KAAMA,EAAM2J,WAAY9I,OAGlFgI,EAAYuK,EAAYhmB,UAExBqI,EAAmB8jB,GAAuBpG,GAE1C8G,EAAS,SAAUja,EAAMtP,EAAK3C,GAChC,IAAIuG,EAAQmB,EAAiBuK,GACzB1H,EAAO8gB,GAAYzlB,GAASjD,IAAM,GAGtC,OAFa,IAAT4H,EAAemhB,GAAoBnlB,GAAON,IAAItD,EAAK3C,GAClDuK,EAAKhE,EAAMlD,IAAMrD,EACfiS,GAkDT,OA/CAgS,GAAYnJ,EAAW,CAIrBtd,OAAU,SAAUmF,GAClB,IAAI4D,EAAQmB,EAAiB9I,MAC7B,IAAKwF,GAASzB,GAAM,OAAO,EAC3B,IAAI4H,EAAO8gB,GAAY1oB,GACvB,OAAa,IAAT4H,EAAsBmhB,GAAoBnlB,GAAe,OAAE5D,GACxD4H,GAAQpH,GAAOoH,EAAMhE,EAAMlD,YAAckH,EAAKhE,EAAMlD,KAK7D6C,IAAK,SAAavD,GAChB,IAAI4D,EAAQmB,EAAiB9I,MAC7B,IAAKwF,GAASzB,GAAM,OAAO,EAC3B,IAAI4H,EAAO8gB,GAAY1oB,GACvB,OAAa,IAAT4H,EAAsBmhB,GAAoBnlB,GAAOL,IAAIvD,GAClD4H,GAAQpH,GAAOoH,EAAMhE,EAAMlD,OAItC4gB,GAAYnJ,EAAWhI,EAAS,CAG9B3T,IAAK,SAAawD,GAChB,IAAI4D,EAAQmB,EAAiB9I,MAC7B,GAAIwF,GAASzB,GAAM,CACjB,IAAI4H,EAAO8gB,GAAY1oB,GACvB,OAAa,IAAT4H,EAAsBmhB,GAAoBnlB,GAAOpH,IAAIwD,GAClD4H,EAAOA,EAAKhE,EAAMlD,SAAMxC,IAKnCoF,IAAK,SAAatD,EAAK3C,GACrB,OAAOksB,EAAOttB,KAAM+D,EAAK3C,KAEzB,CAGFmsB,IAAK,SAAansB,GAChB,OAAOksB,EAAOttB,KAAMoB,GAAO,MAIxBqlB,ICtHP+G,GAAsB3kB,GAAuCR,QAG7DolB,IAAW1tB,EAAO4R,eAAiB,kBAAmB5R,EAGtDmlB,GAAU,SAAUwI,GACtB,OAAO,WACL,OAAOA,EAAK1tB,KAAMY,UAAU6B,OAAS7B,UAAU,QAAKqB,KAMpD0rB,GCPa,SAAUnH,EAAkBtB,EAAS0I,GACpD,IAAI1Z,GAA8C,IAArCsS,EAAiB7b,QAAQ,OAClCkjB,GAAgD,IAAtCrH,EAAiB7b,QAAQ,QACnC0iB,EAAQnZ,EAAS,MAAQ,MACzB4Z,EAAoB/tB,EAAOymB,GAC3BuH,EAAkBD,GAAqBA,EAAkBrtB,UACzDgmB,EAAcqH,EACdE,EAAW,GAEXC,EAAY,SAAUxU,GACxB,IAAIa,EAAwB3Y,EAAYosB,EAAgBtU,IACxDhN,GAASshB,EAAiBtU,EACjB,OAAPA,EAAe,SAAarY,GAE1B,OADAkZ,EAAsBta,KAAgB,IAAVoB,EAAc,EAAIA,GACvCpB,MACE,UAAPyZ,EAAkB,SAAU1V,GAC9B,QAAO8pB,IAAYroB,GAASzB,KAAeuW,EAAsBta,KAAc,IAAR+D,EAAY,EAAIA,IAC9E,OAAP0V,EAAe,SAAa1V,GAC9B,OAAO8pB,IAAYroB,GAASzB,QAAO9B,EAAYqY,EAAsBta,KAAc,IAAR+D,EAAY,EAAIA,IAClF,OAAP0V,EAAe,SAAa1V,GAC9B,QAAO8pB,IAAYroB,GAASzB,KAAeuW,EAAsBta,KAAc,IAAR+D,EAAY,EAAIA,IACrF,SAAaA,EAAK3C,GAEpB,OADAkZ,EAAsBta,KAAc,IAAR+D,EAAY,EAAIA,EAAK3C,GAC1CpB,QAYb,GAPcwL,GACZgb,GACCnkB,GAAWyrB,MAAwBD,GAAWE,EAAgBlZ,UAAYzU,GAAM,YAC/E,IAAI0tB,GAAoBjD,UAAU/N,YAMpC2J,EAAcmH,EAAOR,eAAelI,EAASsB,EAAkBtS,EAAQmZ,GACvEa,GAAuB7B,cAClB,GAAI7gB,GAASgb,GAAkB,GAAO,CAC3C,IAAI2H,EAAW,IAAI1H,EAEf2H,EAAiBD,EAASd,GAAOQ,EAAU,IAAM,EAAG,IAAMM,EAE1DE,EAAuBjuB,GAAM,WAAc+tB,EAAS7mB,IAAI,MAGxDgnB,EAAmB9K,IAA4B,SAAU7G,GAAY,IAAImR,EAAkBnR,MAE3F4R,GAAcV,GAAWztB,GAAM,WAIjC,IAFA,IAAIouB,EAAY,IAAIV,EAChBhkB,EAAQ,EACLA,KAAS0kB,EAAUnB,GAAOvjB,EAAOA,GACxC,OAAQ0kB,EAAUlnB,KAAK,MAGpBgnB,KACH7H,EAAcvB,GAAQ,SAAUuJ,EAAO9R,GACrCyI,GAAWqJ,EAAOV,GAClB,IAAI1a,ECvEK,SAAUhJ,EAAOokB,EAAOC,GACvC,IAAIC,EAAWC,EAUf,OAPEhT,IAEAvZ,GAAWssB,EAAYF,EAAMza,cAC7B2a,IAAcD,GACdlpB,GAASopB,EAAqBD,EAAUluB,YACxCmuB,IAAuBF,EAAQjuB,WAC/Bmb,GAAevR,EAAOukB,GACjBvkB,ED4DUwkB,CAAkB,IAAIf,EAAqBW,EAAOhI,GAE7D,OADgBxkB,MAAZ0a,GAAuBwK,GAAQxK,EAAUtJ,EAAKga,GAAQ,CAAEha,KAAMA,EAAM2J,WAAY9I,IAC7Eb,MAEG5S,UAAYstB,EACxBA,EAAgB/Z,YAAcyS,IAG5B4H,GAAwBE,KAC1BN,EAAU,UACVA,EAAU,OACV/Z,GAAU+Z,EAAU,SAGlBM,GAAcH,IAAgBH,EAAUZ,GAGxCQ,GAAWE,EAAgBvP,cAAcuP,EAAgBvP,MAU/D,OAPAwP,EAASxH,GAAoBC,EAC7BvY,GAAE,CAAEnO,QAAQ,EAAMwM,OAAQka,GAAeqH,GAAqBE,GAE9DzH,GAAeE,EAAaD,GAEvBqH,GAASD,EAAOkB,UAAUrI,EAAaD,EAAkBtS,GAEvDuS,EDhFMsI,CAAW,UAAW7J,GAAS8J,IAK9C,GAAItnB,IAAmB+lB,GAAS,CAC9BN,GAAkB6B,GAAe5B,eAAelI,GAAS,WAAW,GACpEgJ,GAAuB7B,SACvB,IAAI4C,GAAmBtB,GAASltB,UAC5ByuB,GAAevtB,EAAYstB,GAAyB,QACpDE,GAAYxtB,EAAYstB,GAAiB3nB,KACzC8nB,GAAYztB,EAAYstB,GAAiB1uB,KACzC8uB,GAAY1tB,EAAYstB,GAAiB5nB,KAC7Cge,GAAY4J,GAAkB,CAC5BrwB,OAAU,SAAUmF,GAClB,GAAIyB,GAASzB,KAAS4nB,GAAa5nB,GAAM,CACvC,IAAI4D,EAAQ6lB,GAAoBxtB,MAEhC,OADK2H,EAAMolB,SAAQplB,EAAMolB,OAAS,IAAII,IAC/B+B,GAAalvB,KAAM+D,IAAQ4D,EAAMolB,OAAe,OAAEhpB,GACzD,OAAOmrB,GAAalvB,KAAM+D,IAE9BuD,IAAK,SAAavD,GAChB,GAAIyB,GAASzB,KAAS4nB,GAAa5nB,GAAM,CACvC,IAAI4D,EAAQ6lB,GAAoBxtB,MAEhC,OADK2H,EAAMolB,SAAQplB,EAAMolB,OAAS,IAAII,IAC/BgC,GAAUnvB,KAAM+D,IAAQ4D,EAAMolB,OAAOzlB,IAAIvD,GAChD,OAAOorB,GAAUnvB,KAAM+D,IAE3BxD,IAAK,SAAawD,GAChB,GAAIyB,GAASzB,KAAS4nB,GAAa5nB,GAAM,CACvC,IAAI4D,EAAQ6lB,GAAoBxtB,MAEhC,OADK2H,EAAMolB,SAAQplB,EAAMolB,OAAS,IAAII,IAC/BgC,GAAUnvB,KAAM+D,GAAOqrB,GAAUpvB,KAAM+D,GAAO4D,EAAMolB,OAAOxsB,IAAIwD,GACtE,OAAOqrB,GAAUpvB,KAAM+D,IAE3BsD,IAAK,SAAatD,EAAK3C,GACrB,GAAIoE,GAASzB,KAAS4nB,GAAa5nB,GAAM,CACvC,IAAI4D,EAAQ6lB,GAAoBxtB,MAC3B2H,EAAMolB,SAAQplB,EAAMolB,OAAS,IAAII,IACtCgC,GAAUnvB,KAAM+D,GAAOsrB,GAAUrvB,KAAM+D,EAAK3C,GAASuG,EAAMolB,OAAO1lB,IAAItD,EAAK3C,QACtEiuB,GAAUrvB,KAAM+D,EAAK3C,GAC5B,OAAOpB,QG9Db,OAAiB,CACfsvB,YAAa,EACbC,oBAAqB,EACrBC,aAAc,EACdC,eAAgB,EAChBC,YAAa,EACbC,cAAe,EACfC,aAAc,EACdC,qBAAsB,EACtBC,SAAU,EACVC,kBAAmB,EACnBC,eAAgB,EAChBC,gBAAiB,EACjBC,kBAAmB,EACnBC,UAAW,EACXC,cAAe,EACfC,aAAc,EACdC,SAAU,EACVC,iBAAkB,EAClBC,OAAQ,EACRC,YAAa,EACbC,cAAe,EACfC,cAAe,EACfC,eAAgB,EAChBC,aAAc,EACdC,cAAe,EACfC,iBAAkB,EAClBC,iBAAkB,EAClBC,eAAgB,EAChBC,iBAAkB,EAClBC,cAAe,EACfC,UAAW,GC9BTC,GAAYtf,GAAsB,QAAQsf,UAC1CC,GAAwBD,IAAaA,GAAUrd,aAAeqd,GAAUrd,YAAYvT,aAEvE6wB,KAA0BjxB,OAAOI,eAAYwB,EAAYqvB,GCCtEnV,GAAW9W,GAAgB,YAC3BsH,GAAgBtH,GAAgB,eAChCksB,GAAcC,GAAqBvK,OAEnCwK,GAAkB,SAAUC,EAAqBC,GACnD,GAAID,EAAqB,CAEvB,GAAIA,EAAoBvV,MAAcoV,GAAa,IACjDnpB,GAA4BspB,EAAqBvV,GAAUoV,IAC3D,MAAOpxB,GACPuxB,EAAoBvV,IAAYoV,GAKlC,GAHKG,EAAoB/kB,KACvBvE,GAA4BspB,EAAqB/kB,GAAeglB,GAE9DC,GAAaD,GAAkB,IAAK,IAAIjlB,KAAe8kB,GAEzD,GAAIE,EAAoBhlB,KAAiB8kB,GAAqB9kB,GAAc,IAC1EtE,GAA4BspB,EAAqBhlB,EAAa8kB,GAAqB9kB,IACnF,MAAOvM,GACPuxB,EAAoBhlB,GAAe8kB,GAAqB9kB,MAMhE,IAAK,IAAIilB,MAAmBC,GAC1BH,GAAgB1xB,EAAO4xB,KAAoB5xB,EAAO4xB,IAAiBlxB,UAAWkxB,IAGhFF,GAAgBH,GAAuB,gBCpCvC,IAAIO,GAAuBhpB,GAAsC3C,OAE7D5F,GAAiBugB,GAA+Cha,EAEhEtF,GAAoBtB,SAASQ,UAC7B0G,GAAmBxF,EAAYJ,GAAkBG,UACjDowB,GAAS,mEACT1W,GAAazZ,EAAYmwB,GAAO5xB,MAKhCkG,IAAgByrB,IAClBvxB,GAAeiB,GALN,OAK+B,CACtCF,cAAc,EACdd,IAAK,WACH,IACE,OAAO6a,GAAW0W,GAAQ3qB,GAAiBnH,OAAO,GAClD,MAAOG,GACP,MAAO,OChBf,IAAI0T,GAAUxO,GAAgB,WCU1B0sB,GDRa,SAAUrlB,GAIzB,OAAOpJ,IAAc,KAAOlD,GAAM,WAChC,IAAI4xB,EAAQ,GAKZ,OAJkBA,EAAMhe,YAAc,IAC1BH,IAAW,WACrB,MAAO,CAAEoe,IAAK,IAE2B,IAApCD,EAAMtlB,GAAawlB,SAASD,OCFbE,CAA6B,SAEnDte,GAAUxO,GAAgB,WAC1BoN,GAAQ1S,EAAO0S,MACf7I,GAAMjK,KAAKiK,ICRf,SAAS+b,GAAc1W,UAEdA,EAAO0W,cAAc,eDW9BzX,GAAE,CAAE9C,OAAQ,QAASiD,OAAO,EAAM9B,QAASwlB,IAAuB,CAChElwB,MAAO,SAAe0L,EAAOC,GAC3B,IAKIiZ,EAAa/gB,EAAQqT,EALrBxS,EAAIC,GAAgBxG,MACpByC,EAAS+H,GAAkBjE,GAC3B+kB,EAAI7gB,GAAgB8C,EAAO9K,GAC3B8oB,EAAM9gB,QAAwBxI,IAARuL,EAAoB/K,EAAS+K,EAAK/K,GAG5D,GAAI6Q,GAAQ/M,KACVkgB,EAAclgB,EAAEyN,aAEZC,GAAcwS,KAAiBA,IAAgBhU,IAASa,GAAQmT,EAAYhmB,aAErE+E,GAASihB,IAEE,QADpBA,EAAcA,EAAY5S,QAF1B4S,OAAcxkB,GAKZwkB,IAAgBhU,SAAyBxQ,IAAhBwkB,GAC3B,OAAO2L,GAAS7rB,EAAG+kB,EAAGC,GAI1B,IADA7lB,EAAS,SAAqBzD,IAAhBwkB,EAA4BhU,GAAQgU,GAAa7c,GAAI2hB,EAAMD,EAAG,IACvEvS,EAAI,EAAGuS,EAAIC,EAAKD,IAAKvS,IAASuS,KAAK/kB,GAAGilB,GAAe9lB,EAAQqT,EAAGxS,EAAE+kB,IAEvE,OADA5lB,EAAOjD,OAASsW,EACTrT,KC/BX,IAAM2sB,GAAqB,IAAI9qB,QAyE/B,SAAe+qB,GAAWrjB,EAAoBsjB,gHACtCC,EApER,SAAiBvjB,OAEXujB,EAAOH,GAAmB9xB,IAAI0O,MACtB,MAARujB,EAAc,OAAOA,MAEnBC,EAAa9M,GAAc1W,GACzByjB,EAA2DD,YAAhDE,EAAgDF,aAApCG,EAAoCH,WAA1BI,EAA0BJ,eAAZK,EAAYL,iBA4CnED,EAAOO,SACFN,IACHE,WAfsB,SAACK,GACvB/jB,EAAOgkB,gBAAgBD,GAGvBL,GAAcA,EAAWK,IAYzBN,UA5CqB,SAACH,EAAgB3hB,MAKlCiiB,SAEFA,EAAajiB,GAAK,SAACvB,EAAKE,UAAWzQ,GAAYmQ,EAAQI,EAAKE,WAE5DmjB,EAAUH,EAAM3hB,OAIZzB,EAAyByB,QAAzBsiB,aAAQ,IAAG9jB,EAAcwB,OAAdjF,aAAO,QACV,IAAVunB,OAMI5jB,EAA0B3D,MAA1BwnB,aAAM,KAAI3jB,EAAgB7D,SAClC7M,GAAYmQ,EAAQkkB,aADO,MAI3BT,EAAUH,EAAM3hB,QARdgiB,EAASL,EAAM3hB,IA6BjBkiB,QATmB,SAACP,EAAWa,EAAUxiB,GACzCkiB,EAAQP,EAAMa,EAAKxiB,OAWrByhB,GAAmBhrB,IAAI4H,EAAQujB,GAExBA,EASMa,CAAQpkB,GAEb/J,EAAqBqtB,OAAf/pB,EAAe+pB,OAATpa,EAASoa,OAC7BC,EAAKc,QAAQ,CACXpuB,OACAsD,OACA2P,OACAxM,KAAM4mB,OAEFC,EAAKe,wBAAXpkB,oBCtFF,0CACmBmY,EAAE,wC3DGnB,0wB2DDe,gBAEfkM,qBAAA,SAASvkB,SAEA,IAGTukB,qBAAA,SAASvkB,UAEA,GAGTukB,iBAAA,SAAKvkB,EAAoB7N,OACjB+N,EAAmDnP,KAAK2lB,cAAc1W,GAApEG,qBAAAqkB,aAAmB,KAAIC,6BAG3BA,EACFA,GAAsB,SAACrkB,EAAKE,UAAWzQ,GAAYmQ,EAAQI,EAAKE,eAK9DokB,EAAa,GACbF,EAAiBhxB,OAAS,IAC5BkxB,EAAa,WAAWF,EAAiBjqB,KAAK,eAI1CoqB,EAAQ1lB,EAAE,QACV2lB,EAAa3lB,EAAE,sBAAsBylB,iBAC3CE,EAAWC,OACXF,EAAMtlB,OAAOulB,GACbA,EAAWE,QAEXF,EAAWtlB,GAAG,UAAU,eAChBylB,EAASH,EAAW,GAAwBG,gBDmDzB/kB,EAAoB+kB,iHACpC,MAATA,EAAe,UACbC,EAAWxhB,MAAMhS,UAAUoB,MAAMrB,KAAKwzB,GAGpCE,EAAiBvO,GAAc1W,0DAGdklB,EAAAC,EAAAH,6EAAR1B,UAEX2B,KAEIA,EAAa3B,GAAM,SAACljB,EAAKE,UAAWzQ,GAAYmQ,EAAQI,EAAKE,4BAAnEH,+BAGMkjB,GAAWrjB,EAAQsjB,WAAzBnjB,6TCjEAilB,CAAaplB,EAAQ+kB,QAIzBR,uBAAA,SAAWvkB,OACDuY,EAAcvY,mBACL,MAAbuY,KACCC,EAAMC,YAAYF,MAED7X,EAAUgY,iBAAiB1Y,GACd+F,MAAK,SAAAjC,OAChCvK,EAAOmH,EAAUiY,YAAY7U,SACtB,QAATvK,IACS,cAATA,KACAyG,EAAO4Y,OAAO9U,SAQdygB,0BAAR,SAAsBvkB,UAEbA,EAAO0W,cAAc,qBC1E5BjY,GAAuB7E,GAAsCH,OAS7DiP,GAAkBtK,OAAO5M,UACzB6zB,GAAa3c,GAAyB,SACtC4c,GAAW5yB,EAAY6yB,IAEvBC,GAAcr0B,GAAM,WAAc,MAAuD,QAAhDk0B,GAAW9zB,KAAK,CAAE+I,OAAQ,IAAKiN,MAAO,SAE/Eke,GAAiBhnB,IAPL,YAO6B4mB,GAAWpvB,KCIxD,SAASmhB,YACA/M,GAAa,qBDDlBmb,IAAeC,KACjBjoB,GAASY,OAAO5M,UAZF,YAYwB,WACpC,IAAIuY,EAAIhS,GAAShH,MACb20B,EAAIC,GAAU5b,EAAEzP,QAChBsrB,EAAK7b,EAAExC,MAEX,MAAO,IAAMme,EAAI,IADTC,QAAiB3yB,IAAP4yB,GAAoBnyB,GAAciV,GAAiBqB,MAAQ,UAAWrB,IAAmB4c,GAASvb,GAAK6b,KAExH,CAAE1rB,QAAQ,ICHf,0CACmBme,EAAE,iCACJ,yBACM,kBACC,kBACe,uBACLjB,wBACCA,mBACLA,YAEpByO,iCAAR,SAA6B7lB,UACpBU,EAAUolB,sBAAsB9lB,EAAQ,UAGjD6lB,qBAAA,SAAS7lB,SAEA,IAGT6lB,qBAAA,SAAS7lB,UAEA,GAGT6lB,iBAAA,SAAK7lB,EAAoB7N,KAKzB0zB,uBAAA,SAAW7lB,UACe,MAApBA,EAAOuY,WAGM,MADCxnB,KAAKg1B,qBAAqB/lB,IAQ9C6lB,iCAAA,SAAqB7lB,UACZjP,KAAKg1B,qBAAqB/lB,IAGnC6lB,gCAAA,SAAoB7lB,OAGZE,EAA4CnP,KAA1Ci1B,iBAAcC,kBAAelN,aAE/B5Y,EAAA6Y,EAAuCC,EAC3CZ,EAAE,qBACF2N,EACA,WAHKE,OAAoBC,OAKrBC,EAAcnnB,EAAEknB,GAChB9lB,EAAA2Y,EAAyCC,EAC7CZ,EAAE,sBACF4N,EACA,WAHKI,OAAqBC,OAKtBC,EAAetnB,EAAEqnB,GAChB9M,EAADR,EAAwBS,EAAoBV,EAAUV,EAAE,4BAEzC,MAAjBtnB,KAAK2oB,SAAkB,KAEnBC,EAAW1a,EAAE,eAGnB0a,EAASra,GAAG,QAAS,IAAIyZ,GAAY,SAAA1X,GACnCA,EAAEC,qBAEIklB,EAAW7M,EAAS1T,KAAK,IAAI+f,GAAgBpvB,MAAM4H,OACnDioB,EAAY9M,EAAS1T,KAAK,IAAIggB,GAAiBrvB,MAAM4H,OACrDkoB,EAAcC,SAASH,GACvBI,EAAeD,SAASF,GACxBr2B,EAAQs2B,EAAcA,EAAYj0B,WAAa,OAC/CpC,EAASu2B,EAAeA,EAAan0B,WAAa,OAExDuN,EAAOyW,mBAGPS,EAAW2P,SACT7mB,EACA,CAAE5P,QAAOC,UACT,CACEC,MAAO,SAAAwZ,UAAKpJ,EAAUomB,cAAchd,EAAG,YAI3C9J,EAAO4Z,2BAGJF,SAAWC,MAGZD,EAAW3oB,KAAK2oB,SAGtBA,EAAS/Z,QACT+Z,EAASra,OAAO6mB,GAChBxM,EAASra,OAAOgnB,GAChB3M,EAASra,OAAOma,OAEVuN,EAAYh2B,KAAKg1B,qBAAqB/lB,MAC3B,MAAb+mB,EAAmB,OAAOrN,EAAS,OAG/BlZ,EAAoCumB,QAApC32B,aAAQ,SAAQ42B,EAAoBD,SAApB12B,aAAS,gBACjC+1B,EAAYxvB,IAAIxG,GAChBm2B,EAAa3vB,IAAIvG,GACjB4gB,YAAW,WACTmV,EAAY7mB,WAGPma,EAAS,SChIb,ICEDzC,GAA8B,CAClCgQ,YAAa,CAACrnB,IACdsnB,YAAa,CAAC1lB,IACdqC,aAAc,CAACF,IACfwjB,eAAgB,CAACzgB,IACjB0gB,MAAO,CDP0B,CACjCtyB,IAAK,cACLuyB,0BACS,IAAI/O,IAKbgP,OEsDO,CACL3Q,gBAAA,SAAgBpF,KAShBqF,WAAA,SAAWxW,EAAaE,UAIf,GAQTuW,cAAA,SAAczW,UACLA,KF1EsB,CACjCtL,IAAK,cACLuyB,0BACS,IAAI9C,IAKb+C,OERO,CACLC,OAAQ,GAERC,UAAW,4BACXC,YAAa,SACbC,iBAAkB,EAClBlD,iBAAkB,CAAC,WACnBrH,KAAM,GAINwK,aAAa,EAKbC,iBAAiB,EACjBC,QAAS,IAETC,eAAgB,SAAC/C,UAAeA,GAChCrB,WAAY,SAACK,KAGbN,UAAW,SAACH,EAAW3hB,KAGvBgiB,SAAU,SAACL,EAAW3hB,GAEpB8T,QAAQvkB,MAAM,IAAIoyB,EAAKrtB,uBAAuB0L,IAEhDkiB,QAAS,SAACP,EAAWa,EAAUxiB,GAG7B8T,QAAQvkB,MAAM,IAAIoyB,EAAKrtB,qBAAqBkuB,EAAKxiB,MFtBjB,CACpC7M,IAAK,gBACLuyB,0BACS,IAAIxB,MCjBbkC,aEVF,SAAyC/nB,OAC/B4Y,EAA0B5Y,SAAlBgoB,EAAkBhoB,gBAC5BioB,EAAYjoB,SAGlBioB,EAAUrP,OAAS,SAAC9U,SAGL,UAFIA,QAMV8U,EAAO9U,IAIhBmkB,EAAUD,cAAgB,SAAC9nB,OAAAC,EAAA6Y,OAACzH,OAAM2W,OAInB,UAHAxnB,EAAUiY,YAAYpH,KAKlB7Q,EAAUynB,WAAWF,EAAW1W,IAE7C2F,EAAWC,YAAY8Q,EAAWvnB,EAAU0nB,oBAAqB,CAAEC,GAAI,CAACH,EAAK,GAAK,aAK/EF,EAAc,CAACzW,EAAM2W,KAIvBD"}