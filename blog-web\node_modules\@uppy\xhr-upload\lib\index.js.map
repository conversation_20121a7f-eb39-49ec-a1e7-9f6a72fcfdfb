{"version": 3, "sources": ["index.js"], "names": ["BasePlugin", "emitSocketProgress", "getSocketHost", "settle", "EventTracker", "ProgressTimeout", "NetworkError", "isNetworkError", "packageJson", "locale", "buildResponseError", "xhr", "err", "error", "Error", "Object", "assign", "data", "request", "setTypeInBlob", "file", "dataWithUpdatedType", "slice", "size", "meta", "type", "XHRUpload", "constructor", "uppy", "opts", "id", "title", "defaultLocale", "defaultOptions", "formData", "fieldName", "bundle", "method", "metaFields", "responseUrlFieldName", "headers", "timeout", "limit", "withCredentials", "responseType", "getResponseData", "responseText", "parsedResponse", "JSON", "parse", "log", "getResponseError", "_", "response", "validateStatus", "status", "i18nInit", "handleUpload", "bind", "internalRateLimitedQueue", "requests", "RateLimitedQueue", "uploaderEvents", "create", "getOptions", "overrides", "getState", "xhrUpload", "addMetadata", "Array", "isArray", "keys", "for<PERSON>ach", "item", "append", "createFormDataUpload", "formPost", "FormData", "name", "createBundledUpload", "files", "options", "upload", "current", "total", "Promise", "resolve", "reject", "emit", "XMLHttpRequest", "queuedRequest", "timer", "abort", "done", "i18n", "seconds", "Math", "ceil", "addEventListener", "ev", "loaded", "progress", "lengthComputable", "uploader", "bytesUploaded", "bytesTotal", "remove", "body", "uploadURL", "uploadResp", "open", "toUpperCase", "endpoint", "run", "currentOpts", "header", "setRequestHeader", "send", "onFileRemove", "onCancelAll", "reason", "uploadRemote", "fields", "Client", "remote", "providerOptions", "provider", "Provider", "RequestClient", "client", "post", "url", "fieldname", "metadata", "httpMethod", "useFormData", "then", "res", "token", "host", "companionUrl", "socket", "Socket", "target", "autoOpen", "onRetry", "onRetryAll", "on", "progressData", "errData", "resp", "message", "cause", "isPaused", "close", "catch", "uploadBundle", "optsFromState", "emitError", "uploadFiles", "promises", "map", "i", "parseInt", "length", "isRemote", "fileID", "cb", "targetFileID", "getFile", "<PERSON><PERSON><PERSON><PERSON>", "fileIDs", "isSomeFileRemote", "some", "TypeError", "install", "capabilities", "setState", "individualCancellation", "addUploader", "uninstall", "removeUploader", "VERSION", "version"], "mappings": ";;AACA;;AACA;;AAMA;;MAROA,U;;MAGAC,kB;;MACAC,a;;MACAC,M;;MACAC,Y;;MACAC,e;;MAEAC,Y;;MACAC,c;;MAEAC,W;;;;MACAC,M;;AAEP,SAASC,kBAAT,CAA6BC,GAA7B,EAAkCC,GAAlC,EAAuC;AACrC,MAAIC,KAAK,GAAGD,GAAZ,CADqC,CAErC;;AACA,MAAI,CAACC,KAAL,EAAYA,KAAK,GAAG,IAAIC,KAAJ,CAAU,cAAV,CAAR,CAHyB,CAIrC;;AACA,MAAI,OAAOD,KAAP,KAAiB,QAArB,EAA+BA,KAAK,GAAG,IAAIC,KAAJ,CAAUD,KAAV,CAAR,CALM,CAMrC;;AACA,MAAI,EAAEA,KAAK,YAAYC,KAAnB,CAAJ,EAA+B;AAC7BD,IAAAA,KAAK,GAAGE,MAAM,CAACC,MAAP,CAAc,IAAIF,KAAJ,CAAU,cAAV,CAAd,EAAyC;AAAEG,MAAAA,IAAI,EAAEJ;AAAR,KAAzC,CAAR;AACD;;AAED,MAAIN,cAAc,CAACI,GAAD,CAAlB,EAAyB;AACvBE,IAAAA,KAAK,GAAG,IAAIP,YAAJ,CAAiBO,KAAjB,EAAwBF,GAAxB,CAAR;AACA,WAAOE,KAAP;AACD;;AAEDA,EAAAA,KAAK,CAACK,OAAN,GAAgBP,GAAhB;AACA,SAAOE,KAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASM,aAAT,CAAwBC,IAAxB,EAA8B;AAC5B,QAAMC,mBAAmB,GAAGD,IAAI,CAACH,IAAL,CAAUK,KAAV,CAAgB,CAAhB,EAAmBF,IAAI,CAACH,IAAL,CAAUM,IAA7B,EAAmCH,IAAI,CAACI,IAAL,CAAUC,IAA7C,CAA5B;AACA,SAAOJ,mBAAP;AACD;;AAEc,MAAMK,SAAN,SAAwB1B,UAAxB,CAAmC;AAChD;AAGA2B,EAAAA,WAAW,CAAEC,IAAF,EAAQC,IAAR,EAAc;AACvB,UAAMD,IAAN,EAAYC,IAAZ;AACA,SAAKJ,IAAL,GAAY,UAAZ;AACA,SAAKK,EAAL,GAAU,KAAKD,IAAL,CAAUC,EAAV,IAAgB,WAA1B;AACA,SAAKC,KAAL,GAAa,WAAb;AAEA,SAAKC,aAAL,GAAqBvB,MAArB,CANuB,CAQvB;;AACA,UAAMwB,cAAc,GAAG;AACrBC,MAAAA,QAAQ,EAAE,IADW;AAErBC,MAAAA,SAAS,EAAEN,IAAI,CAACO,MAAL,GAAc,SAAd,GAA0B,MAFhB;AAGrBC,MAAAA,MAAM,EAAE,MAHa;AAIrBC,MAAAA,UAAU,EAAE,IAJS;AAKrBC,MAAAA,oBAAoB,EAAE,KALD;AAMrBH,MAAAA,MAAM,EAAE,KANa;AAOrBI,MAAAA,OAAO,EAAE,EAPY;AAQrBC,MAAAA,OAAO,EAAE,KAAK,IARO;AASrBC,MAAAA,KAAK,EAAE,CATc;AAUrBC,MAAAA,eAAe,EAAE,KAVI;AAWrBC,MAAAA,YAAY,EAAE,EAXO;;AAYrB;AACN;AACA;AACMC,MAAAA,eAAe,CAAEC,YAAF,EAAgB;AAC7B,YAAIC,cAAc,GAAG,EAArB;;AACA,YAAI;AACFA,UAAAA,cAAc,GAAGC,IAAI,CAACC,KAAL,CAAWH,YAAX,CAAjB;AACD,SAFD,CAEE,OAAOlC,GAAP,EAAY;AACZgB,UAAAA,IAAI,CAACsB,GAAL,CAAStC,GAAT;AACD;;AAED,eAAOmC,cAAP;AACD,OAxBoB;;AAyBrB;AACN;AACA;AACA;AACA;AACMI,MAAAA,gBAAgB,CAAEC,CAAF,EAAKC,QAAL,EAAe;AAC7B,YAAIxC,KAAK,GAAG,IAAIC,KAAJ,CAAU,cAAV,CAAZ;;AAEA,YAAIP,cAAc,CAAC8C,QAAD,CAAlB,EAA8B;AAC5BxC,UAAAA,KAAK,GAAG,IAAIP,YAAJ,CAAiBO,KAAjB,EAAwBwC,QAAxB,CAAR;AACD;;AAED,eAAOxC,KAAP;AACD,OAtCoB;;AAuCrB;AACN;AACA;AACA;AACA;AACMyC,MAAAA,cAAc,CAAEC,MAAF,EAAU;AACtB,eAAOA,MAAM,IAAI,GAAV,IAAiBA,MAAM,GAAG,GAAjC;AACD;;AA9CoB,KAAvB;AAiDA,SAAK1B,IAAL,GAAY,EAAE,GAAGI,cAAL;AAAqB,SAAGJ;AAAxB,KAAZ;AACA,SAAK2B,QAAL;AAEA,SAAKC,YAAL,GAAoB,KAAKA,YAAL,CAAkBC,IAAlB,CAAuB,IAAvB,CAApB,CA7DuB,CA+DvB;;AACA,QAAIC,8CAA4B,KAAK9B,IAArC,EAA2C;AACzC,WAAK+B,QAAL,GAAgB,KAAK/B,IAAL,CAAU8B,0CAAV,CAAhB;AACD,KAFD,MAEO;AACL,WAAKC,QAAL,GAAgB,IAAIC,kCAAJ,CAAqB,KAAKhC,IAAL,CAAUa,KAA/B,CAAhB;AACD;;AAED,QAAI,KAAKb,IAAL,CAAUO,MAAV,IAAoB,CAAC,KAAKP,IAAL,CAAUK,QAAnC,EAA6C;AAC3C,YAAM,IAAIpB,KAAJ,CAAU,6DAAV,CAAN;AACD;;AAED,SAAKgD,cAAL,GAAsB/C,MAAM,CAACgD,MAAP,CAAc,IAAd,CAAtB;AACD;;AAEDC,EAAAA,UAAU,CAAE5C,IAAF,EAAQ;AAChB,UAAM6C,SAAS,GAAG,KAAKrC,IAAL,CAAUsC,QAAV,GAAqBC,SAAvC;AACA,UAAM;AAAE3B,MAAAA;AAAF,QAAc,KAAKX,IAAzB;AAEA,UAAMA,IAAI,GAAG,EACX,GAAG,KAAKA,IADG;AAEX,UAAIoC,SAAS,IAAI,EAAjB,CAFW;AAGX,UAAI7C,IAAI,CAAC+C,SAAL,IAAkB,EAAtB,CAHW;AAIX3B,MAAAA,OAAO,EAAE;AAJE,KAAb,CAJgB,CAUhB;AACA;AACA;AACA;AACA;AACA;;AACA,QAAI,OAAOA,OAAP,KAAmB,UAAvB,EAAmC;AACjCX,MAAAA,IAAI,CAACW,OAAL,GAAeA,OAAO,CAACpB,IAAD,CAAtB;AACD,KAFD,MAEO;AACLL,MAAAA,MAAM,CAACC,MAAP,CAAca,IAAI,CAACW,OAAnB,EAA4B,KAAKX,IAAL,CAAUW,OAAtC;AACD;;AAED,QAAIyB,SAAJ,EAAe;AACblD,MAAAA,MAAM,CAACC,MAAP,CAAca,IAAI,CAACW,OAAnB,EAA4ByB,SAAS,CAACzB,OAAtC;AACD;;AACD,QAAIpB,IAAI,CAAC+C,SAAT,EAAoB;AAClBpD,MAAAA,MAAM,CAACC,MAAP,CAAca,IAAI,CAACW,OAAnB,EAA4BpB,IAAI,CAAC+C,SAAL,CAAe3B,OAA3C;AACD;;AAED,WAAOX,IAAP;AACD,GA/G+C,CAiHhD;;;AACAuC,EAAAA,WAAW,CAAElC,QAAF,EAAYV,IAAZ,EAAkBK,IAAlB,EAAwB;AACjC,UAAMS,UAAU,GAAG+B,KAAK,CAACC,OAAN,CAAczC,IAAI,CAACS,UAAnB,IACfT,IAAI,CAACS,UADU,GAEfvB,MAAM,CAACwD,IAAP,CAAY/C,IAAZ,CAFJ,CADiC,CAGX;;AAEtBc,IAAAA,UAAU,CAACkC,OAAX,CAAoBC,IAAD,IAAU;AAC3BvC,MAAAA,QAAQ,CAACwC,MAAT,CAAgBD,IAAhB,EAAsBjD,IAAI,CAACiD,IAAD,CAA1B;AACD,KAFD;AAGD;;AAEDE,EAAAA,oBAAoB,CAAEvD,IAAF,EAAQS,IAAR,EAAc;AAChC,UAAM+C,QAAQ,GAAG,IAAIC,QAAJ,EAAjB;AAEA,SAAKT,WAAL,CAAiBQ,QAAjB,EAA2BxD,IAAI,CAACI,IAAhC,EAAsCK,IAAtC;AAEA,UAAMR,mBAAmB,GAAGF,aAAa,CAACC,IAAD,CAAzC;;AAEA,QAAIA,IAAI,CAAC0D,IAAT,EAAe;AACbF,MAAAA,QAAQ,CAACF,MAAT,CAAgB7C,IAAI,CAACM,SAArB,EAAgCd,mBAAhC,EAAqDD,IAAI,CAACI,IAAL,CAAUsD,IAA/D;AACD,KAFD,MAEO;AACLF,MAAAA,QAAQ,CAACF,MAAT,CAAgB7C,IAAI,CAACM,SAArB,EAAgCd,mBAAhC;AACD;;AAED,WAAOuD,QAAP;AACD;;AAEDG,EAAAA,mBAAmB,CAAEC,KAAF,EAASnD,IAAT,EAAe;AAChC,UAAM+C,QAAQ,GAAG,IAAIC,QAAJ,EAAjB;AAEA,UAAM;AAAErD,MAAAA;AAAF,QAAW,KAAKI,IAAL,CAAUsC,QAAV,EAAjB;AACA,SAAKE,WAAL,CAAiBQ,QAAjB,EAA2BpD,IAA3B,EAAiCK,IAAjC;AAEAmD,IAAAA,KAAK,CAACR,OAAN,CAAepD,IAAD,IAAU;AACtB,YAAM6D,OAAO,GAAG,KAAKjB,UAAL,CAAgB5C,IAAhB,CAAhB;AAEA,YAAMC,mBAAmB,GAAGF,aAAa,CAACC,IAAD,CAAzC;;AAEA,UAAIA,IAAI,CAAC0D,IAAT,EAAe;AACbF,QAAAA,QAAQ,CAACF,MAAT,CAAgBO,OAAO,CAAC9C,SAAxB,EAAmCd,mBAAnC,EAAwDD,IAAI,CAAC0D,IAA7D;AACD,OAFD,MAEO;AACLF,QAAAA,QAAQ,CAACF,MAAT,CAAgBO,OAAO,CAAC9C,SAAxB,EAAmCd,mBAAnC;AACD;AACF,KAVD;AAYA,WAAOuD,QAAP;AACD;;AAEDM,EAAAA,MAAM,CAAE9D,IAAF,EAAQ+D,OAAR,EAAiBC,KAAjB,EAAwB;AAC5B,UAAMvD,IAAI,GAAG,KAAKmC,UAAL,CAAgB5C,IAAhB,CAAb;AAEA,SAAKQ,IAAL,CAAUsB,GAAV,CAAe,aAAYiC,OAAQ,OAAMC,KAAM,EAA/C;AACA,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtC,WAAK3D,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC;AAEA,YAAMH,IAAI,GAAGY,IAAI,CAACK,QAAL,GACT,KAAKyC,oBAAL,CAA0BvD,IAA1B,EAAgCS,IAAhC,CADS,GAETT,IAAI,CAACH,IAFT;AAIA,YAAMN,GAAG,GAAG,IAAI8E,cAAJ,EAAZ;AACA,WAAK3B,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,IAA+B,IAAI1B,YAAJ,CAAiB,KAAKwB,IAAtB,CAA/B;AACA,UAAI8D,aAAJ;AAEA,YAAMC,KAAK,GAAG,IAAItF,eAAJ,CAAoBwB,IAAI,CAACY,OAAzB,EAAkC,MAAM;AACpD9B,QAAAA,GAAG,CAACiF,KAAJ;AACAF,QAAAA,aAAa,CAACG,IAAd;AACA,cAAMhF,KAAK,GAAG,IAAIC,KAAJ,CAAU,KAAKgF,IAAL,CAAU,UAAV,EAAsB;AAAEC,UAAAA,OAAO,EAAEC,IAAI,CAACC,IAAL,CAAUpE,IAAI,CAACY,OAAL,GAAe,IAAzB;AAAX,SAAtB,CAAV,CAAd;AACA,aAAKb,IAAL,CAAU4D,IAAV,CAAe,cAAf,EAA+BpE,IAA/B,EAAqCP,KAArC;AACA0E,QAAAA,MAAM,CAAC1E,KAAD,CAAN;AACD,OANa,CAAd;AAQA,YAAMiB,EAAE,GAAG,wBAAX;AAEAnB,MAAAA,GAAG,CAACuE,MAAJ,CAAWgB,gBAAX,CAA4B,WAA5B,EAAyC,MAAM;AAC7C,aAAKtE,IAAL,CAAUsB,GAAV,CAAe,eAAcpB,EAAG,UAAhC;AACD,OAFD;AAIAnB,MAAAA,GAAG,CAACuE,MAAJ,CAAWgB,gBAAX,CAA4B,UAA5B,EAAyCC,EAAD,IAAQ;AAC9C,aAAKvE,IAAL,CAAUsB,GAAV,CAAe,eAAcpB,EAAG,cAAaqE,EAAE,CAACC,MAAO,MAAKD,EAAE,CAACf,KAAM,EAArE,EAD8C,CAE9C;AACA;;AACAO,QAAAA,KAAK,CAACU,QAAN;;AAEA,YAAIF,EAAE,CAACG,gBAAP,EAAyB;AACvB,eAAK1E,IAAL,CAAU4D,IAAV,CAAe,iBAAf,EAAkCpE,IAAlC,EAAwC;AACtCmF,YAAAA,QAAQ,EAAE,IAD4B;AAEtCC,YAAAA,aAAa,EAAEL,EAAE,CAACC,MAFoB;AAGtCK,YAAAA,UAAU,EAAEN,EAAE,CAACf;AAHuB,WAAxC;AAKD;AACF,OAbD;AAeAzE,MAAAA,GAAG,CAACuF,gBAAJ,CAAqB,MAArB,EAA6B,MAAM;AACjC,aAAKtE,IAAL,CAAUsB,GAAV,CAAe,eAAcpB,EAAG,WAAhC;AACA6D,QAAAA,KAAK,CAACE,IAAN;AACAH,QAAAA,aAAa,CAACG,IAAd;;AACA,YAAI,KAAK/B,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,CAAJ,EAAkC;AAChC,eAAKgC,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,EAA6B4E,MAA7B;AACA,eAAK5C,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,IAA+B,IAA/B;AACD;;AAED,YAAID,IAAI,CAACyB,cAAL,CAAoB3C,GAAG,CAAC4C,MAAxB,EAAgC5C,GAAG,CAACmC,YAApC,EAAkDnC,GAAlD,CAAJ,EAA4D;AAC1D,gBAAMgG,IAAI,GAAG9E,IAAI,CAACgB,eAAL,CAAqBlC,GAAG,CAACmC,YAAzB,EAAuCnC,GAAvC,CAAb;AACA,gBAAMiG,SAAS,GAAGD,IAAI,CAAC9E,IAAI,CAACU,oBAAN,CAAtB;AAEA,gBAAMsE,UAAU,GAAG;AACjBtD,YAAAA,MAAM,EAAE5C,GAAG,CAAC4C,MADK;AAEjBoD,YAAAA,IAFiB;AAGjBC,YAAAA;AAHiB,WAAnB;AAMA,eAAKhF,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC,EAAuCyF,UAAvC;;AAEA,cAAID,SAAJ,EAAe;AACb,iBAAKhF,IAAL,CAAUsB,GAAV,CAAe,YAAW9B,IAAI,CAAC0D,IAAK,SAAQ8B,SAAU,EAAtD;AACD;;AAED,iBAAOtB,OAAO,CAAClE,IAAD,CAAd;AACD;;AACD,cAAMuF,IAAI,GAAG9E,IAAI,CAACgB,eAAL,CAAqBlC,GAAG,CAACmC,YAAzB,EAAuCnC,GAAvC,CAAb;AACA,cAAME,KAAK,GAAGH,kBAAkB,CAACC,GAAD,EAAMkB,IAAI,CAACsB,gBAAL,CAAsBxC,GAAG,CAACmC,YAA1B,EAAwCnC,GAAxC,CAAN,CAAhC;AAEA,cAAM0C,QAAQ,GAAG;AACfE,UAAAA,MAAM,EAAE5C,GAAG,CAAC4C,MADG;AAEfoD,UAAAA;AAFe,SAAjB;AAKA,aAAK/E,IAAL,CAAU4D,IAAV,CAAe,cAAf,EAA+BpE,IAA/B,EAAqCP,KAArC,EAA4CwC,QAA5C;AACA,eAAOkC,MAAM,CAAC1E,KAAD,CAAb;AACD,OArCD;AAuCAF,MAAAA,GAAG,CAACuF,gBAAJ,CAAqB,OAArB,EAA8B,MAAM;AAClC,aAAKtE,IAAL,CAAUsB,GAAV,CAAe,eAAcpB,EAAG,UAAhC;AACA6D,QAAAA,KAAK,CAACE,IAAN;AACAH,QAAAA,aAAa,CAACG,IAAd;;AACA,YAAI,KAAK/B,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,CAAJ,EAAkC;AAChC,eAAKgC,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,EAA6B4E,MAA7B;AACA,eAAK5C,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,IAA+B,IAA/B;AACD;;AAED,cAAMjB,KAAK,GAAGH,kBAAkB,CAACC,GAAD,EAAMkB,IAAI,CAACsB,gBAAL,CAAsBxC,GAAG,CAACmC,YAA1B,EAAwCnC,GAAxC,CAAN,CAAhC;AACA,aAAKiB,IAAL,CAAU4D,IAAV,CAAe,cAAf,EAA+BpE,IAA/B,EAAqCP,KAArC;AACA,eAAO0E,MAAM,CAAC1E,KAAD,CAAb;AACD,OAZD;AAcAF,MAAAA,GAAG,CAACmG,IAAJ,CAASjF,IAAI,CAACQ,MAAL,CAAY0E,WAAZ,EAAT,EAAoClF,IAAI,CAACmF,QAAzC,EAAmD,IAAnD,EA7FsC,CA8FtC;AACA;;AACArG,MAAAA,GAAG,CAACgC,eAAJ,GAAsBd,IAAI,CAACc,eAA3B;;AACA,UAAId,IAAI,CAACe,YAAL,KAAsB,EAA1B,EAA8B;AAC5BjC,QAAAA,GAAG,CAACiC,YAAJ,GAAmBf,IAAI,CAACe,YAAxB;AACD;;AAED8C,MAAAA,aAAa,GAAG,KAAK9B,QAAL,CAAcqD,GAAd,CAAkB,MAAM;AACtC,aAAKrF,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC,EADsC,CAGtC;AACA;AACA;AACA;;AACA,cAAM8F,WAAW,GAAG,KAAKlD,UAAL,CAAgB5C,IAAhB,CAApB;AAEAL,QAAAA,MAAM,CAACwD,IAAP,CAAY2C,WAAW,CAAC1E,OAAxB,EAAiCgC,OAAjC,CAA0C2C,MAAD,IAAY;AACnDxG,UAAAA,GAAG,CAACyG,gBAAJ,CAAqBD,MAArB,EAA6BD,WAAW,CAAC1E,OAAZ,CAAoB2E,MAApB,CAA7B;AACD,SAFD;AAIAxG,QAAAA,GAAG,CAAC0G,IAAJ,CAASpG,IAAT;AAEA,eAAO,MAAM;AACX0E,UAAAA,KAAK,CAACE,IAAN;AACAlF,UAAAA,GAAG,CAACiF,KAAJ;AACD,SAHD;AAID,OAnBe,CAAhB;AAqBA,WAAK0B,YAAL,CAAkBlG,IAAI,CAACU,EAAvB,EAA2B,MAAM;AAC/B4D,QAAAA,aAAa,CAACE,KAAd;AACAL,QAAAA,MAAM,CAAC,IAAIzE,KAAJ,CAAU,cAAV,CAAD,CAAN;AACD,OAHD;AAKA,WAAKyG,WAAL,CAAiBnG,IAAI,CAACU,EAAtB,EAA0B,QAAgB;AAAA,YAAf;AAAE0F,UAAAA;AAAF,SAAe;;AACxC,YAAIA,MAAM,KAAK,MAAf,EAAuB;AACrB9B,UAAAA,aAAa,CAACE,KAAd;AACD;;AACDL,QAAAA,MAAM,CAAC,IAAIzE,KAAJ,CAAU,kBAAV,CAAD,CAAN;AACD,OALD;AAMD,KArIM,CAAP;AAsID;;AAED2G,EAAAA,YAAY,CAAErG,IAAF,EAAQ;AAClB,UAAMS,IAAI,GAAG,KAAKmC,UAAL,CAAgB5C,IAAhB,CAAb;AACA,WAAO,IAAIiE,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtC,WAAK3D,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC;AAEA,YAAMsG,MAAM,GAAG,EAAf;AACA,YAAMpF,UAAU,GAAG+B,KAAK,CAACC,OAAN,CAAczC,IAAI,CAACS,UAAnB,IACfT,IAAI,CAACS,UADU,CAEjB;AAFiB,QAGfvB,MAAM,CAACwD,IAAP,CAAYnD,IAAI,CAACI,IAAjB,CAHJ;AAKAc,MAAAA,UAAU,CAACkC,OAAX,CAAoBM,IAAD,IAAU;AAC3B4C,QAAAA,MAAM,CAAC5C,IAAD,CAAN,GAAe1D,IAAI,CAACI,IAAL,CAAUsD,IAAV,CAAf;AACD,OAFD;AAIA,YAAM6C,MAAM,GAAGvG,IAAI,CAACwG,MAAL,CAAYC,eAAZ,CAA4BC,QAA5B,GAAuCC,yBAAvC,GAAkDC,8BAAjE;AACA,YAAMC,MAAM,GAAG,IAAIN,MAAJ,CAAW,KAAK/F,IAAhB,EAAsBR,IAAI,CAACwG,MAAL,CAAYC,eAAlC,CAAf;AACAI,MAAAA,MAAM,CAACC,IAAP,CAAY9G,IAAI,CAACwG,MAAL,CAAYO,GAAxB,EAA6B,EAC3B,GAAG/G,IAAI,CAACwG,MAAL,CAAYjB,IADY;AAE3BK,QAAAA,QAAQ,EAAEnF,IAAI,CAACmF,QAFY;AAG3BzF,QAAAA,IAAI,EAAEH,IAAI,CAACH,IAAL,CAAUM,IAHW;AAI3B6G,QAAAA,SAAS,EAAEvG,IAAI,CAACM,SAJW;AAK3BkG,QAAAA,QAAQ,EAAEX,MALiB;AAM3BY,QAAAA,UAAU,EAAEzG,IAAI,CAACQ,MANU;AAO3BkG,QAAAA,WAAW,EAAE1G,IAAI,CAACK,QAPS;AAQ3BM,QAAAA,OAAO,EAAEX,IAAI,CAACW;AARa,OAA7B,EASGgG,IATH,CASSC,GAAD,IAAS;AACf,cAAM;AAAEC,UAAAA;AAAF,YAAYD,GAAlB;AACA,cAAME,IAAI,GAAGzI,aAAa,CAACkB,IAAI,CAACwG,MAAL,CAAYgB,YAAb,CAA1B;AACA,cAAMC,MAAM,GAAG,IAAIC,uBAAJ,CAAW;AAAEC,UAAAA,MAAM,EAAG,GAAEJ,IAAK,QAAOD,KAAM,EAA/B;AAAkCM,UAAAA,QAAQ,EAAE;AAA5C,SAAX,CAAf;AACA,aAAKlF,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,IAA+B,IAAI1B,YAAJ,CAAiB,KAAKwB,IAAtB,CAA/B;AACA,YAAI8D,aAAJ;AAEA,aAAK4B,YAAL,CAAkBlG,IAAI,CAACU,EAAvB,EAA2B,MAAM;AAC/B+G,UAAAA,MAAM,CAACxB,IAAP,CAAY,QAAZ,EAAsB,EAAtB;AACA3B,UAAAA,aAAa,CAACE,KAAd;AACAN,UAAAA,OAAO,CAAE,UAASlE,IAAI,CAACU,EAAG,cAAnB,CAAP;AACD,SAJD;AAMA,aAAKyF,WAAL,CAAiBnG,IAAI,CAACU,EAAtB,EAA0B,iBAAqB;AAAA,cAApB;AAAE0F,YAAAA;AAAF,WAAoB,sBAAP,EAAO;;AAC7C,cAAIA,MAAM,KAAK,MAAf,EAAuB;AACrBqB,YAAAA,MAAM,CAACxB,IAAP,CAAY,QAAZ,EAAsB,EAAtB;AACA3B,YAAAA,aAAa,CAACE,KAAd;AACD;;AACDN,UAAAA,OAAO,CAAE,UAASlE,IAAI,CAACU,EAAG,eAAnB,CAAP;AACD,SAND;AAQA,aAAKmH,OAAL,CAAa7H,IAAI,CAACU,EAAlB,EAAsB,MAAM;AAC1B+G,UAAAA,MAAM,CAACxB,IAAP,CAAY,OAAZ,EAAqB,EAArB;AACAwB,UAAAA,MAAM,CAACxB,IAAP,CAAY,QAAZ,EAAsB,EAAtB;AACD,SAHD;AAKA,aAAK6B,UAAL,CAAgB9H,IAAI,CAACU,EAArB,EAAyB,MAAM;AAC7B+G,UAAAA,MAAM,CAACxB,IAAP,CAAY,OAAZ,EAAqB,EAArB;AACAwB,UAAAA,MAAM,CAACxB,IAAP,CAAY,QAAZ,EAAsB,EAAtB;AACD,SAHD;AAKAwB,QAAAA,MAAM,CAACM,EAAP,CAAU,UAAV,EAAuBC,YAAD,IAAkBnJ,kBAAkB,CAAC,IAAD,EAAOmJ,YAAP,EAAqBhI,IAArB,CAA1D;AAEAyH,QAAAA,MAAM,CAACM,EAAP,CAAU,SAAV,EAAsBlI,IAAD,IAAU;AAC7B,gBAAM0F,IAAI,GAAG9E,IAAI,CAACgB,eAAL,CAAqB5B,IAAI,CAACoC,QAAL,CAAcP,YAAnC,EAAiD7B,IAAI,CAACoC,QAAtD,CAAb;AACA,gBAAMuD,SAAS,GAAGD,IAAI,CAAC9E,IAAI,CAACU,oBAAN,CAAtB;AAEA,gBAAMsE,UAAU,GAAG;AACjBtD,YAAAA,MAAM,EAAEtC,IAAI,CAACoC,QAAL,CAAcE,MADL;AAEjBoD,YAAAA,IAFiB;AAGjBC,YAAAA;AAHiB,WAAnB;AAMA,eAAKhF,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC,EAAuCyF,UAAvC;AACAnB,UAAAA,aAAa,CAACG,IAAd;;AACA,cAAI,KAAK/B,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,CAAJ,EAAkC;AAChC,iBAAKgC,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,EAA6B4E,MAA7B;AACA,iBAAK5C,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,IAA+B,IAA/B;AACD;;AACD,iBAAOwD,OAAO,EAAd;AACD,SAjBD;AAmBAuD,QAAAA,MAAM,CAACM,EAAP,CAAU,OAAV,EAAoBE,OAAD,IAAa;AAC9B,gBAAMC,IAAI,GAAGD,OAAO,CAAChG,QAArB;AACA,gBAAMxC,KAAK,GAAGyI,IAAI,GACdzH,IAAI,CAACsB,gBAAL,CAAsBmG,IAAI,CAACxG,YAA3B,EAAyCwG,IAAzC,CADc,GAEdvI,MAAM,CAACC,MAAP,CAAc,IAAIF,KAAJ,CAAUuI,OAAO,CAACxI,KAAR,CAAc0I,OAAxB,CAAd,EAAgD;AAAEC,YAAAA,KAAK,EAAEH,OAAO,CAACxI;AAAjB,WAAhD,CAFJ;AAGA,eAAKe,IAAL,CAAU4D,IAAV,CAAe,cAAf,EAA+BpE,IAA/B,EAAqCP,KAArC;AACA6E,UAAAA,aAAa,CAACG,IAAd;;AACA,cAAI,KAAK/B,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,CAAJ,EAAkC;AAChC,iBAAKgC,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,EAA6B4E,MAA7B;AACA,iBAAK5C,cAAL,CAAoB1C,IAAI,CAACU,EAAzB,IAA+B,IAA/B;AACD;;AACDyD,UAAAA,MAAM,CAAC1E,KAAD,CAAN;AACD,SAZD;AAcA6E,QAAAA,aAAa,GAAG,KAAK9B,QAAL,CAAcqD,GAAd,CAAkB,MAAM;AACtC4B,UAAAA,MAAM,CAAC/B,IAAP;;AACA,cAAI1F,IAAI,CAACqI,QAAT,EAAmB;AACjBZ,YAAAA,MAAM,CAACxB,IAAP,CAAY,OAAZ,EAAqB,EAArB;AACD;;AAED,iBAAO,MAAMwB,MAAM,CAACa,KAAP,EAAb;AACD,SAPe,CAAhB;AAQD,OAnFD,EAmFGC,KAnFH,CAmFU/I,GAAD,IAAS;AAChB,aAAKgB,IAAL,CAAU4D,IAAV,CAAe,cAAf,EAA+BpE,IAA/B,EAAqCR,GAArC;AACA2E,QAAAA,MAAM,CAAC3E,GAAD,CAAN;AACD,OAtFD;AAuFD,KAtGM,CAAP;AAuGD;;AAEDgJ,EAAAA,YAAY,CAAE5E,KAAF,EAAS;AACnB,WAAO,IAAIK,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtC,YAAM;AAAEyB,QAAAA;AAAF,UAAe,KAAKnF,IAA1B;AACA,YAAM;AAAEQ,QAAAA;AAAF,UAAa,KAAKR,IAAxB;AAEA,YAAMgI,aAAa,GAAG,KAAKjI,IAAL,CAAUsC,QAAV,GAAqBC,SAA3C;AACA,YAAMjC,QAAQ,GAAG,KAAK6C,mBAAL,CAAyBC,KAAzB,EAAgC,EAC/C,GAAG,KAAKnD,IADuC;AAE/C,YAAIgI,aAAa,IAAI,EAArB;AAF+C,OAAhC,CAAjB;AAKA,YAAMlJ,GAAG,GAAG,IAAI8E,cAAJ,EAAZ;;AAEA,YAAMqE,SAAS,GAAIjJ,KAAD,IAAW;AAC3BmE,QAAAA,KAAK,CAACR,OAAN,CAAepD,IAAD,IAAU;AACtB,eAAKQ,IAAL,CAAU4D,IAAV,CAAe,cAAf,EAA+BpE,IAA/B,EAAqCP,KAArC;AACD,SAFD;AAGD,OAJD;;AAMA,YAAM8E,KAAK,GAAG,IAAItF,eAAJ,CAAoB,KAAKwB,IAAL,CAAUY,OAA9B,EAAuC,MAAM;AACzD9B,QAAAA,GAAG,CAACiF,KAAJ;AACA,cAAM/E,KAAK,GAAG,IAAIC,KAAJ,CAAU,KAAKgF,IAAL,CAAU,UAAV,EAAsB;AAAEC,UAAAA,OAAO,EAAEC,IAAI,CAACC,IAAL,CAAU,KAAKpE,IAAL,CAAUY,OAAV,GAAoB,IAA9B;AAAX,SAAtB,CAAV,CAAd;AACAqH,QAAAA,SAAS,CAACjJ,KAAD,CAAT;AACA0E,QAAAA,MAAM,CAAC1E,KAAD,CAAN;AACD,OALa,CAAd;AAOAF,MAAAA,GAAG,CAACuE,MAAJ,CAAWgB,gBAAX,CAA4B,WAA5B,EAAyC,MAAM;AAC7C,aAAKtE,IAAL,CAAUsB,GAAV,CAAc,sCAAd;AACAyC,QAAAA,KAAK,CAACU,QAAN;AACD,OAHD;AAKA1F,MAAAA,GAAG,CAACuE,MAAJ,CAAWgB,gBAAX,CAA4B,UAA5B,EAAyCC,EAAD,IAAQ;AAC9CR,QAAAA,KAAK,CAACU,QAAN;AAEA,YAAI,CAACF,EAAE,CAACG,gBAAR,EAA0B;AAE1BtB,QAAAA,KAAK,CAACR,OAAN,CAAepD,IAAD,IAAU;AACtB,eAAKQ,IAAL,CAAU4D,IAAV,CAAe,iBAAf,EAAkCpE,IAAlC,EAAwC;AACtCmF,YAAAA,QAAQ,EAAE,IAD4B;AAEtCC,YAAAA,aAAa,EAAGL,EAAE,CAACC,MAAH,GAAYD,EAAE,CAACf,KAAhB,GAAyBhE,IAAI,CAACG,IAFP;AAGtCkF,YAAAA,UAAU,EAAErF,IAAI,CAACG;AAHqB,WAAxC;AAKD,SAND;AAOD,OAZD;AAcAZ,MAAAA,GAAG,CAACuF,gBAAJ,CAAqB,MAArB,EAA8BC,EAAD,IAAQ;AACnCR,QAAAA,KAAK,CAACE,IAAN;;AAEA,YAAI,KAAKhE,IAAL,CAAUyB,cAAV,CAAyB6C,EAAE,CAAC4C,MAAH,CAAUxF,MAAnC,EAA2C5C,GAAG,CAACmC,YAA/C,EAA6DnC,GAA7D,CAAJ,EAAuE;AACrE,gBAAMgG,IAAI,GAAG,KAAK9E,IAAL,CAAUgB,eAAV,CAA0BlC,GAAG,CAACmC,YAA9B,EAA4CnC,GAA5C,CAAb;AACA,gBAAMkG,UAAU,GAAG;AACjBtD,YAAAA,MAAM,EAAE4C,EAAE,CAAC4C,MAAH,CAAUxF,MADD;AAEjBoD,YAAAA;AAFiB,WAAnB;AAIA3B,UAAAA,KAAK,CAACR,OAAN,CAAepD,IAAD,IAAU;AACtB,iBAAKQ,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC,EAAuCyF,UAAvC;AACD,WAFD;AAGA,iBAAOvB,OAAO,EAAd;AACD;;AAED,cAAMzE,KAAK,GAAG,KAAKgB,IAAL,CAAUsB,gBAAV,CAA2BxC,GAAG,CAACmC,YAA/B,EAA6CnC,GAA7C,KAAqD,IAAIG,KAAJ,CAAU,cAAV,CAAnE;AACAD,QAAAA,KAAK,CAACK,OAAN,GAAgBP,GAAhB;AACAmJ,QAAAA,SAAS,CAACjJ,KAAD,CAAT;AACA,eAAO0E,MAAM,CAAC1E,KAAD,CAAb;AACD,OAnBD;AAqBAF,MAAAA,GAAG,CAACuF,gBAAJ,CAAqB,OAArB,EAA8B,MAAM;AAClCP,QAAAA,KAAK,CAACE,IAAN;AAEA,cAAMhF,KAAK,GAAG,KAAKgB,IAAL,CAAUsB,gBAAV,CAA2BxC,GAAG,CAACmC,YAA/B,EAA6CnC,GAA7C,KAAqD,IAAIG,KAAJ,CAAU,cAAV,CAAnE;AACAgJ,QAAAA,SAAS,CAACjJ,KAAD,CAAT;AACA,eAAO0E,MAAM,CAAC1E,KAAD,CAAb;AACD,OAND;AAQA,WAAKe,IAAL,CAAUuH,EAAV,CAAa,YAAb,EAA2B,kBAAqB;AAAA,YAApB;AAAE3B,UAAAA;AAAF,SAAoB,uBAAP,EAAO;AAC9C,YAAIA,MAAM,KAAK,MAAf,EAAuB;AACvB7B,QAAAA,KAAK,CAACE,IAAN;AACAlF,QAAAA,GAAG,CAACiF,KAAJ;AACD,OAJD;AAMAjF,MAAAA,GAAG,CAACmG,IAAJ,CAASzE,MAAM,CAAC0E,WAAP,EAAT,EAA+BC,QAA/B,EAAyC,IAAzC,EA/EsC,CAgFtC;AACA;;AACArG,MAAAA,GAAG,CAACgC,eAAJ,GAAsB,KAAKd,IAAL,CAAUc,eAAhC;;AACA,UAAI,KAAKd,IAAL,CAAUe,YAAV,KAA2B,EAA/B,EAAmC;AACjCjC,QAAAA,GAAG,CAACiC,YAAJ,GAAmB,KAAKf,IAAL,CAAUe,YAA7B;AACD;;AAED7B,MAAAA,MAAM,CAACwD,IAAP,CAAY,KAAK1C,IAAL,CAAUW,OAAtB,EAA+BgC,OAA/B,CAAwC2C,MAAD,IAAY;AACjDxG,QAAAA,GAAG,CAACyG,gBAAJ,CAAqBD,MAArB,EAA6B,KAAKtF,IAAL,CAAUW,OAAV,CAAkB2E,MAAlB,CAA7B;AACD,OAFD;AAIAxG,MAAAA,GAAG,CAAC0G,IAAJ,CAASnF,QAAT;AAEA8C,MAAAA,KAAK,CAACR,OAAN,CAAepD,IAAD,IAAU;AACtB,aAAKQ,IAAL,CAAU4D,IAAV,CAAe,gBAAf,EAAiCpE,IAAjC;AACD,OAFD;AAGD,KAhGM,CAAP;AAiGD;;AAED2I,EAAAA,WAAW,CAAE/E,KAAF,EAAS;AAClB,UAAMgF,QAAQ,GAAGhF,KAAK,CAACiF,GAAN,CAAU,CAAC7I,IAAD,EAAO8I,CAAP,KAAa;AACtC,YAAM/E,OAAO,GAAGgF,QAAQ,CAACD,CAAD,EAAI,EAAJ,CAAR,GAAkB,CAAlC;AACA,YAAM9E,KAAK,GAAGJ,KAAK,CAACoF,MAApB;;AAEA,UAAIhJ,IAAI,CAACP,KAAT,EAAgB;AACd,eAAOwE,OAAO,CAACE,MAAR,CAAe,IAAIzE,KAAJ,CAAUM,IAAI,CAACP,KAAf,CAAf,CAAP;AACD;;AAAC,UAAIO,IAAI,CAACiJ,QAAT,EAAmB;AACnB,eAAO,KAAK5C,YAAL,CAAkBrG,IAAlB,EAAwB+D,OAAxB,EAAiCC,KAAjC,CAAP;AACD;;AACD,aAAO,KAAKF,MAAL,CAAY9D,IAAZ,EAAkB+D,OAAlB,EAA2BC,KAA3B,CAAP;AACD,KAVgB,CAAjB;AAYA,WAAOjF,MAAM,CAAC6J,QAAD,CAAb;AACD;;AAED1C,EAAAA,YAAY,CAAEgD,MAAF,EAAUC,EAAV,EAAc;AACxB,SAAKzG,cAAL,CAAoBwG,MAApB,EAA4BnB,EAA5B,CAA+B,cAA/B,EAAgD/H,IAAD,IAAU;AACvD,UAAIkJ,MAAM,KAAKlJ,IAAI,CAACU,EAApB,EAAwByI,EAAE,CAACnJ,IAAI,CAACU,EAAN,CAAF;AACzB,KAFD;AAGD;;AAEDmH,EAAAA,OAAO,CAAEqB,MAAF,EAAUC,EAAV,EAAc;AACnB,SAAKzG,cAAL,CAAoBwG,MAApB,EAA4BnB,EAA5B,CAA+B,cAA/B,EAAgDqB,YAAD,IAAkB;AAC/D,UAAIF,MAAM,KAAKE,YAAf,EAA6B;AAC3BD,QAAAA,EAAE;AACH;AACF,KAJD;AAKD;;AAEDrB,EAAAA,UAAU,CAAEoB,MAAF,EAAUC,EAAV,EAAc;AACtB,SAAKzG,cAAL,CAAoBwG,MAApB,EAA4BnB,EAA5B,CAA+B,WAA/B,EAA4C,MAAM;AAChD,UAAI,CAAC,KAAKvH,IAAL,CAAU6I,OAAV,CAAkBH,MAAlB,CAAL,EAAgC;AAChCC,MAAAA,EAAE;AACH,KAHD;AAID;;AAEDhD,EAAAA,WAAW,CAAE+C,MAAF,EAAUI,YAAV,EAAwB;AAAA;;AACjC,SAAK5G,cAAL,CAAoBwG,MAApB,EAA4BnB,EAA5B,CAA+B,YAA/B,EAA6C,YAAa;AACxD,UAAI,CAAC,KAAI,CAACvH,IAAL,CAAU6I,OAAV,CAAkBH,MAAlB,CAAL,EAAgC;AAChCI,MAAAA,YAAY,CAAC,YAAD,CAAZ;AACD,KAHD;AAID;;AAEDjH,EAAAA,YAAY,CAAEkH,OAAF,EAAW;AACrB,QAAIA,OAAO,CAACP,MAAR,KAAmB,CAAvB,EAA0B;AACxB,WAAKxI,IAAL,CAAUsB,GAAV,CAAc,iCAAd;AACA,aAAOmC,OAAO,CAACC,OAAR,EAAP;AACD,KAJoB,CAMrB;AACA;;;AACA,QAAI,KAAKzD,IAAL,CAAUa,KAAV,KAAoB,CAApB,IAAyB,CAAC,KAAKb,IAAL,CAAU8B,0CAAV,CAA9B,EAAmE;AACjE,WAAK/B,IAAL,CAAUsB,GAAV,CACE,kPADF,EAEE,SAFF;AAID;;AAED,SAAKtB,IAAL,CAAUsB,GAAV,CAAc,0BAAd;AACA,UAAM8B,KAAK,GAAG2F,OAAO,CAACV,GAAR,CAAaK,MAAD,IAAY,KAAK1I,IAAL,CAAU6I,OAAV,CAAkBH,MAAlB,CAAxB,CAAd;;AAEA,QAAI,KAAKzI,IAAL,CAAUO,MAAd,EAAsB;AACpB;AACA,YAAMwI,gBAAgB,GAAG5F,KAAK,CAAC6F,IAAN,CAAWzJ,IAAI,IAAIA,IAAI,CAACiJ,QAAxB,CAAzB;;AACA,UAAIO,gBAAJ,EAAsB;AACpB,cAAM,IAAI9J,KAAJ,CAAU,iEAAV,CAAN;AACD;;AAED,UAAI,OAAO,KAAKe,IAAL,CAAUW,OAAjB,KAA6B,UAAjC,EAA6C;AAC3C,cAAM,IAAIsI,SAAJ,CAAc,uEAAd,CAAN;AACD;;AAED,aAAO,KAAKlB,YAAL,CAAkB5E,KAAlB,CAAP;AACD;;AAED,WAAO,KAAK+E,WAAL,CAAiB/E,KAAjB,EAAwBwD,IAAxB,CAA6B,MAAM,IAAnC,CAAP;AACD;;AAEDuC,EAAAA,OAAO,GAAI;AACT,QAAI,KAAKlJ,IAAL,CAAUO,MAAd,EAAsB;AACpB,YAAM;AAAE4I,QAAAA;AAAF,UAAmB,KAAKpJ,IAAL,CAAUsC,QAAV,EAAzB;AACA,WAAKtC,IAAL,CAAUqJ,QAAV,CAAmB;AACjBD,QAAAA,YAAY,EAAE,EACZ,GAAGA,YADS;AAEZE,UAAAA,sBAAsB,EAAE;AAFZ;AADG,OAAnB;AAMD;;AAED,SAAKtJ,IAAL,CAAUuJ,WAAV,CAAsB,KAAK1H,YAA3B;AACD;;AAED2H,EAAAA,SAAS,GAAI;AACX,QAAI,KAAKvJ,IAAL,CAAUO,MAAd,EAAsB;AACpB,YAAM;AAAE4I,QAAAA;AAAF,UAAmB,KAAKpJ,IAAL,CAAUsC,QAAV,EAAzB;AACA,WAAKtC,IAAL,CAAUqJ,QAAV,CAAmB;AACjBD,QAAAA,YAAY,EAAE,EACZ,GAAGA,YADS;AAEZE,UAAAA,sBAAsB,EAAE;AAFZ;AADG,OAAnB;AAMD;;AAED,SAAKtJ,IAAL,CAAUyJ,cAAV,CAAyB,KAAK5H,YAA9B;AACD;;AArmB+C;;AAA7B/B,S,CAEZ4J,O,GAAU9K,WAAW,CAAC+K,O;iBAFV7J,S", "sourcesContent": ["import BasePlugin from '@uppy/core/lib/BasePlugin'\nimport { nanoid } from 'nanoid/non-secure'\nimport { Provider, RequestClient, Socket } from '@uppy/companion-client'\nimport emitSocketProgress from '@uppy/utils/lib/emitSocketProgress'\nimport getSocketHost from '@uppy/utils/lib/getSocketHost'\nimport settle from '@uppy/utils/lib/settle'\nimport EventTracker from '@uppy/utils/lib/EventTracker'\nimport ProgressTimeout from '@uppy/utils/lib/ProgressTimeout'\nimport { RateLimitedQueue, internalRateLimitedQueue } from '@uppy/utils/lib/RateLimitedQueue'\nimport NetworkError from '@uppy/utils/lib/NetworkError'\nimport isNetworkError from '@uppy/utils/lib/isNetworkError'\n\nimport packageJson from '../package.json'\nimport locale from './locale.js'\n\nfunction buildResponseError (xhr, err) {\n  let error = err\n  // No error message\n  if (!error) error = new Error('Upload error')\n  // Got an error message string\n  if (typeof error === 'string') error = new Error(error)\n  // Got something else\n  if (!(error instanceof Error)) {\n    error = Object.assign(new Error('Upload error'), { data: error })\n  }\n\n  if (isNetworkError(xhr)) {\n    error = new NetworkError(error, xhr)\n    return error\n  }\n\n  error.request = xhr\n  return error\n}\n\n/**\n * Set `data.type` in the blob to `file.meta.type`,\n * because we might have detected a more accurate file type in Uppy\n * https://stackoverflow.com/a/50875615\n *\n * @param {object} file File object with `data`, `size` and `meta` properties\n * @returns {object} blob updated with the new `type` set from `file.meta.type`\n */\nfunction setTypeInBlob (file) {\n  const dataWithUpdatedType = file.data.slice(0, file.data.size, file.meta.type)\n  return dataWithUpdatedType\n}\n\nexport default class XHRUpload extends BasePlugin {\n  // eslint-disable-next-line global-require\n  static VERSION = packageJson.version\n\n  constructor (uppy, opts) {\n    super(uppy, opts)\n    this.type = 'uploader'\n    this.id = this.opts.id || 'XHRUpload'\n    this.title = 'XHRUpload'\n\n    this.defaultLocale = locale\n\n    // Default options\n    const defaultOptions = {\n      formData: true,\n      fieldName: opts.bundle ? 'files[]' : 'file',\n      method: 'post',\n      metaFields: null,\n      responseUrlFieldName: 'url',\n      bundle: false,\n      headers: {},\n      timeout: 30 * 1000,\n      limit: 5,\n      withCredentials: false,\n      responseType: '',\n      /**\n       * @param {string} responseText the response body string\n       */\n      getResponseData (responseText) {\n        let parsedResponse = {}\n        try {\n          parsedResponse = JSON.parse(responseText)\n        } catch (err) {\n          uppy.log(err)\n        }\n\n        return parsedResponse\n      },\n      /**\n       *\n       * @param {string} _ the response body string\n       * @param {XMLHttpRequest | respObj} response the response object (XHR or similar)\n       */\n      getResponseError (_, response) {\n        let error = new Error('Upload error')\n\n        if (isNetworkError(response)) {\n          error = new NetworkError(error, response)\n        }\n\n        return error\n      },\n      /**\n       * Check if the response from the upload endpoint indicates that the upload was successful.\n       *\n       * @param {number} status the response status code\n       */\n      validateStatus (status) {\n        return status >= 200 && status < 300\n      },\n    }\n\n    this.opts = { ...defaultOptions, ...opts }\n    this.i18nInit()\n\n    this.handleUpload = this.handleUpload.bind(this)\n\n    // Simultaneous upload limiting is shared across all uploads with this plugin.\n    if (internalRateLimitedQueue in this.opts) {\n      this.requests = this.opts[internalRateLimitedQueue]\n    } else {\n      this.requests = new RateLimitedQueue(this.opts.limit)\n    }\n\n    if (this.opts.bundle && !this.opts.formData) {\n      throw new Error('`opts.formData` must be true when `opts.bundle` is enabled.')\n    }\n\n    this.uploaderEvents = Object.create(null)\n  }\n\n  getOptions (file) {\n    const overrides = this.uppy.getState().xhrUpload\n    const { headers } = this.opts\n\n    const opts = {\n      ...this.opts,\n      ...(overrides || {}),\n      ...(file.xhrUpload || {}),\n      headers: {},\n    }\n    // Support for `headers` as a function, only in the XHRUpload settings.\n    // Options set by other plugins in Uppy state or on the files themselves are still merged in afterward.\n    //\n    // ```js\n    // headers: (file) => ({ expires: file.meta.expires })\n    // ```\n    if (typeof headers === 'function') {\n      opts.headers = headers(file)\n    } else {\n      Object.assign(opts.headers, this.opts.headers)\n    }\n\n    if (overrides) {\n      Object.assign(opts.headers, overrides.headers)\n    }\n    if (file.xhrUpload) {\n      Object.assign(opts.headers, file.xhrUpload.headers)\n    }\n\n    return opts\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  addMetadata (formData, meta, opts) {\n    const metaFields = Array.isArray(opts.metaFields)\n      ? opts.metaFields\n      : Object.keys(meta) // Send along all fields by default.\n\n    metaFields.forEach((item) => {\n      formData.append(item, meta[item])\n    })\n  }\n\n  createFormDataUpload (file, opts) {\n    const formPost = new FormData()\n\n    this.addMetadata(formPost, file.meta, opts)\n\n    const dataWithUpdatedType = setTypeInBlob(file)\n\n    if (file.name) {\n      formPost.append(opts.fieldName, dataWithUpdatedType, file.meta.name)\n    } else {\n      formPost.append(opts.fieldName, dataWithUpdatedType)\n    }\n\n    return formPost\n  }\n\n  createBundledUpload (files, opts) {\n    const formPost = new FormData()\n\n    const { meta } = this.uppy.getState()\n    this.addMetadata(formPost, meta, opts)\n\n    files.forEach((file) => {\n      const options = this.getOptions(file)\n\n      const dataWithUpdatedType = setTypeInBlob(file)\n\n      if (file.name) {\n        formPost.append(options.fieldName, dataWithUpdatedType, file.name)\n      } else {\n        formPost.append(options.fieldName, dataWithUpdatedType)\n      }\n    })\n\n    return formPost\n  }\n\n  upload (file, current, total) {\n    const opts = this.getOptions(file)\n\n    this.uppy.log(`uploading ${current} of ${total}`)\n    return new Promise((resolve, reject) => {\n      this.uppy.emit('upload-started', file)\n\n      const data = opts.formData\n        ? this.createFormDataUpload(file, opts)\n        : file.data\n\n      const xhr = new XMLHttpRequest()\n      this.uploaderEvents[file.id] = new EventTracker(this.uppy)\n      let queuedRequest\n\n      const timer = new ProgressTimeout(opts.timeout, () => {\n        xhr.abort()\n        queuedRequest.done()\n        const error = new Error(this.i18n('timedOut', { seconds: Math.ceil(opts.timeout / 1000) }))\n        this.uppy.emit('upload-error', file, error)\n        reject(error)\n      })\n\n      const id = nanoid()\n\n      xhr.upload.addEventListener('loadstart', () => {\n        this.uppy.log(`[XHRUpload] ${id} started`)\n      })\n\n      xhr.upload.addEventListener('progress', (ev) => {\n        this.uppy.log(`[XHRUpload] ${id} progress: ${ev.loaded} / ${ev.total}`)\n        // Begin checking for timeouts when progress starts, instead of loading,\n        // to avoid timing out requests on browser concurrency queue\n        timer.progress()\n\n        if (ev.lengthComputable) {\n          this.uppy.emit('upload-progress', file, {\n            uploader: this,\n            bytesUploaded: ev.loaded,\n            bytesTotal: ev.total,\n          })\n        }\n      })\n\n      xhr.addEventListener('load', () => {\n        this.uppy.log(`[XHRUpload] ${id} finished`)\n        timer.done()\n        queuedRequest.done()\n        if (this.uploaderEvents[file.id]) {\n          this.uploaderEvents[file.id].remove()\n          this.uploaderEvents[file.id] = null\n        }\n\n        if (opts.validateStatus(xhr.status, xhr.responseText, xhr)) {\n          const body = opts.getResponseData(xhr.responseText, xhr)\n          const uploadURL = body[opts.responseUrlFieldName]\n\n          const uploadResp = {\n            status: xhr.status,\n            body,\n            uploadURL,\n          }\n\n          this.uppy.emit('upload-success', file, uploadResp)\n\n          if (uploadURL) {\n            this.uppy.log(`Download ${file.name} from ${uploadURL}`)\n          }\n\n          return resolve(file)\n        }\n        const body = opts.getResponseData(xhr.responseText, xhr)\n        const error = buildResponseError(xhr, opts.getResponseError(xhr.responseText, xhr))\n\n        const response = {\n          status: xhr.status,\n          body,\n        }\n\n        this.uppy.emit('upload-error', file, error, response)\n        return reject(error)\n      })\n\n      xhr.addEventListener('error', () => {\n        this.uppy.log(`[XHRUpload] ${id} errored`)\n        timer.done()\n        queuedRequest.done()\n        if (this.uploaderEvents[file.id]) {\n          this.uploaderEvents[file.id].remove()\n          this.uploaderEvents[file.id] = null\n        }\n\n        const error = buildResponseError(xhr, opts.getResponseError(xhr.responseText, xhr))\n        this.uppy.emit('upload-error', file, error)\n        return reject(error)\n      })\n\n      xhr.open(opts.method.toUpperCase(), opts.endpoint, true)\n      // IE10 does not allow setting `withCredentials` and `responseType`\n      // before `open()` is called.\n      xhr.withCredentials = opts.withCredentials\n      if (opts.responseType !== '') {\n        xhr.responseType = opts.responseType\n      }\n\n      queuedRequest = this.requests.run(() => {\n        this.uppy.emit('upload-started', file)\n\n        // When using an authentication system like JWT, the bearer token goes as a header. This\n        // header needs to be fresh each time the token is refreshed so computing and setting the\n        // headers just before the upload starts enables this kind of authentication to work properly.\n        // Otherwise, half-way through the list of uploads the token could be stale and the upload would fail.\n        const currentOpts = this.getOptions(file)\n\n        Object.keys(currentOpts.headers).forEach((header) => {\n          xhr.setRequestHeader(header, currentOpts.headers[header])\n        })\n\n        xhr.send(data)\n\n        return () => {\n          timer.done()\n          xhr.abort()\n        }\n      })\n\n      this.onFileRemove(file.id, () => {\n        queuedRequest.abort()\n        reject(new Error('File removed'))\n      })\n\n      this.onCancelAll(file.id, ({ reason }) => {\n        if (reason === 'user') {\n          queuedRequest.abort()\n        }\n        reject(new Error('Upload cancelled'))\n      })\n    })\n  }\n\n  uploadRemote (file) {\n    const opts = this.getOptions(file)\n    return new Promise((resolve, reject) => {\n      this.uppy.emit('upload-started', file)\n\n      const fields = {}\n      const metaFields = Array.isArray(opts.metaFields)\n        ? opts.metaFields\n        // Send along all fields by default.\n        : Object.keys(file.meta)\n\n      metaFields.forEach((name) => {\n        fields[name] = file.meta[name]\n      })\n\n      const Client = file.remote.providerOptions.provider ? Provider : RequestClient\n      const client = new Client(this.uppy, file.remote.providerOptions)\n      client.post(file.remote.url, {\n        ...file.remote.body,\n        endpoint: opts.endpoint,\n        size: file.data.size,\n        fieldname: opts.fieldName,\n        metadata: fields,\n        httpMethod: opts.method,\n        useFormData: opts.formData,\n        headers: opts.headers,\n      }).then((res) => {\n        const { token } = res\n        const host = getSocketHost(file.remote.companionUrl)\n        const socket = new Socket({ target: `${host}/api/${token}`, autoOpen: false })\n        this.uploaderEvents[file.id] = new EventTracker(this.uppy)\n        let queuedRequest\n\n        this.onFileRemove(file.id, () => {\n          socket.send('cancel', {})\n          queuedRequest.abort()\n          resolve(`upload ${file.id} was removed`)\n        })\n\n        this.onCancelAll(file.id, ({ reason } = {}) => {\n          if (reason === 'user') {\n            socket.send('cancel', {})\n            queuedRequest.abort()\n          }\n          resolve(`upload ${file.id} was canceled`)\n        })\n\n        this.onRetry(file.id, () => {\n          socket.send('pause', {})\n          socket.send('resume', {})\n        })\n\n        this.onRetryAll(file.id, () => {\n          socket.send('pause', {})\n          socket.send('resume', {})\n        })\n\n        socket.on('progress', (progressData) => emitSocketProgress(this, progressData, file))\n\n        socket.on('success', (data) => {\n          const body = opts.getResponseData(data.response.responseText, data.response)\n          const uploadURL = body[opts.responseUrlFieldName]\n\n          const uploadResp = {\n            status: data.response.status,\n            body,\n            uploadURL,\n          }\n\n          this.uppy.emit('upload-success', file, uploadResp)\n          queuedRequest.done()\n          if (this.uploaderEvents[file.id]) {\n            this.uploaderEvents[file.id].remove()\n            this.uploaderEvents[file.id] = null\n          }\n          return resolve()\n        })\n\n        socket.on('error', (errData) => {\n          const resp = errData.response\n          const error = resp\n            ? opts.getResponseError(resp.responseText, resp)\n            : Object.assign(new Error(errData.error.message), { cause: errData.error })\n          this.uppy.emit('upload-error', file, error)\n          queuedRequest.done()\n          if (this.uploaderEvents[file.id]) {\n            this.uploaderEvents[file.id].remove()\n            this.uploaderEvents[file.id] = null\n          }\n          reject(error)\n        })\n\n        queuedRequest = this.requests.run(() => {\n          socket.open()\n          if (file.isPaused) {\n            socket.send('pause', {})\n          }\n\n          return () => socket.close()\n        })\n      }).catch((err) => {\n        this.uppy.emit('upload-error', file, err)\n        reject(err)\n      })\n    })\n  }\n\n  uploadBundle (files) {\n    return new Promise((resolve, reject) => {\n      const { endpoint } = this.opts\n      const { method } = this.opts\n\n      const optsFromState = this.uppy.getState().xhrUpload\n      const formData = this.createBundledUpload(files, {\n        ...this.opts,\n        ...(optsFromState || {}),\n      })\n\n      const xhr = new XMLHttpRequest()\n\n      const emitError = (error) => {\n        files.forEach((file) => {\n          this.uppy.emit('upload-error', file, error)\n        })\n      }\n\n      const timer = new ProgressTimeout(this.opts.timeout, () => {\n        xhr.abort()\n        const error = new Error(this.i18n('timedOut', { seconds: Math.ceil(this.opts.timeout / 1000) }))\n        emitError(error)\n        reject(error)\n      })\n\n      xhr.upload.addEventListener('loadstart', () => {\n        this.uppy.log('[XHRUpload] started uploading bundle')\n        timer.progress()\n      })\n\n      xhr.upload.addEventListener('progress', (ev) => {\n        timer.progress()\n\n        if (!ev.lengthComputable) return\n\n        files.forEach((file) => {\n          this.uppy.emit('upload-progress', file, {\n            uploader: this,\n            bytesUploaded: (ev.loaded / ev.total) * file.size,\n            bytesTotal: file.size,\n          })\n        })\n      })\n\n      xhr.addEventListener('load', (ev) => {\n        timer.done()\n\n        if (this.opts.validateStatus(ev.target.status, xhr.responseText, xhr)) {\n          const body = this.opts.getResponseData(xhr.responseText, xhr)\n          const uploadResp = {\n            status: ev.target.status,\n            body,\n          }\n          files.forEach((file) => {\n            this.uppy.emit('upload-success', file, uploadResp)\n          })\n          return resolve()\n        }\n\n        const error = this.opts.getResponseError(xhr.responseText, xhr) || new Error('Upload error')\n        error.request = xhr\n        emitError(error)\n        return reject(error)\n      })\n\n      xhr.addEventListener('error', () => {\n        timer.done()\n\n        const error = this.opts.getResponseError(xhr.responseText, xhr) || new Error('Upload error')\n        emitError(error)\n        return reject(error)\n      })\n\n      this.uppy.on('cancel-all', ({ reason } = {}) => {\n        if (reason !== 'user') return\n        timer.done()\n        xhr.abort()\n      })\n\n      xhr.open(method.toUpperCase(), endpoint, true)\n      // IE10 does not allow setting `withCredentials` and `responseType`\n      // before `open()` is called.\n      xhr.withCredentials = this.opts.withCredentials\n      if (this.opts.responseType !== '') {\n        xhr.responseType = this.opts.responseType\n      }\n\n      Object.keys(this.opts.headers).forEach((header) => {\n        xhr.setRequestHeader(header, this.opts.headers[header])\n      })\n\n      xhr.send(formData)\n\n      files.forEach((file) => {\n        this.uppy.emit('upload-started', file)\n      })\n    })\n  }\n\n  uploadFiles (files) {\n    const promises = files.map((file, i) => {\n      const current = parseInt(i, 10) + 1\n      const total = files.length\n\n      if (file.error) {\n        return Promise.reject(new Error(file.error))\n      } if (file.isRemote) {\n        return this.uploadRemote(file, current, total)\n      }\n      return this.upload(file, current, total)\n    })\n\n    return settle(promises)\n  }\n\n  onFileRemove (fileID, cb) {\n    this.uploaderEvents[fileID].on('file-removed', (file) => {\n      if (fileID === file.id) cb(file.id)\n    })\n  }\n\n  onRetry (fileID, cb) {\n    this.uploaderEvents[fileID].on('upload-retry', (targetFileID) => {\n      if (fileID === targetFileID) {\n        cb()\n      }\n    })\n  }\n\n  onRetryAll (fileID, cb) {\n    this.uploaderEvents[fileID].on('retry-all', () => {\n      if (!this.uppy.getFile(fileID)) return\n      cb()\n    })\n  }\n\n  onCancelAll (fileID, eventHandler) {\n    this.uploaderEvents[fileID].on('cancel-all', (...args) => {\n      if (!this.uppy.getFile(fileID)) return\n      eventHandler(...args)\n    })\n  }\n\n  handleUpload (fileIDs) {\n    if (fileIDs.length === 0) {\n      this.uppy.log('[XHRUpload] No files to upload!')\n      return Promise.resolve()\n    }\n\n    // No limit configured by the user, and no RateLimitedQueue passed in by a \"parent\" plugin\n    // (basically just AwsS3) using the internal symbol\n    if (this.opts.limit === 0 && !this.opts[internalRateLimitedQueue]) {\n      this.uppy.log(\n        '[XHRUpload] When uploading multiple files at once, consider setting the `limit` option (to `10` for example), to limit the number of concurrent uploads, which helps prevent memory and network issues: https://uppy.io/docs/xhr-upload/#limit-0',\n        'warning',\n      )\n    }\n\n    this.uppy.log('[XHRUpload] Uploading...')\n    const files = fileIDs.map((fileID) => this.uppy.getFile(fileID))\n\n    if (this.opts.bundle) {\n      // if bundle: true, we don’t support remote uploads\n      const isSomeFileRemote = files.some(file => file.isRemote)\n      if (isSomeFileRemote) {\n        throw new Error('Can’t upload remote files when the `bundle: true` option is set')\n      }\n\n      if (typeof this.opts.headers === 'function') {\n        throw new TypeError('`headers` may not be a function when the `bundle: true` option is set')\n      }\n\n      return this.uploadBundle(files)\n    }\n\n    return this.uploadFiles(files).then(() => null)\n  }\n\n  install () {\n    if (this.opts.bundle) {\n      const { capabilities } = this.uppy.getState()\n      this.uppy.setState({\n        capabilities: {\n          ...capabilities,\n          individualCancellation: false,\n        },\n      })\n    }\n\n    this.uppy.addUploader(this.handleUpload)\n  }\n\n  uninstall () {\n    if (this.opts.bundle) {\n      const { capabilities } = this.uppy.getState()\n      this.uppy.setState({\n        capabilities: {\n          ...capabilities,\n          individualCancellation: true,\n        },\n      })\n    }\n\n    this.uppy.removeUploader(this.handleUpload)\n  }\n}\n"]}