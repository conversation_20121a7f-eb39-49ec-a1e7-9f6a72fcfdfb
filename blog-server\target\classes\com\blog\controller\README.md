# 控制器文档

## UserController - 用户控制器

用户控制器负责处理与用户相关的所有请求，包括获取用户信息、更新用户资料、修改密码和更新头像等功能。

### 基本信息

- **包路径**: `com.blog.controller`
- **请求基础路径**: `/api`

### 接口列表

#### 1. 获取当前登录用户信息

```java
@GetMapping("/user/info")
public Result<UserInfoVO> getCurrentUserInfo()
```

- **描述**: 获取当前登录用户的详细信息
- **请求方式**: GET
- **URL**: `/api/user/info`
- **权限要求**: 需要登录
- **返回数据**: 用户信息视图对象(UserInfoVO)

#### 2. 更新用户信息

```java
@PutMapping({"/user/info", "/users/info"})
public Result<?> updateUserInfo(@RequestBody User user)
```

- **描述**: 更新当前登录用户的个人资料
- **请求方式**: PUT
- **URL**: `/api/user/info` 或 `/api/users/info`
- **权限要求**: 需要登录
- **请求参数**: User对象（只允许修改部分字段）
- **业务规则**:
  - 只能修改自己的信息
  - 不允许修改用户名和角色
  - 不允许通过此接口修改密码

#### 3. 修改密码

```java
@PutMapping("/user/password")
public Result<?> updatePassword(@RequestBody @Valid PasswordUpdateDTO passwordUpdateDTO)
```

- **描述**: 修改当前登录用户的密码
- **请求方式**: PUT
- **URL**: `/api/user/password`
- **权限要求**: 需要登录
- **请求参数**: PasswordUpdateDTO对象
  - `currentPassword`: 当前密码
  - `newPassword`: 新密码
  - `confirmPassword`: 确认新密码
- **业务规则**:
  - 需要验证当前密码是否正确
  - 新密码与确认密码必须一致
  - 密码修改成功后需要重新登录

#### 4. 更新用户头像

```java
@PutMapping("/user/avatar")
public Result<?> updateAvatar(@RequestParam String avatar)
```

- **描述**: 更新当前登录用户的头像
- **请求方式**: PUT
- **URL**: `/api/user/avatar`
- **权限要求**: 需要登录
- **请求参数**: 
  - `avatar`: 头像URL地址
- **业务规则**:
  - 头像URL需要先通过文件上传接口获取

#### 5. 获取指定用户信息（管理员权限）

```java
@GetMapping({"/user/detail/{id}", "/users/{id}"})
@PreAuthorize("hasRole('ADMIN')")
public Result<?> getUserById(@PathVariable Long id)
```

- **描述**: 获取指定ID的用户信息
- **请求方式**: GET
- **URL**: `/api/user/detail/{id}` 或 `/api/users/{id}`
- **权限要求**: 需要ADMIN角色
- **请求参数**: 
  - `id`: 用户ID
- **返回数据**: 用户对象(User)，已清除敏感信息

#### 6. 禁用/启用用户（管理员权限）

```java
@PutMapping({"/user/{id}/status", "/users/{id}/status"})
@PreAuthorize("hasRole('ADMIN')")
public Result<?> updateUserStatus(@PathVariable Long id, @RequestParam Integer status)
```

- **描述**: 更新指定用户的状态（启用/禁用）
- **请求方式**: PUT
- **URL**: `/api/user/{id}/status` 或 `/api/users/{id}/status`
- **权限要求**: 需要ADMIN角色
- **请求参数**: 
  - `id`: 用户ID
  - `status`: 用户状态(0:禁用,1:正常)

### 数据传输对象

#### PasswordUpdateDTO

```java
public class PasswordUpdateDTO {
    private String currentPassword; // 当前密码
    private String newPassword;     // 新密码
    private String confirmPassword; // 确认新密码
}
```

### 视图对象

#### UserInfoVO

```java
public class UserInfoVO {
    private Long id;          // 用户ID
    private String username;  // 用户名
    private String nickname;  // 昵称
    private String email;     // 邮箱
    private String avatar;    // 头像
    private String role;      // 角色
    private Date createTime;  // 创建时间
}
```

### 注意事项

1. 路径冲突问题：避免使用可能与ID路径模式冲突的路径，如`/user/info`可能与`/user/{id}`冲突
2. 安全性：敏感操作需要验证用户身份和权限
3. 数据验证：请求参数需要进行有效性验证
4. 错误处理：接口应当返回明确的错误信息和状态码 