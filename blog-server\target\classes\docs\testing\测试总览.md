# 博客系统测试总览

## 📋 测试结构

本目录包含博客系统各开发阶段的详细测试指南，按功能模块组织。

## 👥 用户角色与权限说明

### 🔑 用户角色定义
- **管理员 (admin)**：系统管理员，拥有所有权限
- **普通用户 (user)**：注册用户，拥有基础功能权限

### 📝 权限对照表
| 功能模块 | 管理员 | 普通用户 | 匿名用户 |
|---------|--------|----------|----------|
| **文章管理** |
| 发布文章 | ✅ | ❌ | ❌ |
| 编辑自己的文章 | ✅ | ❌ | ❌ |
| 编辑他人文章 | ✅ | ❌ | ❌ |
| 删除自己的文章 | ✅ | ❌ | ❌ |
| 删除他人文章 | ✅ | ❌ | ❌ |
| 查看文章 | ✅ | ✅ | ✅ |
| **分类标签** |
| 创建分类标签 | ✅ | ❌ | ❌ |
| 编辑分类标签 | ✅ | ❌ | ❌ |
| 删除分类标签 | ✅ | ❌ | ❌ |
| 查看分类标签 | ✅ | ✅ | ✅ |
| **评论系统** |
| 发表评论 | ✅ | ✅ | ❌ |
| 删除自己评论 | ✅ | ✅ | ❌ |
| 删除他人评论 | ✅ | ❌ | ❌ |
| 评论审核 | ✅ | ❌ | ❌ |
| 查看评论 | ✅ | ✅ | ✅ |
| **用户互动** |
| 点赞收藏 | ✅ | ✅ | ❌ |
| 关注用户 | ✅ | ✅ | ❌ |
| 查看通知 | ✅ | ✅ | ❌ |

### 🧪 测试账号
- **管理员账号**：admin / admin123
- **普通用户1**：user1 / password123
- **普通用户2**：user2 / password123

## 🧪 测试指南列表

### [第一阶段测试指南.md](第一阶段测试指南.md)
**测试内容**：用户认证模块
- 用户注册功能测试
- 用户登录功能测试
- JWT认证机制测试
- 权限控制测试（管理员/普通用户）
- 基础API接口测试

### [第二阶段测试指南.md](第二阶段测试指南.md)
**测试内容**：文章管理模块
- 文章CRUD操作测试
- Markdown编辑器功能测试
- 图片上传功能测试
- 文章发布和草稿保存测试
- 文章列表和详情页面测试

### [第三阶段测试指南.md](第三阶段测试指南.md)
**测试内容**：分类和标签模块
- 分类管理功能测试（增删改查）
- 标签管理功能测试（增删改查）
- 文章与分类标签关联测试
- 按分类标签筛选文章测试
- 管理员权限控制测试

### [第四阶段测试指南.md](第四阶段测试指南.md)
**测试内容**：评论系统模块
- 评论发布和回复功能测试
- 评论树形结构显示测试
- 评论管理页面测试
- 评论权限控制测试
- 评论数据一致性测试

### [第五阶段测试指南.md](第五阶段测试指南.md)
**测试内容**：用户互动模块和系统管理功能
- 文章点赞和收藏功能测试
- 用户关注功能测试
- 消息通知系统测试
- 消息中心页面测试
- 用户个人中心优化测试
- 系统设置管理测试（基本设置、评论设置、邮件设置）
- 用户管理功能测试（用户列表、状态管理、删除用户）
- 归档功能测试（文章归档展示）

## 🚀 快速测试流程

### 环境准备
1. 参考 [../快速开始.md](../快速开始.md) 启动系统
2. 确保前后端服务正常运行
3. 准备测试数据和测试账户

### 测试顺序建议
1. **第一阶段** - 确保用户认证基础功能正常
2. **第二阶段** - 验证文章管理核心功能
3. **第三阶段** - 测试分类标签辅助功能
4. **第四阶段** - 验证评论互动功能
5. **第五阶段** - 测试完整的用户互动体验

### 测试重点
- **功能完整性**：所有功能按预期工作
- **用户体验**：界面友好，操作流畅
- **数据一致性**：数据准确，关联正确
- **权限控制**：安全性验证
- **性能表现**：响应时间合理

## 🔧 测试环境

### 基础要求
- Java 1.8+
- MySQL 8.0+
- Node.js 16+
- 浏览器（Chrome/Firefox推荐）

### 服务端口
- 后端服务：http://localhost:8080
- 前端服务：http://localhost:3000
- 数据库：localhost:3306

### 测试账户
- **管理员**：admin / admin123
- **测试用户**：可自行注册或使用预设账户

## 📊 测试完成标准

### 功能验收标准
- [ ] 所有核心功能正常工作
- [ ] 用户流程完整无阻断
- [ ] 错误处理友好完善
- [ ] 权限控制正确有效

### 性能验收标准
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 500ms
- [ ] 无明显的界面卡顿
- [ ] 移动端适配良好

### 兼容性验收标准
- [ ] 主流浏览器兼容
- [ ] 不同屏幕尺寸适配
- [ ] 基础的无障碍访问支持

## 🐛 问题反馈

### 测试问题记录
发现问题时请记录：
1. 测试阶段和具体功能
2. 复现步骤
3. 预期结果 vs 实际结果
4. 错误截图或日志
5. 浏览器和环境信息

### 问题解决
- 常见问题可参考各阶段测试指南中的问题解决部分
- 配置问题可参考 [../project/系统配置.md](../project/系统配置.md)
- 复杂问题可查看 [../issues/](../issues/) 目录下的问题记录

---

**测试指南版本**：v1.0  
**适用系统版本**：博客系统 v1.0  
**最后更新**：2025年7月14日
