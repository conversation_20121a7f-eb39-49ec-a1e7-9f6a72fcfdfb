#!/bin/bash

# 博客系统智能备份脚本
# 功能：数据库备份、文件备份、自动清理旧备份

# 配置参数
BACKUP_DIR="/opt/blog-system/backups"
DB_NAME="blog_system"
DB_USER="root"
DB_PASS="12345"
UPLOADS_DIR="/opt/blog-system/uploads"
LOG_FILE="/opt/blog-system/logs/backup.log"

# 备份保留策略
KEEP_DAILY=7      # 保留7天的每日备份
KEEP_WEEKLY=4     # 保留4周的周备份
KEEP_MONTHLY=3    # 保留3个月的月备份

# 获取当前时间
DATE=$(date +%Y%m%d_%H%M%S)
TODAY=$(date +%Y%m%d)
WEEK=$(date +%Y%W)
MONTH=$(date +%Y%m)

# 创建备份目录
mkdir -p $BACKUP_DIR/{daily,weekly,monthly}

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 检查磁盘空间
check_disk_space() {
    AVAILABLE=$(df $BACKUP_DIR | tail -1 | awk '{print $4}')
    REQUIRED=1048576  # 1GB in KB
    
    if [ $AVAILABLE -lt $REQUIRED ]; then
        log "警告: 磁盘空间不足，可用空间: $(($AVAILABLE/1024))MB"
        return 1
    fi
    return 0
}

# 数据库备份
backup_database() {
    log "开始数据库备份..."
    
    DB_BACKUP_FILE="$BACKUP_DIR/daily/db_${DB_NAME}_${DATE}.sql"
    
    mysqldump -u$DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        $DB_NAME > $DB_BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        # 压缩备份文件
        gzip $DB_BACKUP_FILE
        log "数据库备份完成: ${DB_BACKUP_FILE}.gz"
        echo "$(ls -lh ${DB_BACKUP_FILE}.gz | awk '{print $5}')"
    else
        log "错误: 数据库备份失败"
        return 1
    fi
}

# 文件备份
backup_files() {
    log "开始文件备份..."
    
    FILES_BACKUP_FILE="$BACKUP_DIR/daily/files_uploads_${DATE}.tar.gz"
    
    tar -czf $FILES_BACKUP_FILE -C $(dirname $UPLOADS_DIR) $(basename $UPLOADS_DIR)
    
    if [ $? -eq 0 ]; then
        log "文件备份完成: $FILES_BACKUP_FILE"
        echo "$(ls -lh $FILES_BACKUP_FILE | awk '{print $5}')"
    else
        log "错误: 文件备份失败"
        return 1
    fi
}

# 创建周备份和月备份
create_periodic_backups() {
    # 周备份（每周一创建）
    if [ $(date +%u) -eq 1 ]; then
        log "创建周备份..."
        cp $BACKUP_DIR/daily/db_${DB_NAME}_${DATE}.sql.gz $BACKUP_DIR/weekly/db_week_${WEEK}.sql.gz 2>/dev/null
        cp $BACKUP_DIR/daily/files_uploads_${DATE}.tar.gz $BACKUP_DIR/weekly/files_week_${WEEK}.tar.gz 2>/dev/null
    fi
    
    # 月备份（每月1号创建）
    if [ $(date +%d) -eq 01 ]; then
        log "创建月备份..."
        cp $BACKUP_DIR/daily/db_${DB_NAME}_${DATE}.sql.gz $BACKUP_DIR/monthly/db_month_${MONTH}.sql.gz 2>/dev/null
        cp $BACKUP_DIR/daily/files_uploads_${DATE}.tar.gz $BACKUP_DIR/monthly/files_month_${MONTH}.tar.gz 2>/dev/null
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份文件..."
    
    # 清理旧的每日备份
    find $BACKUP_DIR/daily -name "*.gz" -mtime +$KEEP_DAILY -delete
    
    # 清理旧的周备份
    find $BACKUP_DIR/weekly -name "*.gz" -mtime +$((KEEP_WEEKLY * 7)) -delete
    
    # 清理旧的月备份
    find $BACKUP_DIR/monthly -name "*.gz" -mtime +$((KEEP_MONTHLY * 30)) -delete
    
    log "旧备份清理完成"
}

# 显示备份统计
show_backup_stats() {
    log "备份统计信息:"
    log "每日备份: $(ls $BACKUP_DIR/daily/*.gz 2>/dev/null | wc -l) 个文件"
    log "周备份: $(ls $BACKUP_DIR/weekly/*.gz 2>/dev/null | wc -l) 个文件"
    log "月备份: $(ls $BACKUP_DIR/monthly/*.gz 2>/dev/null | wc -l) 个文件"
    log "总占用空间: $(du -sh $BACKUP_DIR | cut -f1)"
}

# 主函数
main() {
    log "========== 开始备份任务 =========="
    
    # 检查磁盘空间
    if ! check_disk_space; then
        log "磁盘空间不足，跳过备份"
        exit 1
    fi
    
    # 执行备份
    backup_database
    backup_files
    
    # 创建周期性备份
    create_periodic_backups
    
    # 清理旧备份
    cleanup_old_backups
    
    # 显示统计信息
    show_backup_stats
    
    log "========== 备份任务完成 =========="
}

# 执行主函数
main
