<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.mapper.NotificationMapper">

    <!-- 获取用户的通知列表 -->
    <select id="selectUserNotifications" resultType="com.blog.vo.NotificationVO">
        SELECT 
            n.id,
            n.user_id,
            n.from_user_id,
            u.username as from_username,
            u.nickname as from_nickname,
            u.avatar as from_avatar,
            n.type,
            n.title,
            n.content,
            n.resource_id,
            n.resource_type,
            CASE
                WHEN n.resource_type = 'article' THEN a.title
                WHEN n.resource_type = 'comment' AND n.type = 'reply' THEN
                    (SELECT a2.title FROM article a2 WHERE a2.id = c.article_id)
                WHEN n.resource_type = 'comment' THEN CONCAT('评论: ', SUBSTRING(c.content, 1, 50))
                ELSE NULL
            END as resource_title,
            n.is_read,
            n.create_time,
            n.update_time
        FROM notification n
        LEFT JOIN user u ON n.from_user_id = u.id
        LEFT JOIN article a ON n.resource_type = 'article' AND n.resource_id = a.id
        LEFT JOIN comment c ON n.resource_type = 'comment' AND n.resource_id = c.id
        WHERE n.user_id = #{userId}
        <if test="isRead != null">
            AND n.is_read = #{isRead}
        </if>
        ORDER BY n.create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取用户未读通知数量 -->
    <select id="countUnreadNotifications" resultType="int">
        SELECT COUNT(*)
        FROM notification
        WHERE user_id = #{userId} AND is_read = 0
    </select>

    <!-- 批量标记通知为已读 -->
    <update id="markNotificationsAsRead">
        UPDATE notification 
        SET is_read = 1, update_time = NOW()
        WHERE user_id = #{userId}
        AND id IN
        <foreach collection="notificationIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 标记用户所有通知为已读 -->
    <update id="markAllNotificationsAsRead">
        UPDATE notification 
        SET is_read = 1, update_time = NOW()
        WHERE user_id = #{userId} AND is_read = 0
    </update>

    <!-- 删除用户的通知 -->
    <delete id="deleteUserNotifications">
        DELETE FROM notification
        WHERE user_id = #{userId}
        AND id IN
        <foreach collection="notificationIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 检查是否存在相同的通知 -->
    <select id="countSimilarNotifications" resultType="int">
        SELECT COUNT(*)
        FROM notification
        WHERE user_id = #{userId}
        AND from_user_id = #{fromUserId}
        AND type = #{type}
        <if test="resourceId != null">
            AND resource_id = #{resourceId}
        </if>
        <if test="resourceType != null">
            AND resource_type = #{resourceType}
        </if>
        AND create_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
    </select>

</mapper>
