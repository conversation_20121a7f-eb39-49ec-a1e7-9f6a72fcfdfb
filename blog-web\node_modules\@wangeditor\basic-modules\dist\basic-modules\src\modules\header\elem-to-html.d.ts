/**
 * @description to html
 * <AUTHOR>
 */
import { Element } from 'slate';
export declare const header1ToHtmlConf: {
    type: string;
    elemToHtml: (elem: Element, childrenHtml: string) => string;
};
export declare const header2ToHtmlConf: {
    type: string;
    elemToHtml: (elem: Element, childrenHtml: string) => string;
};
export declare const header3ToHtmlConf: {
    type: string;
    elemToHtml: (elem: Element, childrenHtml: string) => string;
};
export declare const header4ToHtmlConf: {
    type: string;
    elemToHtml: (elem: Element, childrenHtml: string) => string;
};
export declare const header5ToHtmlConf: {
    type: string;
    elemToHtml: (elem: Element, childrenHtml: string) => string;
};
