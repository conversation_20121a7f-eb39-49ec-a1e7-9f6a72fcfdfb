{"name": "@wangeditor/video-module", "version": "1.1.4", "description": "wangEditor video module", "author": "wangfupeng1988 <<EMAIL>>", "contributors": [], "homepage": "https://github.com/wangeditor-team/wangEditor#readme", "license": "MIT", "types": "dist/video-module/src/index.d.ts", "main": "dist/index.js", "module": "dist/index.esm.js", "browser": {"./dist/index.js": "./dist/index.js", "./dist/index.esm.js": "./dist/index.esm.js"}, "directories": {"lib": "dist", "test": "__tests__"}, "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.com/"}, "repository": {"type": "git", "url": "git+https://github.com/wangeditor-team/wangEditor.git"}, "scripts": {"test": "jest", "test-c": "jest --coverage", "dev": "cross-env NODE_ENV=development rollup -c rollup.config.js", "dev-watch": "cross-env NODE_ENV=development rollup -c rollup.config.js -w", "build": "cross-env NODE_ENV=production rollup -c rollup.config.js", "dev-size-stats": "cross-env NODE_ENV=development:size_stats rollup -c rollup.config.js", "size-stats": "cross-env NODE_ENV=production:size_stats rollup -c rollup.config.js"}, "bugs": {"url": "https://github.com/wangeditor-team/wangEditor/issues"}, "peerDependencies": {"@uppy/core": "^2.1.4", "@uppy/xhr-upload": "^2.0.7", "@wangeditor/core": "1.x", "dom7": "^3.0.0", "nanoid": "^3.2.0", "slate": "^0.72.0", "snabbdom": "^3.1.0"}, "gitHead": "e52351a9f1f46f50cc3684d5b142e3d51ac36509"}