<template>
  <div class="rich-text-editor">
    <!-- 工具栏 -->
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <!-- 编辑器 -->
    <Editor
      :style="{ height: height, 'overflow-y': 'hidden' }"
      v-model="valueHtml"
      :defaultConfig="editorConfig.value"
      :mode="mode"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<script>
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, ref, shallowRef, watch, computed } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { useUserStore } from '@/store/user'
import { buildResourceUrl } from '@/config/settings'

export default {
  name: 'RichTextEditor',
  components: { Editor, Toolbar },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: '500px'
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef()
    
    // 内容 HTML
    const valueHtml = ref('')
    
    // 模式
    const mode = ref('default')
    
    // 用户store
    const userStore = useUserStore()
    
    // 上传headers
    const uploadHeaders = computed(() => {
      return {
        Authorization: `Bearer ${userStore.token}`
      }
    })

    // 工具栏配置
    const toolbarConfig = {
      excludeKeys: [
        'group-video' // 排除视频功能
      ]
    }
    
    // 编辑器配置 - 使用computed确保动态更新
    const editorConfig = computed(() => ({
      placeholder: '请输入内容...',
      readOnly: props.readonly,
      MENU_CONF: {
        // 配置上传图片
        uploadImage: {
          server: '/api/image-upload',
          fieldName: 'file',
          headers: uploadHeaders.value,
          maxFileSize: 5 * 1024 * 1024, // 5MB
          customInsert(res, insertFn) {
            console.log('图片上传响应:', res)
            if (res.code === 200) {
              const fullUrl = buildResourceUrl(res.data)
              console.log('插入图片URL:', fullUrl)
              insertFn(fullUrl, '', fullUrl)
            } else {
              console.error('上传失败:', res)
              alert('图片上传失败')
            }
          }
        },
        // 配置代码高亮
        codeSelectLang: {
          langs: [
            'CSS',
            'HTML',
            'XML',
            'JavaScript',
            'TypeScript',
            'Java',
            'Python',
            'C',
            'C++',
            'C#',
            'PHP',
            'Go',
            'Rust',
            'SQL',
            'JSON',
            'Bash',
            'Shell'
          ],
          codeLangs: [
            { text: 'CSS', value: 'css' },
            { text: 'HTML', value: 'html' },
            { text: 'XML', value: 'xml' },
            { text: 'JavaScript', value: 'javascript' },
            { text: 'TypeScript', value: 'typescript' },
            { text: 'Java', value: 'java' },
            { text: 'Python', value: 'python' },
            { text: 'C', value: 'c' },
            { text: 'C++', value: 'cpp' },
            { text: 'C#', value: 'csharp' },
            { text: 'PHP', value: 'php' },
            { text: 'Go', value: 'go' },
            { text: 'Rust', value: 'rust' },
            { text: 'SQL', value: 'sql' },
            { text: 'JSON', value: 'json' },
            { text: 'Bash', value: 'bash' },
            { text: 'Shell', value: 'shell' }
          ]
        }
      }
    }))

    // 编辑器创建完毕时的回调函数
    const handleCreated = (editor) => {
      editorRef.value = editor // 记录 editor 实例，重要！
    }

    // 编辑器内容改变时的回调函数
    const handleChange = (editor) => {
      let html = editor.getHtml()
      
      // 将完整URL转换回相对路径保存到数据库
      if (html) {
        const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
        html = html.replace(
          new RegExp(`<img([^>]*?)src=["']${apiUrl}(/upload/[^"']*?)["']([^>]*)>`, 'g'),
          (match, before, relativePath, after) => {
            console.log('保存时转换图片URL:', `${apiUrl}${relativePath}`, '->', relativePath)
            return `<img${before}src="${relativePath}"${after}>`
          }
        )
      }
      
      emit('update:modelValue', html)
      emit('change', html)
    }

    // 监听modelValue变化，处理图片URL转换
    watch(() => props.modelValue, (newVal) => {
      if (newVal !== valueHtml.value) {
        // 将数据库中的相对路径转换为完整URL供编辑器显示
        let processedContent = newVal || ''
        if (processedContent) {
          processedContent = processedContent.replace(
            /<img([^>]*?)src=["']([^"']*?)["']([^>]*)>/g,
            (match, before, src, after) => {
              if (!src.startsWith('http') && !src.startsWith('data:')) {
                const fullSrc = buildResourceUrl(src)
                console.log('编辑器加载图片:', src, '->', fullSrc)
                return `<img${before}src="${fullSrc}"${after}>`
              }
              return match
            }
          )
        }
        valueHtml.value = processedContent
      }
    }, { immediate: true })

    // 监听readonly变化
    watch(() => props.readonly, (newVal) => {
      if (editorRef.value) {
        if (newVal) {
          editorRef.value.disable()
        } else {
          editorRef.value.enable()
        }
      }
    })

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
      const editor = editorRef.value
      if (editor == null) return
      editor.destroy()
    })

    return {
      editorRef,
      valueHtml,
      mode,
      toolbarConfig,
      editorConfig,
      handleCreated,
      handleChange
    }
  }
}
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #ccc;
  border-radius: 4px;
  z-index: 100;
}
</style>