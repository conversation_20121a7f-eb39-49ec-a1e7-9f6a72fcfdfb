<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.mapper.TagMapper">

    <resultMap id="TagWithCountResultMap" type="com.blog.vo.TagVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="articleCount" column="article_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectTagWithArticleCount" resultMap="TagWithCountResultMap">
        SELECT
            t.id,
            t.name,
            t.create_time,
            t.update_time,
            COUNT(at.article_id) as article_count
        FROM
            tag t
        LEFT JOIN
            article_tag at ON t.id = at.tag_id
        GROUP BY
            t.id, t.name, t.create_time, t.update_time
        ORDER BY
            t.create_time DESC
    </select>

    <select id="selectByArticleId" resultType="com.blog.entity.Tag">
        SELECT t.* FROM tag t JOIN article_tag at ON t.id = at.tag_id WHERE at.article_id = #{articleId}
    </select>

    <select id="countArticleByTagId" resultType="int">
        SELECT COUNT(*) FROM article_tag WHERE tag_id = #{tagId}
    </select>

    <select id="selectPopularTags" resultType="com.blog.entity.Tag">
        SELECT t.*
        FROM tag t
        JOIN article_tag at ON t.id = at.tag_id
        GROUP BY t.id
        ORDER BY COUNT(at.article_id) DESC
        LIMIT #{limit}
    </select>

</mapper> 