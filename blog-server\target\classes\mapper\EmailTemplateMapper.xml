<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blog.mapper.EmailTemplateMapper">

    <!-- 根据模板代码查询模板 -->
    <select id="selectByTemplateCode" resultType="com.blog.entity.EmailTemplate">
        SELECT id, template_name, template_code, subject, content, template_type, 
               description, status, create_time, update_time
        FROM email_template
        WHERE template_code = #{templateCode}
        AND status = 1
    </select>

    <!-- 检查模板代码是否存在（排除指定ID） -->
    <select id="countByTemplateCode" resultType="int">
        SELECT COUNT(1)
        FROM email_template
        WHERE template_code = #{templateCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
