@echo off
echo 开始部署博客系统到新服务器 **************...

echo 1. 后端打包...
cd blog-server
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo 后端打包失败！
    pause
    exit /b 1
)

echo 2. 前端打包...
cd ..\blog-web
call npm run build
if %errorlevel% neq 0 (
    echo 前端打包失败！
    pause
    exit /b 1
)

echo 3. 上传文件到服务器 **************...
cd ..
scp -P 22 blog-server\target\blog-server-0.0.1-SNAPSHOT.jar root@**************:/opt/blog-system/backend/
scp -P 22 blog-server\src\main\resources\application.yml root@**************:/opt/blog-system/backend/
scp -r -P 22 blog-web\dist\* root@**************:/opt/blog-system/frontend/dist/

echo 注意：数据库表和数据需要手动导入，不在自动部署范围内

echo 4. 重启服务...
ssh root@************** -p 22 "cd /opt/blog-system/backend && ./restart.sh"

echo 部署完成！
echo 访问地址: http://**************
pause
