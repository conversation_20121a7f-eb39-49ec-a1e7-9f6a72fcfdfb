{"version": 3, "file": "operation.d.ts", "sourceRoot": "", "sources": ["../packages/slate/src/interfaces/operation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,IAAI,CAAA;AAGpD,oBAAY,uBAAuB,GAAG;IACpC,IAAI,EAAE,aAAa,CAAA;IACnB,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,IAAI,CAAA;CACX,CAAA;AAED,oBAAY,mBAAmB,GAAG,YAAY,CAC5C,qBAAqB,EACrB,uBAAuB,CACxB,CAAA;AAED,oBAAY,uBAAuB,GAAG;IACpC,IAAI,EAAE,aAAa,CAAA;IACnB,IAAI,EAAE,IAAI,CAAA;IACV,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;CACb,CAAA;AAED,oBAAY,mBAAmB,GAAG,YAAY,CAC5C,qBAAqB,EACrB,uBAAuB,CACxB,CAAA;AAED,oBAAY,sBAAsB,GAAG;IACnC,IAAI,EAAE,YAAY,CAAA;IAClB,IAAI,EAAE,IAAI,CAAA;IACV,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;CAC1B,CAAA;AAED,oBAAY,kBAAkB,GAAG,YAAY,CAC3C,oBAAoB,EACpB,sBAAsB,CACvB,CAAA;AAED,oBAAY,qBAAqB,GAAG;IAClC,IAAI,EAAE,WAAW,CAAA;IACjB,IAAI,EAAE,IAAI,CAAA;IACV,OAAO,EAAE,IAAI,CAAA;CACd,CAAA;AAED,oBAAY,iBAAiB,GAAG,YAAY,CAC1C,mBAAmB,EACnB,qBAAqB,CACtB,CAAA;AAED,oBAAY,uBAAuB,GAAG;IACpC,IAAI,EAAE,aAAa,CAAA;IACnB,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,IAAI,CAAA;CACX,CAAA;AAED,oBAAY,mBAAmB,GAAG,YAAY,CAC5C,qBAAqB,EACrB,uBAAuB,CACxB,CAAA;AAED,oBAAY,uBAAuB,GAAG;IACpC,IAAI,EAAE,aAAa,CAAA;IACnB,IAAI,EAAE,IAAI,CAAA;IACV,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;CACb,CAAA;AAED,oBAAY,mBAAmB,GAAG,YAAY,CAC5C,qBAAqB,EACrB,uBAAuB,CACxB,CAAA;AAED,oBAAY,oBAAoB,GAAG;IACjC,IAAI,EAAE,UAAU,CAAA;IAChB,IAAI,EAAE,IAAI,CAAA;IACV,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;IACzB,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;CAC7B,CAAA;AAED,oBAAY,gBAAgB,GAAG,YAAY,CACzC,kBAAkB,EAClB,oBAAoB,CACrB,CAAA;AAED,oBAAY,yBAAyB,GACjC;IACE,IAAI,EAAE,eAAe,CAAA;IACrB,UAAU,EAAE,IAAI,CAAA;IAChB,aAAa,EAAE,KAAK,CAAA;CACrB,GACD;IACE,IAAI,EAAE,eAAe,CAAA;IACrB,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;IAC1B,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;CAC9B,GACD;IACE,IAAI,EAAE,eAAe,CAAA;IACrB,UAAU,EAAE,KAAK,CAAA;IACjB,aAAa,EAAE,IAAI,CAAA;CACpB,CAAA;AAEL,oBAAY,qBAAqB,GAAG,YAAY,CAC9C,uBAAuB,EACvB,yBAAyB,CAC1B,CAAA;AAED,oBAAY,sBAAsB,GAAG;IACnC,IAAI,EAAE,YAAY,CAAA;IAClB,IAAI,EAAE,IAAI,CAAA;IACV,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;CAC1B,CAAA;AAED,oBAAY,kBAAkB,GAAG,YAAY,CAC3C,oBAAoB,EACpB,sBAAsB,CACvB,CAAA;AAED,oBAAY,aAAa,GACrB,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,mBAAmB,GACnB,gBAAgB,GAChB,kBAAkB,CAAA;AAEtB,oBAAY,kBAAkB,GAAG,qBAAqB,CAAA;AAEtD,oBAAY,aAAa,GAAG,mBAAmB,GAAG,mBAAmB,CAAA;AAErE;;;;;GAKG;AAEH,oBAAY,aAAa,GAAG,aAAa,GAAG,kBAAkB,GAAG,aAAa,CAAA;AAC9E,oBAAY,SAAS,GAAG,YAAY,CAAC,WAAW,EAAE,aAAa,CAAC,CAAA;AAEhE,MAAM,WAAW,kBAAkB;IACjC,eAAe,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,aAAa,CAAA;IACvD,WAAW,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,SAAS,CAAA;IAC/C,eAAe,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,SAAS,EAAE,CAAA;IACrD,oBAAoB,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,kBAAkB,CAAA;IACjE,eAAe,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,aAAa,CAAA;IACvD,OAAO,EAAE,CAAC,EAAE,EAAE,SAAS,KAAK,SAAS,CAAA;CACtC;AAED,eAAO,MAAM,SAAS,EAAE,kBA+KvB,CAAA"}