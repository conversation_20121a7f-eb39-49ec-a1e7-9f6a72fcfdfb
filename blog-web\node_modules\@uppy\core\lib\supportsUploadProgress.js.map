{"version": 3, "sources": ["supportsUploadProgress.js"], "names": ["supportsUploadProgress", "userAgent", "navigator", "m", "exec", "edgeVersion", "major", "minor", "split", "parseInt"], "mappings": ";;AAGe,SAASA,sBAAT,CAAiCC,SAAjC,EAA4C;AACzD;AACA,MAAIA,SAAS,IAAI,IAAb,IAAqB,OAAOC,SAAP,KAAqB,WAA9C,EAA2D;AACzD;AACAD,IAAAA,SAAS,GAAGC,SAAS,CAACD,SAAtB;AACD,GALwD,CAMzD;;;AACA,MAAI,CAACA,SAAL,EAAgB,OAAO,IAAP;AAEhB,QAAME,CAAC,GAAG,mBAAmBC,IAAnB,CAAwBH,SAAxB,CAAV;AACA,MAAI,CAACE,CAAL,EAAQ,OAAO,IAAP;AAER,QAAME,WAAW,GAAGF,CAAC,CAAC,CAAD,CAArB;AACA,MAAI,CAACG,KAAD,EAAQC,KAAR,IAAiBF,WAAW,CAACG,KAAZ,CAAkB,GAAlB,CAArB;AACAF,EAAAA,KAAK,GAAGG,QAAQ,CAACH,KAAD,EAAQ,EAAR,CAAhB;AACAC,EAAAA,KAAK,GAAGE,QAAQ,CAACF,KAAD,EAAQ,EAAR,CAAhB,CAfyD,CAiBzD;AACA;AACA;;AACA,MAAID,KAAK,GAAG,EAAR,IAAeA,KAAK,KAAK,EAAV,IAAgBC,KAAK,GAAG,KAA3C,EAAmD;AACjD,WAAO,IAAP;AACD,GAtBwD,CAwBzD;AACA;;;AACA,MAAID,KAAK,GAAG,EAAR,IAAeA,KAAK,KAAK,EAAV,IAAgBC,KAAK,IAAI,KAA5C,EAAoD;AAClD,WAAO,IAAP;AACD,GA5BwD,CA8BzD;;;AACA,SAAO,KAAP;AACD;;AAnCD;AACA;AACA;iBACwBP,sB", "sourcesContent": ["// Edge 15.x does not fire 'progress' events on uploads.\n// See https://github.com/transloadit/uppy/issues/945\n// And https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/12224510/\nexport default function supportsUploadProgress (userAgent) {\n  // Allow passing in userAgent for tests\n  if (userAgent == null && typeof navigator !== 'undefined') {\n    // eslint-disable-next-line no-param-reassign\n    userAgent = navigator.userAgent\n  }\n  // Assume it works because basically everything supports progress events.\n  if (!userAgent) return true\n\n  const m = /Edge\\/(\\d+\\.\\d+)/.exec(userAgent)\n  if (!m) return true\n\n  const edgeVersion = m[1]\n  let [major, minor] = edgeVersion.split('.')\n  major = parseInt(major, 10)\n  minor = parseInt(minor, 10)\n\n  // Worked before:\n  // Edge 40.15063.0.0\n  // Microsoft EdgeHTML 15.15063\n  if (major < 15 || (major === 15 && minor < 15063)) {\n    return true\n  }\n\n  // Fixed in:\n  // Microsoft EdgeHTML 18.18218\n  if (major > 18 || (major === 18 && minor >= 18218)) {\n    return true\n  }\n\n  // other versions don't work.\n  return false\n}\n"]}